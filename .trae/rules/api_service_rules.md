# GBH API 服務開發規則

## 專案概述
這是一個基於 go-zero 框架的 RESTful API 服務，提供 HTTP 接口供前端和外部系統調用。採用分層架構設計，包含 Handler、Logic、Repository 等層次。

## 技術棧
- **框架**: go-zero (REST)
- **語言**: Go 1.24+
- **協議**: HTTP/HTTPS + JSON
- **路由**: go-zero REST
- **資料庫**: MySQL + GORM
- **快取**: Redis
- **驗證**: go-playground/validator

## 目錄結構規範

### API 服務標準目錄結構
```
{service_name}/
├── {service_name}.go          # 服務主入口
├── {service_name}.api         # API 定義檔案
├── etc/
│   └── {service_name}.yaml.example  # 配置範例
├── internal/
│   ├── config/                # 配置結構
│   ├── constants/             # 常數定義
│   ├── handler/               # HTTP 處理器
│   │   ├── {module}/          # 按模組分組
│   │   └── routes.go          # 路由註冊
│   ├── logic/                 # 業務邏輯層
│   │   └── {module}/          # 按模組分組
│   ├── middleware/            # 中間件
│   ├── repository/            # 資料存取層
│   ├── svc/                   # 服務上下文
│   ├── types/                 # 請求/回應類型
│   └── validator/             # 自訂驗證器
└── routes/
    ├── base.api               # 基礎 API 定義
    └── {module}.api           # 模組 API 定義
```

## API 定義規範

### 1. API 檔案結構
```api
syntax = "v1"

info (
    title:   "{Service} API"
    desc:    "{Service} API"
    version: "v1"
)

// 請求類型定義
type {Operation}Request {
    // 欄位定義
}

// 回應類型定義
type {Operation}Response {
    BaseResponse
    // 額外欄位
}

// 服務定義
service {service-name} {
    @handler {operationHandler}
    {method} /{path} ({Operation}Request) returns ({Operation}Response)
}
```

### 2. 請求/回應類型規範
```go
// 基礎回應結構
type BaseResponse struct {
    Code    int32  `json:"code"`
    Message string `json:"message"`
    Data    any    `json:"data,omitempty"`
}

// 分頁請求
type PaginationRequest struct {
    Page     *uint32 `form:"page,optional" validate:"omitempty,gte=1"`
    PageSize *uint32 `form:"page_size,optional" validate:"omitempty,gte=1,lte=100"`
}

// 分頁回應
type PaginationResponse struct {
    Total       uint32 `json:"total"`
    TotalPages  uint32 `json:"total_pages"`
    CurrentPage uint32 `json:"current_page"`
    PageSize    uint32 `json:"page_size"`
}
```

## Handler 層實現

### 1. Handler 函數結構
```go
func {Operation}Handler(svcCtx *svc.ServiceContext) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        var req types.{Operation}Request
        if err := httpx.Parse(r, &req); err != nil {
            httpx.Error(w, errorx.InvalidParameters)
            return
        }

        // 參數驗證
        valid := validator.New(r.Context())
        if validErr := valid.ValidAll(req); validErr != nil {
            httpx.Error(w, validErr)
            return
        }

        // 調用業務邏輯
        l := logic.New{Operation}Logic(r.Context(), svcCtx)
        resp, err := l.{Operation}(&req)

        if err != nil {
            httpx.Error(w, err)
            return
        }

        httpx.OkJsonCtx(r.Context(), w, resp)
    }
}
```

### 2. 路由註冊
```go
func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
    server.AddRoutes(
        []rest.Route{
            {
                Method:  http.MethodGet,
                Path:    "/api/v1/{resource}",
                Handler: {module}.{Operation}Handler(serverCtx),
            },
        },
        rest.WithPrefix("/api/v1"),
        rest.WithMiddlewares([]rest.Middleware{
            middleware.AuthMiddleware,
            middleware.LogMiddleware,
        }...),
    )
}
```

## Logic 層實現

### 1. Logic 結構
```go
type {Operation}Logic struct {
    logx.Logger
    ctx    context.Context
    svcCtx *svc.ServiceContext
}

func New{Operation}Logic(ctx context.Context, svcCtx *svc.ServiceContext) *{Operation}Logic {
    return &{Operation}Logic{
        Logger: logx.WithContext(ctx),
        ctx:    ctx,
        svcCtx: svcCtx,
    }
}

func (l *{Operation}Logic) {Operation}(req *types.{Operation}Request) (*types.BaseResponse, error) {
    // 業務邏輯實現
    
    return &types.BaseResponse{
        Code:    0,
        Message: "success",
        Data:    result,
    }, nil
}
```

## 中間件規範

### 1. 認證中間件
```go
func AuthMiddleware(next http.HandlerFunc) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        // 認證邏輯
        token := r.Header.Get("Authorization")
        if token == "" {
            httpx.Error(w, errorx.Unauthorized)
            return
        }
        
        // 驗證 token
        
        next(w, r)
    }
}
```

### 2. 日誌中間件
```go
func LogMiddleware(next http.HandlerFunc) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        start := time.Now()
        
        // 記錄請求
        logx.Infof("Request: %s %s", r.Method, r.URL.Path)
        
        next(w, r)
        
        // 記錄回應
        logx.Infof("Response: %s %s - %v", r.Method, r.URL.Path, time.Since(start))
    }
}
```

## 驗證規範

### 1. 請求參數驗證
```go
// 使用 struct tags 進行驗證
type CreateUserRequest struct {
    Username string `json:"username" validate:"required,min=3,max=20"`
    Email    string `json:"email" validate:"required,email"`
    Age      int    `json:"age" validate:"gte=0,lte=130"`
}

// 自訂驗證器
func (v *Validator) ValidateCreateUser(req *CreateUserRequest) error {
    if err := v.validator.Struct(req); err != nil {
        return errorx.NewValidationError(err)
    }
    return nil
}
```

### 2. 業務邏輯驗證
```go
func (l *CreateUserLogic) validateBusinessRules(req *types.CreateUserRequest) error {
    // 檢查用戶名是否已存在
    exists, err := l.svcCtx.UserRepo.ExistsByUsername(l.ctx, req.Username)
    if err != nil {
        return err
    }
    if exists {
        return errorx.UserAlreadyExists
    }
    
    return nil
}
```

## 錯誤處理

### 1. 統一錯誤回應
```go
// 設置全域錯誤處理器
httpx.SetErrorHandler(func(err error) (int, any) {
    errx := errorx.ErrorToErrorx(err)
    
    return http.StatusOK, &types.BaseResponse{
        Code:    errx.Code,
        Message: errx.Message,
    }
})
```

### 2. 自訂錯誤類型
```go
var (
    InvalidParameters = errorx.New(40001, "參數錯誤")
    Unauthorized     = errorx.New(40101, "未授權")
    UserNotFound     = errorx.New(40401, "用戶不存在")
    InternalError    = errorx.New(50001, "內部錯誤")
)
```

## 資料庫操作

### 1. Repository 模式
```go
type UserRepository interface {
    Create(ctx context.Context, user *model.User) error
    GetByID(ctx context.Context, id uint32) (*model.User, error)
    Update(ctx context.Context, user *model.User) error
    Delete(ctx context.Context, id uint32) error
    List(ctx context.Context, req *ListRequest) ([]*model.User, int64, error)
}

type userRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
    return &userRepository{db: db}
}
```

### 2. 事務處理
```go
func (l *CreateUserLogic) CreateUser(req *types.CreateUserRequest) error {
    return l.svcCtx.DB.Transaction(func(tx *gorm.DB) error {
        // 創建用戶
        user := &model.User{
            Username: req.Username,
            Email:    req.Email,
        }
        
        if err := tx.Create(user).Error; err != nil {
            return err
        }
        
        // 其他相關操作
        
        return nil
    })
}
```

## 快取策略

### 1. Redis 操作
```go
func (l *GetUserLogic) GetUser(req *types.GetUserRequest) (*types.GetUserResponse, error) {
    // 先從快取獲取
    cacheKey := fmt.Sprintf("user:%d", req.ID)
    cached, err := l.svcCtx.Redis.Get(l.ctx, cacheKey).Result()
    if err == nil {
        var user model.User
        if err := json.Unmarshal([]byte(cached), &user); err == nil {
            return &types.GetUserResponse{
                BaseResponse: types.BaseResponse{Code: 0, Message: "success"},
                User:         user,
            }, nil
        }
    }
    
    // 從資料庫獲取
    user, err := l.svcCtx.UserRepo.GetByID(l.ctx, req.ID)
    if err != nil {
        return nil, err
    }
    
    // 寫入快取
    userJSON, _ := json.Marshal(user)
    l.svcCtx.Redis.Set(l.ctx, cacheKey, userJSON, time.Hour)
    
    return &types.GetUserResponse{
        BaseResponse: types.BaseResponse{Code: 0, Message: "success"},
        User:         *user,
    }, nil
}
```

## 測試規範

### 1. Handler 測試
```go
func TestCreateUserHandler(t *testing.T) {
    // 設置測試環境
    svcCtx := &svc.ServiceContext{}
    handler := CreateUserHandler(svcCtx)
    
    // 準備測試資料
    reqBody := `{"username":"test","email":"<EMAIL>"}`
    req := httptest.NewRequest("POST", "/api/v1/users", strings.NewReader(reqBody))
    req.Header.Set("Content-Type", "application/json")
    
    w := httptest.NewRecorder()
    handler(w, req)
    
    // 驗證結果
    assert.Equal(t, http.StatusOK, w.Code)
}
```

### 2. Logic 測試
```go
func TestCreateUserLogic(t *testing.T) {
    // Mock 依賴
    mockRepo := &MockUserRepository{}
    svcCtx := &svc.ServiceContext{
        UserRepo: mockRepo,
    }
    
    logic := NewCreateUserLogic(context.Background(), svcCtx)
    
    // 測試正常情況
    req := &types.CreateUserRequest{
        Username: "test",
        Email:    "<EMAIL>",
    }
    
    resp, err := logic.CreateUser(req)
    assert.NoError(t, err)
    assert.Equal(t, int32(0), resp.Code)
}
```

## 性能優化

### 1. 分頁查詢
```go
func (r *userRepository) List(ctx context.Context, req *ListRequest) ([]*model.User, int64, error) {
    var users []*model.User
    var total int64
    
    query := r.db.WithContext(ctx).Model(&model.User{})
    
    // 計算總數
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // 分頁查詢
    offset := (req.Page - 1) * req.PageSize
    if err := query.Offset(int(offset)).Limit(int(req.PageSize)).Find(&users).Error; err != nil {
        return nil, 0, err
    }
    
    return users, total, nil
}
```

### 2. 連接池配置
```go
func setupDB(config DatabaseConfig) (*gorm.DB, error) {
    db, err := gorm.Open(mysql.Open(config.DSN), &gorm.Config{})
    if err != nil {
        return nil, err
    }
    
    sqlDB, err := db.DB()
    if err != nil {
        return nil, err
    }
    
    // 設置連接池
    sqlDB.SetMaxIdleConns(config.MaxIdleConns)
    sqlDB.SetMaxOpenConns(config.MaxOpenConns)
    sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)
    
    return db, nil
}
```

## 安全規範

### 1. 輸入驗證
- 所有外部輸入都必須驗證
- 使用白名單而非黑名單
- 防止 SQL 注入和 XSS 攻擊

### 2. 認證授權
- 實現 JWT 或 Session 認證
- 使用 RBAC 權限模型
- 敏感操作需要額外驗證

### 3. 資料保護
- 敏感資料加密存儲
- 使用 HTTPS 傳輸
- 實現資料脫敏

## 監控和日誌

### 1. 結構化日誌
```go
logx.WithContext(ctx).Infow("user created", 
    "user_id", user.ID,
    "username", user.Username,
    "operation", "create_user",
)
```

### 2. 指標收集
- API 請求次數和延遲
- 錯誤率統計
- 業務指標監控

## 程式碼生成

使用 go-zero 工具鏈：
```bash
# 生成 API 服務程式碼
goctl api go -api {service_name}.api -dir .

# 生成 Handler
goctl api handler -api {service_name}.api -dir .

# 生成 Logic
goctl api logic -api {service_name}.api -dir .
```