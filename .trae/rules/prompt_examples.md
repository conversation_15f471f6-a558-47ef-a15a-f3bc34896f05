# GBH 專案 Trae AI 提示詞範例

本檔案提供了在 GBH 專案中使用 Trae AI 的具體提示詞範例，幫助開發者快速生成符合專案規範的程式碼。

## RPC 服務開發提示詞範例

### 1. 創建新的 RPC 服務

**提示詞：**
```
請為 GBH 專案創建一個名為 "notification" 的 RPC 服務，用於處理通知相關功能。需要包含以下功能：
1. 發送通知 (SendNotification)
2. 獲取通知列表 (GetNotificationList)
3. 標記通知為已讀 (MarkAsRead)
4. 刪除通知 (DeleteNotification)

請按照專案的 RPC 服務規範創建完整的服務結構，包括：
- Protocol Buffers 定義
- 服務實現
- Logic 層
- 配置檔案
- 測試檔案
```

**預期輸出：**
- `notification/notification.proto` - Protocol Buffers 定義
- `notification/notification.go` - 服務主入口
- `notification/internal/server/notificationserver.go` - gRPC 服務實現
- `notification/internal/logic/` - 各個方法的業務邏輯
- `notification/internal/config/config.go` - 配置結構
- `notification/etc/notification.yaml.example` - 配置範例

### 2. 為現有 RPC 服務添加新方法

**提示詞：**
```
請為 admin RPC 服務添加一個新的方法 "GetAdminProfile"，用於獲取管理員個人資料。

需求：
- 輸入：管理員 ID
- 輸出：管理員詳細資料（ID、用戶名、別名、部門、職位、創建時間、最後登入時間）
- 需要包含完整的 proto 定義、server 實現、logic 實現和測試
```

### 3. RPC 服務錯誤處理優化

**提示詞：**
```
請優化 admin RPC 服務的錯誤處理機制，要求：
1. 統一錯誤碼定義
2. 結構化錯誤回應
3. 詳細的錯誤日誌記錄
4. 適當的 gRPC 狀態碼映射

請修改現有的 LoginLogic 作為範例。
```

## API 服務開發提示詞範例

### 1. 創建新的 API 模組

**提示詞：**
```
請為 backstage API 服務創建一個新的 "analytics" 模組，用於數據分析功能。需要包含以下 API：

1. GET /api/v1/analytics/dashboard - 獲取儀表板數據
2. GET /api/v1/analytics/reports - 獲取報表列表
3. POST /api/v1/analytics/reports - 創建新報表
4. GET /api/v1/analytics/reports/{id} - 獲取特定報表
5. PUT /api/v1/analytics/reports/{id} - 更新報表
6. DELETE /api/v1/analytics/reports/{id} - 刪除報表

請按照專案的 API 服務規範創建完整的模組結構。
```

**預期輸出：**
- `backstage/routes/analytics.api` - API 定義
- `backstage/internal/handler/analytics/` - HTTP 處理器
- `backstage/internal/logic/analytics/` - 業務邏輯
- `backstage/internal/types/` - 請求/回應類型更新
- 相應的測試檔案

### 2. 實現 RESTful API 的 CRUD 操作

**提示詞：**
```
請為 backstage API 服務實現用戶管理的完整 CRUD 操作：

1. 創建用戶 (POST /api/v1/users)
2. 獲取用戶列表 (GET /api/v1/users) - 支持分頁、排序、篩選
3. 獲取單個用戶 (GET /api/v1/users/{id})
4. 更新用戶 (PUT /api/v1/users/{id})
5. 刪除用戶 (DELETE /api/v1/users/{id})

要求：
- 完整的參數驗證
- 統一的錯誤處理
- 適當的 HTTP 狀態碼
- 結構化的 JSON 回應
- 包含單元測試
```

### 3. 添加認證和授權中間件

**提示詞：**
```
請為 backstage API 服務實現 JWT 認證和 RBAC 授權機制：

1. JWT 認證中間件 - 驗證 token 有效性
2. 權限檢查中間件 - 基於角色的訪問控制
3. 登入 API - 生成 JWT token
4. 刷新 token API
5. 登出 API

要求：
- 使用 RS256 算法
- Token 過期時間配置
- 權限細粒度控制
- 安全的密鑰管理
```

## 資料庫相關提示詞範例

### 1. 創建資料模型和 Repository

**提示詞：**
```
請為 GBH 專案創建一個 "Order" 資料模型和對應的 Repository，包含以下欄位：
- ID (主鍵)
- OrderNumber (訂單號，唯一)
- UserID (用戶 ID，外鍵)
- Amount (金額)
- Status (狀態：pending, completed, cancelled)
- CreatedAt (創建時間)
- UpdatedAt (更新時間)

要求：
- 使用 GORM 標籤
- 實現完整的 CRUD 操作
- 包含索引定義
- 軟刪除支持
- 事務處理範例
```

### 2. 資料庫遷移腳本

**提示詞：**
```
請創建資料庫遷移腳本，為現有的 users 表添加以下欄位：
- avatar_url (頭像 URL)
- last_login_at (最後登入時間)
- login_count (登入次數)
- is_verified (是否已驗證)

要求：
- 向前和向後遷移腳本
- 適當的預設值
- 索引優化
- 資料完整性檢查
```

## 測試相關提示詞範例

### 1. 單元測試生成

**提示詞：**
```
請為 admin RPC 服務的 LoginLogic 創建完整的單元測試，包括：
1. 正常登入流程測試
2. 錯誤密碼測試
3. 用戶不存在測試
4. 網路錯誤測試
5. 並發登入測試

要求：
- 使用 testify 框架
- Mock 外部依賴
- 測試覆蓋率 > 90%
- 包含基準測試
```

### 2. 整合測試

**提示詞：**
```
請為 backstage API 服務創建整合測試，測試用戶管理模組的完整流程：
1. 創建用戶
2. 登入獲取 token
3. 使用 token 訪問受保護的 API
4. 更新用戶資料
5. 刪除用戶

要求：
- 使用測試資料庫
- 測試資料清理
- HTTP 客戶端測試
- 斷言完整性
```

## 性能優化提示詞範例

### 1. 快取實現

**提示詞：**
```
請為 backstage API 服務的用戶查詢功能添加 Redis 快取機制：
1. 單個用戶查詢快取
2. 用戶列表查詢快取
3. 快取失效策略
4. 快取預熱
5. 快取穿透防護

要求：
- 合理的過期時間
- 快取鍵命名規範
- 快取更新策略
- 監控指標
```

### 2. 資料庫查詢優化

**提示詞：**
```
請優化 admin RPC 服務中的用戶列表查詢性能：
1. 分析現有查詢的性能瓶頸
2. 添加適當的資料庫索引
3. 優化 SQL 查詢語句
4. 實現查詢結果快取
5. 添加分頁優化

要求：
- 查詢時間 < 100ms
- 支持大數據量
- 記憶體使用優化
- 包含性能測試
```

## 部署和運維提示詞範例

### 1. Docker 配置

**提示詞：**
```
請為 GBH 專案創建完整的 Docker 配置：
1. 多階段構建的 Dockerfile
2. docker-compose.yml 用於本地開發
3. 生產環境的 Docker 配置
4. 健康檢查配置
5. 日誌收集配置

要求：
- 最小化鏡像大小
- 安全性考慮
- 環境變數管理
- 資源限制
```

### 2. 監控和日誌

**提示詞：**
```
請為 GBH 專案實現完整的監控和日誌系統：
1. 結構化日誌輸出
2. 指標收集 (Prometheus)
3. 鏈路追蹤 (Jaeger)
4. 健康檢查端點
5. 告警規則配置

要求：
- 統一的日誌格式
- 關鍵業務指標
- 錯誤追蹤
- 性能監控
```

## 安全相關提示詞範例

### 1. 安全加固

**提示詞：**
```
請對 GBH 專案進行安全加固：
1. 輸入驗證和清理
2. SQL 注入防護
3. XSS 攻擊防護
4. CSRF 保護
5. 速率限制
6. 敏感資料加密

要求：
- 遵循 OWASP 安全標準
- 安全配置檢查
- 漏洞掃描
- 安全測試
```

### 2. 權限系統設計

**提示詞：**
```
請為 GBH 專案設計和實現細粒度的權限系統：
1. 角色定義 (超級管理員、管理員、操作員)
2. 權限定義 (讀取、寫入、刪除、審核)
3. 資源權限控制
4. 動態權限檢查
5. 權限繼承機制

要求：
- RBAC 模型
- 權限快取
- 審計日誌
- 權限變更通知
```

## 使用技巧

### 1. 提示詞最佳實踐
- **明確需求**：清楚描述要實現的功能和需求
- **指定規範**：明確要遵循的編碼規範和架構模式
- **包含範例**：提供具體的輸入輸出範例
- **測試要求**：明確測試覆蓋率和測試類型要求
- **性能指標**：指定性能要求和優化目標

### 2. 常用修飾詞
- "請按照 GBH 專案的規範"
- "使用 go-zero 框架"
- "遵循 Clean Architecture"
- "包含完整的錯誤處理"
- "添加適當的日誌記錄"
- "實現單元測試"
- "考慮性能優化"
- "確保安全性"

### 3. 分步驟開發
當需求複雜時，可以分步驟進行：
1. 先創建基本結構
2. 再實現核心邏輯
3. 然後添加錯誤處理
4. 最後優化和測試

### 4. 程式碼審查提示詞
```
請審查以下程式碼，檢查是否符合 GBH 專案的規範：
1. 程式碼風格和命名規範
2. 錯誤處理是否完整
3. 安全性問題
4. 性能優化機會
5. 測試覆蓋率

[貼上程式碼]

請提供具體的改進建議。
```