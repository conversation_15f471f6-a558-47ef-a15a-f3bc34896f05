# GBH RPC 服務開發規則

## 專案概述
這是一個基於 go-zero 框架的微服務專案，使用 gRPC 進行服務間通信。專案採用 Protocol Buffers 定義服務接口，並遵循 Clean Architecture 架構模式。

## 技術棧
- **框架**: go-zero (gRPC)
- **語言**: Go 1.24+
- **協議**: Protocol Buffers 3
- **資料庫**: MySQL + GORM
- **快取**: Redis
- **日誌**: logrus

## 目錄結構規範

### RPC 服務標準目錄結構
```
{service_name}/
├── {service_name}.go          # 服務主入口
├── {service_name}.proto       # Protocol Buffers 定義
├── {service_name}client/      # 客戶端程式碼
│   └── {service_name}.go
├── etc/
│   └── {service_name}.yaml.example  # 配置範例
└── internal/
    ├── config/                # 配置結構
    ├── constants/             # 常數定義
    ├── logic/                 # 業務邏輯層
    ├── schema/                # 資料模型
    ├── server/                # gRPC 服務實現
    └── svc/                   # 服務上下文
```

## 程式碼規範

### 1. Protocol Buffers 定義
- 使用 proto3 語法
- 服務名稱使用 PascalCase
- 方法名稱使用 PascalCase
- 訊息類型使用 PascalCase
- 欄位名稱使用 snake_case
- 必須包含 `option go_package` 指令

### 2. 服務實現結構
```go
type {ServiceName}Server struct {
    svcCtx *svc.ServiceContext
    {package}.Unimplemented{ServiceName}Server
}

func New{ServiceName}Server(svcCtx *svc.ServiceContext) *{ServiceName}Server {
    return &{ServiceName}Server{
        svcCtx: svcCtx,
    }
}
```

### 3. Logic 層實現
```go
type {MethodName}Logic struct {
    ctx    context.Context
    svcCtx *svc.ServiceContext
    logx.Logger
}

func New{MethodName}Logic(ctx context.Context, svcCtx *svc.ServiceContext) *{MethodName}Logic {
    return &{MethodName}Logic{
        ctx:    ctx,
        svcCtx: svcCtx,
        Logger: logx.WithContext(ctx),
    }
}

func (l *{MethodName}Logic) {MethodName}(in *{package}.{RequestType}) (*{package}.{ResponseType}, error) {
    // 業務邏輯實現
    return &{package}.{ResponseType}{}, nil
}
```

### 4. 服務上下文 (svc)
```go
type ServiceContext struct {
    Config config.Config
    Logger logx.Logger
    // 外部依賴
}

func NewServiceContext(c config.Config, logger logx.Logger, extSvc ExternalContext) *ServiceContext {
    return &ServiceContext{
        Config: c,
        Logger: logger,
        // 初始化外部依賴
    }
}
```

## 開發最佳實踐

### 1. 錯誤處理
- 使用專案統一的 errorx 包處理錯誤
- 返回適當的 gRPC 狀態碼
- 記錄詳細的錯誤日誌

### 2. 日誌記錄
- 使用結構化日誌
- 記錄關鍵業務操作
- 包含 trace ID 用於鏈路追蹤

### 3. 資料驗證
- 在 Logic 層進行業務邏輯驗證
- 使用 Protocol Buffers 的內建驗證
- 對外部輸入進行嚴格驗證

### 4. 資料庫操作
- 使用 GORM 進行資料庫操作
- 實現適當的事務管理
- 使用連接池優化性能

### 5. 快取策略
- 使用 Redis 進行資料快取
- 實現快取失效策略
- 避免快取穿透和雪崩

## 測試規範

### 1. 單元測試
- 每個 Logic 方法都要有對應的測試
- 使用 testify 進行斷言
- 使用 mock 隔離外部依賴

### 2. 測試檔案命名
- 測試檔案以 `_test.go` 結尾
- 測試函數以 `Test` 開頭

## 部署配置

### 1. 配置檔案
- 使用 YAML 格式
- 區分不同環境配置
- 敏感資訊使用環境變數

### 2. 服務註冊
- 使用 go-zero 的服務發現機制
- 配置健康檢查端點

## 安全規範

### 1. 認證授權
- 實現 gRPC 攔截器進行認證
- 使用 JWT 或類似機制
- 實現細粒度的權限控制

### 2. 資料安全
- 敏感資料加密存儲
- 使用 TLS 加密傳輸
- 實現資料脫敏

## 性能優化

### 1. 連接管理
- 使用連接池
- 實現連接復用
- 配置適當的超時時間

### 2. 資源管理
- 及時釋放資源
- 使用 context 控制請求生命週期
- 實現熔斷和限流機制

## 監控和運維

### 1. 指標收集
- 收集業務指標
- 監控系統資源使用
- 實現分散式追蹤

### 2. 日誌管理
- 結構化日誌輸出
- 日誌等級管理
- 日誌輪轉和歸檔

## 資料庫操作規範

### 1. 資料庫查詢實現
```go
func (l *{Operation}Logic) {Operation}(in *{package}.{RequestType}) (*{package}.{ResponseType}, error) {
    var totalCount int64
    query := l.svcCtx.AdminDB.Table("TableName").
        Select("id", "name", "created_at").
        Where("status = ?", "active")

    // 條件過濾
    if in.GetFilter() != "" {
        query = query.Where("name LIKE ?", "%"+in.GetFilter()+"%")
    }

    // 計算總筆數
    if err := query.Count(&totalCount).Error; err != nil {
        l.Logger.Errorf("Database count error: %v", err)
        return nil, errorx.DatabaseError
    }

    // 設定分頁
    limit := int(in.GetPageLimit())
    if limit <= 0 {
        limit = 10 // 預設值
    }
    
    page := int(in.GetPage())
    if page <= 0 {
        page = 1
    }

    // 設定排序
    validSortColumns := map[string]string{
        "id":         "id",
        "name":       "name",
        "created_at": "created_at",
    }
    sort := validSortColumns[in.GetSort()]
    if sort == "" {
        sort = "created_at"
    }

    order := "desc"
    if orderValue := strings.ToLower(in.GetOrder()); orderValue == "asc" || orderValue == "desc" {
        order = orderValue
    }

    // 執行查詢
    var dbResult []YourStruct
    if err := query.
        Order(fmt.Sprintf("%s %s", sort, order)).
        Limit(limit).
        Offset(limit * (page - 1)).
        Find(&dbResult).Error; err != nil {
        l.Logger.Errorf("Database query error: %v", err)
        return nil, errorx.DatabaseError
    }

    // 轉換結果
    respList := make([]*{package}.{ItemType}, 0, len(dbResult))
    for _, v := range dbResult {
        respList = append(respList, &{package}.{ItemType}{
            Id:        v.ID,
            Name:      v.Name,
            CreatedAt: carbon.Parse(v.CreatedAt, constants.TimezoneGMT4).ToRfc3339String(),
        })
    }

    // 計算總頁數
    totalPage := uint32((totalCount + int64(limit) - 1) / int64(limit))

    return &{package}.{ResponseType}{
        List: respList,
        Pagination: &{package}.Pagination{
            Total:       uint32(totalCount),
            TotalPage:   totalPage,
            CurrentPage: uint32(page),
            PageLimit:   uint32(limit),
        },
    }, nil
}
```

### 2. 資料庫錯誤處理
```go
// 統一的資料庫錯誤處理
if err := query.Error; err != nil {
    l.Logger.Errorf("Database operation failed: %v", err)
    
    // 根據錯誤類型返回不同的錯誤
    if errors.Is(err, gorm.ErrRecordNotFound) {
        return nil, errorx.RecordNotFound
    }
    
    // 檢查是否為連接錯誤
    if strings.Contains(err.Error(), "connection") {
        return nil, errorx.DatabaseConnectionError
    }
    
    // 其他資料庫錯誤
    return nil, errorx.DatabaseError
}
```

### 3. 服務上下文中的資料庫配置
```go
type ServiceContext struct {
    Config   config.Config
    AdminDB  *gorm.DB  // 資料庫連接
    Logger   logger.Context
}

type ExternalContext struct {
    AdminDB *gorm.DB
}

func NewServiceContext(c config.Config, logCtx logger.Context, extSvc ExternalContext) *ServiceContext {
    return &ServiceContext{
        Config:  c,
        Logger:  logCtx,
        AdminDB: extSvc.AdminDB,
    }
}
```

## 外部 API 調用規範

### 1. 使用 Resty 客戶端
```go
// 在服務上下文中配置 Resty 客戶端
type ServiceContext struct {
    Config     config.Config
    GMClient   *resty.Client  // 外部 API 客戶端
    Logger     logger.Context
}

func NewServiceContext(c config.Config, logCtx logger.Context, extSvc ExternalContext) *ServiceContext {
    // 配置 Resty 客戶端
    gmClient := resty.New()
    baseURL := fmt.Sprintf("%s://%s", c.ACCGMConf.Schema, c.ACCGMConf.IP)
    gmClient.SetBaseURL(baseURL)
    gmClient.SetTimeout(constants.APITimeout)
    gmClient.SetHeader("Host", c.ACCGMConf.Host)
    gmClient.SetHeader("Api-Key", c.ACCGMConf.APIKey)

    // HTTPS 配置
    if c.ACCGMConf.Schema == constants.HTTPS {
        gmClient.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
    }

    return &ServiceContext{
        Config:   c,
        Logger:   logCtx,
        GMClient: gmClient,
    }
}
```

### 2. 外部 API 調用實現
```go
func (l *{Operation}Logic) {Operation}(in *{package}.{RequestType}) (*{package}.{ResponseType}, error) {
    // 構建請求參數
    req := urlutil.NewBuilder()
    req.AddInt("site", constants.GMSite)
    req.AddString("sort[]", in.GetSort())
    req.AddString("order[]", in.GetOrder())

    // 條件參數
    if in.GetUsername() != "" {
        req.AddString("username", in.GetUsername())
    }

    if in.GetEnable() != nil {
        req.AddBoolToInt("enable", in.GetEnable().GetValue())
    }

    // 分頁參數
    if in.GetFirstResult() > 0 {
        req.AddUint32("first_result", in.GetFirstResult())
    }

    if in.GetMaxResults() > 0 {
        req.AddUint32("max_results", in.GetMaxResults())
    }

    // 發送 API 請求
    apiResponse, err := l.svcCtx.GMClient.R().
        SetQueryParamsFromValues(req.Values()).
        SetHeader("X-Forwarded-For", in.GetIp()).
        Get(constants.APIEndpoint)

    // 處理網路錯誤
    if err != nil {
        l.Logger.Errorf("API request failed: %v", err)
        return nil, errorx.ConnectionFailed
    }

    // 檢查 HTTP 狀態碼
    if apiResponse.StatusCode() != http.StatusOK {
        l.Logger.Errorf("API returned non-200 status: %d, body: %s", 
            apiResponse.StatusCode(), string(apiResponse.Body()))
        return nil, errorx.InvalidResponse
    }

    // 解析 JSON 回應
    var resp APIResponse
    if err := json.Unmarshal(apiResponse.Body(), &resp); err != nil {
        l.Logger.Errorf("JSON parse failed: %v, body: %s", err, string(apiResponse.Body()))
        return nil, errorx.JSONParseFailed
    }

    // 檢查 API 業務邏輯錯誤
    if resp.Code != 0 {
        l.Logger.Errorf("API business error: code=%d, msg=%s", resp.Code, resp.Msg)
        return nil, errorx.APIBusinessError
    }

    // 轉換回應資料
    resultList := make([]*{package}.{ItemType}, 0, len(resp.Data))
    for _, item := range resp.Data {
        resultList = append(resultList, &{package}.{ItemType}{
            Id:        item.GetId(),
            Username:  item.GetUsername(),
            CreatedAt: carbon.Parse(item.GetCreatedAt(), carbon.Taipei).ToRfc3339String(constants.TimezoneGMT4),
        })
    }

    return &{package}.{ResponseType}{
        List:       resultList,
        Pagination: resp.Pagination,
    }, nil
}
```

### 3. API 回應結構定義
```go
type APIResponse struct {
    Result     string                `json:"result"`
    Data       []*{ItemType}         `json:"ret"`
    Pagination *{package}.Pagination `json:"pagination"`
    Code       int                   `json:"code"`
    Msg        string                `json:"msg"`
}
```

### 4. 外部 API 錯誤處理
```go
// 網路連接錯誤
if err != nil {
    l.Logger.Errorf("Network error: %v", err)
    
    // 檢查是否為超時錯誤
    if strings.Contains(err.Error(), "timeout") {
        return nil, errorx.RequestTimeout
    }
    
    // 檢查是否為連接拒絕
    if strings.Contains(err.Error(), "connection refused") {
        return nil, errorx.ConnectionRefused
    }
    
    return nil, errorx.ConnectionFailed
}

// HTTP 狀態碼錯誤
switch apiResponse.StatusCode() {
case http.StatusUnauthorized:
    return nil, errorx.Unauthorized
case http.StatusForbidden:
    return nil, errorx.Forbidden
case http.StatusNotFound:
    return nil, errorx.APINotFound
case http.StatusInternalServerError:
    return nil, errorx.InternalServerError
default:
    if apiResponse.StatusCode() >= 400 {
        return nil, errorx.InvalidResponse
    }
}

// JSON 解析錯誤
if err := json.Unmarshal(apiResponse.Body(), &resp); err != nil {
    l.Logger.Errorf("JSON unmarshal error: %v, response body: %s", 
        err, string(apiResponse.Body()))
    return nil, errorx.JSONParseFailed
}

// API 業務邏輯錯誤
if resp.Code != 0 {
    l.Logger.Errorf("API business logic error: code=%d, message=%s", 
        resp.Code, resp.Msg)
    
    // 根據業務錯誤碼返回對應錯誤
    switch resp.Code {
    case 1001:
        return nil, errorx.UserNotFound
    case 1002:
        return nil, errorx.InvalidCredentials
    default:
        return nil, errorx.APIBusinessError
    }
}
```

### 5. 請求參數構建工具
```go
// 使用 urlutil.NewBuilder() 構建請求參數
req := urlutil.NewBuilder()
req.AddString("key", "value")           // 字串參數
req.AddInt("number", 123)               // 整數參數
req.AddUint32("id", uint32(456))        // 無符號整數
req.AddBoolToInt("flag", true)          // 布林值轉整數

// 條件性添加參數
if condition {
    req.AddString("optional", "value")
}

// 使用參數
apiResponse, err := client.R().
    SetQueryParamsFromValues(req.Values()).
    Get("/api/endpoint")
```

## 程式碼生成

使用 go-zero 工具鏈：
```bash
# 生成 RPC 服務程式碼
cd {service_name}
goctl rpc protoc {service_name}.proto --go_out=../ --go-grpc_out=../ --zrpc_out=.
cd ../
```

## 版本管理

### 1. API 版本控制
- 使用語義化版本
- 向後相容性考慮
- 廢棄 API 的遷移策略

### 2. 依賴管理
- 使用 Go Modules
- 定期更新依賴
- 安全漏洞檢查