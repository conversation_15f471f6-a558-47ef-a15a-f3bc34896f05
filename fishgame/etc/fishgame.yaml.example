Name: fishgame.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: fishgame.rpc

PDCloudConf:
  Schema: http
  Host: cloud-apollo.vir777.net
  IP: 127.0.0.1
  Port: 80

FishConf:
  Schema: http
  Host: fish-test-web
  IP: 127.0.0.1
  Port: 80
  Ekey: Ekey

FishPlatformConf:
  Schema: http
  Host: fish-test-web
  IP: 127.0.0.1
  Port: 80
  Ekey: Ekey
  Token: token

WagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers38
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers38
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

GameSetDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: GameSet
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: GameSet
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

Middlewares:
  Stat: false
