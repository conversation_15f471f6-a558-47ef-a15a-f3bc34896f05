Name: domain.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: domain.rpc

DomainDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: DomainDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: DomainDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

SECConf:
  Schema: http
  Host: pdns-api-cloud-int.vir999.com
  Token: token

Middlewares:
  Stat: false
