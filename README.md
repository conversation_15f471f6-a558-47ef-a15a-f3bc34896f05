## 系統需求

- [ ] golang 1.24.0 以上
- [ ] etcd 3.5.4 以上
- [ ] goctl 1.6.2 以上

## 下載程式

```
git clone https://swissknife.vip/gt/gtp/gbh.git
```

## 使用 docker-compose 建立相關服務

```
## 啟動服務
docker-compose up -d

## 停止服務
docker-compose down
```

[本地開發 docker-compose 說明](https://vgjira.atlassian.net/wiki/spaces/GTP/pages/182026488/Docker+Compose)

## 微服務補上錯誤

新建立的微服務需要在 main 的程式中加入下面這段程式，才可以正確處理 gRPC 回傳的錯誤

```
statConf := rpcserver.StatConf{
	MetricsName:          "",         // 微服務類別
	IgnoreContentMethods: []string{}, // 可填要略過的方法
}

s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))
```
