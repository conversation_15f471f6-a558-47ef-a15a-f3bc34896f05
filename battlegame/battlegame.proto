syntax = "proto3";

package battlegame;
option go_package = "proto/battlegame";

message EmptyResponse {}

message GetUserPermissionsRequest { repeated uint32 user_ids = 1; }

message User {
  uint32 id = 1;
  int32 status = 2;
}

message GetUserPermissionsResponse { repeated User users = 1; }

message EnableUserPermissionsRequest {
  repeated uint32 user_ids = 1;
  bool enable = 2;
}

message Uint32Value { uint32 value = 1; }

message WagersByBetTimeRequest {
  string start_time = 1;
  string end_time = 2;
  uint32 hall_id = 3;
  uint32 agent_id = 4;
  Uint32Value game_id = 5;
  Uint32Value current_page = 6;
  Uint32Value page_limit = 7;
}

message Wager {
  uint64 id = 1;
  uint32 user_id = 2;
  uint32 game_id = 3;
  double bet_amount = 4;
  double payoff = 5;
  double commissionable = 6;
  double revenue = 7;
  string currency = 8;
  double exchange_rate = 9;
  int32 result_status = 10;
  uint32 platform = 11;
  uint32 client = 12;
  uint32 portal = 13;
  string wagers_date = 14;
  string modified_date = 15;
}

message Pagination {
  uint32 current_page = 1;
  uint32 page_limit = 2;
  uint32 total = 3;
  uint32 total_page = 4;
}

message WagersResponse {
  repeated Wager wagers = 1;
  Pagination pagination = 2;
}

message WagersByModifiedTimeRequest {
  string start_time = 1;
  string end_time = 2;
  uint32 hall_id = 3;
  Uint32Value agent_id = 4;
  Uint32Value game_id = 5;
  Uint32Value current_page = 6;
  Uint32Value page_limit = 7;
}

message StringValue { string value = 1; }

message LinkListRequest {
  string session = 1;
  string client_ip = 2;
  StringValue lang = 3;
  Uint32Value exit_option = 4;
  StringValue exit_param = 5;
}

message Link {
  uint32 game_id = 1;
  string pc = 2;
  string mobile = 3;
  string rwd = 4;
}
message LinkListResponse { repeated Link links = 1; }

message SubWagersURLRequest {
  string lang = 1;
  uint32 user_id = 2;
  uint64 wagers_id = 3;
}

message SubWagersURLResponse { string url = 1; }

message WagersByUserIDRequest {
  uint32 hall_id = 1;
  uint32 user_id = 2;
  uint32 game_id = 3;
  string start_round_date = 4;
  string end_round_date = 5;
  uint32 current_page = 6;
  uint32 page_limit = 7;
}

message WagersByUserID {
  string round_date = 1;
  uint64 wagers_id = 2;
  uint32 game_id = 3;
  int32 result_status = 4;
  double bet_amount = 5;
  double payoff = 6;
}

message WagersSumByUserID {
  double total_bet_amount = 2;
  double total_payoff = 3;
}

message WagersByUserIDResponse {
  repeated WagersByUserID wagers = 1;
  WagersSumByUserID wagers_sum = 2;
  Pagination pagination = 3;
}

message GetWagersRequest {
  string start_round_date = 1;
  string end_round_date = 2;
  uint32 hall_id = 3;
  repeated uint32 user_id = 4;
  uint64 wagers_id = 5;
  repeated uint32 game_id = 6;
  uint32 round_serial = 7;
  uint32 wagers_type = 8;
  double min_bet_amount = 9;
  double max_bet_amount = 10;
  double min_payoff = 11;
  double max_payoff = 12;
  string close_date = 13;
  uint64 reference_id = 14;
  uint32 page = 15;
  uint32 page_limit = 16;
  string order = 17;
}

message FetchWagersByDB {
  uint64 wagers_id = 1;
  string wagers_time = 2;
  uint32 hierarchy = 3;
  uint32 portal = 4;
  uint32 wagers_type = 5;
  uint32 platform = 6;
  uint32 client = 7;
  uint32 game_id = 8;
  uint32 user_id = 9;
  string round_date = 10;
  string round_time = 11;
  double bet_amount = 12;
  double commissionable = 13;
  string currency = 14;
  double exchange_rate = 15;
  sint32 result = 16;
  double payoff = 17;
  double revenue = 18;
  double jackpot_contribution = 19;
  uint32 hall_id = 20;
  uint32 round_serial = 21;
  uint64 reference_id = 22;
  string settled_time = 23;
  string modified_date = 24;
}

message Total {
  uint32 number = 1;
  double bet_amount = 2;
  double commissionable = 3;
  double payoff = 4;
  double revenue = 5;
}

message GetWagersResponse {
  repeated FetchWagersByDB wagers = 1;
  Pagination pagination = 2;
  Total sub_total = 3;
  Total total = 4;
}

message UpdateGameSwitchRequest {
  repeated uint32 game_id = 1;
  bool enable = 2;
}

message GetMultiSubWagersURLRequest {
  repeated uint64 wagers_ids = 1;
  string lang = 2;
}

message SubWagersURL {
  uint64 wagers_id = 1;
  string url = 2;
}

message GetMultiSubWagersURLResponse {
  repeated SubWagersURL sub_wagers_url = 1;
}

message LogoutByHallRequest { uint32 hall_id = 1; }

service BattleGame {
  rpc GetUserPermissions(GetUserPermissionsRequest)
      returns (GetUserPermissionsResponse);
  rpc EnableUserPermissions(EnableUserPermissionsRequest)
      returns (EmptyResponse);
  rpc WagersByBetTime(WagersByBetTimeRequest) returns (WagersResponse);
  rpc WagersByModifiedTime(WagersByModifiedTimeRequest)
      returns (WagersResponse);
  rpc LinkList(LinkListRequest) returns (LinkListResponse);
  rpc SubWagersURL(SubWagersURLRequest) returns (SubWagersURLResponse);
  rpc WagersByUserID(WagersByUserIDRequest) returns (WagersByUserIDResponse);
  rpc GetWagers(GetWagersRequest) returns (GetWagersResponse);
  rpc UpdateGameSwitch(UpdateGameSwitchRequest) returns (EmptyResponse);
  rpc GetMultiSubWagersURL(GetMultiSubWagersURLRequest)
      returns (GetMultiSubWagersURLResponse);
  rpc LogoutByHall(LogoutByHallRequest) returns (EmptyResponse);
}