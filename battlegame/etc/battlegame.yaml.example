Name: battlegame.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: battlegame.rpc

PDCloudConf:
  Schema: http
  Host: cloud-apollo.vir777.net
  IP: 127.0.0.1
  Port: 80

WagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers66
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers66
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

BattleConf:
  Schema: https
  Host: manage-api-onup.pokerworldbattle.com
  Ekey: ekey
  Token: token

Middlewares:
  Stat: false
