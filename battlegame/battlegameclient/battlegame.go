// Code generated by goctl. DO NOT EDIT.
// Source: battlegame.proto

package battlegameclient

import (
	"context"

	"gbh/proto/battlegame"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	EmptyResponse                = battlegame.EmptyResponse
	EnableUserPermissionsRequest = battlegame.EnableUserPermissionsRequest
	FetchWagersByDB              = battlegame.FetchWagersByDB
	GetMultiSubWagersURLRequest  = battlegame.GetMultiSubWagersURLRequest
	GetMultiSubWagersURLResponse = battlegame.GetMultiSubWagersURLResponse
	GetUserPermissionsRequest    = battlegame.GetUserPermissionsRequest
	GetUserPermissionsResponse   = battlegame.GetUserPermissionsResponse
	GetWagersRequest             = battlegame.GetWagersRequest
	GetWagersResponse            = battlegame.GetWagersResponse
	Link                         = battlegame.Link
	LinkListRequest              = battlegame.LinkListRequest
	LinkListResponse             = battlegame.LinkListResponse
	LogoutByHallRequest          = battlegame.LogoutByHallRequest
	Pagination                   = battlegame.Pagination
	StringValue                  = battlegame.StringValue
	SubWagersURL                 = battlegame.SubWagersURL
	SubWagersURLRequest          = battlegame.SubWagersURLRequest
	SubWagersURLResponse         = battlegame.SubWagersURLResponse
	Total                        = battlegame.Total
	Uint32Value                  = battlegame.Uint32Value
	UpdateGameSwitchRequest      = battlegame.UpdateGameSwitchRequest
	User                         = battlegame.User
	Wager                        = battlegame.Wager
	WagersByBetTimeRequest       = battlegame.WagersByBetTimeRequest
	WagersByModifiedTimeRequest  = battlegame.WagersByModifiedTimeRequest
	WagersByUserID               = battlegame.WagersByUserID
	WagersByUserIDRequest        = battlegame.WagersByUserIDRequest
	WagersByUserIDResponse       = battlegame.WagersByUserIDResponse
	WagersResponse               = battlegame.WagersResponse
	WagersSumByUserID            = battlegame.WagersSumByUserID

	BattleGame interface {
		GetUserPermissions(ctx context.Context, in *GetUserPermissionsRequest, opts ...grpc.CallOption) (*GetUserPermissionsResponse, error)
		EnableUserPermissions(ctx context.Context, in *EnableUserPermissionsRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		WagersByBetTime(ctx context.Context, in *WagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
		WagersByModifiedTime(ctx context.Context, in *WagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
		LinkList(ctx context.Context, in *LinkListRequest, opts ...grpc.CallOption) (*LinkListResponse, error)
		SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error)
		WagersByUserID(ctx context.Context, in *WagersByUserIDRequest, opts ...grpc.CallOption) (*WagersByUserIDResponse, error)
		GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error)
		UpdateGameSwitch(ctx context.Context, in *UpdateGameSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetMultiSubWagersURL(ctx context.Context, in *GetMultiSubWagersURLRequest, opts ...grpc.CallOption) (*GetMultiSubWagersURLResponse, error)
		LogoutByHall(ctx context.Context, in *LogoutByHallRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	}

	defaultBattleGame struct {
		cli zrpc.Client
	}
)

func NewBattleGame(cli zrpc.Client) BattleGame {
	return &defaultBattleGame{
		cli: cli,
	}
}

func (m *defaultBattleGame) GetUserPermissions(ctx context.Context, in *GetUserPermissionsRequest, opts ...grpc.CallOption) (*GetUserPermissionsResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.GetUserPermissions(ctx, in, opts...)
}

func (m *defaultBattleGame) EnableUserPermissions(ctx context.Context, in *EnableUserPermissionsRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.EnableUserPermissions(ctx, in, opts...)
}

func (m *defaultBattleGame) WagersByBetTime(ctx context.Context, in *WagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.WagersByBetTime(ctx, in, opts...)
}

func (m *defaultBattleGame) WagersByModifiedTime(ctx context.Context, in *WagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.WagersByModifiedTime(ctx, in, opts...)
}

func (m *defaultBattleGame) LinkList(ctx context.Context, in *LinkListRequest, opts ...grpc.CallOption) (*LinkListResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.LinkList(ctx, in, opts...)
}

func (m *defaultBattleGame) SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.SubWagersURL(ctx, in, opts...)
}

func (m *defaultBattleGame) WagersByUserID(ctx context.Context, in *WagersByUserIDRequest, opts ...grpc.CallOption) (*WagersByUserIDResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.WagersByUserID(ctx, in, opts...)
}

func (m *defaultBattleGame) GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.GetWagers(ctx, in, opts...)
}

func (m *defaultBattleGame) UpdateGameSwitch(ctx context.Context, in *UpdateGameSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.UpdateGameSwitch(ctx, in, opts...)
}

func (m *defaultBattleGame) GetMultiSubWagersURL(ctx context.Context, in *GetMultiSubWagersURLRequest, opts ...grpc.CallOption) (*GetMultiSubWagersURLResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.GetMultiSubWagersURL(ctx, in, opts...)
}

func (m *defaultBattleGame) LogoutByHall(ctx context.Context, in *LogoutByHallRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := battlegame.NewBattleGameClient(m.cli.Conn())
	return client.LogoutByHall(ctx, in, opts...)
}
