package constants

import "time"

const APITimeout = 5 * time.Second

const TimezoneGMT4 = "Etc/GMT+4"

const HTTPS = "https"

const (
	BattleGameKind = 66
)

const (
	PDCloudGameDisabled int = 68010009
	PDCloudUserDisabled int = 68010019
)

const (
	PDCloudGetUserPermissionsAPI    = "/api/user/permissions/status"
	PDCloudEnableUserPermissionsAPI = "/api/user/permissions/enable"
	PDCloudGameLinkListAPI          = "/api/game/link/list"
	PDCloudSubWagersURLAPI          = "/api/wagers/betrecord/link"
	BattleCompanyTagGameSwitchAPI   = "/API/M/Company/Tag/GameSwitch"
	BattleUserWagersDetailAPI       = "/API/M/User/WagersDetail"
	BattleUserTagKickOutAPI         = "/API/M/User/TagKickOut"
)

const (
	DefaultCurrentPage = 1
	DefaultPageLimit   = 500
)

const (
	OrderDesc = "desc"
)

const (
	ZhTw = "zh-tw"
)
