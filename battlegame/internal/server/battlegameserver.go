// Code generated by goctl. DO NOT EDIT.
// Source: battlegame.proto

package server

import (
	"context"

	"gbh/battlegame/internal/logic"
	"gbh/battlegame/internal/svc"
	"gbh/proto/battlegame"
)

type BattleGameServer struct {
	svcCtx *svc.ServiceContext
	battlegame.UnimplementedBattleGameServer
}

func NewBattleGameServer(svcCtx *svc.ServiceContext) *BattleGameServer {
	return &BattleGameServer{
		svcCtx: svcCtx,
	}
}

func (s *BattleGameServer) GetUserPermissions(ctx context.Context, in *battlegame.GetUserPermissionsRequest) (*battlegame.GetUserPermissionsResponse, error) {
	l := logic.NewGetUserPermissionsLogic(ctx, s.svcCtx)
	return l.GetUserPermissions(in)
}

func (s *BattleGameServer) EnableUserPermissions(ctx context.Context, in *battlegame.EnableUserPermissionsRequest) (*battlegame.EmptyResponse, error) {
	l := logic.NewEnableUserPermissionsLogic(ctx, s.svcCtx)
	return l.EnableUserPermissions(in)
}

func (s *BattleGameServer) WagersByBetTime(ctx context.Context, in *battlegame.WagersByBetTimeRequest) (*battlegame.WagersResponse, error) {
	l := logic.NewWagersByBetTimeLogic(ctx, s.svcCtx)
	return l.WagersByBetTime(in)
}

func (s *BattleGameServer) WagersByModifiedTime(ctx context.Context, in *battlegame.WagersByModifiedTimeRequest) (*battlegame.WagersResponse, error) {
	l := logic.NewWagersByModifiedTimeLogic(ctx, s.svcCtx)
	return l.WagersByModifiedTime(in)
}

func (s *BattleGameServer) LinkList(ctx context.Context, in *battlegame.LinkListRequest) (*battlegame.LinkListResponse, error) {
	l := logic.NewLinkListLogic(ctx, s.svcCtx)
	return l.LinkList(in)
}

func (s *BattleGameServer) SubWagersURL(ctx context.Context, in *battlegame.SubWagersURLRequest) (*battlegame.SubWagersURLResponse, error) {
	l := logic.NewSubWagersURLLogic(ctx, s.svcCtx)
	return l.SubWagersURL(in)
}

func (s *BattleGameServer) WagersByUserID(ctx context.Context, in *battlegame.WagersByUserIDRequest) (*battlegame.WagersByUserIDResponse, error) {
	l := logic.NewWagersByUserIDLogic(ctx, s.svcCtx)
	return l.WagersByUserID(in)
}

func (s *BattleGameServer) GetWagers(ctx context.Context, in *battlegame.GetWagersRequest) (*battlegame.GetWagersResponse, error) {
	l := logic.NewGetWagersLogic(ctx, s.svcCtx)
	return l.GetWagers(in)
}

func (s *BattleGameServer) UpdateGameSwitch(ctx context.Context, in *battlegame.UpdateGameSwitchRequest) (*battlegame.EmptyResponse, error) {
	l := logic.NewUpdateGameSwitchLogic(ctx, s.svcCtx)
	return l.UpdateGameSwitch(in)
}

func (s *BattleGameServer) GetMultiSubWagersURL(ctx context.Context, in *battlegame.GetMultiSubWagersURLRequest) (*battlegame.GetMultiSubWagersURLResponse, error) {
	l := logic.NewGetMultiSubWagersURLLogic(ctx, s.svcCtx)
	return l.GetMultiSubWagersURL(in)
}

func (s *BattleGameServer) LogoutByHall(ctx context.Context, in *battlegame.LogoutByHallRequest) (*battlegame.EmptyResponse, error) {
	l := logic.NewLogoutByHallLogic(ctx, s.svcCtx)
	return l.LogoutByHall(in)
}
