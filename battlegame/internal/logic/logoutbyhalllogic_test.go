package logic

import (
	"net/http"
	"testing"

	"gbh/battlegame/internal/constants"
	"gbh/errorx"
	"gbh/proto/battlegame"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
)

func TestLogoutByHall_ConnectionFailed(t *testing.T) {
	getRequest := battlegame.LogoutByHallRequest{
		HallId: 3820587,
	}

	uri := battleURL + constants.BattleUserTagKickOutAPI
	responder := httpmock.NewErrorResponder(http.ErrHandlerTimeout)

	httpmock.ActivateNonDefault(svcCtx.BattleClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewLogoutByHallLogic(ctx, svcCtx)
	resp, err := l.LogoutByHall(&getRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestLogoutByHall_InvalidHttpStatus(t *testing.T) {
	getRequest := battlegame.LogoutByHallRequest{
		HallId: 3820587,
	}

	uri := battleURL + constants.BattleUserTagKickOutAPI
	responder := httpmock.NewStringResponder(503, `ServiceUnavailable`)

	httpmock.ActivateNonDefault(svcCtx.BattleClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewLogoutByHallLogic(ctx, svcCtx)
	resp, err := l.LogoutByHall(&getRequest)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestLogoutByHall_JSONParseFailed(t *testing.T) {
	getRequest := battlegame.LogoutByHallRequest{
		HallId: 3820587,
	}

	uri := battleURL + constants.BattleUserTagKickOutAPI
	responder := httpmock.NewStringResponder(200, `ok`)

	httpmock.ActivateNonDefault(svcCtx.BattleClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewLogoutByHallLogic(ctx, svcCtx)
	resp, err := l.LogoutByHall(&getRequest)

	assert.Equal(t, errorx.JSONParseFailed, err)
	assert.Nil(t, resp)
}

func TestLogoutByHall_StatusNotZero(t *testing.T) {
	getRequest := battlegame.LogoutByHallRequest{
		HallId: 3820587,
	}

	uri := battleURL + constants.BattleUserTagKickOutAPI
	responder := httpmock.NewStringResponder(200, `{"status":"503"}`)

	httpmock.ActivateNonDefault(svcCtx.BattleClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewLogoutByHallLogic(ctx, svcCtx)
	resp, err := l.LogoutByHall(&getRequest)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestLogoutByHall_ErrorCodeNotZero(t *testing.T) {
	getRequest := battlegame.LogoutByHallRequest{
		HallId: 3820587,
	}

	uri := battleURL + constants.BattleUserTagKickOutAPI
	responder := httpmock.NewStringResponder(200, `{"errorCode":"03"}`)

	httpmock.ActivateNonDefault(svcCtx.BattleClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewLogoutByHallLogic(ctx, svcCtx)
	resp, err := l.LogoutByHall(&getRequest)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestLogoutByHall(t *testing.T) {
	getRequest := battlegame.LogoutByHallRequest{
		HallId: 3820587,
	}

	uri := battleURL + constants.BattleUserTagKickOutAPI
	responder := httpmock.NewStringResponder(200, `{
		"status": "000",
		"errorCode": "00",
		"data": [
		],
		"version": "4.7.0",
		"guid": "aa73594c-3781-11f0-8822-/sW8cEPvIPbi"
	}`)

	httpmock.ActivateNonDefault(svcCtx.BattleClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewLogoutByHallLogic(ctx, svcCtx)
	resp, err := l.LogoutByHall(&getRequest)

	assert.NoError(t, err)
	assert.Equal(t, &battlegame.EmptyResponse{}, resp)
}
