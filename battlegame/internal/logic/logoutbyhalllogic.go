package logic

import (
	"context"
	"encoding/json"
	"net/http"

	"gbh/battlegame/internal/constants"
	"gbh/battlegame/internal/svc"
	"gbh/errorx"
	"gbh/proto/battlegame"
	"gbh/utils/strutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type LogoutByHallLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLogoutByHallLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LogoutByHallLogic {
	return &LogoutByHallLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LogoutByHallLogic) LogoutByHall(in *battlegame.LogoutByHallRequest) (*battlegame.EmptyResponse, error) {
	params := map[string]string{
		"tag": strutil.Uint32ToString(in.GetHallId()),
	}

	apiResponse, err := l.svcCtx.BattleClient.R().
		SetBody(params).
		Put(constants.BattleUserTagKickOutAPI)

	if err != nil {
		return nil, errorx.ConnectionFailed
	}

	if apiResponse.StatusCode() != http.StatusOK {
		return nil, errorx.InvalidResponse
	}

	var logoutByHallResponse struct {
		Status    string `json:"status"`
		ErrorCode string `json:"errorCode"`
	}
	jsonParseErr := json.Unmarshal(apiResponse.Body(), &logoutByHallResponse)

	if jsonParseErr != nil {
		return nil, errorx.JSONParseFailed
	}

	if logoutByHallResponse.Status != "000" || logoutByHallResponse.ErrorCode != "00" {
		return nil, errorx.InvalidResponse
	}

	return &battlegame.EmptyResponse{}, nil
}
