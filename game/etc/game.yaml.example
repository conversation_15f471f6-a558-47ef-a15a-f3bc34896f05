Name: game.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
    - 127.0.0.1:2379
  Key: game.rpc

PDHexConf:
  Schema: http
  Host: bgp-api.vir777.net
  IP: 127.0.0.1
  Port: 80
  AuthenticateKey: authenticateKey

PDCloudConf:
  Schema: http
  Host: cloud-apollo.vir777.net
  IP: 127.0.0.1
  Port: 80

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

GameSetDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: GameSet
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: GameSet
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200

CloudSettingDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: CloudSetting
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: CloudSetting
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200

CsMemDB:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: CS_MEM
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: CS_MEM
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

GameDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: GameDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: GameDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200

UserDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: UserDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: UserDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200

DomainDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: DomainDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: DomainDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200

ScheduleDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: ScheduleDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: ScheduleDB
    MaxIdleConn: 10
    MaxOpenConn: 5
    SlowThreshold: 200

Middlewares:
  Stat: false
