package constants

import "time"

const APITimeout = 5 * time.Second

const (
	PDHexGetGameLobbyAPI               = "/hex/game/lobby"
	PDCloudGetEnableGameListAPI        = "/api/game/%d/enable/list"
	PDCloudGetDemoLinkAPI              = "/api/platform/%d/demo_link"
	PDHexGameURLAPI                    = "/hex/game/%d/url/v2"
	PDCloudGetLobbyLinkAPI             = "/api/game/lobby/link"
	PDHexGetLobbySwitchAPI             = "/hex/user/%s/lobbyswitch"
	PDHexGetLobbySwitchByHallIDAPI     = "/hex/user/%d/lobby/domain"
	PDHexGetLobbyGameEntranceSwitchAPI = "/hex/lobby/%d/domain/%d/game_type/entrance/switch"
	PDHexBulletinAPI                   = "/hex/domain/bulletin"
	PDHexGameIconKindAPI               = "/hex/game/icon_kind"
	PDHexGetMenuNameAPI                = "/hex/game/menu/language"
	PDCloudGameListAPI                 = "/api/platform/%d/translation/games/export"
)

const (
	PDCloudUnsupportGameKindError uint32 = 68080006
	PDHexBulletinNoInfo           uint32 = 667100008
)

const (
	BBLive    = 3
	BBCasino  = 5
	BBLottery = 12
	NBBSport  = 31
	BBFish    = 38
	BBBattle  = 66
)

const TimezoneGMT4 = "Etc/GMT+4"

const (
	TwentyEightDays = 28
	SevenDays       = 7
)

const (
	TenThousand = 10000
	OneHundred  = 100
)
