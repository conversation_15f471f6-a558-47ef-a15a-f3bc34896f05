package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateHallLobbySwitchLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateHallLobbySwitchLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateHallLobbySwitchLogic {
	return &UpdateHallLobbySwitchLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateHallLobbySwitchLogic) UpdateHallLobbySwitch(in *game.UpdateHallLobbySwitchRequest) (*game.EmptyResponse, error) {
	err := l.svcCtx.UserDB.Table("UserLobby").
		Where("domain = ?", in.GetHallId()).
		Where("userid = ?", in.GetHallId()).
		Where("lobby = ?", in.GetGameKind()).
		Update("enable", in.GetEnable()).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &game.EmptyResponse{}, nil
}
