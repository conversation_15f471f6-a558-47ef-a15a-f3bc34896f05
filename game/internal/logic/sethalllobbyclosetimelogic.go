package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type SetHallLobbyCloseTimeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

type LobbyCloseTime struct {
	Lobby     uint32 `json:"lobby"`
	Domain    uint32 `json:"domain"`
	CloseTime string `json:"close_time"`
}

func NewSetHallLobbyCloseTimeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetHallLobbyCloseTimeLogic {
	return &SetHallLobbyCloseTimeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SetHallLobbyCloseTimeLogic) SetHallLobbyCloseTime(in *game.SetHallLobbyCloseTimeRequest) (*game.EmptyResponse, error) {
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	updateData := LobbyCloseTime{
		CloseTime: now,
	}

	queryErr := l.svcCtx.DomainDB.Table("LobbyCloseTime").
		Where("lobby", in.GetGameKind()).Where("domain", in.GetHallId()).
		Assign(updateData).
		FirstOrCreate(&LobbyCloseTime{}).Error

	if queryErr != nil {
		return nil, errorx.DatabaseError
	}

	return &game.EmptyResponse{}, nil
}
