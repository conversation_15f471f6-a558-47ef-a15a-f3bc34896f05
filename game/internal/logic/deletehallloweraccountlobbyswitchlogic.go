package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteHallLowerAccountLobbySwitchLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteHallLowerAccountLobbySwitchLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteHallLowerAccountLobbySwitchLogic {
	return &DeleteHallLowerAccountLobbySwitchLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteHallLowerAccountLobbySwitchLogic) DeleteHallLowerAccountLobbySwitch(in *game.DeleteHallLowerAccountLobbySwitchRequest) (*game.EmptyResponse, error) {
	err := l.svcCtx.UserDB.Table("UserLobby").
		Where("domain = ?", in.GetHallId()).
		Where("userid != ?", in.GetHallId()).
		Where("lobby = ?", in.GetGameKind()).
		Delete(nil).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &game.EmptyResponse{}, nil
}
