package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetOnlineMemberHourLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetOnlineMemberHourLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOnlineMemberHourLogic {
	return &GetOnlineMemberHourLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetOnlineMemberHourLogic) GetOnlineMemberHour(in *game.GetOnlineMemberHourRequest) (*game.GetOnlineMemberHourResponse, error) {
	startTime := carbon.Parse(in.GetStartTime(), constants.TimezoneGMT4).ToDateTimeString()
	endTime := carbon.Parse(in.GetStartTime(), constants.TimezoneGMT4).AddDay().SubSecond().ToDateTimeString()

	now := carbon.Now()
	today := carbon.Parse(now.EndOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	yesterday := carbon.Parse(now.Yesterday().StartOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfDay().SubDays(constants.TwentyEightDays).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfDay().SubDays(constants.TwentyEightDays).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	var onlineMemberInfo []*game.OnlineMember
	query := l.svcCtx.DomainDB.Table("OnlineMemberHour").
		Select("datetime, SUM(amount) AS count").
		Where("domain = ?", in.GetHallId()).
		Where("datetime BETWEEN ? AND ?", startTime, endTime).
		Or("datetime BETWEEN ? AND ?", yesterday, today).
		Or("datetime BETWEEN ? AND ?", startLastMonth, endLastMonth).
		Group("datetime").
		Find(&onlineMemberInfo)

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	ingressInfo, err := GetIngressData(l.svcCtx, onlineMemberInfo, GetIngressRequest{
		TableName: "OnlineMemberHour",
		HallID:    in.GetHallId(),
		StartTime: startTime,
		EndTime:   endTime,
	})
	if err != nil {
		return nil, err
	}

	resp := &game.GetOnlineMemberHourResponse{
		OnlineMemberSummary:        onlineMemberInfo,
		OnlineMemberIngressSummary: ingressInfo,
	}

	return resp, nil
}
