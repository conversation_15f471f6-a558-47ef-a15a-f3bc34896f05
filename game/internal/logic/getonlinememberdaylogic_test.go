package logic

import (
	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/proto/game"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetOnlineMemberDayLogic_Get(t *testing.T) {
	request := game.GetOnlineMemberDayRequest{
		HallId:    3820474,
		StartTime: "2025-03-25T00:00:00-04:00",
		EndTime:   "2025-03-25T23:59:59-04:00",
	}

	rows := sqlmock.NewRows([]string{"datetime", "count"}).
		AddRow("2025-03-25 00:00:00", 1)

	now := carbon.Now()
	startLastWeek := carbon.Parse(now.StartOfWeek().SubDays(7).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastWeek := carbon.Parse(now.EndOfWeek().SubDays(7).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectQuery("SELECT datetime, SUM(amount) AS count FROM `OnlineMemberDay` WHERE domain = ? AND (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) GROUP BY `datetime`").
		WithArgs(request.GetHallId(), "2025-03-25 00:00:00", "2025-03-25 23:59:59", startLastWeek, endLastWeek, startLastMonth, endLastMonth).
		WillReturnRows(rows)

	ingressRows := sqlmock.NewRows([]string{"datetime", "ingress", "count"}).
		AddRow("2025-03-25 00:00:00", 0, 0).
		AddRow("2025-03-25 00:00:00", 1, 1).
		AddRow("2025-03-25 00:00:00", 2, 0).
		AddRow("2025-03-25 00:00:00", 3, 0).
		AddRow("2025-03-25 00:00:00", 4, 0).
		AddRow("2025-03-25 00:00:00", 5, 0).
		AddRow("2025-03-25 00:00:00", 6, 0).
		AddRow("2025-03-25 00:00:00", 7, 0).
		AddRow("2025-03-25 00:00:00", 8, 0).
		AddRow("2025-03-25 00:00:00", 9, 0).
		AddRow("2025-03-25 00:00:00", 10, 0).
		AddRow("2025-03-25 00:00:00", 11, 0).
		AddRow("2025-03-25 00:00:00", 12, 0).
		AddRow("2025-03-25 00:00:00", 13, 0).
		AddRow("2025-03-25 00:00:00", 14, 0).
		AddRow("2025-03-25 00:00:00", 15, 0).
		AddRow("2025-03-25 00:00:00", 16, 0).
		AddRow("2025-03-25 00:00:00", 17, 0).
		AddRow("2025-03-25 00:00:00", 18, 0).
		AddRow("2025-03-25 00:00:00", 19, 0)
	sqlMockDomain.ExpectQuery("SELECT datetime, ingress, SUM(amount) AS count FROM `OnlineMemberDay` WHERE domain = ? AND (datetime BETWEEN ? AND ?) GROUP BY ingress, datetime ORDER BY ingress, datetime").
		WithArgs(request.GetHallId(), "2025-03-25 00:00:00", "2025-03-25 23:59:59").
		WillReturnRows(ingressRows)

	l := NewGetOnlineMemberDayLogic(ctx, svcCtx)
	resp, err := l.GetOnlineMemberDay(&request)

	ingressInfo := []*game.OnlineMemberIngressInfo{
		{
			Datetime:   "2025-03-25 00:00:00",
			Count:      0,
			Percentage: 0,
		},
	}

	expected := &game.GetOnlineMemberDayResponse{
		OnlineMemberSummary: []*game.OnlineMember{
			{
				Datetime: "2025-03-25 00:00:00",
				Count:    1,
			},
		},
		OnlineMemberIngressSummary: []*game.OnlineMemberIngressSummary{
			{
				IngressId:               0,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId: 1,
				OnlineMemberIngressInfo: []*game.OnlineMemberIngressInfo{
					{
						Datetime:   "2025-03-25 00:00:00",
						Count:      1,
						Percentage: 100,
					},
				},
			},
			{
				IngressId:               2,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               3,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               4,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               5,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               6,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               7,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               8,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               9,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               10,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               11,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               12,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               13,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               14,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               15,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               16,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               17,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               18,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               19,
				OnlineMemberIngressInfo: ingressInfo,
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestGetOnlineMemberDayLogic_DatabaseError(t *testing.T) {
	request := game.GetOnlineMemberDayRequest{
		HallId:    3820474,
		StartTime: "2025-03-25T00:00:00-04:00",
		EndTime:   "2025-03-25T23:59:59-04:00",
	}

	now := carbon.Now()
	startLastWeek := carbon.Parse(now.StartOfWeek().SubDays(7).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastWeek := carbon.Parse(now.EndOfWeek().SubDays(7).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectQuery("SELECT datetime, SUM(amount) AS count FROM `OnlineMemberDay` WHERE domain = ? AND (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) GROUP BY `datetime`").
		WithArgs(request.GetHallId(), "2025-03-25 00:00:00", "2025-03-25 23:59:59", startLastWeek, endLastWeek, startLastMonth, endLastMonth).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetOnlineMemberDayLogic(ctx, svcCtx)
	resp, err := l.GetOnlineMemberDay(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestGetOnlineMemberDayLogic_IngressDatabaseError(t *testing.T) {
	request := game.GetOnlineMemberDayRequest{
		HallId:    3820474,
		StartTime: "2025-03-25T00:00:00-04:00",
		EndTime:   "2025-03-25T23:59:59-04:00",
	}

	now := carbon.Now()
	startLastWeek := carbon.Parse(now.StartOfWeek().SubDays(7).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastWeek := carbon.Parse(now.EndOfWeek().SubDays(7).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	rows := sqlmock.NewRows([]string{"datetime", "count"}).
		AddRow("2025-03-25 00:00:00", 1)
	sqlMockDomain.ExpectQuery("SELECT datetime, SUM(amount) AS count FROM `OnlineMemberDay` WHERE domain = ? AND (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) GROUP BY `datetime`").
		WithArgs(request.GetHallId(), "2025-03-25 00:00:00", "2025-03-25 23:59:59", startLastWeek, endLastWeek, startLastMonth, endLastMonth).
		WillReturnRows(rows)

	sqlMockDomain.ExpectQuery("SELECT datetime, ingress, SUM(amount) AS count FROM `OnlineMemberDay` WHERE domain = ? AND (datetime BETWEEN ? AND ?) GROUP BY ingress, datetime ORDER BY ingress, datetime").
		WithArgs(request.GetHallId(), "2025-03-25 00:00:00", "2025-03-25 23:59:59").
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetOnlineMemberDayLogic(ctx, svcCtx)
	resp, err := l.GetOnlineMemberDay(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}
