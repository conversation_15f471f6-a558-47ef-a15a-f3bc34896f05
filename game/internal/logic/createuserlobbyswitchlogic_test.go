package logic

import (
	"testing"

	"gbh/errorx"
	"gbh/proto/game"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreateUserLobbySwitch(t *testing.T) {
	request := game.CreateUserLobbySwitchRequest{
		UserLobbySwitch: []*game.UserLobbySwitch{
			{
				UserId:   1,
				HallId:   3820474,
				GameKind: 3,
				Enable:   true,
			},
			{
				UserId:   2,
				HallId:   3820474,
				GameKind: 2,
				Enable:   false,
			},
		},
	}

	userMock.ExpectBegin()

	userMock.ExpectExec("INSERT INTO `UserLobby` (`domain`,`enable`,`lobby`,`userid`) VALUES (?,?,?,?),(?,?,?,?)").
		WithArgs(request.GetUserLobbySwitch()[0].GetHallId(), request.GetUserLobbySwitch()[0].GetEnable(), request.GetUserLobbySwitch()[0].GetGameKind(), request.GetUserLobbySwitch()[0].GetUserId(), request.GetUserLobbySwitch()[1].GetHallId(), request.GetUserLobbySwitch()[1].GetEnable(), request.GetUserLobbySwitch()[1].GetGameKind(), request.GetUserLobbySwitch()[1].GetUserId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	userMock.ExpectCommit()

	l := NewCreateUserLobbySwitchLogic(ctx, svcCtx)
	resp, err := l.CreateUserLobbySwitch(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.NoError(t, err)
	assert.Equal(t, &game.EmptyResponse{}, resp)
}

func TestCreateUserLobbySwitch_DatabaseError(t *testing.T) {
	request := game.CreateUserLobbySwitchRequest{
		UserLobbySwitch: []*game.UserLobbySwitch{
			{
				UserId:   1,
				HallId:   3820474,
				GameKind: 3,
				Enable:   true,
			},
		},
	}

	userMock.ExpectBegin()

	userMock.ExpectExec("INSERT INTO `UserLobby` (`domain`,`enable`,`lobby`,`userid`) VALUES (?,?,?,?)").
		WithArgs(request.GetUserLobbySwitch()[0].GetHallId(), request.GetUserLobbySwitch()[0].GetEnable(), request.GetUserLobbySwitch()[0].GetGameKind(), request.GetUserLobbySwitch()[0].GetUserId()).
		WillReturnError(gorm.ErrInvalidDB)

	userMock.ExpectRollback()

	l := NewCreateUserLobbySwitchLogic(ctx, svcCtx)
	resp, err := l.CreateUserLobbySwitch(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.Equal(t, errorx.DatabaseError, err)
	assert.Nil(t, resp)
}
