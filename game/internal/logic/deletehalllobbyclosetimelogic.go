package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteHallLobbyCloseTimeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteHallLobbyCloseTimeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteHallLobbyCloseTimeLogic {
	return &DeleteHallLobbyCloseTimeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteHallLobbyCloseTimeLogic) DeleteHallLobbyCloseTime(in *game.DeleteHallLobbyCloseTimeRequest) (*game.EmptyResponse, error) {
	err := l.svcCtx.DomainDB.Table("LobbyCloseTime").
		Where("domain = ?", in.GetHallId()).
		Where("lobby = ?", in.GetGameKind()).
		Delete(nil).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &game.EmptyResponse{}, nil
}
