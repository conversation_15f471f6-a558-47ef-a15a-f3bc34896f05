package logic

import (
	"context"
	"fmt"
	"gbh/game/internal/config"
	"gbh/game/internal/svc"
	"gbh/logger"
	"log"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	ctx              context.Context
	svcCtx           *svc.ServiceContext
	pdHexConf        config.PDHexConf
	pdHexURL         string
	pdCloudConf      config.PDCloudConf
	pdCloudURL       string
	gameSetGormDb    *gorm.DB
	gameSetSqlMock   sqlmock.Sqlmock
	cloudSettingDb   *gorm.DB
	cloudSettingMock sqlmock.Sqlmock
	csMemDb          *gorm.DB
	csMemMock        sqlmock.Sqlmock
	gameSqlMock      sqlmock.Sqlmock
	gameGormDb       *gorm.DB
	userDb           *gorm.DB
	userMock         sqlmock.Sqlmock
	gormDomainDB     *gorm.DB
	sqlMockDomain    sqlmock.Sqlmock
	gormScheduleDB   *gorm.DB
	sqlMockSchedule  sqlmock.Sqlmock
	logCtx           logger.Context
)

func init() {
	ctx = context.Background()
	pdHexConf = config.PDHexConf{
		Schema:          "http",
		Host:            "bgp-api.vir777.net",
		IP:              "127.0.0.1",
		Port:            80,
		AuthenticateKey: "authenticateKey",
	}

	pdHexURL = fmt.Sprintf("%s://%s:%d", pdHexConf.Schema, pdHexConf.IP, pdHexConf.Port)

	pdCloudConf = config.PDCloudConf{
		Schema: "http",
		Host:   "cloud-apollo.vir777.net",
		IP:     "127.0.0.1",
		Port:   80,
	}

	pdCloudURL = fmt.Sprintf("%s://%s:%d", pdCloudConf.Schema, pdCloudConf.IP, pdCloudConf.Port)

	conf := config.Config{
		PDHexConf:   pdHexConf,
		PDCloudConf: pdCloudConf,
	}

	gameSetGormDb, gameSetSqlMock = NewMockDB()
	cloudSettingDb, cloudSettingMock = NewMockDB()
	gameGormDb, gameSqlMock = NewMockDB()
	csMemDb, csMemMock = NewMockDB()
	userDb, userMock = NewMockDB()
	gormDomainDB, sqlMockDomain = NewMockDB()
	gormScheduleDB, sqlMockSchedule = NewMockDB()

	logCtx, _ = logger.New(logger.Conf{})

	svcCtx = svc.NewServiceContext(conf, logCtx, svc.ExternalContext{
		GameSetDB:      gameSetGormDb,
		CloudSettingDB: cloudSettingDb,
		CsMemDB:        csMemDb,
		GameDB:         gameGormDb,
		UserDB:         userDb,
		DomainDB:       gormDomainDB,
		ScheduleDB:     gormScheduleDB,
	})
}

func NewMockDB() (*gorm.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	if err != nil {
		log.Fatalf("An error '%s' was not expected when opening a stub database connection", err)
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		DriverName:                "mysql",
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})

	if err != nil {
		log.Fatalf("An error '%s' was not expected when opening gorm database", err)
	}

	if gormDB == nil {
		log.Fatal("gorm db is null")
	}
	return gormDB, mock
}
