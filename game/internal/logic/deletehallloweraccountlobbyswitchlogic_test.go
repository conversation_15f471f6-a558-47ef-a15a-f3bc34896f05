package logic

import (
	"testing"

	"gbh/errorx"
	"gbh/proto/game"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestDeleteHallLowerAccountLobbySwitch(t *testing.T) {
	request := game.DeleteHallLowerAccountLobbySwitchRequest{
		HallId:   3820474,
		GameKind: 3,
	}

	userMock.ExpectBegin()

	userMock.ExpectExec("DELETE FROM `UserLobby` WHERE domain = ? AND userid != ? AND lobby = ?").
		WithArgs(request.GetHallId(), request.GetHallId(), request.GetGameKind()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	userMock.ExpectCommit()

	l := NewDeleteHallLowerAccountLobbySwitchLogic(ctx, svcCtx)
	resp, err := l.DeleteHallLowerAccountLobbySwitch(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.NoError(t, err)
	assert.Equal(t, &game.EmptyResponse{}, resp)
}

func TestDeleteHallLowerAccountLobbySwitch_DatabaseError(t *testing.T) {
	request := game.DeleteHallLowerAccountLobbySwitchRequest{
		HallId:   3820474,
		GameKind: 3,
	}

	userMock.ExpectBegin()

	userMock.ExpectExec("DELETE FROM `UserLobby` WHERE domain = ? AND userid != ? AND lobby = ?").
		WithArgs(request.GetHallId(), request.GetHallId(), request.GetGameKind()).
		WillReturnError(gorm.ErrInvalidDB)

	userMock.ExpectRollback()

	l := NewDeleteHallLowerAccountLobbySwitchLogic(ctx, svcCtx)
	resp, err := l.DeleteHallLowerAccountLobbySwitch(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.Equal(t, errorx.DatabaseError, err)
	assert.Nil(t, resp)
}
