package logic

import (
	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/proto/game"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestSetHallLobbyCloseTime_CountDatabaseError(t *testing.T) {
	req := game.SetHallLobbyCloseTimeRequest{
		GameKind: 3,
		HallId:   3820474,
	}

	sqlMockDomain.ExpectQuery("SELECT * FROM `LobbyCloseTime` WHERE `lobby` = ? AND `domain` = ? ORDER BY `LobbyCloseTime`.`lobby` LIMIT ?").
		WithArgs(req.GetGameKind(), req.GetHallId(), 1).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewSetHallLobbyCloseTimeLogic(ctx, svcCtx)
	resp, err := l.SetHallLobbyCloseTime(&req)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestSetHallLobbyCloseTime_Insert(t *testing.T) {
	req := game.SetHallLobbyCloseTimeRequest{
		GameKind: 3,
		HallId:   3820474,
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectQuery("SELECT * FROM `LobbyCloseTime` WHERE `lobby` = ? AND `domain` = ? ORDER BY `LobbyCloseTime`.`lobby` LIMIT ?").
		WithArgs(req.GetGameKind(), req.GetHallId(), 1).
		WillReturnRows(sqlMockDomain.NewRows([]string{"lobby", "domain", "close_time"}))

	sqlMockDomain.ExpectBegin()

	sqlMockDomain.ExpectExec("INSERT INTO `LobbyCloseTime` (`lobby`,`domain`,`close_time`) VALUES (?,?,?)").
		WithArgs(req.GetGameKind(), req.GetHallId(), now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockDomain.ExpectCommit()

	l := NewSetHallLobbyCloseTimeLogic(ctx, svcCtx)
	resp, err := l.SetHallLobbyCloseTime(&req)

	assert.Equal(t, &game.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestSetHallLobbyCloseTime_Update(t *testing.T) {
	req := game.SetHallLobbyCloseTimeRequest{
		GameKind: 3,
		HallId:   3820474,
	}

	rows := sqlMockDomain.NewRows([]string{"lobby", "domain", "close_time"}).
		AddRow(3, 3820474, "2025-05-21 03:38:11")

	sqlMockDomain.ExpectQuery("SELECT * FROM `LobbyCloseTime` WHERE `lobby` = ? AND `domain` = ? ORDER BY `LobbyCloseTime`.`lobby` LIMIT ?").
		WithArgs(req.GetGameKind(), req.GetHallId(), 1).
		WillReturnRows(rows)

	sqlMockDomain.ExpectBegin()

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectExec("UPDATE `LobbyCloseTime` SET `close_time`=? WHERE `lobby` = ? AND `domain` = ?").
		WithArgs(now, req.GetGameKind(), req.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockDomain.ExpectCommit()

	l := NewSetHallLobbyCloseTimeLogic(ctx, svcCtx)
	resp, err := l.SetHallLobbyCloseTime(&req)

	assert.Equal(t, &game.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestSetHallLobbyCloseTime_InsertDatebaseError(t *testing.T) {
	req := game.SetHallLobbyCloseTimeRequest{
		GameKind: 3,
		HallId:   3820474,
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectQuery("SELECT * FROM `LobbyCloseTime` WHERE `lobby` = ? AND `domain` = ? ORDER BY `LobbyCloseTime`.`lobby` LIMIT ?").
		WithArgs(req.GetGameKind(), req.GetHallId(), 1).
		WillReturnRows(sqlMockDomain.NewRows([]string{"lobby", "domain", "close_time"}))

	sqlMockDomain.ExpectBegin()

	sqlMockDomain.ExpectExec("INSERT INTO `LobbyCloseTime` (`lobby`,`domain`,`close_time`) VALUES (?,?,?)").
		WithArgs(req.GetGameKind(), req.GetHallId(), now).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockDomain.ExpectRollback()

	l := NewSetHallLobbyCloseTimeLogic(ctx, svcCtx)
	resp, err := l.SetHallLobbyCloseTime(&req)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestSetHallLobbyCloseTime_UpdateDatabaseError(t *testing.T) {
	req := game.SetHallLobbyCloseTimeRequest{
		GameKind: 3,
		HallId:   3820474,
	}

	rows := sqlMockDomain.NewRows([]string{"lobby", "domain", "close_time"}).
		AddRow(3, 3820474, "2025-05-21 03:38:11")

	sqlMockDomain.ExpectQuery("SELECT * FROM `LobbyCloseTime` WHERE `lobby` = ? AND `domain` = ? ORDER BY `LobbyCloseTime`.`lobby` LIMIT ?").
		WithArgs(req.GetGameKind(), req.GetHallId(), 1).
		WillReturnRows(rows)

	sqlMockDomain.ExpectBegin()

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectExec("UPDATE `LobbyCloseTime` SET `close_time`=? WHERE `lobby` = ? AND `domain` = ?").
		WithArgs(now, req.GetGameKind(), req.GetHallId()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockDomain.ExpectRollback()

	l := NewSetHallLobbyCloseTimeLogic(ctx, svcCtx)
	resp, err := l.SetHallLobbyCloseTime(&req)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}
