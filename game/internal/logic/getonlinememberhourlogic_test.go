package logic

import (
	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/proto/game"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetOnlineMemberHourLogic_Get(t *testing.T) {
	request := game.GetOnlineMemberHourRequest{
		HallId:    3820474,
		StartTime: "2025-03-25T17:00:00-04:00",
	}

	rows := sqlmock.NewRows([]string{"datetime", "count"}).
		AddRow("2025-03-25 17:00:00", 1).
		AddRow("2025-03-25 18:00:00", 1)

	now := carbon.Now()
	today := carbon.Parse(now.EndOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	yesterday := carbon.Parse(now.Yesterday().StartOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfDay().SubDays(28).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfDay().SubDays(28).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectQuery("SELECT datetime, SUM(amount) AS count FROM `OnlineMemberHour` WHERE domain = ? AND (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) GROUP BY `datetime`").
		WithArgs(request.GetHallId(), "2025-03-25 17:00:00", "2025-03-26 16:59:59", yesterday, today, startLastMonth, endLastMonth).
		WillReturnRows(rows)

	ingressRows := sqlmock.NewRows([]string{"datetime", "ingress", "count"}).
		AddRow("2025-03-25 17:00:00", 0, 0).
		AddRow("2025-03-25 18:00:00", 0, 0).
		AddRow("2025-03-25 17:00:00", 1, 1).
		AddRow("2025-03-25 18:00:00", 1, 1).
		AddRow("2025-03-25 17:00:00", 2, 0).
		AddRow("2025-03-25 18:00:00", 2, 0).
		AddRow("2025-03-25 17:00:00", 3, 0).
		AddRow("2025-03-25 18:00:00", 3, 0).
		AddRow("2025-03-25 17:00:00", 4, 0).
		AddRow("2025-03-25 18:00:00", 4, 0).
		AddRow("2025-03-25 17:00:00", 5, 0).
		AddRow("2025-03-25 18:00:00", 5, 0).
		AddRow("2025-03-25 17:00:00", 6, 0).
		AddRow("2025-03-25 18:00:00", 6, 0).
		AddRow("2025-03-25 17:00:00", 7, 0).
		AddRow("2025-03-25 18:00:00", 7, 0).
		AddRow("2025-03-25 17:00:00", 8, 0).
		AddRow("2025-03-25 18:00:00", 8, 0).
		AddRow("2025-03-25 17:00:00", 9, 0).
		AddRow("2025-03-25 18:00:00", 9, 0).
		AddRow("2025-03-25 17:00:00", 10, 0).
		AddRow("2025-03-25 18:00:00", 10, 0).
		AddRow("2025-03-25 17:00:00", 11, 0).
		AddRow("2025-03-25 18:00:00", 11, 0).
		AddRow("2025-03-25 17:00:00", 12, 0).
		AddRow("2025-03-25 18:00:00", 12, 0).
		AddRow("2025-03-25 17:00:00", 13, 0).
		AddRow("2025-03-25 18:00:00", 13, 0).
		AddRow("2025-03-25 17:00:00", 14, 0).
		AddRow("2025-03-25 18:00:00", 14, 0).
		AddRow("2025-03-25 17:00:00", 15, 0).
		AddRow("2025-03-25 18:00:00", 15, 0).
		AddRow("2025-03-25 17:00:00", 16, 0).
		AddRow("2025-03-25 18:00:00", 16, 0).
		AddRow("2025-03-25 17:00:00", 17, 0).
		AddRow("2025-03-25 18:00:00", 17, 0).
		AddRow("2025-03-25 17:00:00", 18, 0).
		AddRow("2025-03-25 18:00:00", 18, 0).
		AddRow("2025-03-25 17:00:00", 19, 0).
		AddRow("2025-03-25 18:00:00", 19, 0)
	sqlMockDomain.ExpectQuery("SELECT datetime, ingress, SUM(amount) AS count FROM `OnlineMemberHour` WHERE domain = ? AND (datetime BETWEEN ? AND ?) GROUP BY ingress, datetime ORDER BY ingress, datetime").
		WithArgs(request.GetHallId(), "2025-03-25 17:00:00", "2025-03-26 16:59:59").
		WillReturnRows(ingressRows)

	l := NewGetOnlineMemberHourLogic(ctx, svcCtx)
	resp, err := l.GetOnlineMemberHour(&request)

	ingressInfo := []*game.OnlineMemberIngressInfo{
		{
			Datetime:   "2025-03-25 17:00:00",
			Count:      0,
			Percentage: 0,
		},
		{
			Datetime:   "2025-03-25 18:00:00",
			Count:      0,
			Percentage: 0,
		},
	}

	expected := &game.GetOnlineMemberHourResponse{
		OnlineMemberSummary: []*game.OnlineMember{
			{
				Datetime: "2025-03-25 17:00:00",
				Count:    1,
			},
			{
				Datetime: "2025-03-25 18:00:00",
				Count:    1,
			},
		},
		OnlineMemberIngressSummary: []*game.OnlineMemberIngressSummary{
			{
				IngressId:               0,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId: 1,
				OnlineMemberIngressInfo: []*game.OnlineMemberIngressInfo{
					{
						Datetime:   "2025-03-25 17:00:00",
						Count:      1,
						Percentage: 100,
					},
					{
						Datetime:   "2025-03-25 18:00:00",
						Count:      1,
						Percentage: 100,
					},
				},
			},
			{
				IngressId:               2,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               3,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               4,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               5,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               6,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               7,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               8,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               9,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               10,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               11,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               12,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               13,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               14,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               15,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               16,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               17,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               18,
				OnlineMemberIngressInfo: ingressInfo,
			},
			{
				IngressId:               19,
				OnlineMemberIngressInfo: ingressInfo,
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestGetOnlineMemberHourLogic_DatabaseError(t *testing.T) {
	request := game.GetOnlineMemberHourRequest{
		HallId:    3820474,
		StartTime: "2025-03-25T17:00:00-04:00",
	}

	now := carbon.Now()
	today := carbon.Parse(now.EndOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	yesterday := carbon.Parse(now.Yesterday().StartOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfDay().SubDays(28).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfDay().SubDays(28).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	sqlMockDomain.ExpectQuery("SELECT datetime, SUM(amount) AS count FROM `OnlineMemberHour` WHERE domain = ? AND (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) GROUP BY `datetime`").
		WithArgs(request.GetHallId(), "2025-03-25 17:00:00", "2025-03-26 16:59:59", yesterday, today, startLastMonth, endLastMonth).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetOnlineMemberHourLogic(ctx, svcCtx)
	resp, err := l.GetOnlineMemberHour(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}

func TestGetOnlineMemberHourLogic_IngressDatabaseError(t *testing.T) {
	request := game.GetOnlineMemberHourRequest{
		HallId:    3820474,
		StartTime: "2025-03-25T17:00:00-04:00",
	}

	now := carbon.Now()
	today := carbon.Parse(now.EndOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	yesterday := carbon.Parse(now.Yesterday().StartOfDay().ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfDay().SubDays(28).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfDay().SubDays(28).ToDateTimeString(), constants.TimezoneGMT4).ToDateTimeString()

	rows := sqlmock.NewRows([]string{"datetime", "count"}).
		AddRow("2025-03-25 17:00:00", 1).
		AddRow("2025-03-25 18:00:00", 1)
	sqlMockDomain.ExpectQuery("SELECT datetime, SUM(amount) AS count FROM `OnlineMemberHour` WHERE domain = ? AND (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) OR (datetime BETWEEN ? AND ?) GROUP BY `datetime`").
		WithArgs(request.GetHallId(), "2025-03-25 17:00:00", "2025-03-26 16:59:59", yesterday, today, startLastMonth, endLastMonth).
		WillReturnRows(rows)

	sqlMockDomain.ExpectQuery("SELECT datetime, ingress, SUM(amount) AS count FROM `OnlineMemberHour` WHERE domain = ? AND (datetime BETWEEN ? AND ?) GROUP BY ingress, datetime ORDER BY ingress, datetime").
		WithArgs(request.GetHallId(), "2025-03-25 17:00:00", "2025-03-26 16:59:59").
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetOnlineMemberHourLogic(ctx, svcCtx)
	resp, err := l.GetOnlineMemberHour(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
}
