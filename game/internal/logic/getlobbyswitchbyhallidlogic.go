package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLobbySwitchByHallIDLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLobbySwitchByHallIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLobbySwitchByHallIDLogic {
	return &GetLobbySwitchByHallIDLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetLobbySwitchByHallIDLogic) GetLobbySwitchByHallID(in *game.GetLobbySwitchByHallIDRequest) (*game.GetLobbySwitchByHallIDResponse, error) {
	var result []*game.LobbySwitch

	query := l.svcCtx.UserDB.Table("UserLobby").
		Select("lobby AS game_kind", "enable as switch").
		Where("domain = ?", in.GetHallId()).
		Where("userid = ?", in.GetHallId())

	if in.GetLobbySwitchEnable() != nil {
		query.Where("enable", in.GetLobbySwitchEnable().GetValue())
	}

	query.Find(&result)

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	// 篩選出該 Lobby 存在且 Enable 為 true 的數據
	if in.GetFilterLobbyEnable() {
		var filteredResult []*game.LobbySwitch
		for _, data := range result {
			if lobbyInfo, exist := constants.Lobby[data.GetGameKind()]; exist && lobbyInfo.Enable {
				filteredResult = append(filteredResult, data)
			}
		}

		result = filteredResult
	}

	return &game.GetLobbySwitchByHallIDResponse{
		LobbySwitch: result,
	}, nil
}
