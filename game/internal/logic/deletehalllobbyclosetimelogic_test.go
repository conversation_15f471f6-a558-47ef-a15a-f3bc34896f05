package logic

import (
	"testing"

	"gbh/errorx"
	"gbh/proto/game"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestDeleteHallLobbyCloseTime(t *testing.T) {
	request := game.DeleteHallLobbyCloseTimeRequest{
		HallId:   3820474,
		GameKind: 3,
	}

	sqlMockDomain.ExpectBegin()

	sqlMockDomain.ExpectExec("DELETE FROM `LobbyCloseTime` WHERE domain = ? AND lobby = ?").
		WithArgs(request.GetHallId(), request.GetGameKind()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockDomain.ExpectCommit()

	l := NewDeleteHallLobbyCloseTimeLogic(ctx, svcCtx)
	resp, err := l.DeleteHallLobbyCloseTime(&request)

	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
	assert.NoError(t, err)
	assert.Equal(t, &game.EmptyResponse{}, resp)
}

func TestDeleteHallLobbyCloseTime_DatabaseError(t *testing.T) {
	request := game.DeleteHallLobbyCloseTimeRequest{
		HallId:   3820474,
		GameKind: 3,
	}

	sqlMockDomain.ExpectBegin()

	sqlMockDomain.ExpectExec("DELETE FROM `LobbyCloseTime` WHERE domain = ? AND lobby = ?").
		WithArgs(request.GetHallId(), request.GetGameKind()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockDomain.ExpectRollback()

	l := NewDeleteHallLobbyCloseTimeLogic(ctx, svcCtx)
	resp, err := l.DeleteHallLobbyCloseTime(&request)

	assert.NoError(t, sqlMockDomain.ExpectationsWereMet())
	assert.Equal(t, errorx.DatabaseError, err)
	assert.Nil(t, resp)
}
