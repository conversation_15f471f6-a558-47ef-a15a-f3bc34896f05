package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateUserLobbySwitchLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateUserLobbySwitchLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUserLobbySwitchLogic {
	return &CreateUserLobbySwitchLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateUserLobbySwitchLogic) CreateUserLobbySwitch(in *game.CreateUserLobbySwitchRequest) (*game.EmptyResponse, error) {
	userLobbySwitch := []map[string]interface{}{}

	for _, v := range in.GetUserLobbySwitch() {
		userLobbySwitch = append(userLobbySwitch, map[string]interface{}{
			"userid": v.GetUserId(),
			"domain": v.GetHallId(),
			"lobby":  v.<PERSON>Game<PERSON>(),
			"enable": v.GetEnable(),
		})
	}

	err := l.svcCtx.UserDB.Table("UserLobby").Create(&userLobbySwitch).Error
	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &game.EmptyResponse{}, nil
}
