package logic

import (
	"context"
	"math"
	"sort"

	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetIngressRequest struct {
	TableName string
	HallID    uint32
	StartTime string
	EndTime   string
}

type onlineMemberMinIngressData struct {
	Datetime string
	Ingress  uint32
	Count    uint32
}

type GetOnlineMemberMinLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetOnlineMemberMinLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOnlineMemberMinLogic {
	return &GetOnlineMemberMinLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetOnlineMemberMinLogic) GetOnlineMemberMin(in *game.GetOnlineMemberMinRequest) (*game.GetOnlineMemberMinResponse, error) {
	startTime := carbon.Parse(in.GetStartTime(), constants.TimezoneGMT4).SetSecond(0).ToDateTimeString()
	endTime := carbon.Parse(in.GetStartTime(), constants.TimezoneGMT4).EndOfHour().ToDateTimeString()

	var onlineMemberInfo []*game.OnlineMember
	query := l.svcCtx.DomainDB.Table("OnlineMemberMin").
		Select("datetime, SUM(amount) AS count").
		Where("domain = ?", in.GetHallId()).
		Where("datetime BETWEEN ? AND ?", startTime, endTime).
		Group("datetime").
		Find(&onlineMemberInfo)

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	ingressInfo, err := GetIngressData(l.svcCtx, onlineMemberInfo, GetIngressRequest{
		TableName: "OnlineMemberMin",
		HallID:    in.GetHallId(),
		StartTime: startTime,
		EndTime:   endTime,
	})
	if err != nil {
		return nil, err
	}

	resp := &game.GetOnlineMemberMinResponse{
		OnlineMemberSummary:        onlineMemberInfo,
		OnlineMemberIngressSummary: ingressInfo,
	}

	return resp, nil
}

func GetIngressData(svcCtx *svc.ServiceContext, result []*game.OnlineMember, request GetIngressRequest) ([]*game.OnlineMemberIngressSummary, error) {
	var ingressData []onlineMemberMinIngressData
	query := svcCtx.DomainDB.Table(request.TableName).
		Select("datetime, ingress, SUM(amount) AS count").
		Where("domain = ?", request.HallID).
		Where("datetime BETWEEN ? AND ?", request.StartTime, request.EndTime).
		Group("ingress, datetime").
		Order("ingress, datetime").
		Find(&ingressData)

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	dateTimeMap := make(map[string]uint32)
	for _, v := range result {
		dateTimeMap[v.GetDatetime()] = v.GetCount()
	}

	var ingress = []uint32{
		0,
		1,
		2,
		3,
		4,
		5,
		6,
		7,
		8,
		9,
		10,
		11,
		12,
		13,
		14,
		15,
		16,
		17,
		18,
		19,
	}

	// 避免來源資料不齊的狀況
	// 因為目前舊功能沒有ingress = 0的顯示，需要額外判斷
	summaryMap := make(map[uint32]*game.OnlineMemberIngressSummary)
	// 先初始化每個 ingress id
	for _, ingressID := range ingress {
		summaryMap[ingressID] = &game.OnlineMemberIngressSummary{
			IngressId: ingressID,
		}
	}

	// 將ingressData依ingress塞進對應的map裡
	for _, v := range ingressData {
		// 取出當天總數並且計算百分比
		var percentage uint32
		if count, ok := dateTimeMap[v.Datetime]; ok {
			if count > 0 {
				percentage = uint32(math.Floor((float64(v.Count)/float64(count))*constants.TenThousand)) / constants.OneHundred
			}
		}

		if summary, ok := summaryMap[v.Ingress]; ok {
			summary.OnlineMemberIngressInfo = append(summary.OnlineMemberIngressInfo, &game.OnlineMemberIngressInfo{
				Datetime:   v.Datetime,
				Count:      v.Count,
				Percentage: percentage,
			})
		}
	}

	// 最後轉成Slice
	ingressSummary := []*game.OnlineMemberIngressSummary{}
	for _, summary := range summaryMap {
		ingressSummary = append(ingressSummary, summary)
	}

	// 根據IngressId的順序排序資料
	sort.Slice(ingressSummary, func(i, j int) bool {
		return ingressSummary[i].GetIngressId() < ingressSummary[j].GetIngressId()
	})

	return ingressSummary, nil
}
