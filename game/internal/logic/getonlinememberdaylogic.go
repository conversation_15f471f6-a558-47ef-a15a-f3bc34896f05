package logic

import (
	"context"

	"gbh/errorx"
	"gbh/game/internal/constants"
	"gbh/game/internal/svc"
	"gbh/proto/game"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetOnlineMemberDayLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetOnlineMemberDayLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOnlineMemberDayLogic {
	return &GetOnlineMemberDayLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetOnlineMemberDayLogic) GetOnlineMemberDay(in *game.GetOnlineMemberDayRequest) (*game.GetOnlineMemberDayResponse, error) {
	startTime := carbon.Parse(in.GetStartTime(), constants.TimezoneGMT4).ToDateTimeString()
	endTime := carbon.Parse(in.GetEndTime(), constants.TimezoneGMT4).ToDateTimeString()

	now := carbon.Now()
	startLastWeek := carbon.Parse(now.StartOfWeek().SubDays(constants.SevenDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastWeek := carbon.Parse(now.EndOfWeek().SubDays(constants.SevenDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	startLastMonth := carbon.Parse(now.StartOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()
	endLastMonth := carbon.Parse(now.EndOfWeek().SubDays(constants.TwentyEightDays).ToDateString(), constants.TimezoneGMT4).ToDateTimeString()

	var onlineMemberInfo []*game.OnlineMember
	query := l.svcCtx.DomainDB.Table("OnlineMemberDay").
		Select("datetime, SUM(amount) AS count").
		Where("domain = ?", in.GetHallId()).
		Where("datetime BETWEEN ? AND ?", startTime, endTime).
		Or("datetime BETWEEN ? AND ?", startLastWeek, endLastWeek).
		Or("datetime BETWEEN ? AND ?", startLastMonth, endLastMonth).
		Group("datetime").
		Find(&onlineMemberInfo)

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	ingressInfo, err := GetIngressData(l.svcCtx, onlineMemberInfo, GetIngressRequest{
		TableName: "OnlineMemberDay",
		HallID:    in.GetHallId(),
		StartTime: startTime,
		EndTime:   endTime,
	})
	if err != nil {
		return nil, err
	}

	resp := &game.GetOnlineMemberDayResponse{
		OnlineMemberSummary:        onlineMemberInfo,
		OnlineMemberIngressSummary: ingressInfo,
	}

	return resp, nil
}
