package logic

import (
	"gbh/errorx"
	"gbh/proto/game"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetLobbySwitchByHallID(t *testing.T) {
	request := game.GetLobbySwitchByHallIDRequest{
		HallId: 3820474,
	}

	mockRows := sqlmock.NewRows([]string{"game_kind", "switch"}).
		AddRow(5, true).
		AddRow(12, false)

	userMock.ExpectQuery("SELECT lobby AS game_kind,enable as switch FROM `UserLobby` WHERE domain = ? AND userid = ?").
		WithArgs(request.GetHallId(), request.GetHallId()).
		WillReturnRows(mockRows)

	l := NewGetLobbySwitchByHallIDLogic(ctx, svcCtx)
	resp, err := l.GetLobbySwitchByHallID(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.NoError(t, err)
	assert.Equal(t, &game.GetLobbySwitchByHallIDResponse{
		LobbySwitch: []*game.LobbySwitch{
			{
				GameKind: 5,
				Switch:   true,
			},
			{
				GameKind: 12,
				Switch:   false,
			},
		},
	}, resp)
}

func TestGetLobbySwitchByHallID_WithLobbySwitchEnable(t *testing.T) {
	request := game.GetLobbySwitchByHallIDRequest{
		HallId: 3820474,
		LobbySwitchEnable: &game.BoolValue{
			Value: true,
		},
	}

	mockRows := sqlmock.NewRows([]string{"game_kind", "switch"}).
		AddRow(5, true)

	userMock.ExpectQuery("SELECT lobby AS game_kind,enable as switch FROM `UserLobby` WHERE domain = ? AND userid = ? AND `enable` = ?").
		WithArgs(request.GetHallId(), request.GetHallId(), request.GetLobbySwitchEnable().GetValue()).
		WillReturnRows(mockRows)

	l := NewGetLobbySwitchByHallIDLogic(ctx, svcCtx)
	resp, err := l.GetLobbySwitchByHallID(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.NoError(t, err)
	assert.Equal(t, &game.GetLobbySwitchByHallIDResponse{
		LobbySwitch: []*game.LobbySwitch{
			{
				GameKind: 5,
				Switch:   true,
			},
		},
	}, resp)
}

func TestGetLobbySwitchByHallID_WithFilterLobbyEnable(t *testing.T) {
	request := game.GetLobbySwitchByHallIDRequest{
		HallId:            3820474,
		FilterLobbyEnable: true,
	}

	mockRows := sqlmock.NewRows([]string{"game_kind", "switch"}).
		AddRow(5, true).
		AddRow(12, false).
		AddRow(100, false)

	userMock.ExpectQuery("SELECT lobby AS game_kind,enable as switch FROM `UserLobby` WHERE domain = ? AND userid = ?").
		WithArgs(request.GetHallId(), request.GetHallId()).
		WillReturnRows(mockRows)

	l := NewGetLobbySwitchByHallIDLogic(ctx, svcCtx)
	resp, err := l.GetLobbySwitchByHallID(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.NoError(t, err)
	assert.Equal(t, &game.GetLobbySwitchByHallIDResponse{
		LobbySwitch: []*game.LobbySwitch{
			{
				GameKind: 5,
				Switch:   true,
			},
			{
				GameKind: 12,
				Switch:   false,
			},
		},
	}, resp)
}

func TestGetLobbySwitchByHallID_DatabaseError(t *testing.T) {
	request := game.GetLobbySwitchByHallIDRequest{
		HallId: 3820474,
	}

	userMock.ExpectQuery("SELECT lobby AS game_kind,enable as switch FROM `UserLobby` WHERE domain = ? AND userid = ?").
		WithArgs(request.GetHallId(), request.GetHallId()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetLobbySwitchByHallIDLogic(ctx, svcCtx)
	resp, err := l.GetLobbySwitchByHallID(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.Equal(t, errorx.DatabaseError, err)
	assert.Nil(t, resp)
}
