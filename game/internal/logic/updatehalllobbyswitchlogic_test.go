package logic

import (
	"testing"

	"gbh/errorx"
	"gbh/proto/game"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestUpdateHallLobbySwitch(t *testing.T) {
	request := game.UpdateHallLobbySwitchRequest{
		HallId:   3820474,
		GameKind: 3,
		Enable:   true,
	}

	userMock.ExpectBegin()

	userMock.ExpectExec("UPDATE `UserLobby` SET `enable`=? WHERE domain = ? AND userid = ? AND lobby = ?").
		WithArgs(request.GetEnable(), request.GetHallId(), request.GetHallId(), request.GetGameKind()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	userMock.ExpectCommit()

	l := NewUpdateHallLobbySwitchLogic(ctx, svcCtx)
	resp, err := l.UpdateHallLobbySwitch(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.NoError(t, err)
	assert.Equal(t, &game.EmptyResponse{}, resp)
}

func TestUpdateHallLobbySwitch_DatabaseError(t *testing.T) {
	request := game.UpdateHallLobbySwitchRequest{
		HallId:   3820474,
		GameKind: 3,
		Enable:   false,
	}

	userMock.ExpectBegin()

	userMock.ExpectExec("UPDATE `UserLobby` SET `enable`=? WHERE domain = ? AND userid = ? AND lobby = ?").
		WithArgs(request.GetEnable(), request.GetHallId(), request.GetHallId(), request.GetGameKind()).
		WillReturnError(gorm.ErrInvalidDB)

	userMock.ExpectRollback()

	l := NewUpdateHallLobbySwitchLogic(ctx, svcCtx)
	resp, err := l.UpdateHallLobbySwitch(&request)

	assert.NoError(t, userMock.ExpectationsWereMet())
	assert.Equal(t, errorx.DatabaseError, err)
	assert.Nil(t, resp)
}
