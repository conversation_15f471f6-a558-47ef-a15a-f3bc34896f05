// Code generated by goctl. DO NOT EDIT.
// Source: game.proto

package server

import (
	"context"

	"gbh/game/internal/logic"
	"gbh/game/internal/svc"
	"gbh/proto/game"
)

type GameServer struct {
	svcCtx *svc.ServiceContext
	game.UnimplementedGameServer
}

func NewGameServer(svcCtx *svc.ServiceContext) *GameServer {
	return &GameServer{
		svcCtx: svcCtx,
	}
}

func (s *GameServer) Lobby(ctx context.Context, in *game.EmptyRequest) (*game.LobbyResponse, error) {
	l := logic.NewLobbyLogic(ctx, s.svcCtx)
	return l.<PERSON><PERSON>(in)
}

func (s *GameServer) EnableGameList(ctx context.Context, in *game.EnableGameListRequest) (*game.EnableGameListResponse, error) {
	l := logic.NewEnableGameListLogic(ctx, s.svcCtx)
	return l.EnableGameList(in)
}

func (s *GameServer) DemoLink(ctx context.Context, in *game.DemoLinkRequest) (*game.DemoLinkResponse, error) {
	l := logic.NewDemoLinkLogic(ctx, s.svcCtx)
	return l.DemoLink(in)
}

func (s *GameServer) GameDomain(ctx context.Context, in *game.GameDomainRequest) (*game.GameDomainResponse, error) {
	l := logic.NewGameDomainLogic(ctx, s.svcCtx)
	return l.GameDomain(in)
}

func (s *GameServer) LobbyLink(ctx context.Context, in *game.LobbyLinkRequest) (*game.LobbyLinkResponse, error) {
	l := logic.NewLobbyLinkLogic(ctx, s.svcCtx)
	return l.LobbyLink(in)
}

func (s *GameServer) GetLobbySwitch(ctx context.Context, in *game.GetLobbySwitchRequest) (*game.GetLobbySwitchResponse, error) {
	l := logic.NewGetLobbySwitchLogic(ctx, s.svcCtx)
	return l.GetLobbySwitch(in)
}

func (s *GameServer) GetLobbySwitchByHallID(ctx context.Context, in *game.GetLobbySwitchByHallIDRequest) (*game.GetLobbySwitchByHallIDResponse, error) {
	l := logic.NewGetLobbySwitchByHallIDLogic(ctx, s.svcCtx)
	return l.GetLobbySwitchByHallID(in)
}

func (s *GameServer) GetCategory(ctx context.Context, in *game.GetCategoryRequest) (*game.GetCategoryResponse, error) {
	l := logic.NewGetCategoryLogic(ctx, s.svcCtx)
	return l.GetCategory(in)
}

func (s *GameServer) GetMenuSort(ctx context.Context, in *game.GetMenuSortRequest) (*game.GetMenuSortResponse, error) {
	l := logic.NewGetMenuSortLogic(ctx, s.svcCtx)
	return l.GetMenuSort(in)
}

func (s *GameServer) GetGameInfo(ctx context.Context, in *game.GetGameInfoRequest) (*game.GetGameInfoResponse, error) {
	l := logic.NewGetGameInfoLogic(ctx, s.svcCtx)
	return l.GetGameInfo(in)
}

func (s *GameServer) GetLobbyGameEntranceSwitch(ctx context.Context, in *game.GetLobbyGameEntranceSwitchRequest) (*game.GetLobbyGameEntranceSwitchResponse, error) {
	l := logic.NewGetLobbyGameEntranceSwitchLogic(ctx, s.svcCtx)
	return l.GetLobbyGameEntranceSwitch(in)
}

func (s *GameServer) BulletinList(ctx context.Context, in *game.BulletinRequest) (*game.BulletinResponse, error) {
	l := logic.NewBulletinListLogic(ctx, s.svcCtx)
	return l.BulletinList(in)
}

func (s *GameServer) GameKindList(ctx context.Context, in *game.EmptyRequest) (*game.GameKindListResponse, error) {
	l := logic.NewGameKindListLogic(ctx, s.svcCtx)
	return l.GameKindList(in)
}

func (s *GameServer) GameIconKind(ctx context.Context, in *game.EmptyRequest) (*game.GameIconKindResponse, error) {
	l := logic.NewGameIconKindLogic(ctx, s.svcCtx)
	return l.GameIconKind(in)
}

func (s *GameServer) AddUserFavorite(ctx context.Context, in *game.AddUserFavoriteRequest) (*game.EmptyResponse, error) {
	l := logic.NewAddUserFavoriteLogic(ctx, s.svcCtx)
	return l.AddUserFavorite(in)
}

func (s *GameServer) GetMenuName(ctx context.Context, in *game.GetMenuNameRequest) (*game.GetMenuNameResponse, error) {
	l := logic.NewGetMenuNameLogic(ctx, s.svcCtx)
	return l.GetMenuName(in)
}

func (s *GameServer) GetUserFavorite(ctx context.Context, in *game.GetUserFavoriteRequest) (*game.GetUserFavoriteResponse, error) {
	l := logic.NewGetUserFavoriteLogic(ctx, s.svcCtx)
	return l.GetUserFavorite(in)
}

func (s *GameServer) DeleteUserFavorite(ctx context.Context, in *game.DeleteUserFavoriteRequest) (*game.EmptyResponse, error) {
	l := logic.NewDeleteUserFavoriteLogic(ctx, s.svcCtx)
	return l.DeleteUserFavorite(in)
}

func (s *GameServer) GameList(ctx context.Context, in *game.GameListRequest) (*game.GameListResponse, error) {
	l := logic.NewGameListLogic(ctx, s.svcCtx)
	return l.GameList(in)
}

func (s *GameServer) GetGameMaintainLabel(ctx context.Context, in *game.GameMaintainLabelRequest) (*game.GameMaintainLabelResponse, error) {
	l := logic.NewGetGameMaintainLabelLogic(ctx, s.svcCtx)
	return l.GetGameMaintainLabel(in)
}

func (s *GameServer) GetGameURLList(ctx context.Context, in *game.GameURLListRequest) (*game.GameURLListResponse, error) {
	l := logic.NewGetGameURLListLogic(ctx, s.svcCtx)
	return l.GetGameURLList(in)
}

func (s *GameServer) LobbyCategory(ctx context.Context, in *game.LobbyCategoryRequest) (*game.LobbyCategoryResponse, error) {
	l := logic.NewLobbyCategoryLogic(ctx, s.svcCtx)
	return l.LobbyCategory(in)
}

func (s *GameServer) GetGameListWithSwitch(ctx context.Context, in *game.GetGameListWithSwitchRequest) (*game.GetGameListWithSwitchResponse, error) {
	l := logic.NewGetGameListWithSwitchLogic(ctx, s.svcCtx)
	return l.GetGameListWithSwitch(in)
}

func (s *GameServer) GetUserLobbySwitch(ctx context.Context, in *game.GetUserLobbySwitchRequest) (*game.GetUserLobbySwitchResponse, error) {
	l := logic.NewGetUserLobbySwitchLogic(ctx, s.svcCtx)
	return l.GetUserLobbySwitch(in)
}

func (s *GameServer) GetGameDetail(ctx context.Context, in *game.GetGameDetailRequest) (*game.GetGameDetailResponse, error) {
	l := logic.NewGetGameDetailLogic(ctx, s.svcCtx)
	return l.GetGameDetail(in)
}

func (s *GameServer) CreateGameDetail(ctx context.Context, in *game.CreateGameDetailRequest) (*game.EmptyResponse, error) {
	l := logic.NewCreateGameDetailLogic(ctx, s.svcCtx)
	return l.CreateGameDetail(in)
}

func (s *GameServer) GetOnlineMemberMin(ctx context.Context, in *game.GetOnlineMemberMinRequest) (*game.GetOnlineMemberMinResponse, error) {
	l := logic.NewGetOnlineMemberMinLogic(ctx, s.svcCtx)
	return l.GetOnlineMemberMin(in)
}

func (s *GameServer) DeleteAPISynchronize(ctx context.Context, in *game.DeleteAPISynchronizeRequest) (*game.EmptyResponse, error) {
	l := logic.NewDeleteAPISynchronizeLogic(ctx, s.svcCtx)
	return l.DeleteAPISynchronize(in)
}

func (s *GameServer) ModifyGameTypeSwitch(ctx context.Context, in *game.ModifyGameTypeSwitchRequest) (*game.EmptyResponse, error) {
	l := logic.NewModifyGameTypeSwitchLogic(ctx, s.svcCtx)
	return l.ModifyGameTypeSwitch(in)
}

func (s *GameServer) DeleteGameTypeSynchronize(ctx context.Context, in *game.DeleteGameTypeSynchronizeRequest) (*game.EmptyResponse, error) {
	l := logic.NewDeleteGameTypeSynchronizeLogic(ctx, s.svcCtx)
	return l.DeleteGameTypeSynchronize(in)
}

func (s *GameServer) ManageGameHallSwitch(ctx context.Context, in *game.ManageGameHallSwitchRequest) (*game.EmptyResponse, error) {
	l := logic.NewManageGameHallSwitchLogic(ctx, s.svcCtx)
	return l.ManageGameHallSwitch(in)
}

func (s *GameServer) UpdateGameInfoHallList(ctx context.Context, in *game.UpdateGameInfoHallListRequest) (*game.EmptyResponse, error) {
	l := logic.NewUpdateGameInfoHallListLogic(ctx, s.svcCtx)
	return l.UpdateGameInfoHallList(in)
}

func (s *GameServer) DeleteUserLobby(ctx context.Context, in *game.DeleteUserLobbyRequest) (*game.EmptyResponse, error) {
	l := logic.NewDeleteUserLobbyLogic(ctx, s.svcCtx)
	return l.DeleteUserLobby(in)
}

func (s *GameServer) GetOnlineMemberHour(ctx context.Context, in *game.GetOnlineMemberHourRequest) (*game.GetOnlineMemberHourResponse, error) {
	l := logic.NewGetOnlineMemberHourLogic(ctx, s.svcCtx)
	return l.GetOnlineMemberHour(in)
}

func (s *GameServer) UpdateHallLobbySwitch(ctx context.Context, in *game.UpdateHallLobbySwitchRequest) (*game.EmptyResponse, error) {
	l := logic.NewUpdateHallLobbySwitchLogic(ctx, s.svcCtx)
	return l.UpdateHallLobbySwitch(in)
}

func (s *GameServer) DeleteHallLowerAccountLobbySwitch(ctx context.Context, in *game.DeleteHallLowerAccountLobbySwitchRequest) (*game.EmptyResponse, error) {
	l := logic.NewDeleteHallLowerAccountLobbySwitchLogic(ctx, s.svcCtx)
	return l.DeleteHallLowerAccountLobbySwitch(in)
}

func (s *GameServer) CreateUserLobbySwitch(ctx context.Context, in *game.CreateUserLobbySwitchRequest) (*game.EmptyResponse, error) {
	l := logic.NewCreateUserLobbySwitchLogic(ctx, s.svcCtx)
	return l.CreateUserLobbySwitch(in)
}

func (s *GameServer) GetOnlineMemberDay(ctx context.Context, in *game.GetOnlineMemberDayRequest) (*game.GetOnlineMemberDayResponse, error) {
	l := logic.NewGetOnlineMemberDayLogic(ctx, s.svcCtx)
	return l.GetOnlineMemberDay(in)
}

func (s *GameServer) DeleteHallLobbyCloseTime(ctx context.Context, in *game.DeleteHallLobbyCloseTimeRequest) (*game.EmptyResponse, error) {
	l := logic.NewDeleteHallLobbyCloseTimeLogic(ctx, s.svcCtx)
	return l.DeleteHallLobbyCloseTime(in)
}

func (s *GameServer) SetHallLobbyCloseTime(ctx context.Context, in *game.SetHallLobbyCloseTimeRequest) (*game.EmptyResponse, error) {
	l := logic.NewSetHallLobbyCloseTimeLogic(ctx, s.svcCtx)
	return l.SetHallLobbyCloseTime(in)
}
