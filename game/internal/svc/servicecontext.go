package svc

import (
	"crypto/tls"
	"fmt"

	"gbh/game/internal/config"
	"gbh/game/internal/constants"
	"gbh/logger"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config         config.Config
	PdHexClient    *resty.Client
	PdCloudClient  *resty.Client
	GameSetDB      *gorm.DB
	CloudSettingDB *gorm.DB
	CsMemDB        *gorm.DB
	GameDB         *gorm.DB
	UserDB         *gorm.DB
	DomainDB       *gorm.DB
	ScheduleDB     *gorm.DB
	Logger         logger.Context
}

type ExternalContext struct {
	GameSetDB      *gorm.DB
	CloudSettingDB *gorm.DB
	CsMemDB        *gorm.DB
	GameDB         *gorm.DB
	UserDB         *gorm.DB
	DomainDB       *gorm.DB
	ScheduleDB     *gorm.DB
}

func NewServiceContext(c config.Config, logCtx logger.Context, extSvc ExternalContext) *ServiceContext {
	pdHexClient := resty.New()
	pdHexBaseURL := fmt.Sprintf("%s://%s:%d", c.PDHexConf.Schema, c.PDHexConf.IP, c.PDHexConf.Port)
	pdHexClient.SetBaseURL(pdHexBaseURL)
	pdHexClient.SetHeader("Host", c.PDHexConf.Host)
	pdHexClient.SetHeader("Route", "RD3")
	pdHexClient.SetHeader("Authenticate-Key", c.PDHexConf.AuthenticateKey)
	pdHexClient.SetTimeout(constants.APITimeout)

	if c.PDHexConf.Schema == "https" {
		pdHexClient.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}

	pdCloudClient := resty.New()
	pdCloudBaseURL := fmt.Sprintf("%s://%s:%d", c.PDCloudConf.Schema, c.PDCloudConf.IP, c.PDCloudConf.Port)
	pdCloudClient.SetBaseURL(pdCloudBaseURL)
	pdCloudClient.SetHeader("Host", c.PDCloudConf.Host)
	pdCloudClient.SetTimeout(constants.APITimeout)

	if c.PDCloudConf.Schema == "https" {
		pdCloudClient.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}

	return &ServiceContext{
		Config:         c,
		Logger:         logCtx,
		PdHexClient:    pdHexClient,
		PdCloudClient:  pdCloudClient,
		GameSetDB:      extSvc.GameSetDB,
		CloudSettingDB: extSvc.CloudSettingDB,
		CsMemDB:        extSvc.CsMemDB,
		GameDB:         extSvc.GameDB,
		UserDB:         extSvc.UserDB,
		DomainDB:       extSvc.DomainDB,
		ScheduleDB:     extSvc.ScheduleDB,
	}
}
