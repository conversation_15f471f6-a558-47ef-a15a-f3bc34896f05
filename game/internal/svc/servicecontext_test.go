package svc

import (
	"testing"

	"gbh/game/internal/config"
	"gbh/logger"

	"github.com/stretchr/testify/assert"
)

var (
	logCtx logger.Context
)

func TestNewServiceContext(t *testing.T) {
	pdHexConf := config.PDHexConf{
		Schema:          "http",
		Host:            "bgp-api.vir777.net",
		IP:              "127.0.0.1",
		Port:            80,
		AuthenticateKey: "authenticateKey",
	}

	pdCloudConf := config.PDCloudConf{
		Schema: "http",
		Host:   "cloud-apollo.vir777.net",
		IP:     "127.0.0.1",
		Port:   80,
	}

	conf := config.Config{
		PDHexConf:   pdHexConf,
		PDCloudConf: pdCloudConf,
	}

	svcCtx := NewServiceContext(conf, logCtx, ExternalContext{})

	assert.ObjectsAreEqual(&ServiceContext{}, svcCtx)
	assert.Equal(t, conf, svcCtx.Config)
}

func TestNewServiceContext_WithPDHexHttps(t *testing.T) {
	pdHexConf := config.PDHexConf{
		Schema:          "https",
		Host:            "bgp-api.vir777.net",
		IP:              "127.0.0.1",
		Port:            80,
		AuthenticateKey: "authenticateKey",
	}

	pdCloudConf := config.PDCloudConf{
		Schema: "http",
		Host:   "cloud-apollo.vir777.net",
		IP:     "127.0.0.1",
		Port:   80,
	}

	conf := config.Config{
		PDHexConf:   pdHexConf,
		PDCloudConf: pdCloudConf,
	}

	svcCtx := NewServiceContext(conf, logCtx, ExternalContext{})

	assert.ObjectsAreEqual(&ServiceContext{}, svcCtx)
	assert.Equal(t, conf, svcCtx.Config)

	transport, err := svcCtx.PdHexClient.Transport()

	assert.NoError(t, err)
	assert.True(t, transport.TLSClientConfig.InsecureSkipVerify)
}

func TestNewServiceContext_WithPDCloudHttps(t *testing.T) {
	pdHexConf := config.PDHexConf{
		Schema:          "http",
		Host:            "bgp-api.vir777.net",
		IP:              "127.0.0.1",
		Port:            80,
		AuthenticateKey: "authenticateKey",
	}

	pdCloudConf := config.PDCloudConf{
		Schema: "https",
		Host:   "cloud-apollo.vir777.net",
		IP:     "127.0.0.1",
		Port:   80,
	}

	conf := config.Config{
		PDHexConf:   pdHexConf,
		PDCloudConf: pdCloudConf,
	}

	svcCtx := NewServiceContext(conf, logCtx, ExternalContext{})

	assert.ObjectsAreEqual(&ServiceContext{}, svcCtx)
	assert.Equal(t, conf, svcCtx.Config)

	transport, err := svcCtx.PdCloudClient.Transport()

	assert.NoError(t, err)
	assert.True(t, transport.TLSClientConfig.InsecureSkipVerify)
}
