syntax = "proto3";

package game;
option go_package = "proto/game";

message EmptyRequest {}

message LobbyResponse { repeated uint32 game_kind = 1; }

message Uint32Value { uint32 value = 1; }

message StringValue { string value = 1; }

message BoolValue { bool value = 1; }

message RepeatedUint32Value { repeated uint32 value = 1; }

message EnableGameListRequest {
  uint32 game_kind = 1;
  Uint32Value hall_id = 2;
  StringValue lang = 3;
}

message EnableGame {
  uint32 id = 1;
  string name = 2;
}

message DemoLinkRequest {
  uint32 game_kind = 1;
  Uint32Value hall_id = 2;
  StringValue lang = 3;
}

message DemoLinkResponse { repeated DemoLinkInfo demo_link_info = 1; }

message DemoLinkInfo {
  uint32 game_id = 1;
  string url = 2;
}

message EnableGameListResponse { repeated EnableGame games = 1; }

message GameDomainRequest {
  uint32 game_kind = 1;
  uint32 hall_id = 2;
}

message GameDomainResponse { repeated string domain = 1; }

message LobbyLinkRequest {
  uint32 game_kind = 1;
  string session = 2;
  StringValue lang = 3;
  StringValue ip = 4;
  Uint32Value exit_option = 5;
  StringValue exit_url = 6;
}

message LobbyLinkResponse { string url = 1; }

message GetLobbySwitchRequest {
  uint32 agent_id = 1;
  repeated uint32 all_parents = 2;
  Uint32Value user_id = 3;
}

message LobbySwitch {
  uint32 game_kind = 1;
  bool switch = 2;
  uint32 user_id = 3;
}

message GetLobbySwitchResponse { repeated LobbySwitch lobby_switch = 1; }

message GetLobbySwitchByHallIDRequest {
  uint32 hall_id = 1;
  bool filter_lobby_enable = 2;
  BoolValue lobby_switch_enable = 3;
}

message GetLobbySwitchByHallIDResponse {
  repeated LobbySwitch lobby_switch = 1;
}

message GetCategoryRequest { uint32 game_kind = 1; }

message CategoryInfo {
  uint32 menu_id = 1;
  uint32 top_id = 2;
  uint32 depth = 3;
  uint32 sort = 4;
}

message GetCategoryResponse { repeated CategoryInfo data = 1; }

message GetMenuSortRequest { uint32 game_kind = 1; }

message MenuSortInfo {
  uint32 menu_id = 1;
  uint32 sort = 2;
  uint32 game_type = 3;
}

message GetMenuSortResponse { repeated MenuSortInfo data = 1; }

message GetGameInfoRequest {
  uint32 game_kind = 1;
  uint32 game_id = 2;
}

message GameInfo {
  uint32 lobby = 1;
  uint32 device = 2;
  uint32 game_type = 3;
  string name = 4;
  bool enable = 5;
  bool pc_enable = 6;
  bool mobile_enable = 7;
  bool demo_enable = 8;
  bool is_jackpot = 9;
  string icon_kind = 10;
  string open_date = 11;
  repeated uint32 allow_list = 12;
  repeated uint32 block_list = 13;
  string external_id = 14;
}

message ExternalList {
  string external_id = 1;
  uint32 game_type = 2;
}

message GetGameInfoResponse {
  repeated GameInfo data = 1;
  repeated ExternalList external_list = 2;
}

message GetLobbyGameEntranceSwitchRequest {
  uint32 game_kind = 1;
  uint32 hall_id = 2;
}

message LobbyGameEntranceSwitch {
  string game_type = 1;
  bool pc_enable = 2;
  bool mobile_enable = 3;
}

message GetLobbyGameEntranceSwitchResponse {
  repeated LobbyGameEntranceSwitch data = 1;
}

message BulletinRequest {
  string start_date = 1;
  string end_date = 2;
  string lang = 3;
}

message BulletinResponse { repeated BulletinData bulletin = 1; }

message BulletinData {
  string date = 1;
  string content = 2;
}

message GameKindList {
  uint32 game_kind = 1;
  string game_code = 2;
}

message GameKindListResponse { repeated GameKindList game_kind_list = 1; }

message GameIconKindInfo {
  uint32 kind = 1;
  string icon = 2;
}

message GameIconKindResponse { repeated GameIconKindInfo icon_kind = 1; }

message AddUserFavoriteRequest {
  uint32 user_id = 1;
  uint32 game_kind = 2;
  uint32 game_id = 3;
}

message EmptyResponse {}

message GetMenuNameRequest { string lang = 1; }

message MenuInfo {
  uint32 menu_id = 1;
  string menu_name = 2;
}

message GetMenuNameResponse { repeated MenuInfo menu_list = 1; }

message GetUserFavoriteRequest {
  uint64 user_id = 1;
  uint32 game_kind = 2;
}

message UserFavorite { uint32 game_type = 1; }

message GetUserFavoriteResponse { repeated UserFavorite user_favorite = 1; }

message DeleteUserFavoriteRequest {
  uint32 user_id = 1;
  uint32 game_kind = 2;
  uint32 game_id = 3;
}

message GameListRequest {
  uint32 game_kind = 1;
  string lang = 2;
}

message GameList {
  string id = 1;
  string name = 2;
}

message GameListResponse { repeated GameList data = 1; }

message GameMaintainLabelRequest { repeated uint32 game_kind = 1; }

message GameMaintainLabelResponse { repeated GameMaintainLabel data = 1; }

message GameMaintainLabel {
  uint32 game_kind = 1;
  string game_id = 2;
  uint32 label = 3;
}

message GameURLListRequest {
  uint32 game_kind = 1;
  Uint32Value hall_id = 2;
}

message GameURLListResponse { repeated GameURL data = 1; }

message GameURL {
  uint32 id = 1;
  string url = 2;
}

message LobbyCategoryRequest {
  bool bb_tip = 1;
  bool transfer_wagers_type = 2;
  BoolValue enable = 3;
  string category = 4;
  BoolValue report = 5;
  BoolValue commissionable = 6;
  BoolValue jp = 7;
  BoolValue external = 8;
  string last_bet = 9;
  string series = 10;
}

message LobbyCategoryResponse {
  repeated string live = 1;
  repeated string prob = 2;
  repeated string sport = 3;
  repeated string lottery = 4;
  repeated string card = 5;
}

message GetGameListWithSwitchRequest {
  repeated uint32 game_kind = 1;
  repeated uint32 game_id = 2;
  repeated string external_id = 3;
  string icon_kind = 4;
  StringValue commissionable_group = 5;
  string open_start_date = 6;
  string open_end_date = 7;
  Uint32Value device = 8;
  BoolValue platform_enable = 9;
  BoolValue pc_enable = 10;
  BoolValue mobile_enable = 11;
  BoolValue demo_enable = 12;
  BoolValue is_jackpot = 13;
}

message GameListWithSwitch {
  uint32 game_kind = 1;
  uint32 game_id = 2;
  string name = 3;
  uint32 device = 4;
  bool platform_enable = 5;
  bool pc_enable = 6;
  bool mobile_enable = 7;
  bool demo_enable = 8;
  bool is_jackpot = 9;
  string commissionable_group = 10;
  string open_date = 11;
  string updated_at = 12;
  string external_id = 13;
  string icon_kind = 14;
  repeated uint32 white_list = 15;
  repeated uint32 disable_entrance_hall_ids = 16;
  repeated uint32 disable_platform_hall_ids = 17;
}

message GetGameListWithSwitchResponse { repeated GameListWithSwitch data = 1; }

message GetUserLobbySwitchRequest {
  uint32 user_id = 1;
  uint32 game_kind = 2;
  repeated uint32 all_parents = 3;
  uint32 role = 4;
  BoolValue sub = 5;
}

message GetUserLobbySwitchResponse { repeated LobbySwitch lobby_switch = 1; }

message GetGameDetailRequest {
  repeated uint32 id = 1;
  repeated uint32 hall_id = 2;
}

message GetGameDetailResponse { repeated GameDetail game_detail = 1; }

message GameDetail {
  uint32 id = 1;
  uint32 game_kind = 2;
  uint32 hall_id = 3;
  bool switch = 4;
}

message CreateGameDetailRequest {
  uint32 hall_id = 1;
  uint32 game_kind = 2;
  uint32 id = 3;
  bool enable = 4;
}

message GetOnlineMemberMinRequest {
  uint32 hall_id = 1;
  string start_time = 2;
}

message GetOnlineMemberMinResponse {
  repeated OnlineMember online_member_summary = 1;
  repeated OnlineMemberIngressSummary online_member_ingress_summary = 2;
}

message OnlineMember {
  string datetime = 1;
  uint32 count = 2;
}

message OnlineMemberIngressSummary {
  uint32 ingress_id = 1;
  repeated OnlineMemberIngressInfo online_member_ingress_info = 2;
}

message OnlineMemberIngressInfo {
  string datetime = 1;
  uint32 count = 2;
  uint32 percentage = 3;
}

message CoverCondition {
  string game_type = 1;
  uint32 lobby = 2;
}

message DeleteAPISynchronizeRequest {
  string api_facade = 1;
  string function_name = 2;
  CoverCondition cover_condition = 3;
}

message ModifyGameTypeSwitchRequest {
  repeated GameTypeInfo game_type_info = 1;
}

message GameTypeInfo {
  uint32 game_kind = 1;
  string game_id = 2;
  uint32 platform_enable = 3;
  uint32 pc_enable = 4;
  uint32 mobile_enable = 5;
  uint32 label = 6;
}

message DeleteGameTypeSynchronizeRequest {
  repeated uint32 hall_id = 1;
  repeated string game_type = 2;
  string sync_function = 3;
}

message ManageGameHallSwitchRequest { repeated GameHallSwitch data = 1; }

message GameHallSwitch {
  uint32 game_kind = 1;
  uint32 hall_id = 2;
  string game_id = 3;
  bool platform_enable = 4;
  bool entrance_enable = 5;
  bool not_support = 6;
}

message UpdateGameInfoHallListRequest {
  uint32 game_kind = 1;
  uint32 game_id = 2;
  RepeatedUint32Value allow_list = 3;
  RepeatedUint32Value block_list = 4;
}

message DeleteUserLobbyRequest { uint32 user_id = 1; }

message GetOnlineMemberHourRequest {
  uint32 hall_id = 1;
  string start_time = 2;
}

message GetOnlineMemberHourResponse {
  repeated OnlineMember online_member_summary = 1;
  repeated OnlineMemberIngressSummary online_member_ingress_summary = 2;
}

message UpdateHallLobbySwitchRequest {
  uint32 hall_id = 1;
  uint32 game_kind = 2;
  bool enable = 3;
}

message DeleteHallLowerAccountLobbySwitchRequest {
  uint32 hall_id = 1;
  uint32 game_kind = 2;
}

message CreateUserLobbySwitchRequest {
  repeated UserLobbySwitch user_lobby_switch = 1;
}

message UserLobbySwitch {
  uint32 hall_id = 1;
  uint32 user_id = 2;
  uint32 game_kind = 3;
  bool enable = 4;
}

message GetOnlineMemberDayRequest {
  uint32 hall_id = 1;
  string start_time = 2;
  string end_time = 3;
}

message GetOnlineMemberDayResponse {
  repeated OnlineMember online_member_summary = 1;
  repeated OnlineMemberIngressSummary online_member_ingress_summary = 2;
}

message DeleteHallLobbyCloseTimeRequest {
  uint32 hall_id = 1;
  uint32 game_kind = 2;
}

message SetHallLobbyCloseTimeRequest {
  uint32 hall_id = 1;
  uint32 game_kind = 2;
}

service Game {
  rpc Lobby(EmptyRequest) returns (LobbyResponse);
  rpc EnableGameList(EnableGameListRequest) returns (EnableGameListResponse);
  rpc DemoLink(DemoLinkRequest) returns (DemoLinkResponse);
  rpc GameDomain(GameDomainRequest) returns (GameDomainResponse);
  rpc LobbyLink(LobbyLinkRequest) returns (LobbyLinkResponse);
  rpc GetLobbySwitch(GetLobbySwitchRequest) returns (GetLobbySwitchResponse);
  rpc GetLobbySwitchByHallID(GetLobbySwitchByHallIDRequest)
      returns (GetLobbySwitchByHallIDResponse);
  rpc GetCategory(GetCategoryRequest) returns (GetCategoryResponse);
  rpc GetMenuSort(GetMenuSortRequest) returns (GetMenuSortResponse);
  rpc GetGameInfo(GetGameInfoRequest) returns (GetGameInfoResponse);
  rpc GetLobbyGameEntranceSwitch(GetLobbyGameEntranceSwitchRequest)
      returns (GetLobbyGameEntranceSwitchResponse);
  rpc BulletinList(BulletinRequest) returns (BulletinResponse);
  rpc GameKindList(EmptyRequest) returns (GameKindListResponse);
  rpc GameIconKind(EmptyRequest) returns (GameIconKindResponse);
  rpc AddUserFavorite(AddUserFavoriteRequest) returns (EmptyResponse);
  rpc GetMenuName(GetMenuNameRequest) returns (GetMenuNameResponse);
  rpc GetUserFavorite(GetUserFavoriteRequest) returns (GetUserFavoriteResponse);
  rpc DeleteUserFavorite(DeleteUserFavoriteRequest) returns (EmptyResponse);
  rpc GameList(GameListRequest) returns (GameListResponse);
  rpc GetGameMaintainLabel(GameMaintainLabelRequest)
      returns (GameMaintainLabelResponse);
  rpc GetGameURLList(GameURLListRequest) returns (GameURLListResponse);
  rpc LobbyCategory(LobbyCategoryRequest) returns (LobbyCategoryResponse);
  rpc GetGameListWithSwitch(GetGameListWithSwitchRequest)
      returns (GetGameListWithSwitchResponse);
  rpc GetUserLobbySwitch(GetUserLobbySwitchRequest)
      returns (GetUserLobbySwitchResponse);
  rpc GetGameDetail(GetGameDetailRequest) returns (GetGameDetailResponse);
  rpc CreateGameDetail(CreateGameDetailRequest) returns (EmptyResponse);
  rpc GetOnlineMemberMin(GetOnlineMemberMinRequest)
      returns (GetOnlineMemberMinResponse);
  rpc DeleteAPISynchronize(DeleteAPISynchronizeRequest) returns (EmptyResponse);
  rpc ModifyGameTypeSwitch(ModifyGameTypeSwitchRequest) returns (EmptyResponse);
  rpc DeleteGameTypeSynchronize(DeleteGameTypeSynchronizeRequest)
      returns (EmptyResponse);
  rpc ManageGameHallSwitch(ManageGameHallSwitchRequest) returns (EmptyResponse);
  rpc UpdateGameInfoHallList(UpdateGameInfoHallListRequest)
      returns (EmptyResponse);
  rpc DeleteUserLobby(DeleteUserLobbyRequest) returns (EmptyResponse);
  rpc GetOnlineMemberHour(GetOnlineMemberHourRequest)
      returns (GetOnlineMemberHourResponse);
  rpc UpdateHallLobbySwitch(UpdateHallLobbySwitchRequest)
      returns (EmptyResponse);
  rpc DeleteHallLowerAccountLobbySwitch(
      DeleteHallLowerAccountLobbySwitchRequest) returns (EmptyResponse);
  rpc CreateUserLobbySwitch(CreateUserLobbySwitchRequest)
      returns (EmptyResponse);
  rpc GetOnlineMemberDay(GetOnlineMemberDayRequest)
      returns (GetOnlineMemberDayResponse);
  rpc DeleteHallLobbyCloseTime(DeleteHallLobbyCloseTimeRequest)
      returns (EmptyResponse);
  rpc SetHallLobbyCloseTime(SetHallLobbyCloseTimeRequest)
      returns (EmptyResponse);
}
