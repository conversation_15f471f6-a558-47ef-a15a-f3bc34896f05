package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/game/internal/config"
	"gbh/game/internal/server"
	"gbh/game/internal/svc"
	"gbh/logger"
	"gbh/proto/game"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/game.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatalln(logErr)
	}

	cloudSettingDB, err := database.New(c.CloudSettingDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	csMemDB, err := database.New(c.CsMemDB, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	gameDB, err := database.New(c.GameDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	gameSetDB, err := database.New(c.GameSetDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	userDB, err := database.New(c.UserDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	domainDB, err := database.New(c.DomainDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	scheduleDB, err := database.New(c.ScheduleDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		CloudSettingDB: cloudSettingDB,
		CsMemDB:        csMemDB,
		GameDB:         gameDB,
		GameSetDB:      gameSetDB,
		UserDB:         userDB,
		DomainDB:       domainDB,
		ScheduleDB:     scheduleDB,
	}

	ctx := svc.NewServiceContext(c, customLogger, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		game.RegisterGameServer(grpcServer, server.NewGameServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "game",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
