// Code generated by goctl. DO NOT EDIT.
// Source: game.proto

package gameclient

import (
	"context"

	"gbh/proto/game"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AddUserFavoriteRequest                   = game.AddUserFavoriteRequest
	BoolValue                                = game.BoolValue
	BulletinData                             = game.BulletinData
	BulletinRequest                          = game.BulletinRequest
	BulletinResponse                         = game.BulletinResponse
	CategoryInfo                             = game.CategoryInfo
	CoverCondition                           = game.CoverCondition
	CreateGameDetailRequest                  = game.CreateGameDetailRequest
	CreateUserLobbySwitchRequest             = game.CreateUserLobbySwitchRequest
	DeleteAPISynchronizeRequest              = game.DeleteAPISynchronizeRequest
	DeleteGameTypeSynchronizeRequest         = game.DeleteGameTypeSynchronizeRequest
	DeleteHallLobbyCloseTimeRequest          = game.DeleteHallLobbyCloseTimeRequest
	DeleteHallLowerAccountLobbySwitchRequest = game.DeleteHallLowerAccountLobbySwitchRequest
	DeleteUserFavoriteRequest                = game.DeleteUserFavoriteRequest
	DeleteUserLobbyRequest                   = game.DeleteUserLobbyRequest
	DemoLinkInfo                             = game.DemoLinkInfo
	DemoLinkRequest                          = game.DemoLinkRequest
	DemoLinkResponse                         = game.DemoLinkResponse
	EmptyRequest                             = game.EmptyRequest
	EmptyResponse                            = game.EmptyResponse
	EnableGame                               = game.EnableGame
	EnableGameListRequest                    = game.EnableGameListRequest
	EnableGameListResponse                   = game.EnableGameListResponse
	ExternalList                             = game.ExternalList
	GameDetail                               = game.GameDetail
	GameDomainRequest                        = game.GameDomainRequest
	GameDomainResponse                       = game.GameDomainResponse
	GameHallSwitch                           = game.GameHallSwitch
	GameIconKindInfo                         = game.GameIconKindInfo
	GameIconKindResponse                     = game.GameIconKindResponse
	GameInfo                                 = game.GameInfo
	GameKindList                             = game.GameKindList
	GameKindListResponse                     = game.GameKindListResponse
	GameList                                 = game.GameList
	GameListRequest                          = game.GameListRequest
	GameListResponse                         = game.GameListResponse
	GameListWithSwitch                       = game.GameListWithSwitch
	GameMaintainLabel                        = game.GameMaintainLabel
	GameMaintainLabelRequest                 = game.GameMaintainLabelRequest
	GameMaintainLabelResponse                = game.GameMaintainLabelResponse
	GameTypeInfo                             = game.GameTypeInfo
	GameURL                                  = game.GameURL
	GameURLListRequest                       = game.GameURLListRequest
	GameURLListResponse                      = game.GameURLListResponse
	GetCategoryRequest                       = game.GetCategoryRequest
	GetCategoryResponse                      = game.GetCategoryResponse
	GetGameDetailRequest                     = game.GetGameDetailRequest
	GetGameDetailResponse                    = game.GetGameDetailResponse
	GetGameInfoRequest                       = game.GetGameInfoRequest
	GetGameInfoResponse                      = game.GetGameInfoResponse
	GetGameListWithSwitchRequest             = game.GetGameListWithSwitchRequest
	GetGameListWithSwitchResponse            = game.GetGameListWithSwitchResponse
	GetLobbyGameEntranceSwitchRequest        = game.GetLobbyGameEntranceSwitchRequest
	GetLobbyGameEntranceSwitchResponse       = game.GetLobbyGameEntranceSwitchResponse
	GetLobbySwitchByHallIDRequest            = game.GetLobbySwitchByHallIDRequest
	GetLobbySwitchByHallIDResponse           = game.GetLobbySwitchByHallIDResponse
	GetLobbySwitchRequest                    = game.GetLobbySwitchRequest
	GetLobbySwitchResponse                   = game.GetLobbySwitchResponse
	GetMenuNameRequest                       = game.GetMenuNameRequest
	GetMenuNameResponse                      = game.GetMenuNameResponse
	GetMenuSortRequest                       = game.GetMenuSortRequest
	GetMenuSortResponse                      = game.GetMenuSortResponse
	GetOnlineMemberDayRequest                = game.GetOnlineMemberDayRequest
	GetOnlineMemberDayResponse               = game.GetOnlineMemberDayResponse
	GetOnlineMemberHourRequest               = game.GetOnlineMemberHourRequest
	GetOnlineMemberHourResponse              = game.GetOnlineMemberHourResponse
	GetOnlineMemberMinRequest                = game.GetOnlineMemberMinRequest
	GetOnlineMemberMinResponse               = game.GetOnlineMemberMinResponse
	GetUserFavoriteRequest                   = game.GetUserFavoriteRequest
	GetUserFavoriteResponse                  = game.GetUserFavoriteResponse
	GetUserLobbySwitchRequest                = game.GetUserLobbySwitchRequest
	GetUserLobbySwitchResponse               = game.GetUserLobbySwitchResponse
	LobbyCategoryRequest                     = game.LobbyCategoryRequest
	LobbyCategoryResponse                    = game.LobbyCategoryResponse
	LobbyGameEntranceSwitch                  = game.LobbyGameEntranceSwitch
	LobbyLinkRequest                         = game.LobbyLinkRequest
	LobbyLinkResponse                        = game.LobbyLinkResponse
	LobbyResponse                            = game.LobbyResponse
	LobbySwitch                              = game.LobbySwitch
	ManageGameHallSwitchRequest              = game.ManageGameHallSwitchRequest
	MenuInfo                                 = game.MenuInfo
	MenuSortInfo                             = game.MenuSortInfo
	ModifyGameTypeSwitchRequest              = game.ModifyGameTypeSwitchRequest
	OnlineMember                             = game.OnlineMember
	OnlineMemberIngressInfo                  = game.OnlineMemberIngressInfo
	OnlineMemberIngressSummary               = game.OnlineMemberIngressSummary
	RepeatedUint32Value                      = game.RepeatedUint32Value
	SetHallLobbyCloseTimeRequest             = game.SetHallLobbyCloseTimeRequest
	StringValue                              = game.StringValue
	Uint32Value                              = game.Uint32Value
	UpdateGameInfoHallListRequest            = game.UpdateGameInfoHallListRequest
	UpdateHallLobbySwitchRequest             = game.UpdateHallLobbySwitchRequest
	UserFavorite                             = game.UserFavorite
	UserLobbySwitch                          = game.UserLobbySwitch

	Game interface {
		Lobby(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*LobbyResponse, error)
		EnableGameList(ctx context.Context, in *EnableGameListRequest, opts ...grpc.CallOption) (*EnableGameListResponse, error)
		DemoLink(ctx context.Context, in *DemoLinkRequest, opts ...grpc.CallOption) (*DemoLinkResponse, error)
		GameDomain(ctx context.Context, in *GameDomainRequest, opts ...grpc.CallOption) (*GameDomainResponse, error)
		LobbyLink(ctx context.Context, in *LobbyLinkRequest, opts ...grpc.CallOption) (*LobbyLinkResponse, error)
		GetLobbySwitch(ctx context.Context, in *GetLobbySwitchRequest, opts ...grpc.CallOption) (*GetLobbySwitchResponse, error)
		GetLobbySwitchByHallID(ctx context.Context, in *GetLobbySwitchByHallIDRequest, opts ...grpc.CallOption) (*GetLobbySwitchByHallIDResponse, error)
		GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error)
		GetMenuSort(ctx context.Context, in *GetMenuSortRequest, opts ...grpc.CallOption) (*GetMenuSortResponse, error)
		GetGameInfo(ctx context.Context, in *GetGameInfoRequest, opts ...grpc.CallOption) (*GetGameInfoResponse, error)
		GetLobbyGameEntranceSwitch(ctx context.Context, in *GetLobbyGameEntranceSwitchRequest, opts ...grpc.CallOption) (*GetLobbyGameEntranceSwitchResponse, error)
		BulletinList(ctx context.Context, in *BulletinRequest, opts ...grpc.CallOption) (*BulletinResponse, error)
		GameKindList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameKindListResponse, error)
		GameIconKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameIconKindResponse, error)
		AddUserFavorite(ctx context.Context, in *AddUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetMenuName(ctx context.Context, in *GetMenuNameRequest, opts ...grpc.CallOption) (*GetMenuNameResponse, error)
		GetUserFavorite(ctx context.Context, in *GetUserFavoriteRequest, opts ...grpc.CallOption) (*GetUserFavoriteResponse, error)
		DeleteUserFavorite(ctx context.Context, in *DeleteUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error)
		GetGameMaintainLabel(ctx context.Context, in *GameMaintainLabelRequest, opts ...grpc.CallOption) (*GameMaintainLabelResponse, error)
		GetGameURLList(ctx context.Context, in *GameURLListRequest, opts ...grpc.CallOption) (*GameURLListResponse, error)
		LobbyCategory(ctx context.Context, in *LobbyCategoryRequest, opts ...grpc.CallOption) (*LobbyCategoryResponse, error)
		GetGameListWithSwitch(ctx context.Context, in *GetGameListWithSwitchRequest, opts ...grpc.CallOption) (*GetGameListWithSwitchResponse, error)
		GetUserLobbySwitch(ctx context.Context, in *GetUserLobbySwitchRequest, opts ...grpc.CallOption) (*GetUserLobbySwitchResponse, error)
		GetGameDetail(ctx context.Context, in *GetGameDetailRequest, opts ...grpc.CallOption) (*GetGameDetailResponse, error)
		CreateGameDetail(ctx context.Context, in *CreateGameDetailRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetOnlineMemberMin(ctx context.Context, in *GetOnlineMemberMinRequest, opts ...grpc.CallOption) (*GetOnlineMemberMinResponse, error)
		DeleteAPISynchronize(ctx context.Context, in *DeleteAPISynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		ModifyGameTypeSwitch(ctx context.Context, in *ModifyGameTypeSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		DeleteGameTypeSynchronize(ctx context.Context, in *DeleteGameTypeSynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		ManageGameHallSwitch(ctx context.Context, in *ManageGameHallSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		UpdateGameInfoHallList(ctx context.Context, in *UpdateGameInfoHallListRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		DeleteUserLobby(ctx context.Context, in *DeleteUserLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetOnlineMemberHour(ctx context.Context, in *GetOnlineMemberHourRequest, opts ...grpc.CallOption) (*GetOnlineMemberHourResponse, error)
		UpdateHallLobbySwitch(ctx context.Context, in *UpdateHallLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		DeleteHallLowerAccountLobbySwitch(ctx context.Context, in *DeleteHallLowerAccountLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		CreateUserLobbySwitch(ctx context.Context, in *CreateUserLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetOnlineMemberDay(ctx context.Context, in *GetOnlineMemberDayRequest, opts ...grpc.CallOption) (*GetOnlineMemberDayResponse, error)
		DeleteHallLobbyCloseTime(ctx context.Context, in *DeleteHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetHallLobbyCloseTime(ctx context.Context, in *SetHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	}

	defaultGame struct {
		cli zrpc.Client
	}
)

func NewGame(cli zrpc.Client) Game {
	return &defaultGame{
		cli: cli,
	}
}

func (m *defaultGame) Lobby(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*LobbyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.Lobby(ctx, in, opts...)
}

func (m *defaultGame) EnableGameList(ctx context.Context, in *EnableGameListRequest, opts ...grpc.CallOption) (*EnableGameListResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.EnableGameList(ctx, in, opts...)
}

func (m *defaultGame) DemoLink(ctx context.Context, in *DemoLinkRequest, opts ...grpc.CallOption) (*DemoLinkResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.DemoLink(ctx, in, opts...)
}

func (m *defaultGame) GameDomain(ctx context.Context, in *GameDomainRequest, opts ...grpc.CallOption) (*GameDomainResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GameDomain(ctx, in, opts...)
}

func (m *defaultGame) LobbyLink(ctx context.Context, in *LobbyLinkRequest, opts ...grpc.CallOption) (*LobbyLinkResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.LobbyLink(ctx, in, opts...)
}

func (m *defaultGame) GetLobbySwitch(ctx context.Context, in *GetLobbySwitchRequest, opts ...grpc.CallOption) (*GetLobbySwitchResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetLobbySwitch(ctx, in, opts...)
}

func (m *defaultGame) GetLobbySwitchByHallID(ctx context.Context, in *GetLobbySwitchByHallIDRequest, opts ...grpc.CallOption) (*GetLobbySwitchByHallIDResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetLobbySwitchByHallID(ctx, in, opts...)
}

func (m *defaultGame) GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetCategory(ctx, in, opts...)
}

func (m *defaultGame) GetMenuSort(ctx context.Context, in *GetMenuSortRequest, opts ...grpc.CallOption) (*GetMenuSortResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetMenuSort(ctx, in, opts...)
}

func (m *defaultGame) GetGameInfo(ctx context.Context, in *GetGameInfoRequest, opts ...grpc.CallOption) (*GetGameInfoResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetGameInfo(ctx, in, opts...)
}

func (m *defaultGame) GetLobbyGameEntranceSwitch(ctx context.Context, in *GetLobbyGameEntranceSwitchRequest, opts ...grpc.CallOption) (*GetLobbyGameEntranceSwitchResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetLobbyGameEntranceSwitch(ctx, in, opts...)
}

func (m *defaultGame) BulletinList(ctx context.Context, in *BulletinRequest, opts ...grpc.CallOption) (*BulletinResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.BulletinList(ctx, in, opts...)
}

func (m *defaultGame) GameKindList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameKindListResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GameKindList(ctx, in, opts...)
}

func (m *defaultGame) GameIconKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameIconKindResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GameIconKind(ctx, in, opts...)
}

func (m *defaultGame) AddUserFavorite(ctx context.Context, in *AddUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.AddUserFavorite(ctx, in, opts...)
}

func (m *defaultGame) GetMenuName(ctx context.Context, in *GetMenuNameRequest, opts ...grpc.CallOption) (*GetMenuNameResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetMenuName(ctx, in, opts...)
}

func (m *defaultGame) GetUserFavorite(ctx context.Context, in *GetUserFavoriteRequest, opts ...grpc.CallOption) (*GetUserFavoriteResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetUserFavorite(ctx, in, opts...)
}

func (m *defaultGame) DeleteUserFavorite(ctx context.Context, in *DeleteUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.DeleteUserFavorite(ctx, in, opts...)
}

func (m *defaultGame) GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GameList(ctx, in, opts...)
}

func (m *defaultGame) GetGameMaintainLabel(ctx context.Context, in *GameMaintainLabelRequest, opts ...grpc.CallOption) (*GameMaintainLabelResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetGameMaintainLabel(ctx, in, opts...)
}

func (m *defaultGame) GetGameURLList(ctx context.Context, in *GameURLListRequest, opts ...grpc.CallOption) (*GameURLListResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetGameURLList(ctx, in, opts...)
}

func (m *defaultGame) LobbyCategory(ctx context.Context, in *LobbyCategoryRequest, opts ...grpc.CallOption) (*LobbyCategoryResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.LobbyCategory(ctx, in, opts...)
}

func (m *defaultGame) GetGameListWithSwitch(ctx context.Context, in *GetGameListWithSwitchRequest, opts ...grpc.CallOption) (*GetGameListWithSwitchResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetGameListWithSwitch(ctx, in, opts...)
}

func (m *defaultGame) GetUserLobbySwitch(ctx context.Context, in *GetUserLobbySwitchRequest, opts ...grpc.CallOption) (*GetUserLobbySwitchResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetUserLobbySwitch(ctx, in, opts...)
}

func (m *defaultGame) GetGameDetail(ctx context.Context, in *GetGameDetailRequest, opts ...grpc.CallOption) (*GetGameDetailResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetGameDetail(ctx, in, opts...)
}

func (m *defaultGame) CreateGameDetail(ctx context.Context, in *CreateGameDetailRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.CreateGameDetail(ctx, in, opts...)
}

func (m *defaultGame) GetOnlineMemberMin(ctx context.Context, in *GetOnlineMemberMinRequest, opts ...grpc.CallOption) (*GetOnlineMemberMinResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetOnlineMemberMin(ctx, in, opts...)
}

func (m *defaultGame) DeleteAPISynchronize(ctx context.Context, in *DeleteAPISynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.DeleteAPISynchronize(ctx, in, opts...)
}

func (m *defaultGame) ModifyGameTypeSwitch(ctx context.Context, in *ModifyGameTypeSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.ModifyGameTypeSwitch(ctx, in, opts...)
}

func (m *defaultGame) DeleteGameTypeSynchronize(ctx context.Context, in *DeleteGameTypeSynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.DeleteGameTypeSynchronize(ctx, in, opts...)
}

func (m *defaultGame) ManageGameHallSwitch(ctx context.Context, in *ManageGameHallSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.ManageGameHallSwitch(ctx, in, opts...)
}

func (m *defaultGame) UpdateGameInfoHallList(ctx context.Context, in *UpdateGameInfoHallListRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.UpdateGameInfoHallList(ctx, in, opts...)
}

func (m *defaultGame) DeleteUserLobby(ctx context.Context, in *DeleteUserLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.DeleteUserLobby(ctx, in, opts...)
}

func (m *defaultGame) GetOnlineMemberHour(ctx context.Context, in *GetOnlineMemberHourRequest, opts ...grpc.CallOption) (*GetOnlineMemberHourResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetOnlineMemberHour(ctx, in, opts...)
}

func (m *defaultGame) UpdateHallLobbySwitch(ctx context.Context, in *UpdateHallLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.UpdateHallLobbySwitch(ctx, in, opts...)
}

func (m *defaultGame) DeleteHallLowerAccountLobbySwitch(ctx context.Context, in *DeleteHallLowerAccountLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.DeleteHallLowerAccountLobbySwitch(ctx, in, opts...)
}

func (m *defaultGame) CreateUserLobbySwitch(ctx context.Context, in *CreateUserLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.CreateUserLobbySwitch(ctx, in, opts...)
}

func (m *defaultGame) GetOnlineMemberDay(ctx context.Context, in *GetOnlineMemberDayRequest, opts ...grpc.CallOption) (*GetOnlineMemberDayResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.GetOnlineMemberDay(ctx, in, opts...)
}

func (m *defaultGame) DeleteHallLobbyCloseTime(ctx context.Context, in *DeleteHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.DeleteHallLobbyCloseTime(ctx, in, opts...)
}

func (m *defaultGame) SetHallLobbyCloseTime(ctx context.Context, in *SetHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := game.NewGameClient(m.cli.Conn())
	return client.SetHallLobbyCloseTime(ctx, in, opts...)
}
