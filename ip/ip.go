package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/ip/internal/config"
	"gbh/ip/internal/server"
	"gbh/ip/internal/svc"
	"gbh/logger"
	"gbh/proto/ip"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/ip.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	systemDB, err := database.New(c.<PERSON>, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		SystemDB: systemDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		ip.RegisterIPServer(grpcServer, server.NewIPServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "ip",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
