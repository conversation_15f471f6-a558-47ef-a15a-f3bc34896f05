package svc

import (
	"crypto/tls"
	"fmt"
	"gbh/hall/internal/config"
	"gbh/jackpot/constants"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config       config.Config
	AccClient    *resty.Client
	APIServiceDB *gorm.DB
	SecClient    *resty.Client
	DomainDB     *gorm.DB
	UserDB       *gorm.DB
}

func NewServiceContext(c config.Config, extSvc ExternalContext) *ServiceContext {
	accClient := resty.New()
	baseURL := fmt.Sprintf("%s://%s:%d", c.ACCConf.Schema, c.ACCConf.IP, c.ACCConf.Port)
	accClient.SetBaseURL(baseURL)
	accClient.SetTimeout(constants.APITimeout)
	accClient.SetHeader("Host", c.ACCConf.Host)

	if c.ACCConf.Schema == "https" {
		accClient.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}

	secClient := resty.New()
	secBaseURL := fmt.Sprintf("%s://%s", c.SECConf.Schema, c.SECConf.Host)

	secClient.SetBaseURL(secBaseURL)
	secClient.SetTimeout(constants.APITimeout)
	secClient.SetHeader("Content-Type", "application/json")

	if c.SECConf.Schema == "https" {
		secClient.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}

	return &ServiceContext{
		Config:       c,
		AccClient:    accClient,
		APIServiceDB: extSvc.APIServiceDB,
		SecClient:    secClient,
		DomainDB:     extSvc.DomainDB,
		UserDB:       extSvc.UserDB,
	}
}
