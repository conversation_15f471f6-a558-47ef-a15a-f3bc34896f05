package config

import (
	"gbh/database"
	"gbh/logger"

	"github.com/zeromicro/go-zero/zrpc"
)

type ACCConf struct {
	Schema string
	Host   string
	IP     string
	Port   int
}

type SECConf struct {
	Schema string
	Host   string
}

type Config struct {
	zrpc.RpcServerConf
	ACCConf          ACCConf
	APIServiceDBConf database.Conf
	LogConf          logger.Conf
	SECConf          SECConf
	DomainDBConf     database.Conf
	UserDBConf       database.Conf
}
