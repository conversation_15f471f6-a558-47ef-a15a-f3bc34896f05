package logic

import (
	"gbh/errorx"
	"gbh/proto/hall"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestGetPermissionByDefaultIdLogic(t *testing.T) {
	request := &hall.GetPermissionByDefaultIdRequest{
		DefaultId: 1,
	}

	userMock.ExpectQuery("SELECT * FROM `DomainDefaultPerm` WHERE default_id = ?").
		WithArgs(request.GetDefaultId()).
		WillReturnRows(sqlmock.NewRows([]string{"default_id", "perm_id", "role_id", "modify"}).
			AddRow(1, 1, 1, 1).
			AddRow(1, 2, 2, 0))

	l := NewGetPermissionByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetPermissionByDefaultId(request)

	expectResp := &hall.GetPermissionByDefaultIdResponse{
		DomainDefaultPerm: []*hall.GetDomainDefaultPerm{
			{
				PermId: 1,
				RoleId: 1,
				Modify: true,
			},
			{
				PermId: 2,
				RoleId: 2,
				Modify: false,
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectResp, resp)
	assert.NoError(t, userMock.ExpectationsWereMet())
}

func TestGetPermissionByDefaultIdLogic_DatabaseError(t *testing.T) {
	request := &hall.GetPermissionByDefaultIdRequest{
		DefaultId: 1,
	}

	l := NewGetPermissionByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetPermissionByDefaultId(request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, userMock.ExpectationsWereMet())
}
