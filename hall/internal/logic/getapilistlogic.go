package logic

import (
	"context"

	"gbh/errorx"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAPIListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

type APIListSchema struct {
	HallID  uint32 `gorm:"column:HallID"`
	Type    uint32 `gorm:"column:Type"`
	KeyA    uint32 `gorm:"column:KeyA"`
	KeyB    string `gorm:"column:KeyB"`
	KeyC    uint32 `gorm:"column:KeyC"`
	Content string `gorm:"column:content"`
	Name    string `gorm:"column:name"`
	OrderBy uint32 `gorm:"column:order_by"`
}

func NewGetAPIListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAPIListLogic {
	return &GetAPIListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetAPIListLogic) GetAPIList(in *hall.GetAPIListRequest) (*hall.GetAPIListResponse, error) {
	selectFields := []string{
		"hall_sets.HallID",
		"hall_sets.Type",
		"hall_sets.KeyA",
		"hall_sets.KeyB",
		"hall_sets.KeyC",
		"api_list.content",
		"api_list.name",
		"api_list.order_by",
	}

	query := l.svcCtx.APIServiceDB.Table("hall_sets").
		Joins("LEFT JOIN api_list ON hall_sets.Type = api_list.id").
		Select(selectFields).
		Where("HallID = ?", in.GetHallId()).
		Where("enable = 1").
		Order("api_list.order_by")

	if in.GetName() != "" {
		query.Where("api_list.name = ?", in.GetName())
	}

	apiList := make([]APIListSchema, 0)
	query.Find(&apiList)

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	result := make([]*hall.APIListData, 0, len(apiList))

	for _, v := range apiList {
		result = append(result, &hall.APIListData{
			HallId:  v.HallID,
			Type:    v.Type,
			KeyA:    v.KeyA,
			KeyB:    v.KeyB,
			KeyC:    v.KeyC,
			Content: v.Content,
			Name:    v.Name,
			OrderBy: v.OrderBy,
		})
	}

	return &hall.GetAPIListResponse{
		ApiList: result,
	}, nil
}
