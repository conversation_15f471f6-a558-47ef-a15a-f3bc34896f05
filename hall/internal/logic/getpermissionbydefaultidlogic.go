package logic

import (
	"context"

	"gbh/errorx"
	"gbh/hall/internal/schema"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPermissionByDefaultIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetPermissionByDefaultIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPermissionByDefaultIdLogic {
	return &GetPermissionByDefaultIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetPermissionByDefaultIdLogic) GetPermissionByDefaultId(in *hall.GetPermissionByDefaultIdRequest) (*hall.GetPermissionByDefaultIdResponse, error) {
	defaultPerm := make([]schema.DomainDefaultPerm, 0)

	err := l.svcCtx.UserDB.Table("DomainDefaultPerm").
		Where("default_id = ?", in.GetDefaultId()).
		Find(&defaultPerm).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	permList := []*hall.GetDomainDefaultPerm{}
	for _, v := range defaultPerm {
		permList = append(permList, &hall.GetDomainDefaultPerm{
			PermId: v.PermID,
			RoleId: v.RoleID,
			Modify: v.Modify == 1,
		})
	}

	return &hall.GetPermissionByDefaultIdResponse{
		DomainDefaultPerm: permList,
	}, nil
}
