package logic

import (
	"context"

	"gbh/errorx"
	"gbh/hall/internal/schema"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCurrencyByDefaultIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCurrencyByDefaultIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCurrencyByDefaultIdLogic {
	return &GetCurrencyByDefaultIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetCurrencyByDefaultIdLogic) GetCurrencyByDefaultId(in *hall.GetCurrencyByDefaultIdRequest) (*hall.GetCurrencyByDefaultIdResponse, error) {
	defaultCurrency := make([]schema.DomainDefaultCurrency, 0)

	err := l.svcCtx.UserDB.Table("DomainDefaultCurrency").
		Where("default_id = ?", in.GetDefaultId()).
		Find(&defaultCurrency).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	currencyList := []*hall.GetDomainDefaultCurrency{}
	for _, currency := range defaultCurrency {
		currencyList = append(currencyList, &hall.GetDomainDefaultCurrency{
			Currency: currency.Currency,
			Preset:   currency.Preset == 1,
		})
	}

	return &hall.GetCurrencyByDefaultIdResponse{
		DomainDefaultCurrency: currencyList,
	}, nil
}
