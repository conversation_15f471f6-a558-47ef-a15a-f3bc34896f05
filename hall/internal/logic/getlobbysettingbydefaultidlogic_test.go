package logic

import (
	"gbh/errorx"
	"gbh/proto/hall"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetLobbySettingByDefaultIdLogic(t *testing.T) {
	request := hall.GetLobbySettingByDefaultIdRequest{
		DefaultId: 1,
	}

	userMock.ExpectQuery("SELECT * FROM `DomainDefaultLobbySetting` WHERE default_id = ?").
		WithArgs(request.GetDefaultId()).
		WillReturnRows(sqlmock.NewRows([]string{"default_id", "lobby"}).
			AddRow(1, 3).
			AddRow(1, 5))

	l := NewGetLobbySettingByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetLobbySettingByDefaultId(&request)

	expectResp := &hall.GetLobbySettingByDefaultIdResponse{
		GameKind: []uint32{3, 5},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectResp, resp)
	assert.NoError(t, userMock.ExpectationsWereMet())
}

func TestGetLobbySettingByDefaultIdLogic_DatabaseError(t *testing.T) {
	request := hall.GetLobbySettingByDefaultIdRequest{
		DefaultId: 1,
	}

	userMock.ExpectQuery("SELECT * FROM `DomainDefaultLobbySetting` WHERE default_id = ?").
		WithArgs(request.GetDefaultId()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetLobbySettingByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetLobbySettingByDefaultId(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, userMock.ExpectationsWereMet())
}
