package logic

import (
	"gbh/errorx"
	"gbh/proto/hall"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetCurrencyByDefaultIdLogic(t *testing.T) {
	request := hall.GetCurrencyByDefaultIdRequest{
		DefaultId: 1,
	}

	userMock.ExpectQuery("SELECT * FROM `DomainDefaultCurrency` WHERE default_id = ?").
		WithArgs(request.GetDefaultId()).
		WillReturnRows(sqlmock.NewRows([]string{"default_id", "currency", "preset"}).
			AddRow(1, "CNY", 1).
			AddRow(1, "EUR", 0))

	l := NewGetCurrencyByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyByDefaultId(&request)

	expectedResp := &hall.GetCurrencyByDefaultIdResponse{
		DomainDefaultCurrency: []*hall.GetDomainDefaultCurrency{
			{
				Currency: "CNY",
				Preset:   true,
			},
			{
				Currency: "EUR",
				Preset:   false,
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
	assert.NoError(t, userMock.ExpectationsWereMet())
}

func TestGetCurrencyByDefaultIdLogic_DatabaseError(t *testing.T) {
	request := hall.GetCurrencyByDefaultIdRequest{
		DefaultId: 1,
	}

	userMock.ExpectQuery("SELECT * FROM `DomainDefaultCurrency` WHERE default_id = ?").
		WithArgs(request.GetDefaultId()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetCurrencyByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyByDefaultId(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, userMock.ExpectationsWereMet())
}
