package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"gbh/errorx"
	"gbh/hall/constants"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"
	"gbh/utils/urlutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetGameSwitchInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetGameSwitchInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetGameSwitchInfoLogic {
	return &SetGameSwitchInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SetGameSwitchInfoLogic) SetGameSwitchInfo(in *hall.SetGameSwitchInfoRequest) (*hall.EmptyResponse, error) {
	uri := fmt.Sprintf(constants.ACCSetGameSwitchInfoAPI, in.GetHallId())

	params := urlutil.NewBuilder()
	params.AddUint32("game_code", in.GetGameKind())
	params.AddBoolToInt("switch", in.GetEnable())

	resp, err := l.svcCtx.AccClient.R().
		SetHeader("Content-Type", "application/x-www-form-urlencoded").
		SetFormDataFromValues(params.Values()).
		Put(uri)

	if err != nil {
		return nil, errorx.ConnectionFailed
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, errorx.InvalidResponse
	}

	var getResponse struct {
		Result string `json:"result"`
	}

	jsonParseErr := json.Unmarshal(resp.Body(), &getResponse)

	if jsonParseErr != nil {
		return nil, errorx.JSONParseFailed
	}

	if getResponse.Result != "ok" {
		return nil, errorx.InvalidResponse
	}

	return &hall.EmptyResponse{}, nil
}
