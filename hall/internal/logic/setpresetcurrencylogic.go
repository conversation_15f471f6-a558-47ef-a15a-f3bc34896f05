package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"gbh/errorx"
	"gbh/hall/constants"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetPresetCurrencyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetPresetCurrencyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetPresetCurrencyLogic {
	return &SetPresetCurrencyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SetPresetCurrencyLogic) SetPresetCurrency(in *hall.SetPresetCurrencyRequest) (*hall.EmptyResponse, error) {
	uri := fmt.Sprintf(constants.ACCSetPresetCurrencyAPI, in.GetHallId(), in.GetCurrency())

	resp, err := l.svcCtx.AccClient.R().
		Put(uri)

	if err != nil {
		return nil, errorx.ConnectionFailed
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, errorx.InvalidResponse
	}

	var getResponse struct {
		Result string `json:"result"`
	}

	jsonParseErr := json.Unmarshal(resp.Body(), &getResponse)

	if jsonParseErr != nil {
		return nil, errorx.JSONParseFailed
	}

	if getResponse.Result != "ok" {
		return nil, errorx.InvalidResponse
	}

	return &hall.EmptyResponse{}, nil
}
