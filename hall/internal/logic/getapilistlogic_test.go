package logic

import (
	"gbh/errorx"
	"gbh/proto/hall"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetAPIListLogic(t *testing.T) {
	request := hall.GetAPIListRequest{
		HallId: 3820474,
		Name:   "test",
	}

	rows := sqlmock.NewRows([]string{"HallID", "Type", "KeyA", "KeyB", "KeyC", "enable", "content", "name", "order_by"}).
		AddRow(3820474, 1, 1, "keyB", 1, 1, "content test", "test", 1)
	apiServiceMock.ExpectQuery("SELECT hall_sets.HallID,hall_sets.Type,hall_sets.KeyA,hall_sets.KeyB,hall_sets.KeyC,api_list.content,api_list.name,api_list.order_by FROM `hall_sets` LEFT JOIN api_list ON hall_sets.Type = api_list.id WHERE HallID = ? AND enable = 1 AND api_list.name = ? ORDER BY api_list.order_by").
		WithArgs(request.GetHallId(), request.GetName()).
		WillReturnRows(rows)

	l := NewGetAPIListLogic(ctx, svcCtx)
	resp, err := l.GetAPIList(&request)

	expected := &hall.GetAPIListResponse{
		ApiList: []*hall.APIListData{
			{
				HallId:  3820474,
				Type:    1,
				KeyA:    1,
				KeyB:    "keyB",
				KeyC:    1,
				Content: "content test",
				Name:    "test",
				OrderBy: 1,
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
	assert.NoError(t, apiServiceMock.ExpectationsWereMet())
}

func TestGetAPIListLogic_DatabaseError(t *testing.T) {
	request := hall.GetAPIListRequest{
		HallId: 3820474,
		Name:   "test",
	}

	apiServiceMock.ExpectQuery("SELECT hall_sets.HallID,hall_sets.Type,hall_sets.KeyA,hall_sets.KeyB,hall_sets.KeyC,api_list.content,api_list.name,api_list.order_by FROM `hall_sets` LEFT JOIN api_list ON hall_sets.Type = api_list.id WHERE HallID = ? AND enable = 1 AND api_list.name = ? ORDER BY api_list.order_by").
		WithArgs(request.GetHallId(), request.GetName()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetAPIListLogic(ctx, svcCtx)
	resp, err := l.GetAPIList(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, apiServiceMock.ExpectationsWereMet())
}
