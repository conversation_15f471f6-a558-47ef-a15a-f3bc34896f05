package logic

import (
	"fmt"
	"net/http"
	"testing"

	"gbh/errorx"
	"gbh/hall/constants"
	"gbh/proto/hall"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
)

func TestSetPresetCurrency_ConnectionFailed(t *testing.T) {
	request := hall.SetPresetCurrencyRequest{
		HallId:   3820587,
		Currency: "CNY",
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetPresetCurrencyAPI, request.GetHallId(), request.GetCurrency())
	responder := httpmock.NewErrorResponder(http.ErrHandlerTimeout)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetPresetCurrencyLogic(ctx, svcCtx)
	resp, err := l.SetPresetCurrency(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestSetPresetCurrency_InvalidHttpStatus(t *testing.T) {
	request := hall.SetPresetCurrencyRequest{
		HallId:   3820587,
		Currency: "CNY",
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetPresetCurrencyAPI, request.GetHallId(), request.GetCurrency())
	responder := httpmock.NewStringResponder(503, `ServiceUnavailable`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetPresetCurrencyLogic(ctx, svcCtx)
	resp, err := l.SetPresetCurrency(&request)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestSetPresetCurrency_JSONParseFailed(t *testing.T) {
	request := hall.SetPresetCurrencyRequest{
		HallId:   3820587,
		Currency: "CNY",
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetPresetCurrencyAPI, request.GetHallId(), request.GetCurrency())
	responder := httpmock.NewStringResponder(200, `ok`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetPresetCurrencyLogic(ctx, svcCtx)
	resp, err := l.SetPresetCurrency(&request)

	assert.Equal(t, errorx.JSONParseFailed, err)
	assert.Nil(t, resp)
}

func TestSetPresetCurrency_ResultNotOK(t *testing.T) {
	request := hall.SetPresetCurrencyRequest{
		HallId:   3820587,
		Currency: "CNY",
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetPresetCurrencyAPI, request.GetHallId(), request.GetCurrency())
	responder := httpmock.NewStringResponder(200, `{"result":"error"}`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetPresetCurrencyLogic(ctx, svcCtx)
	resp, err := l.SetPresetCurrency(&request)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestSetPresetCurrency(t *testing.T) {
	request := hall.SetPresetCurrencyRequest{
		HallId:   3820587,
		Currency: "CNY",
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetPresetCurrencyAPI, request.GetHallId(), request.GetCurrency())
	responder := httpmock.NewStringResponder(200, `{
		"result": "ok",
		"ret": {
			"domain": 3820587,
			"preset": true,
			"currency": "CNY",
			"is_virtual": false
		},
		"profile": {
			"execution_time": 29,
			"server_name": "durian2-577f45ddcc-7n275"
		}
	}`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetPresetCurrencyLogic(ctx, svcCtx)
	resp, err := l.SetPresetCurrency(&request)

	assert.Equal(t, &hall.EmptyResponse{}, resp)
	assert.NoError(t, err)
}
