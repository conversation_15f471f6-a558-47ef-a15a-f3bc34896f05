package logic

import (
	"fmt"
	"net/http"
	"testing"

	"gbh/errorx"
	"gbh/hall/constants"
	"gbh/proto/hall"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
)

func TestSetGameSwitchInfo_ConnectionFailed(t *testing.T) {
	request := hall.SetGameSwitchInfoRequest{
		HallId:   3820587,
		GameKind: 3,
		Enable:   false,
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetGameSwitchInfoAPI, request.GetHallId())
	responder := httpmock.NewErrorResponder(http.ErrHandlerTimeout)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetGameSwitchInfoLogic(ctx, svcCtx)
	resp, err := l.SetGameSwitchInfo(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestSetGameSwitchInfo_InvalidHttpStatus(t *testing.T) {
	request := hall.SetGameSwitchInfoRequest{
		HallId:   3820587,
		GameKind: 3,
		Enable:   false,
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetGameSwitchInfoAPI, request.GetHallId())
	responder := httpmock.NewStringResponder(503, `ServiceUnavailable`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetGameSwitchInfoLogic(ctx, svcCtx)
	resp, err := l.SetGameSwitchInfo(&request)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestSetGameSwitchInfo_JSONParseFailed(t *testing.T) {
	request := hall.SetGameSwitchInfoRequest{
		HallId:   3820587,
		GameKind: 3,
		Enable:   false,
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetGameSwitchInfoAPI, request.GetHallId())
	responder := httpmock.NewStringResponder(200, `ok`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetGameSwitchInfoLogic(ctx, svcCtx)
	resp, err := l.SetGameSwitchInfo(&request)

	assert.Equal(t, errorx.JSONParseFailed, err)
	assert.Nil(t, resp)
}

func TestSetGameSwitchInfo_ResultNotOK(t *testing.T) {
	request := hall.SetGameSwitchInfoRequest{
		HallId:   3820587,
		GameKind: 3,
		Enable:   false,
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetGameSwitchInfoAPI, request.GetHallId())
	responder := httpmock.NewStringResponder(200, `{"result":"error"}`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetGameSwitchInfoLogic(ctx, svcCtx)
	resp, err := l.SetGameSwitchInfo(&request)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestSetGameSwitchInfo(t *testing.T) {
	request := hall.SetGameSwitchInfoRequest{
		HallId:   3820587,
		GameKind: 3,
		Enable:   false,
	}
	uri := fmt.Sprintf(accURL+constants.ACCSetGameSwitchInfoAPI, request.GetHallId())
	responder := httpmock.NewStringResponder(200, `{
		"result": "ok",
		"ret": [],
		"profile": {
			"execution_time": 13,
			"server_name": "durian2-54ddd754d6-sbzc6"
		}
	}`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("PUT", uri, responder)

	l := NewSetGameSwitchInfoLogic(ctx, svcCtx)
	resp, err := l.SetGameSwitchInfo(&request)

	assert.Equal(t, &hall.EmptyResponse{}, resp)
	assert.NoError(t, err)
}
