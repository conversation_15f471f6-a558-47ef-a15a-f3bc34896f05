package logic

import (
	"context"
	"fmt"
	"gbh/hall/internal/config"
	"gbh/hall/internal/svc"

	"log"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	ctx            context.Context
	svcCtx         *svc.ServiceContext
	apiServiceDb   *gorm.DB
	apiServiceMock sqlmock.Sqlmock
	accConf        config.ACCConf
	secConf        config.SECConf
	accURL         string
	secURL         string
	domainDb       *gorm.DB
	domainMock     sqlmock.Sqlmock
	userDb         *gorm.DB
	userMock       sqlmock.Sqlmock
)

func init() {
	ctx = context.Background()

	accConf = config.ACCConf{
		Schema: "http",
		Host:   "bgp.durian",
		IP:     "127.0.0.1",
		Port:   80,
	}

	accURL = fmt.Sprintf("%s://%s:%d", accConf.Schema, accConf.IP, accConf.Port)

	secConf = config.SECConf{
		Schema: "http",
		Host:   "pdns-api-cloud-int.vir999.com",
	}

	secURL = fmt.Sprintf("%s://%s", secConf.Schema, secConf.Host)

	conf := config.Config{
		ACCConf: accConf,
		SECConf: secConf,
	}

	apiServiceDb, apiServiceMock = NewMockDB()
	domainDb, domainMock = NewMockDB()
	userDb, userMock = NewMockDB()

	svcCtx = svc.NewServiceContext(conf, svc.ExternalContext{
		APIServiceDB: apiServiceDb,
		DomainDB:     domainDb,
		UserDB:       userDb,
	})
}

func NewMockDB() (*gorm.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	if err != nil {
		log.Fatalf("An error '%s' was not expected when opening a stub database connection", err)
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		DriverName:                "mysql",
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})

	if err != nil {
		log.Fatalf("An error '%s' was not expected when opening gorm database", err)
	}

	if gormDB == nil {
		log.Fatal("gorm db is null")
	}
	return gormDB, mock
}
