package logic

import (
	"gbh/errorx"
	"gbh/proto/hall"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestGetGameInfoSettingByDefaultIdLogic(t *testing.T) {
	request := hall.GetGameInfoSettingByDefaultIdRequest{
		DefaultId: 1,
	}

	row := sqlmock.NewRows([]string{"default_id", "lobby", "name"}).
		AddRow(1, 3, "tip_switch").
		AddRow(1, 3, "recommended_menu_switch")

	userMock.ExpectQuery("SELECT * FROM `DomainDefaultGameInfoSetting` WHERE default_id = ?").
		WithArgs(request.GetDefaultId()).
		WillReturnRows(row)

	l := NewGetGameInfoSettingByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetGameInfoSettingByDefaultId(&request)

	expectResp := hall.GetGameInfoSettingByDefaultIdResponse{
		GameInfoSetting: []*hall.GetDomainDefaultGameInfoSetting{
			{
				Lobby: 3,
				Name:  "tip_switch",
			},
			{
				Lobby: 3,
				Name:  "recommended_menu_switch",
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, &expectResp, resp)
	assert.NoError(t, userMock.ExpectationsWereMet())
}

func TestGetGameInfoSettingByDefaultIdLogic_DatabaseError(t *testing.T) {
	request := hall.GetGameInfoSettingByDefaultIdRequest{
		DefaultId: 1,
	}

	userMock.ExpectQuery("SELECT * FROM `DomainDefaultGameInfoSetting` WHERE default_id = ?").
		WithArgs(request.GetDefaultId()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetGameInfoSettingByDefaultIdLogic(ctx, svcCtx)
	resp, err := l.GetGameInfoSettingByDefaultId(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, userMock.ExpectationsWereMet())
}
