package logic

import (
	"context"

	"gbh/errorx"
	"gbh/hall/internal/schema"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLobbySettingByDefaultIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLobbySettingByDefaultIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLobbySettingByDefaultIdLogic {
	return &GetLobbySettingByDefaultIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetLobbySettingByDefaultIdLogic) GetLobbySettingByDefaultId(in *hall.GetLobbySettingByDefaultIdRequest) (*hall.GetLobbySettingByDefaultIdResponse, error) {
	defaultLobbySetting := make([]schema.DomainDefaultLobbySetting, 0)

	err := l.svcCtx.UserDB.Table("DomainDefaultLobbySetting").
		Where("default_id = ?", in.GetDefaultId()).
		Find(&defaultLobbySetting).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	lobbyList := []uint32{}
	for _, v := range defaultLobbySetting {
		lobbyList = append(lobbyList, v.Lobby)
	}

	return &hall.GetLobbySettingByDefaultIdResponse{
		GameKind: lobbyList,
	}, nil
}
