package logic

import (
	"context"

	"gbh/errorx"
	"gbh/hall/internal/schema"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGameInfoSettingByDefaultIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetGameInfoSettingByDefaultIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGameInfoSettingByDefaultIdLogic {
	return &GetGameInfoSettingByDefaultIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetGameInfoSettingByDefaultIdLogic) GetGameInfoSettingByDefaultId(in *hall.GetGameInfoSettingByDefaultIdRequest) (*hall.GetGameInfoSettingByDefaultIdResponse, error) {
	settings := make([]schema.DomainDefaultGameInfoSetting, 0)

	err := l.svcCtx.UserDB.Table("DomainDefaultGameInfoSetting").
		Where("default_id = ?", in.GetDefaultId()).
		Find(&settings).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	respList := make([]*hall.GetDomainDefaultGameInfoSetting, 0, len(settings))
	for _, v := range settings {
		respList = append(respList, &hall.GetDomainDefaultGameInfoSetting{
			Lobby: v.Lobby,
			Name:  v.Name,
		})
	}

	return &hall.GetGameInfoSettingByDefaultIdResponse{
		GameInfoSetting: respList,
	}, nil
}
