// Code generated by goctl. DO NOT EDIT.
// Source: hall.proto

package server

import (
	"context"

	"gbh/hall/internal/logic"
	"gbh/hall/internal/svc"
	"gbh/proto/hall"
)

type HallServer struct {
	svcCtx *svc.ServiceContext
	hall.UnimplementedHallServer
}

func NewHallServer(svcCtx *svc.ServiceContext) *HallServer {
	return &HallServer{
		svcCtx: svcCtx,
	}
}

func (s *HallServer) GetHallIdByWebsite(ctx context.Context, in *hall.GetHallIdByWebsiteRequest) (*hall.GetHallIdByWebsiteResponse, error) {
	l := logic.NewGetHallIdByWebsiteLogic(ctx, s.svcCtx)
	return l.GetHallIdByWebsite(in)
}

func (s *HallServer) GetHallById(ctx context.Context, in *hall.GetHallByIdRequest) (*hall.GetHallByIdResponse, error) {
	l := logic.NewGetHallByIdLogic(ctx, s.svcCtx)
	return l.GetHallById(in)
}

func (s *HallServer) GetHallList(ctx context.Context, in *hall.HallListRequest) (*hall.HallListResponse, error) {
	l := logic.NewGetHallListLogic(ctx, s.svcCtx)
	return l.GetHallList(in)
}

func (s *HallServer) GetHallSiteList(ctx context.Context, in *hall.EmptyRequest) (*hall.GetHallSiteListResponse, error) {
	l := logic.NewGetHallSiteListLogic(ctx, s.svcCtx)
	return l.GetHallSiteList(in)
}

func (s *HallServer) GetCurrency(ctx context.Context, in *hall.GetCurrencyRequest) (*hall.GetCurrencyResponse, error) {
	l := logic.NewGetCurrencyLogic(ctx, s.svcCtx)
	return l.GetCurrency(in)
}

func (s *HallServer) GetPopUpBulletin(ctx context.Context, in *hall.GetPopUpBulletinRequest) (*hall.GetPopUpBulletinResponse, error) {
	l := logic.NewGetPopUpBulletinLogic(ctx, s.svcCtx)
	return l.GetPopUpBulletin(in)
}

func (s *HallServer) UpdatePopUpBulletin(ctx context.Context, in *hall.UpdatePopUpBulletinRequest) (*hall.EmptyResponse, error) {
	l := logic.NewUpdatePopUpBulletinLogic(ctx, s.svcCtx)
	return l.UpdatePopUpBulletin(in)
}

func (s *HallServer) DeletePopUpBulletin(ctx context.Context, in *hall.DeletePopUpBulletinRequest) (*hall.EmptyResponse, error) {
	l := logic.NewDeletePopUpBulletinLogic(ctx, s.svcCtx)
	return l.DeletePopUpBulletin(in)
}

func (s *HallServer) SetHallConfig(ctx context.Context, in *hall.SetHallConfigRequest) (*hall.EmptyResponse, error) {
	l := logic.NewSetHallConfigLogic(ctx, s.svcCtx)
	return l.SetHallConfig(in)
}

func (s *HallServer) SetCurrency(ctx context.Context, in *hall.SetCurrencyRequest) (*hall.EmptyResponse, error) {
	l := logic.NewSetCurrencyLogic(ctx, s.svcCtx)
	return l.SetCurrency(in)
}

func (s *HallServer) SetPresetCurrency(ctx context.Context, in *hall.SetPresetCurrencyRequest) (*hall.EmptyResponse, error) {
	l := logic.NewSetPresetCurrencyLogic(ctx, s.svcCtx)
	return l.SetPresetCurrency(in)
}

func (s *HallServer) GetCurrencyByDefaultId(ctx context.Context, in *hall.GetCurrencyByDefaultIdRequest) (*hall.GetCurrencyByDefaultIdResponse, error) {
	l := logic.NewGetCurrencyByDefaultIdLogic(ctx, s.svcCtx)
	return l.GetCurrencyByDefaultId(in)
}

func (s *HallServer) GetPermissionByDefaultId(ctx context.Context, in *hall.GetPermissionByDefaultIdRequest) (*hall.GetPermissionByDefaultIdResponse, error) {
	l := logic.NewGetPermissionByDefaultIdLogic(ctx, s.svcCtx)
	return l.GetPermissionByDefaultId(in)
}

func (s *HallServer) GetGameInfoSettingByDefaultId(ctx context.Context, in *hall.GetGameInfoSettingByDefaultIdRequest) (*hall.GetGameInfoSettingByDefaultIdResponse, error) {
	l := logic.NewGetGameInfoSettingByDefaultIdLogic(ctx, s.svcCtx)
	return l.GetGameInfoSettingByDefaultId(in)
}

func (s *HallServer) SetGameSwitchInfo(ctx context.Context, in *hall.SetGameSwitchInfoRequest) (*hall.EmptyResponse, error) {
	l := logic.NewSetGameSwitchInfoLogic(ctx, s.svcCtx)
	return l.SetGameSwitchInfo(in)
}

func (s *HallServer) GetAPIList(ctx context.Context, in *hall.GetAPIListRequest) (*hall.GetAPIListResponse, error) {
	l := logic.NewGetAPIListLogic(ctx, s.svcCtx)
	return l.GetAPIList(in)
}

func (s *HallServer) GetLobbySettingByDefaultId(ctx context.Context, in *hall.GetLobbySettingByDefaultIdRequest) (*hall.GetLobbySettingByDefaultIdResponse, error) {
	l := logic.NewGetLobbySettingByDefaultIdLogic(ctx, s.svcCtx)
	return l.GetLobbySettingByDefaultId(in)
}
