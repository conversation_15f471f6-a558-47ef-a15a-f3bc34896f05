package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/hall/internal/config"
	"gbh/hall/internal/server"
	"gbh/hall/internal/svc"
	"gbh/logger"
	"gbh/proto/hall"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/hall.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	apiServiceDB, err := database.New(c.APIServiceDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	domainDB, err := database.New(c.DomainDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	userDB, err := database.New(c.UserDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		APIServiceDB: apiServiceDB,
		DomainDB:     domainDB,
		UserDB:       userDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		hall.RegisterHallServer(grpcServer, server.NewHallServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "hall",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
