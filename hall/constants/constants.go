package constants

import "time"

const APITimeout = 5 * time.Second

const (
	ACCGetDomainAPI         = "/api/domain/%d"
	ACCGetDomainListAPI     = "/api/domain"
	SECDomainURI            = "/api/adm/"
	ACCGetCurrencyAPI       = "/api/domain/%d/currency"
	ACCSetDomainConfigAPI   = "/api/domain/%d/config"
	ACCSetCurrencyAPI       = "/api/domain/%d/currency"
	ACCSetPresetCurrencyAPI = "/api/domain/%d/currency/%s/preset"
	ACCSetGameSwitchInfoAPI = "/api/user/session/%d/game_switch_info"
)

const (
	ACCHallNotFoundError         = 150360004
	ACCCurrencyUserNotFoundError = 150360006
)

const SECToken = "63f599ed099d0bfcd9269a8326d2f93b"

const TimezoneGMT4 = "Etc/GMT+4"
