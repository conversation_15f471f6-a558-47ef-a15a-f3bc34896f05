// Code generated by goctl. DO NOT EDIT.
// Source: hall.proto

package hallclient

import (
	"context"

	"gbh/proto/hall"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	APIListData                           = hall.APIListData
	BoolValue                             = hall.BoolValue
	CurrencyInfo                          = hall.CurrencyInfo
	DeletePopUpBulletinRequest            = hall.DeletePopUpBulletinRequest
	EmptyRequest                          = hall.EmptyRequest
	EmptyResponse                         = hall.EmptyResponse
	GetAPIListRequest                     = hall.GetAPIListRequest
	GetAPIListResponse                    = hall.GetAPIListResponse
	GetCurrencyByDefaultIdRequest         = hall.GetCurrencyByDefaultIdRequest
	GetCurrencyByDefaultIdResponse        = hall.GetCurrencyByDefaultIdResponse
	GetCurrencyRequest                    = hall.GetCurrencyRequest
	GetCurrencyResponse                   = hall.GetCurrencyResponse
	GetDomainDefaultCurrency              = hall.GetDomainDefaultCurrency
	GetDomainDefaultGameInfoSetting       = hall.GetDomainDefaultGameInfoSetting
	GetDomainDefaultPerm                  = hall.GetDomainDefaultPerm
	GetGameInfoSettingByDefaultIdRequest  = hall.GetGameInfoSettingByDefaultIdRequest
	GetGameInfoSettingByDefaultIdResponse = hall.GetGameInfoSettingByDefaultIdResponse
	GetHallByIdRequest                    = hall.GetHallByIdRequest
	GetHallByIdResponse                   = hall.GetHallByIdResponse
	GetHallIdByWebsiteRequest             = hall.GetHallIdByWebsiteRequest
	GetHallIdByWebsiteResponse            = hall.GetHallIdByWebsiteResponse
	GetHallSiteListResponse               = hall.GetHallSiteListResponse
	GetLobbySettingByDefaultIdRequest     = hall.GetLobbySettingByDefaultIdRequest
	GetLobbySettingByDefaultIdResponse    = hall.GetLobbySettingByDefaultIdResponse
	GetPermissionByDefaultIdRequest       = hall.GetPermissionByDefaultIdRequest
	GetPermissionByDefaultIdResponse      = hall.GetPermissionByDefaultIdResponse
	GetPopUpBulletinRequest               = hall.GetPopUpBulletinRequest
	GetPopUpBulletinResponse              = hall.GetPopUpBulletinResponse
	HallListRequest                       = hall.HallListRequest
	HallListResponse                      = hall.HallListResponse
	SetCurrencyRequest                    = hall.SetCurrencyRequest
	SetGameSwitchInfoRequest              = hall.SetGameSwitchInfoRequest
	SetHallConfigRequest                  = hall.SetHallConfigRequest
	SetPresetCurrencyRequest              = hall.SetPresetCurrencyRequest
	SiteList                              = hall.SiteList
	UpdatePopUpBulletinRequest            = hall.UpdatePopUpBulletinRequest

	Hall interface {
		GetHallIdByWebsite(ctx context.Context, in *GetHallIdByWebsiteRequest, opts ...grpc.CallOption) (*GetHallIdByWebsiteResponse, error)
		GetHallById(ctx context.Context, in *GetHallByIdRequest, opts ...grpc.CallOption) (*GetHallByIdResponse, error)
		GetHallList(ctx context.Context, in *HallListRequest, opts ...grpc.CallOption) (*HallListResponse, error)
		GetHallSiteList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetHallSiteListResponse, error)
		GetCurrency(ctx context.Context, in *GetCurrencyRequest, opts ...grpc.CallOption) (*GetCurrencyResponse, error)
		GetPopUpBulletin(ctx context.Context, in *GetPopUpBulletinRequest, opts ...grpc.CallOption) (*GetPopUpBulletinResponse, error)
		UpdatePopUpBulletin(ctx context.Context, in *UpdatePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		DeletePopUpBulletin(ctx context.Context, in *DeletePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetHallConfig(ctx context.Context, in *SetHallConfigRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetCurrency(ctx context.Context, in *SetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetPresetCurrency(ctx context.Context, in *SetPresetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetCurrencyByDefaultId(ctx context.Context, in *GetCurrencyByDefaultIdRequest, opts ...grpc.CallOption) (*GetCurrencyByDefaultIdResponse, error)
		GetPermissionByDefaultId(ctx context.Context, in *GetPermissionByDefaultIdRequest, opts ...grpc.CallOption) (*GetPermissionByDefaultIdResponse, error)
		GetGameInfoSettingByDefaultId(ctx context.Context, in *GetGameInfoSettingByDefaultIdRequest, opts ...grpc.CallOption) (*GetGameInfoSettingByDefaultIdResponse, error)
		SetGameSwitchInfo(ctx context.Context, in *SetGameSwitchInfoRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetAPIList(ctx context.Context, in *GetAPIListRequest, opts ...grpc.CallOption) (*GetAPIListResponse, error)
		GetLobbySettingByDefaultId(ctx context.Context, in *GetLobbySettingByDefaultIdRequest, opts ...grpc.CallOption) (*GetLobbySettingByDefaultIdResponse, error)
	}

	defaultHall struct {
		cli zrpc.Client
	}
)

func NewHall(cli zrpc.Client) Hall {
	return &defaultHall{
		cli: cli,
	}
}

func (m *defaultHall) GetHallIdByWebsite(ctx context.Context, in *GetHallIdByWebsiteRequest, opts ...grpc.CallOption) (*GetHallIdByWebsiteResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallIdByWebsite(ctx, in, opts...)
}

func (m *defaultHall) GetHallById(ctx context.Context, in *GetHallByIdRequest, opts ...grpc.CallOption) (*GetHallByIdResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallById(ctx, in, opts...)
}

func (m *defaultHall) GetHallList(ctx context.Context, in *HallListRequest, opts ...grpc.CallOption) (*HallListResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallList(ctx, in, opts...)
}

func (m *defaultHall) GetHallSiteList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetHallSiteListResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetHallSiteList(ctx, in, opts...)
}

func (m *defaultHall) GetCurrency(ctx context.Context, in *GetCurrencyRequest, opts ...grpc.CallOption) (*GetCurrencyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetCurrency(ctx, in, opts...)
}

func (m *defaultHall) GetPopUpBulletin(ctx context.Context, in *GetPopUpBulletinRequest, opts ...grpc.CallOption) (*GetPopUpBulletinResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetPopUpBulletin(ctx, in, opts...)
}

func (m *defaultHall) UpdatePopUpBulletin(ctx context.Context, in *UpdatePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.UpdatePopUpBulletin(ctx, in, opts...)
}

func (m *defaultHall) DeletePopUpBulletin(ctx context.Context, in *DeletePopUpBulletinRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.DeletePopUpBulletin(ctx, in, opts...)
}

func (m *defaultHall) SetHallConfig(ctx context.Context, in *SetHallConfigRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.SetHallConfig(ctx, in, opts...)
}

func (m *defaultHall) SetCurrency(ctx context.Context, in *SetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.SetCurrency(ctx, in, opts...)
}

func (m *defaultHall) SetPresetCurrency(ctx context.Context, in *SetPresetCurrencyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.SetPresetCurrency(ctx, in, opts...)
}

func (m *defaultHall) GetCurrencyByDefaultId(ctx context.Context, in *GetCurrencyByDefaultIdRequest, opts ...grpc.CallOption) (*GetCurrencyByDefaultIdResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetCurrencyByDefaultId(ctx, in, opts...)
}

func (m *defaultHall) GetPermissionByDefaultId(ctx context.Context, in *GetPermissionByDefaultIdRequest, opts ...grpc.CallOption) (*GetPermissionByDefaultIdResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetPermissionByDefaultId(ctx, in, opts...)
}

func (m *defaultHall) GetGameInfoSettingByDefaultId(ctx context.Context, in *GetGameInfoSettingByDefaultIdRequest, opts ...grpc.CallOption) (*GetGameInfoSettingByDefaultIdResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetGameInfoSettingByDefaultId(ctx, in, opts...)
}

func (m *defaultHall) SetGameSwitchInfo(ctx context.Context, in *SetGameSwitchInfoRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.SetGameSwitchInfo(ctx, in, opts...)
}

func (m *defaultHall) GetAPIList(ctx context.Context, in *GetAPIListRequest, opts ...grpc.CallOption) (*GetAPIListResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetAPIList(ctx, in, opts...)
}

func (m *defaultHall) GetLobbySettingByDefaultId(ctx context.Context, in *GetLobbySettingByDefaultIdRequest, opts ...grpc.CallOption) (*GetLobbySettingByDefaultIdResponse, error) {
	client := hall.NewHallClient(m.cli.Conn())
	return client.GetLobbySettingByDefaultId(ctx, in, opts...)
}
