Name: hall.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - etc:2379
  Key: hall.rpc

APIServiceDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: APIService
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: APIService
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

ACCConf:
  Schema: http
  Host: bgp.qa.durian
  IP: 127.0.0.1
  Port: 80

SECConf:
  Schema: http
  Host: pdns-api-cloud-int.vir999.com

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

DomainDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: DomainDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: username
    Password: password
    Database: DomainDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

UserDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: UserDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: UserDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

Middlewares:
  Stat: false

