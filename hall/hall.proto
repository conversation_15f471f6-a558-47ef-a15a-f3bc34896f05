syntax = "proto3";

package hall;

option go_package = "proto/hall";

message BoolValue { bool value = 1; }
message EmptyRequest {}
message EmptyResponse {}

message GetHallIdByWebsiteRequest { string website = 1; }

message GetHallIdByWebsiteResponse { uint32 hall_id = 1; }

message GetHallByIdRequest { uint32 hall_id = 1; }

message GetHallByIdResponse {
  uint32 hall_id = 1;
  string name = 2;
  string login_code = 3;
  bool enable = 4;
}

message HallListRequest { BoolValue enable = 1; }

message HallListResponse { repeated GetHallByIdResponse data = 1; }

message GetHallSiteListResponse { repeated SiteList site_list = 1; }

message SiteList {
  string big_group = 1;
  string site_group = 2;
  string site_name = 3;
  string login_code = 4;
  uint32 hall_id = 5;
}

message GetCurrencyRequest { uint32 hall_id = 1; }

message GetCurrencyResponse { repeated CurrencyInfo currency_info = 1; }

message CurrencyInfo {
  bool preset = 1;
  string currency = 2;
}

message GetPopUpBulletinRequest { uint32 hall_id = 1; }

message GetPopUpBulletinResponse {
  uint32 hall_id = 1;
  bool enable = 2;
  uint32 role = 3;
  string zh_tw = 4;
  string zh_cn = 5;
  string en = 6;
  string th = 7;
  string ja = 8;
  string ko = 9;
  string vi = 10;
  string created_at = 11;
}

message UpdatePopUpBulletinRequest {
  uint32 hall_id = 1;
  BoolValue enable = 2;
}

message DeletePopUpBulletinRequest { uint32 hall_id = 1; }

message SetHallConfigRequest {
  uint32 hall_id = 1;
  string login_code = 2;
  string name = 3;
}

message SetCurrencyRequest {
  uint32 hall_id = 1;
  repeated string currency_list = 2;
}

message SetPresetCurrencyRequest {
  uint32 hall_id = 1;
  string currency = 2;
}

message GetCurrencyByDefaultIdRequest { uint32 default_id = 1; }

message GetDomainDefaultCurrency {
  string currency = 1;
  bool preset = 2;
}

message GetCurrencyByDefaultIdResponse {
  repeated GetDomainDefaultCurrency domain_default_currency = 1;
}

message GetPermissionByDefaultIdRequest { uint32 default_id = 1; }

message GetDomainDefaultPerm {
  uint32 perm_id = 1;
  uint32 role_id = 2;
  bool modify = 3;
}

message GetPermissionByDefaultIdResponse {
  repeated GetDomainDefaultPerm domain_default_perm = 1;
}

message GetGameInfoSettingByDefaultIdRequest { uint32 default_id = 1; }

message GetDomainDefaultGameInfoSetting {
  uint32 lobby = 1;
  string name = 2;
}

message GetGameInfoSettingByDefaultIdResponse {
  repeated GetDomainDefaultGameInfoSetting game_info_setting = 1;
}

message SetGameSwitchInfoRequest {
  uint32 hall_id = 1;
  uint32 game_kind = 2;
  bool enable = 3;
}

message GetAPIListRequest {
  uint32 hall_id = 1;
  string name = 2;
}

message GetAPIListResponse { repeated APIListData api_list = 1; }

message APIListData {
  uint32 hall_id = 1;
  uint32 type = 2;
  uint32 key_a = 3;
  string key_b = 4;
  uint32 key_c = 5;
  string content = 6;
  string name = 7;
  uint32 order_by = 8;
}

message GetLobbySettingByDefaultIdRequest { uint32 default_id = 1; }

message GetLobbySettingByDefaultIdResponse { repeated uint32 game_kind = 1; }

service Hall {
  rpc GetHallIdByWebsite(GetHallIdByWebsiteRequest)
      returns (GetHallIdByWebsiteResponse);
  rpc GetHallById(GetHallByIdRequest) returns (GetHallByIdResponse);
  rpc GetHallList(HallListRequest) returns (HallListResponse);
  rpc GetHallSiteList(EmptyRequest) returns (GetHallSiteListResponse);
  rpc GetCurrency(GetCurrencyRequest) returns (GetCurrencyResponse);
  rpc GetPopUpBulletin(GetPopUpBulletinRequest)
      returns (GetPopUpBulletinResponse);
  rpc UpdatePopUpBulletin(UpdatePopUpBulletinRequest) returns (EmptyResponse);
  rpc DeletePopUpBulletin(DeletePopUpBulletinRequest) returns (EmptyResponse);
  rpc SetHallConfig(SetHallConfigRequest) returns (EmptyResponse);
  rpc SetCurrency(SetCurrencyRequest) returns (EmptyResponse);
  rpc SetPresetCurrency(SetPresetCurrencyRequest) returns (EmptyResponse);
  rpc GetCurrencyByDefaultId(GetCurrencyByDefaultIdRequest)
      returns (GetCurrencyByDefaultIdResponse);
  rpc GetPermissionByDefaultId(GetPermissionByDefaultIdRequest)
      returns (GetPermissionByDefaultIdResponse);
  rpc GetGameInfoSettingByDefaultId(GetGameInfoSettingByDefaultIdRequest)
      returns (GetGameInfoSettingByDefaultIdResponse);
  rpc SetGameSwitchInfo(SetGameSwitchInfoRequest) returns (EmptyResponse);
  rpc GetAPIList(GetAPIListRequest) returns (GetAPIListResponse);
  rpc GetLobbySettingByDefaultId(GetLobbySettingByDefaultIdRequest)
      returns (GetLobbySettingByDefaultIdResponse);
}
