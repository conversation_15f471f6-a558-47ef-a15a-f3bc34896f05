package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/export/internal/config"
	"gbh/export/internal/server"
	"gbh/export/internal/svc"
	"gbh/logger"
	"gbh/proto/export"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/export.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	systemDB, err := database.New(c.SystemD<PERSON>onf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		SystemDB: systemDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		export.RegisterExportServer(grpcServer, server.NewExportServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	// 設定統計與日誌攔截器參數
	statConf := rpcserver.StatConf{
		MetricsName:          "export",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
