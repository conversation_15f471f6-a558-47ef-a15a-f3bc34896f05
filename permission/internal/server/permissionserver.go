// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.7
// Source: permission.proto

package server

import (
	"context"

	"gbh/permission/internal/logic"
	"gbh/permission/internal/svc"
	"gbh/proto/permission"
)

type PermissionServer struct {
	svcCtx *svc.ServiceContext
	permission.UnimplementedPermissionServer
}

func NewPermissionServer(svcCtx *svc.ServiceContext) *PermissionServer {
	return &PermissionServer{
		svcCtx: svcCtx,
	}
}

func (s *PermissionServer) GetVisitedHistory(ctx context.Context, in *permission.GetVisitedHistoryRequest) (*permission.GetVisitedHistoryResponse, error) {
	l := logic.NewGetVisitedHistoryLogic(ctx, s.svcCtx)
	return l.GetVisitedHistory(in)
}

func (s *PermissionServer) AdminControllerPermissionIDs(ctx context.Context, in *permission.AdminControllerPermissionIDsRequest) (*permission.AdminControllerPermissionIDsResponse, error) {
	l := logic.NewAdminControllerPermissionIDsLogic(ctx, s.svcCtx)
	return l.AdminControllerPermissionIDs(in)
}

func (s *PermissionServer) AdminPermissions(ctx context.Context, in *permission.AdminPermissionsRequest) (*permission.AdminPermissionsResponse, error) {
	l := logic.NewAdminPermissionsLogic(ctx, s.svcCtx)
	return l.AdminPermissions(in)
}

func (s *PermissionServer) ModifyVisited(ctx context.Context, in *permission.VisitedRequest) (*permission.EmptyResponse, error) {
	l := logic.NewModifyVisitedLogic(ctx, s.svcCtx)
	return l.ModifyVisited(in)
}

func (s *PermissionServer) GetAdminPermissionEnabled(ctx context.Context, in *permission.GetAdminPermissionEnabledRequest) (*permission.GetAdminPermissionEnabledResponse, error) {
	l := logic.NewGetAdminPermissionEnabledLogic(ctx, s.svcCtx)
	return l.GetAdminPermissionEnabled(in)
}

func (s *PermissionServer) AdminPermissionInfoList(ctx context.Context, in *permission.AdminPermissionInfoListRequest) (*permission.AdminPermissionInfoListResponse, error) {
	l := logic.NewAdminPermissionInfoListLogic(ctx, s.svcCtx)
	return l.AdminPermissionInfoList(in)
}

func (s *PermissionServer) GetUserPermissionAffectList(ctx context.Context, in *permission.EmptyRequest) (*permission.GetUserPermissionAffectListResponse, error) {
	l := logic.NewGetUserPermissionAffectListLogic(ctx, s.svcCtx)
	return l.GetUserPermissionAffectList(in)
}

func (s *PermissionServer) UserPermissionConfigList(ctx context.Context, in *permission.UserPermissionConfigListRequest) (*permission.UserPermissionConfigListResponse, error) {
	l := logic.NewUserPermissionConfigListLogic(ctx, s.svcCtx)
	return l.UserPermissionConfigList(in)
}

func (s *PermissionServer) GetUserPermissionEnabled(ctx context.Context, in *permission.UserPermissionEnabledRequest) (*permission.UserPermissionEnabledResponse, error) {
	l := logic.NewGetUserPermissionEnabledLogic(ctx, s.svcCtx)
	return l.GetUserPermissionEnabled(in)
}

func (s *PermissionServer) GetUserAllPermission(ctx context.Context, in *permission.UserAllPermissionRequest) (*permission.UserAllPermissionResponse, error) {
	l := logic.NewGetUserAllPermissionLogic(ctx, s.svcCtx)
	return l.GetUserAllPermission(in)
}

func (s *PermissionServer) DeleteUserPermissionByUserID(ctx context.Context, in *permission.DeleteUserPermissionByUserIDRequest) (*permission.EmptyResponse, error) {
	l := logic.NewDeleteUserPermissionByUserIDLogic(ctx, s.svcCtx)
	return l.DeleteUserPermissionByUserID(in)
}

func (s *PermissionServer) UserPermList(ctx context.Context, in *permission.UserPermListRequest) (*permission.UserPermListResponse, error) {
	l := logic.NewUserPermListLogic(ctx, s.svcCtx)
	return l.UserPermList(in)
}

func (s *PermissionServer) DeleteUserDepartmentByUserID(ctx context.Context, in *permission.DeleteUserDepartmentByUserIDRequest) (*permission.EmptyResponse, error) {
	l := logic.NewDeleteUserDepartmentByUserIDLogic(ctx, s.svcCtx)
	return l.DeleteUserDepartmentByUserID(in)
}

func (s *PermissionServer) SetUserPermission(ctx context.Context, in *permission.SetUserPermissionRequest) (*permission.EmptyResponse, error) {
	l := logic.NewSetUserPermissionLogic(ctx, s.svcCtx)
	return l.SetUserPermission(in)
}

func (s *PermissionServer) SetAdminPermissionByUser(ctx context.Context, in *permission.SetAdminPermissionByUserRequest) (*permission.EmptyResponse, error) {
	l := logic.NewSetAdminPermissionByUserLogic(ctx, s.svcCtx)
	return l.SetAdminPermissionByUser(in)
}

func (s *PermissionServer) SetAdminPermission(ctx context.Context, in *permission.SetAdminPermissionRequest) (*permission.EmptyResponse, error) {
	l := logic.NewSetAdminPermissionLogic(ctx, s.svcCtx)
	return l.SetAdminPermission(in)
}
