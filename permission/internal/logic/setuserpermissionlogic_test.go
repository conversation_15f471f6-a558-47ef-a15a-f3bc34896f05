package logic

import (
	"gbh/errorx"
	"gbh/proto/permission"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestSetUserPermission_CountDatabaseError(t *testing.T) {
	req := permission.SetUserPermissionRequest{
		UserPermissions: []*permission.UserPerm{
			{
				Domain:       1,
				UserId:       2,
				RoleId:       3,
				PermissionId: 4,
				Enable:       true,
				Modify:       true,
			},
		},
	}

	sqlMockPermission.ExpectBegin()

	sqlMockPermission.ExpectQuery("SELECT * FROM `UserPerm` WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ? ORDER BY `UserPerm`.`domain` LIMIT ?").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), 1).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockPermission.ExpectRollback()

	l := NewSetUserPermissionLogic(ctx, svcCtx)
	resp, err := l.SetUserPermission(&req)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockPermission.ExpectationsWereMet())
}

func TestSetUserPermission_Insert(t *testing.T) {
	req := permission.SetUserPermissionRequest{
		UserPermissions: []*permission.UserPerm{
			{
				Domain:       1,
				UserId:       2,
				RoleId:       3,
				PermissionId: 4,
				Enable:       true,
				Modify:       true,
			},
		},
	}

	sqlMockPermission.ExpectBegin()

	sqlMockPermission.ExpectQuery("SELECT * FROM `UserPerm` WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ? ORDER BY `UserPerm`.`domain` LIMIT ?").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), 1).
		WillReturnRows(sqlMockPermission.NewRows([]string{"domain", "user_id", "role_id", "perm_id", "enable", "modify"}))

	sqlMockPermission.ExpectExec("INSERT INTO `UserPerm` (`domain`,`user_id`,`role_id`,`perm_id`,`enable`,`modify`) VALUES (?,?,?,?,?,?)").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), req.GetUserPermissions()[0].GetEnable(), req.GetUserPermissions()[0].GetModify()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockPermission.ExpectCommit()

	l := NewSetUserPermissionLogic(ctx, svcCtx)
	resp, err := l.SetUserPermission(&req)

	assert.Equal(t, &permission.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockPermission.ExpectationsWereMet())
}

func TestSetUserPermission_Update(t *testing.T) {
	req := permission.SetUserPermissionRequest{
		UserPermissions: []*permission.UserPerm{
			{
				Domain:       1,
				UserId:       2,
				RoleId:       3,
				PermissionId: 4,
				Enable:       true,
				Modify:       true,
			},
		},
	}

	sqlMockPermission.ExpectBegin()

	rows := sqlMockPermission.NewRows([]string{"domain", "user_id", "role_id", "perm_id", "enable", "modify"}).
		AddRow(1, 2, 3, 4, false, false)

	sqlMockPermission.ExpectQuery("SELECT * FROM `UserPerm` WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ? ORDER BY `UserPerm`.`domain` LIMIT ?").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), 1).
		WillReturnRows(rows)

	sqlMockPermission.ExpectExec("UPDATE `UserPerm` SET `enable`=?,`modify`=? WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ?").
		WithArgs(req.GetUserPermissions()[0].GetEnable(), req.GetUserPermissions()[0].GetModify(), req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockPermission.ExpectCommit()

	l := NewSetUserPermissionLogic(ctx, svcCtx)
	resp, err := l.SetUserPermission(&req)

	assert.Equal(t, &permission.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockPermission.ExpectationsWereMet())
}

func TestSetUserPermission_InsertAndUpdate(t *testing.T) {
	req := permission.SetUserPermissionRequest{
		UserPermissions: []*permission.UserPerm{
			{
				Domain:       1,
				UserId:       2,
				RoleId:       3,
				PermissionId: 4,
				Enable:       true,
				Modify:       true,
			},
			{
				Domain:       1,
				UserId:       2,
				RoleId:       3,
				PermissionId: 5,
				Enable:       true,
				Modify:       true,
			},
		},
	}

	sqlMockPermission.ExpectBegin()

	sqlMockPermission.ExpectQuery("SELECT * FROM `UserPerm` WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ? ORDER BY `UserPerm`.`domain` LIMIT ?").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), 1).
		WillReturnRows(sqlMockPermission.NewRows([]string{"domain", "user_id", "role_id", "perm_id", "enable", "modify"}))

	sqlMockPermission.ExpectExec("INSERT INTO `UserPerm` (`domain`,`user_id`,`role_id`,`perm_id`,`enable`,`modify`) VALUES (?,?,?,?,?,?)").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), req.GetUserPermissions()[0].GetEnable(), req.GetUserPermissions()[0].GetModify()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	rows := sqlMockPermission.NewRows([]string{"domain", "user_id", "role_id", "perm_id", "enable", "modify"}).
		AddRow(1, 2, 3, 5, false, false)

	sqlMockPermission.ExpectQuery("SELECT * FROM `UserPerm` WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ? ORDER BY `UserPerm`.`domain` LIMIT ?").
		WithArgs(req.GetUserPermissions()[1].GetDomain(), req.GetUserPermissions()[1].GetUserId(), req.GetUserPermissions()[1].GetRoleId(), req.GetUserPermissions()[1].GetPermissionId(), 1).
		WillReturnRows(rows)

	sqlMockPermission.ExpectExec("UPDATE `UserPerm` SET `enable`=?,`modify`=? WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ?").
		WithArgs(req.GetUserPermissions()[1].GetEnable(), req.GetUserPermissions()[1].GetModify(), req.GetUserPermissions()[1].GetDomain(), req.GetUserPermissions()[1].GetUserId(), req.GetUserPermissions()[1].GetRoleId(), req.GetUserPermissions()[1].GetPermissionId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockPermission.ExpectCommit()

	l := NewSetUserPermissionLogic(ctx, svcCtx)
	resp, err := l.SetUserPermission(&req)

	assert.Equal(t, &permission.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockPermission.ExpectationsWereMet())
}

func TestSetUserPermission_InsertDatabaseError(t *testing.T) {
	req := permission.SetUserPermissionRequest{
		UserPermissions: []*permission.UserPerm{
			{
				Domain:       1,
				UserId:       2,
				RoleId:       3,
				PermissionId: 4,
				Enable:       true,
				Modify:       true,
			},
		},
	}

	sqlMockPermission.ExpectBegin()

	sqlMockPermission.ExpectQuery("SELECT * FROM `UserPerm` WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ? ORDER BY `UserPerm`.`domain` LIMIT ?").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), 1).
		WillReturnRows(sqlMockPermission.NewRows([]string{"domain", "user_id", "role_id", "perm_id", "enable", "modify"}))

	sqlMockPermission.ExpectExec("INSERT INTO `UserPerm` (`domain`,`user_id`,`role_id`,`perm_id`,`enable`,`modify`) VALUES (?,?,?,?,?,?)").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), req.GetUserPermissions()[0].GetEnable(), req.GetUserPermissions()[0].GetModify()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockPermission.ExpectRollback()

	l := NewSetUserPermissionLogic(ctx, svcCtx)
	resp, err := l.SetUserPermission(&req)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockPermission.ExpectationsWereMet())
}

func TestSetUserPermission_UpdateDatebaseError(t *testing.T) {
	req := permission.SetUserPermissionRequest{
		UserPermissions: []*permission.UserPerm{
			{
				Domain:       1,
				UserId:       2,
				RoleId:       3,
				PermissionId: 4,
				Enable:       true,
				Modify:       true,
			},
		},
	}

	sqlMockPermission.ExpectBegin()

	rows := sqlMockPermission.NewRows([]string{"domain", "user_id", "role_id", "perm_id", "enable", "modify"}).
		AddRow(1, 2, 3, 4, false, false)

	sqlMockPermission.ExpectQuery("SELECT * FROM `UserPerm` WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ? ORDER BY `UserPerm`.`domain` LIMIT ?").
		WithArgs(req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId(), 1).
		WillReturnRows(rows)

	sqlMockPermission.ExpectExec("UPDATE `UserPerm` SET `enable`=?,`modify`=? WHERE `domain` = ? AND `user_id` = ? AND `role_id` = ? AND `perm_id` = ?").
		WithArgs(req.GetUserPermissions()[0].GetEnable(), req.GetUserPermissions()[0].GetModify(), req.GetUserPermissions()[0].GetDomain(), req.GetUserPermissions()[0].GetUserId(), req.GetUserPermissions()[0].GetRoleId(), req.GetUserPermissions()[0].GetPermissionId()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockPermission.ExpectRollback()

	l := NewSetUserPermissionLogic(ctx, svcCtx)
	resp, err := l.SetUserPermission(&req)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockPermission.ExpectationsWereMet())
}
