package logic

import (
	"gbh/errorx"
	"gbh/proto/permission"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestSetAdminPermission_Insert_Success(t *testing.T) {
	request := permission.SetAdminPermissionRequest{
		List: []*permission.AdminPermission{{
			AdminId:      1,
			PermissionId: 1,
		}},
	}

	sqlMockAdmin.ExpectBegin()

	sqlMockAdmin.ExpectExec("INSERT IGNORE INTO `GMPerm` (`gm_id`,`perm_id`,`favorite`,`favorite_sort`,`visited_at`,`modify`) VALUES (?,?,?,?,?,?)").
		WithArgs(request.GetList()[0].GetAdminId(), request.GetList()[0].GetPermissionId(), 0, nil, nil, false).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockAdmin.ExpectCommit()

	l := NewSetAdminPermissionLogic(ctx, svcCtx)
	resp, err := l.SetAdminPermission(&request)

	assert.Equal(t, &permission.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockAdmin.ExpectationsWereMet())
}

func TestSetAdminPermission_Insert_DatabaseError(t *testing.T) {
	request := permission.SetAdminPermissionRequest{
		List: []*permission.AdminPermission{{
			AdminId:      1,
			PermissionId: 1,
		}},
	}

	sqlMockAdmin.ExpectBegin()

	sqlMockAdmin.ExpectExec("INSERT IGNORE INTO `GMPerm` (`gm_id`,`perm_id`,`favorite`,`favorite_sort`,`visited_at`,`modify`) VALUES (?,?,?,?,?,?)").
		WithArgs(request.GetList()[0].GetAdminId(), request.GetList()[0].GetPermissionId(), 0, nil, nil, false).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockAdmin.ExpectRollback()

	l := NewSetAdminPermissionLogic(ctx, svcCtx)
	resp, err := l.SetAdminPermission(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockAdmin.ExpectationsWereMet())
}

func TestSetAdminPermission_InsertOrUpdate_Success(t *testing.T) {
	request := permission.SetAdminPermissionRequest{
		List: []*permission.AdminPermission{{
			AdminId:      1,
			PermissionId: 1,
			Modify:       &permission.BoolValue{Value: true},
		}},
	}

	sqlMockAdmin.ExpectBegin()

	sqlMockAdmin.ExpectExec("INSERT INTO `GMPerm` (`gm_id`,`perm_id`,`favorite`,`favorite_sort`,`visited_at`,`modify`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `modify`=VALUES(`modify`)").
		WithArgs(request.GetList()[0].GetAdminId(), request.GetList()[0].GetPermissionId(), 0, nil, nil, request.GetList()[0].GetModify().GetValue()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockAdmin.ExpectCommit()

	l := NewSetAdminPermissionLogic(ctx, svcCtx)
	resp, err := l.SetAdminPermission(&request)

	assert.Equal(t, &permission.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockAdmin.ExpectationsWereMet())
}

func TestSetAdminPermission_InsertOrUpdate_DatabaseError(t *testing.T) {
	request := permission.SetAdminPermissionRequest{
		List: []*permission.AdminPermission{{
			AdminId:      1,
			PermissionId: 1,
			Modify:       &permission.BoolValue{Value: true},
		}},
	}

	sqlMockAdmin.ExpectBegin()

	sqlMockAdmin.ExpectExec("INSERT INTO `GMPerm` (`gm_id`,`perm_id`,`favorite`,`favorite_sort`,`visited_at`,`modify`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `modify`=VALUES(`modify`)").
		WithArgs(request.GetList()[0].GetAdminId(), request.GetList()[0].GetPermissionId(), 0, nil, nil, request.GetList()[0].GetModify().GetValue()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockAdmin.ExpectRollback()

	l := NewSetAdminPermissionLogic(ctx, svcCtx)
	resp, err := l.SetAdminPermission(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockAdmin.ExpectationsWereMet())
}
