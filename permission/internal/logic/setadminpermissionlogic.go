package logic

import (
	"context"

	"gbh/errorx"
	"gbh/permission/internal/schema"
	"gbh/permission/internal/svc"
	"gbh/proto/permission"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type SetAdminPermissionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetAdminPermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetAdminPermissionLogic {
	return &SetAdminPermissionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SetAdminPermissionLogic) SetAdminPermission(in *permission.SetAdminPermissionRequest) (*permission.EmptyResponse, error) {
	var insertInfos []schema.GMPerm
	var saveInfos []schema.GMPerm

	for _, data := range in.GetList() {
		if data.GetModify() == nil {
			insertInfos = append(insertInfos, schema.GMPerm{
				GmID:   data.GetAdminId(),
				PermID: data.GetPermissionId(),
			})
		} else {
			saveInfos = append(saveInfos, schema.GMPerm{
				GmID:   data.GetAdminId(),
				PermID: data.GetPermissionId(),
				Modify: data.GetModify().GetValue(),
			})
		}
	}

	err := l.svcCtx.AdminDB.Transaction(func(tx *gorm.DB) error {
		if len(insertInfos) > 0 {
			insertErr := tx.Table("GMPerm").Clauses(
				clause.Insert{Modifier: "IGNORE"},
			).Create(&insertInfos).Error

			if insertErr != nil {
				return insertErr
			}
		}

		if len(saveInfos) > 0 {
			saveErr := tx.Table("GMPerm").Clauses(
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "gm_id"}, {Name: "perm_id"}},
					DoUpdates: clause.AssignmentColumns([]string{"modify"}),
				}).Create(&saveInfos).Error

			if saveErr != nil {
				return saveErr
			}
		}

		return nil
	})

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &permission.EmptyResponse{}, nil
}
