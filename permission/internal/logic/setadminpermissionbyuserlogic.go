package logic

import (
	"context"

	"gbh/errorx"
	"gbh/permission/internal/schema"
	"gbh/permission/internal/svc"
	"gbh/proto/permission"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type SetAdminPermissionByUserLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetAdminPermissionByUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetAdminPermissionByUserLogic {
	return &SetAdminPermissionByUserLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SetAdminPermissionByUserLogic) SetAdminPermissionByUser(in *permission.SetAdminPermissionByUserRequest) (*permission.EmptyResponse, error) {
	err := l.svcCtx.AdminDB.Transaction(func(tx *gorm.DB) error {
		if len(in.GetDeleteId()) > 0 {
			deleteErr := tx.Table("GMPerm").
				Where("gm_id = ?", in.GetAdminId()).
				Where("perm_id IN (?)", in.GetDeleteId()).
				Delete(nil).
				Error

			if deleteErr != nil {
				return deleteErr
			}
		}

		var saveInfos []schema.GMPerm
		for _, data := range in.GetSetPermission() {
			saveInfos = append(saveInfos, schema.GMPerm{
				GmID:   in.GetAdminId(),
				PermID: data.GetPermissionId(),
				Modify: data.GetModify(),
			})
		}

		if len(saveInfos) > 0 {
			saveErr := tx.Table("GMPerm").Clauses(
				clause.OnConflict{
					Columns:   []clause.Column{{Name: "gm_id"}, {Name: "perm_id"}},
					DoUpdates: clause.AssignmentColumns([]string{"modify"}),
				}).Create(&saveInfos).
				Error

			if saveErr != nil {
				return saveErr
			}
		}

		return nil
	})

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &permission.EmptyResponse{}, nil
}
