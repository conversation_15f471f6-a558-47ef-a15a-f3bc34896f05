package logic

import (
	"gbh/errorx"
	"gbh/proto/permission"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestSetAdminPermissionByUser_Success(t *testing.T) {
	request := permission.SetAdminPermissionByUserRequest{
		AdminId: 11579,
		SetPermission: []*permission.SetPermission{
			{
				PermissionId: 1,
				Modify:       true,
			},
		},
		DeleteId: []uint32{2},
	}

	sqlMockAdmin.ExpectBegin()

	sqlMockAdmin.ExpectExec("DELETE FROM `GMPerm` WHERE gm_id = ? AND perm_id IN (?)").
		WithArgs(request.GetAdminId(), request.GetDeleteId()[0]).
		WillReturnResult(sqlmock.NewResult(0, 1))

	sqlMockAdmin.ExpectExec("INSERT INTO `GMPerm` (`gm_id`,`perm_id`,`favorite`,`favorite_sort`,`visited_at`,`modify`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `modify`=VALUES(`modify`)").
		WithArgs(request.GetAdminId(), request.GetSetPermission()[0].GetPermissionId(), 0, nil, nil, request.GetSetPermission()[0].GetModify()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockAdmin.ExpectCommit()

	l := NewSetAdminPermissionByUserLogic(ctx, svcCtx)
	resp, err := l.SetAdminPermissionByUser(&request)

	assert.Equal(t, &permission.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockAdmin.ExpectationsWereMet())
}

func TestSetAdminPermissionByUser_Delete_DatabaseError(t *testing.T) {
	request := permission.SetAdminPermissionByUserRequest{
		AdminId: 11579,
		SetPermission: []*permission.SetPermission{
			{
				PermissionId: 1,
				Modify:       true,
			},
		},
		DeleteId: []uint32{2},
	}

	sqlMockAdmin.ExpectBegin()

	sqlMockAdmin.ExpectExec("DELETE FROM `GMPerm` WHERE gm_id = ? AND perm_id IN (?)").
		WithArgs(request.GetAdminId(), request.GetDeleteId()[0]).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockAdmin.ExpectRollback()

	l := NewSetAdminPermissionByUserLogic(ctx, svcCtx)
	resp, err := l.SetAdminPermissionByUser(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockAdmin.ExpectationsWereMet())
}

func TestSetAdminPermissionByUser_Save_DatabaseError(t *testing.T) {
	request := permission.SetAdminPermissionByUserRequest{
		AdminId: 11579,
		SetPermission: []*permission.SetPermission{
			{
				PermissionId: 1,
				Modify:       true,
			},
		},
		DeleteId: []uint32{2},
	}

	sqlMockAdmin.ExpectBegin()

	sqlMockAdmin.ExpectExec("DELETE FROM `GMPerm` WHERE gm_id = ? AND perm_id IN (?)").
		WithArgs(request.GetAdminId(), request.GetDeleteId()[0]).
		WillReturnResult(sqlmock.NewResult(0, 1))

	sqlMockAdmin.ExpectExec("INSERT INTO `GMPerm` (`gm_id`,`perm_id`,`favorite`,`favorite_sort`,`visited_at`,`modify`) VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE `modify`=VALUES(`modify`)").
		WithArgs(request.GetAdminId(), request.GetSetPermission()[0].GetPermissionId(), 0, nil, nil, request.GetSetPermission()[0].GetModify()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockAdmin.ExpectRollback()

	l := NewSetAdminPermissionByUserLogic(ctx, svcCtx)
	resp, err := l.SetAdminPermissionByUser(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockAdmin.ExpectationsWereMet())
}
