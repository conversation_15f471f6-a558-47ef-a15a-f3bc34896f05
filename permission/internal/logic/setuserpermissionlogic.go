package logic

import (
	"context"

	"gbh/errorx"
	"gbh/permission/internal/svc"
	"gbh/proto/permission"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SetUserPermissionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

type UserPerm struct {
	Domain uint32 `json:"domain"`
	UserID uint32 `json:"user_id"`
	RoleID uint32 `json:"role_id"`
	PermID uint32 `json:"perm_id"`
	Enable bool   `json:"enable"`
	Modify bool   `json:"modify"`
}

func NewSetUserPermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserPermissionLogic {
	return &SetUserPermissionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SetUserPermissionLogic) SetUserPermission(in *permission.SetUserPermissionRequest) (*permission.EmptyResponse, error) {
	err := l.svcCtx.PermissionDB.Transaction(func(tx *gorm.DB) error {
		for _, v := range in.GetUserPermissions() {
			queryErr := tx.Table("UserPerm").
				Where("domain", v.GetDomain()).Where("user_id", v.GetUserId()).Where("role_id", v.GetRoleId()).Where("perm_id", v.GetPermissionId()).
				Assign(map[string]interface{}{
					"Enable": v.GetEnable(),
					"Modify": v.GetModify(),
				}).FirstOrCreate(&UserPerm{}).Error

			if queryErr != nil {
				return errorx.DatabaseError
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &permission.EmptyResponse{}, nil
}
