syntax = "proto3";

package permission;
option go_package = "proto/permission";

message BoolValue { bool value = 1; }
message Uint32Value { uint32 value = 1; }

message GetVisitedHistoryRequest { uint32 admin_id = 1; }

message GetVisitedHistoryResponse {
  repeated VisitedHistoryDetail visited_history_detail = 1;
}

message VisitedHistoryDetail {
  uint32 id = 1;
  string dict = 2;
  bool is_ai = 3;
  bool is_new = 4;
}

message AdminControllerPermissionIDsRequest { string controller_name = 1; }

message AdminControllerPermissionIDsResponse {
  repeated uint32 permission_id = 1;
}

message AdminPermissionsRequest {
  repeated uint32 admin_id = 1;
  repeated uint32 permission_id = 2;
  BoolValue enable = 3;
  BoolValue favorite = 4;
  BoolValue modify = 5;
}

message AdminPermissionsResponse {
  repeated AdminPermissionInfo permission_info = 1;
}

message AdminPermissionInfo {
  uint32 admin_id = 1;
  uint32 permission_id = 2;
  bool enable = 3;
  bool favorite = 4;
  uint32 favorite_sort = 5;
  bool modify = 6;
}

message VisitedRequest {
  uint32 admin_id = 1;
  uint32 permission_id = 2;
}

message EmptyResponse {}

message GetAdminPermissionEnabledRequest { BoolValue enable = 1; }

message AdminPermissionEnabled {
  uint32 id = 1;
  uint32 parent_id = 2;
  string type = 3;
  string name = 4;
  uint32 weight = 5;
  string note = 6;
  string dict = 7;
  string rd3_dict = 8;
  bool partial = 9;
  bool modify = 10;
  bool old_perm = 11;
  bool child = 12;
  bool enable = 13;
}

message GetAdminPermissionEnabledResponse {
  repeated AdminPermissionEnabled menu = 1;
  repeated AdminPermissionEnabled special = 2;
  repeated AdminPermissionEnabled general = 3;
}

message AdminPermissionInfoListRequest {
  repeated uint32 permission_id = 1;
  repeated uint32 parent_id = 2;
  repeated string type = 3;
  repeated string name = 4;
  BoolValue enable = 5;
  Uint32Value weight = 6;
  BoolValue partial = 7;
  BoolValue modify = 8;
  BoolValue old_perm = 9;
  BoolValue maintenance = 10;
}

message AdminPermissionInfoListResponse {
  repeated PermissionListInfo permission_info = 1;
}

message PermissionListInfo {
  uint32 permission_id = 1;
  uint32 parent_id = 2;
  string type = 3;
  string name = 4;
  bool enable = 5;
  uint32 weight = 6;
  string note = 7;
  string dict = 8;
  string rd3_dict = 9;
  uint32 sort = 10;
  bool partial = 11;
  bool modify = 12;
  bool old_perm = 13;
  bool maintenance = 14;
  bool domain_only = 15;
  bool top_sub_domain = 16;
  bool open_new = 17;
  bool strict = 18;
  bool modifiable = 19;
  bool hierarchy_perm = 20;
  PermissionExtra extra = 21;
  repeated PermissionRolePerm role_perm = 22;
  repeated uint32 role_settable = 23;
}

message PermissionExtra {
  string icon = 1;
  string file = 2;
  string host = 3;
  string qstr = 4;
  string route = 5;
  string conditions = 6;
  string api_name = 7;
  string api_type = 8;
}

message PermissionRolePerm {
  uint32 role_id = 1;
  bool view = 2;
  bool modify = 3;
}

message EmptyRequest {}

message GetUserPermissionAffectListResponse {
  map<uint32, PermissionAffectList> list = 1;
}

message PermissionAffectList {
  PermissionAffectListData data = 1;
  uint32 count = 2;
}

message PermissionAffectListData {
  map<uint32, PermissionAffectData> menu = 1;
  map<uint32, PermissionAffectData> special = 2;
  repeated PermissionAffectData other = 3;
}

message PermissionAffectData {
  uint32 id = 1;
  uint32 parent = 2;
  string type = 3;
  string name = 4;
  string dict = 5;
  uint32 affect_id = 6;
  string note = 7;
  uint32 sort = 8;
  repeated PermissionAffectData child = 9;
}

message UserPermissionConfigListRequest {
  repeated uint32 permission_id = 1;
  uint32 parent_id = 2;
  BoolValue enable = 3;
  BoolValue hierarchy_perm = 4;
  repeated string type = 5;
  repeated string name = 6;
}

message UserPermissionConfigListResponse {
  repeated UserPermissionConfig permission_config = 1;
}

message UserPermissionConfig {
  uint32 permission_id = 1;
  uint32 parent_id = 2;
  string type = 3;
  string name = 4;
  string note = 5;
  string dict = 6;
  string rd3_dict = 7;
  bool enable = 8;
  uint32 sort = 9;
  bool domain_only = 10;
  bool top_sub_domain = 11;
  bool open_new = 12;
  bool strict = 13;
  bool modifiable = 14;
  bool old_perm = 15;
  bool hierarchy_perm = 16;
  bool maintenance = 17;
  PermissionExtra extra = 18;
  repeated PermissionRolePerm role_perm = 19;
  repeated uint32 role_settable = 20;
}
message UserPermissionEnabledRequest { repeated uint32 permission_id = 1; }

message UserPermissionEnabledResponse { repeated uint32 permission_id = 1; }

message UserAllPermissionRequest {
  repeated uint32 all_parents = 1;
  uint32 user_id = 2;
  uint32 role = 3;
  bool sub = 4;
  repeated uint32 permission_id = 5;
  BoolValue enable = 6;
}

message UserAllPermissionResponse { repeated UserPermission data = 1; }

message UserPermission {
  uint32 user_id = 1;
  uint32 role_id = 2;
  uint32 permission_id = 3;
  bool enable = 4;
  bool modify = 5;
}

message DeleteUserPermissionByUserIDRequest { uint32 user_id = 1; }

message UserPermListRequest {
  repeated uint32 user_id = 1;
  repeated uint32 permission_id = 2;
  repeated uint32 role_id = 3;
  BoolValue enable = 4;
}

message UserPermListResponse { repeated UserPerm data = 1; }

message UserPerm {
  uint32 domain = 1;
  uint32 user_id = 2;
  uint32 role_id = 3;
  uint32 permission_id = 4;
  bool enable = 5;
  bool modify = 6;
}

message DeleteUserDepartmentByUserIDRequest { uint32 user_id = 1; }

message SetUserPermissionRequest { repeated UserPerm user_permissions = 1; }

message SetAdminPermissionByUserRequest {
  uint32 admin_id = 1;
  repeated SetPermission set_permission = 2;
  repeated uint32 delete_id = 3;
}

message SetPermission {
  uint32 permission_id = 1;
  bool modify = 2;
}

message SetAdminPermissionRequest { repeated AdminPermission list = 1; }

message AdminPermission {
  uint32 admin_id = 1;
  uint32 permission_id = 2;
  BoolValue modify = 3;
}

service Permission {
  rpc GetVisitedHistory(GetVisitedHistoryRequest)
      returns (GetVisitedHistoryResponse);
  rpc AdminControllerPermissionIDs(AdminControllerPermissionIDsRequest)
      returns (AdminControllerPermissionIDsResponse);
  rpc AdminPermissions(AdminPermissionsRequest)
      returns (AdminPermissionsResponse);
  rpc ModifyVisited(VisitedRequest) returns (EmptyResponse);
  rpc GetAdminPermissionEnabled(GetAdminPermissionEnabledRequest)
      returns (GetAdminPermissionEnabledResponse);
  rpc AdminPermissionInfoList(AdminPermissionInfoListRequest)
      returns (AdminPermissionInfoListResponse);
  rpc GetUserPermissionAffectList(EmptyRequest)
      returns (GetUserPermissionAffectListResponse);
  rpc UserPermissionConfigList(UserPermissionConfigListRequest)
      returns (UserPermissionConfigListResponse);
  rpc GetUserPermissionEnabled(UserPermissionEnabledRequest)
      returns (UserPermissionEnabledResponse);
  rpc GetUserAllPermission(UserAllPermissionRequest)
      returns (UserAllPermissionResponse);
  rpc DeleteUserPermissionByUserID(DeleteUserPermissionByUserIDRequest)
      returns (EmptyResponse);
  rpc UserPermList(UserPermListRequest) returns (UserPermListResponse);
  rpc DeleteUserDepartmentByUserID(DeleteUserDepartmentByUserIDRequest)
      returns (EmptyResponse);
  rpc SetUserPermission(SetUserPermissionRequest) returns (EmptyResponse);
  rpc SetAdminPermissionByUser(SetAdminPermissionByUserRequest)
      returns (EmptyResponse);
  rpc SetAdminPermission(SetAdminPermissionRequest) returns (EmptyResponse);
}
