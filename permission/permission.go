package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/logger"
	"gbh/permission/internal/config"
	"gbh/permission/internal/server"
	"gbh/permission/internal/svc"
	"gbh/proto/permission"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/permission.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	permissionDB, err := database.New(c.PermissionDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	adminDB, err := database.New(c.Admin<PERSON>onf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	userDB, err := database.New(c.UserDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		PermissionDB: permissionDB,
		AdminDB:      adminDB,
		UserDB:       userDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		permission.RegisterPermissionServer(grpcServer, server.NewPermissionServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	// 設定統計與日誌攔截器參數
	statConf := rpcserver.StatConf{
		MetricsName:          "permission",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
