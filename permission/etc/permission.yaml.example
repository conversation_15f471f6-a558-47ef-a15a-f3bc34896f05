Name: permission.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: permission.rpc

PermissionDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PermissionDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PermissionDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

AdminDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: AdminDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: AdminDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

UserDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: UserDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: UserDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

Middlewares:
  Stat: false
