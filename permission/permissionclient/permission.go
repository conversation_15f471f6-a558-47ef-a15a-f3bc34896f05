// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.7
// Source: permission.proto

package permissionclient

import (
	"context"

	"gbh/proto/permission"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AdminControllerPermissionIDsRequest  = permission.AdminControllerPermissionIDsRequest
	AdminControllerPermissionIDsResponse = permission.AdminControllerPermissionIDsResponse
	AdminPermission                      = permission.AdminPermission
	AdminPermissionEnabled               = permission.AdminPermissionEnabled
	AdminPermissionInfo                  = permission.AdminPermissionInfo
	AdminPermissionInfoListRequest       = permission.AdminPermissionInfoListRequest
	AdminPermissionInfoListResponse      = permission.AdminPermissionInfoListResponse
	AdminPermissionsRequest              = permission.AdminPermissionsRequest
	AdminPermissionsResponse             = permission.AdminPermissionsResponse
	BoolValue                            = permission.BoolValue
	DeleteUserDepartmentByUserIDRequest  = permission.DeleteUserDepartmentByUserIDRequest
	DeleteUserPermissionByUserIDRequest  = permission.DeleteUserPermissionByUserIDRequest
	EmptyRequest                         = permission.EmptyRequest
	EmptyResponse                        = permission.EmptyResponse
	GetAdminPermissionEnabledRequest     = permission.GetAdminPermissionEnabledRequest
	GetAdminPermissionEnabledResponse    = permission.GetAdminPermissionEnabledResponse
	GetUserPermissionAffectListResponse  = permission.GetUserPermissionAffectListResponse
	GetVisitedHistoryRequest             = permission.GetVisitedHistoryRequest
	GetVisitedHistoryResponse            = permission.GetVisitedHistoryResponse
	PermissionAffectData                 = permission.PermissionAffectData
	PermissionAffectList                 = permission.PermissionAffectList
	PermissionAffectListData             = permission.PermissionAffectListData
	PermissionExtra                      = permission.PermissionExtra
	PermissionListInfo                   = permission.PermissionListInfo
	PermissionRolePerm                   = permission.PermissionRolePerm
	SetAdminPermissionByUserRequest      = permission.SetAdminPermissionByUserRequest
	SetAdminPermissionRequest            = permission.SetAdminPermissionRequest
	SetPermission                        = permission.SetPermission
	SetUserPermissionRequest             = permission.SetUserPermissionRequest
	Uint32Value                          = permission.Uint32Value
	UserAllPermissionRequest             = permission.UserAllPermissionRequest
	UserAllPermissionResponse            = permission.UserAllPermissionResponse
	UserPerm                             = permission.UserPerm
	UserPermListRequest                  = permission.UserPermListRequest
	UserPermListResponse                 = permission.UserPermListResponse
	UserPermission                       = permission.UserPermission
	UserPermissionConfig                 = permission.UserPermissionConfig
	UserPermissionConfigListRequest      = permission.UserPermissionConfigListRequest
	UserPermissionConfigListResponse     = permission.UserPermissionConfigListResponse
	UserPermissionEnabledRequest         = permission.UserPermissionEnabledRequest
	UserPermissionEnabledResponse        = permission.UserPermissionEnabledResponse
	VisitedHistoryDetail                 = permission.VisitedHistoryDetail
	VisitedRequest                       = permission.VisitedRequest

	Permission interface {
		GetVisitedHistory(ctx context.Context, in *GetVisitedHistoryRequest, opts ...grpc.CallOption) (*GetVisitedHistoryResponse, error)
		AdminControllerPermissionIDs(ctx context.Context, in *AdminControllerPermissionIDsRequest, opts ...grpc.CallOption) (*AdminControllerPermissionIDsResponse, error)
		AdminPermissions(ctx context.Context, in *AdminPermissionsRequest, opts ...grpc.CallOption) (*AdminPermissionsResponse, error)
		ModifyVisited(ctx context.Context, in *VisitedRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetAdminPermissionEnabled(ctx context.Context, in *GetAdminPermissionEnabledRequest, opts ...grpc.CallOption) (*GetAdminPermissionEnabledResponse, error)
		AdminPermissionInfoList(ctx context.Context, in *AdminPermissionInfoListRequest, opts ...grpc.CallOption) (*AdminPermissionInfoListResponse, error)
		GetUserPermissionAffectList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserPermissionAffectListResponse, error)
		UserPermissionConfigList(ctx context.Context, in *UserPermissionConfigListRequest, opts ...grpc.CallOption) (*UserPermissionConfigListResponse, error)
		GetUserPermissionEnabled(ctx context.Context, in *UserPermissionEnabledRequest, opts ...grpc.CallOption) (*UserPermissionEnabledResponse, error)
		GetUserAllPermission(ctx context.Context, in *UserAllPermissionRequest, opts ...grpc.CallOption) (*UserAllPermissionResponse, error)
		DeleteUserPermissionByUserID(ctx context.Context, in *DeleteUserPermissionByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		UserPermList(ctx context.Context, in *UserPermListRequest, opts ...grpc.CallOption) (*UserPermListResponse, error)
		DeleteUserDepartmentByUserID(ctx context.Context, in *DeleteUserDepartmentByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetUserPermission(ctx context.Context, in *SetUserPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetAdminPermissionByUser(ctx context.Context, in *SetAdminPermissionByUserRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetAdminPermission(ctx context.Context, in *SetAdminPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	}

	defaultPermission struct {
		cli zrpc.Client
	}
)

func NewPermission(cli zrpc.Client) Permission {
	return &defaultPermission{
		cli: cli,
	}
}

func (m *defaultPermission) GetVisitedHistory(ctx context.Context, in *GetVisitedHistoryRequest, opts ...grpc.CallOption) (*GetVisitedHistoryResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.GetVisitedHistory(ctx, in, opts...)
}

func (m *defaultPermission) AdminControllerPermissionIDs(ctx context.Context, in *AdminControllerPermissionIDsRequest, opts ...grpc.CallOption) (*AdminControllerPermissionIDsResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.AdminControllerPermissionIDs(ctx, in, opts...)
}

func (m *defaultPermission) AdminPermissions(ctx context.Context, in *AdminPermissionsRequest, opts ...grpc.CallOption) (*AdminPermissionsResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.AdminPermissions(ctx, in, opts...)
}

func (m *defaultPermission) ModifyVisited(ctx context.Context, in *VisitedRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.ModifyVisited(ctx, in, opts...)
}

func (m *defaultPermission) GetAdminPermissionEnabled(ctx context.Context, in *GetAdminPermissionEnabledRequest, opts ...grpc.CallOption) (*GetAdminPermissionEnabledResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.GetAdminPermissionEnabled(ctx, in, opts...)
}

func (m *defaultPermission) AdminPermissionInfoList(ctx context.Context, in *AdminPermissionInfoListRequest, opts ...grpc.CallOption) (*AdminPermissionInfoListResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.AdminPermissionInfoList(ctx, in, opts...)
}

func (m *defaultPermission) GetUserPermissionAffectList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserPermissionAffectListResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.GetUserPermissionAffectList(ctx, in, opts...)
}

func (m *defaultPermission) UserPermissionConfigList(ctx context.Context, in *UserPermissionConfigListRequest, opts ...grpc.CallOption) (*UserPermissionConfigListResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.UserPermissionConfigList(ctx, in, opts...)
}

func (m *defaultPermission) GetUserPermissionEnabled(ctx context.Context, in *UserPermissionEnabledRequest, opts ...grpc.CallOption) (*UserPermissionEnabledResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.GetUserPermissionEnabled(ctx, in, opts...)
}

func (m *defaultPermission) GetUserAllPermission(ctx context.Context, in *UserAllPermissionRequest, opts ...grpc.CallOption) (*UserAllPermissionResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.GetUserAllPermission(ctx, in, opts...)
}

func (m *defaultPermission) DeleteUserPermissionByUserID(ctx context.Context, in *DeleteUserPermissionByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.DeleteUserPermissionByUserID(ctx, in, opts...)
}

func (m *defaultPermission) UserPermList(ctx context.Context, in *UserPermListRequest, opts ...grpc.CallOption) (*UserPermListResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.UserPermList(ctx, in, opts...)
}

func (m *defaultPermission) DeleteUserDepartmentByUserID(ctx context.Context, in *DeleteUserDepartmentByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.DeleteUserDepartmentByUserID(ctx, in, opts...)
}

func (m *defaultPermission) SetUserPermission(ctx context.Context, in *SetUserPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.SetUserPermission(ctx, in, opts...)
}

func (m *defaultPermission) SetAdminPermissionByUser(ctx context.Context, in *SetAdminPermissionByUserRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.SetAdminPermissionByUser(ctx, in, opts...)
}

func (m *defaultPermission) SetAdminPermission(ctx context.Context, in *SetAdminPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := permission.NewPermissionClient(m.cli.Conn())
	return client.SetAdminPermission(ctx, in, opts...)
}
