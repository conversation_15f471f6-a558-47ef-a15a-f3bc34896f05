package rpcserver

import (
	"context"
	"testing"
	"time"

	"gbh/errorx"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var mockRequest = "test request"

func TestLoggingInterceptor_Success(t *testing.T) {
	conf := StatConf{
		MetricsName:          "test_metrics",
		IgnoreContentMethods: []string{},
	}

	interceptor := LoggingInterceptor(conf)

	// 模擬 gRPC handler
	handler := func(_ context.Context, _ interface{}) (interface{}, error) {
		return "success", nil
	}

	// 模擬 gRPC 請求
	ctx := context.Background()
	req := mockRequest
	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.Service/TestMethod",
	}

	// 執行 interceptor
	resp, err := interceptor(ctx, req, info, handler)

	// 驗證結果
	assert.NoError(t, err)
	assert.Equal(t, "success", resp)
}

func TestLoggingInterceptor_Error(t *testing.T) {
	conf := StatConf{
		MetricsName:          "test_metrics",
		IgnoreContentMethods: []string{},
	}

	interceptor := LoggingInterceptor(conf)

	// 模擬 gRPC handler 返回錯誤
	handler := func(_ context.Context, _ interface{}) (interface{}, error) {
		return nil, errorx.New(int32(codes.Internal), "internal error")
	}

	// 模擬 gRPC 請求
	ctx := context.Background()
	req := mockRequest
	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.Service/TestMethod",
	}

	// 執行 interceptor
	resp, err := interceptor(ctx, req, info, handler)

	// 驗證結果
	assert.Nil(t, resp)
	assert.Error(t, err)
	assert.Equal(t, codes.Internal, status.Code(err))
	assert.Equal(t, "internal error", status.Convert(err).Message())
}

func TestLoggingInterceptor_SlowCall(t *testing.T) {
	conf := StatConf{
		MetricsName:          "test_metrics",
		IgnoreContentMethods: []string{},
	}

	interceptor := LoggingInterceptor(conf)

	// 模擬 gRPC handler，模擬慢調用
	handler := func(_ context.Context, _ interface{}) (interface{}, error) {
		time.Sleep(600 * time.Millisecond) // 模擬慢調用
		return "slow response", nil
	}

	// 模擬 gRPC 請求
	ctx := context.Background()
	req := mockRequest
	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.Service/TestMethod",
	}

	// 執行 interceptor
	resp, err := interceptor(ctx, req, info, handler)

	// 驗證結果
	assert.NoError(t, err)
	assert.Equal(t, "slow response", resp)
}

func TestLoggingInterceptor_IgnoreMethod(t *testing.T) {
	conf := StatConf{
		MetricsName:          "test_metrics",
		IgnoreContentMethods: []string{"/test.Service/IgnoredMethod"},
	}

	interceptor := LoggingInterceptor(conf)

	// 模擬 gRPC handler
	handler := func(_ context.Context, _ interface{}) (interface{}, error) {
		return "ignored response", nil
	}

	// 模擬 gRPC 請求
	ctx := context.Background()
	req := mockRequest
	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.Service/IgnoredMethod",
	}

	// 執行 interceptor
	resp, err := interceptor(ctx, req, info, handler)

	// 驗證結果
	assert.NoError(t, err)
	assert.Equal(t, "ignored response", resp)
}
