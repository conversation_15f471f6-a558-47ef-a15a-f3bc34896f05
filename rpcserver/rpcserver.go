package rpcserver

import (
	"context"
	"errors"
	"time"

	"gbh/errorx"
	"gbh/utils/logger"

	"github.com/zeromicro/go-zero/core/collection"
	"github.com/zeromicro/go-zero/core/stat"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var defaultSlowThreshold = 500 * time.Millisecond

type StatConf struct {
	MetricsName          string
	IgnoreContentMethods []string `json:",optional"`
}

func LoggingInterceptor(conf StatConf) grpc.UnaryServerInterceptor {
	metrics := stat.NewMetrics(conf.MetricsName)

	staticIgnoreMethods := collection.NewSet()
	staticIgnoreMethods.AddStr(conf.IgnoreContentMethods...)

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		tags := []string{conf.MetricsName}

		startTime := time.Now()
		resp, err := handler(ctx, req)
		duration := time.Since(startTime)

		// 收集統計資訊
		metrics.Add(stat.Task{
			Duration: duration,
		})

		// 錯誤轉換
		if err != nil {
			var errx errorx.Errorx
			if errors.As(err, &errx) {
				err = status.Error(codes.Code(errx.Code), errx.Message)
			}
		}

		if !staticIgnoreMethods.Contains(info.FullMethod) {
			switch {
			case err != nil:
				logger.WithGRPC(ctx, tags, info.FullMethod, req, resp, err, duration, "severe").Infof("[gRPC] call failed")
			case duration > defaultSlowThreshold:
				logger.WithGRPC(ctx, tags, info.FullMethod, req, resp, err, duration, "warning").Infof("[gRPC] slow call")
			default:
				logger.WithGRPC(ctx, tags, info.FullMethod, req, resp, err, duration, "info").Info()
			}
		}

		return resp, err
	}
}
