package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/lang/internal/config"
	"gbh/lang/internal/server"
	"gbh/lang/internal/svc"
	"gbh/logger"
	"gbh/proto/lang"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/lang.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)
	if logErr != nil {
		log.Fatal(logErr)
	}

	commonDB, err := database.New(c.Common<PERSON>, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		CommonDB: commonDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		lang.RegisterLangServer(grpcServer, server.NewLangServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "lang",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
