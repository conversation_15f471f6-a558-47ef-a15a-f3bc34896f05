// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.19.4
// source: monitor.proto

package monitor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{0}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type Uint64Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint64 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint64Value) Reset() {
	*x = Uint64Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint64Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint64Value) ProtoMessage() {}

func (x *Uint64Value) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint64Value.ProtoReflect.Descriptor instead.
func (*Uint64Value) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{1}
}

func (x *Uint64Value) GetValue() uint64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type BoolValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value bool `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{2}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{3}
}

type GetConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site      string   `protobuf:"bytes,1,opt,name=site,proto3" json:"site,omitempty"`
	GroupId   uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId    uint32   `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId    uint32   `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKinds []uint32 `protobuf:"varint,5,rep,packed,name=game_kinds,json=gameKinds,proto3" json:"game_kinds,omitempty"`
	MonitorId uint32   `protobuf:"varint,6,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
}

func (x *GetConditionRequest) Reset() {
	*x = GetConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConditionRequest) ProtoMessage() {}

func (x *GetConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConditionRequest.ProtoReflect.Descriptor instead.
func (*GetConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{4}
}

func (x *GetConditionRequest) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *GetConditionRequest) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetConditionRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetConditionRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetConditionRequest) GetGameKinds() []uint32 {
	if x != nil {
		return x.GameKinds
	}
	return nil
}

func (x *GetConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

type SubCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConditionType string `protobuf:"bytes,1,opt,name=condition_type,json=conditionType,proto3" json:"condition_type,omitempty"`
	Win           uint64 `protobuf:"varint,2,opt,name=win,proto3" json:"win,omitempty"`
	Lose          uint64 `protobuf:"varint,3,opt,name=lose,proto3" json:"lose,omitempty"`
	Day           uint32 `protobuf:"varint,4,opt,name=day,proto3" json:"day,omitempty"`
	Percent       uint32 `protobuf:"varint,5,opt,name=percent,proto3" json:"percent,omitempty"`
	BetAmount     uint64 `protobuf:"varint,6,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
}

func (x *SubCondition) Reset() {
	*x = SubCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubCondition) ProtoMessage() {}

func (x *SubCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubCondition.ProtoReflect.Descriptor instead.
func (*SubCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{5}
}

func (x *SubCondition) GetConditionType() string {
	if x != nil {
		return x.ConditionType
	}
	return ""
}

func (x *SubCondition) GetWin() uint64 {
	if x != nil {
		return x.Win
	}
	return 0
}

func (x *SubCondition) GetLose() uint64 {
	if x != nil {
		return x.Lose
	}
	return 0
}

func (x *SubCondition) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *SubCondition) GetPercent() uint32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *SubCondition) GetBetAmount() uint64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

type MonitorCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId uint32          `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	Site      string          `protobuf:"bytes,2,opt,name=site,proto3" json:"site,omitempty"`
	GroupId   uint32          `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId    uint32          `protobuf:"varint,4,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId    uint32          `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind  []uint32        `protobuf:"varint,6,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Condition []*SubCondition `protobuf:"bytes,7,rep,name=condition,proto3" json:"condition,omitempty"`
}

func (x *MonitorCondition) Reset() {
	*x = MonitorCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorCondition) ProtoMessage() {}

func (x *MonitorCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorCondition.ProtoReflect.Descriptor instead.
func (*MonitorCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{6}
}

func (x *MonitorCondition) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *MonitorCondition) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *MonitorCondition) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MonitorCondition) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *MonitorCondition) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MonitorCondition) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

func (x *MonitorCondition) GetCondition() []*SubCondition {
	if x != nil {
		return x.Condition
	}
	return nil
}

type GetConditionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Monitors []*MonitorCondition `protobuf:"bytes,1,rep,name=monitors,proto3" json:"monitors,omitempty"`
}

func (x *GetConditionResponse) Reset() {
	*x = GetConditionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConditionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConditionResponse) ProtoMessage() {}

func (x *GetConditionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConditionResponse.ProtoReflect.Descriptor instead.
func (*GetConditionResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{7}
}

func (x *GetConditionResponse) GetMonitors() []*MonitorCondition {
	if x != nil {
		return x.Monitors
	}
	return nil
}

type CreateConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Condition    *CreateCondition    `protobuf:"bytes,1,opt,name=condition,proto3" json:"condition,omitempty"`
	SubCondition *CreateSubCondition `protobuf:"bytes,2,opt,name=sub_condition,json=subCondition,proto3" json:"sub_condition,omitempty"`
	GameKinds    []uint32            `protobuf:"varint,3,rep,packed,name=game_kinds,json=gameKinds,proto3" json:"game_kinds,omitempty"`
}

func (x *CreateConditionRequest) Reset() {
	*x = CreateConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConditionRequest) ProtoMessage() {}

func (x *CreateConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConditionRequest.ProtoReflect.Descriptor instead.
func (*CreateConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{8}
}

func (x *CreateConditionRequest) GetCondition() *CreateCondition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *CreateConditionRequest) GetSubCondition() *CreateSubCondition {
	if x != nil {
		return x.SubCondition
	}
	return nil
}

func (x *CreateConditionRequest) GetGameKinds() []uint32 {
	if x != nil {
		return x.GameKinds
	}
	return nil
}

type CreateCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site    string `protobuf:"bytes,1,opt,name=site,proto3" json:"site,omitempty"`
	GroupId uint32 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId  uint32 `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId  uint32 `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *CreateCondition) Reset() {
	*x = CreateCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCondition) ProtoMessage() {}

func (x *CreateCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCondition.ProtoReflect.Descriptor instead.
func (*CreateCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{9}
}

func (x *CreateCondition) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *CreateCondition) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *CreateCondition) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *CreateCondition) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CreateSubCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Win        uint64 `protobuf:"varint,1,opt,name=win,proto3" json:"win,omitempty"`
	Lose       uint64 `protobuf:"varint,2,opt,name=lose,proto3" json:"lose,omitempty"`
	MemberWin  uint64 `protobuf:"varint,3,opt,name=member_win,json=memberWin,proto3" json:"member_win,omitempty"`
	MemberLose uint64 `protobuf:"varint,4,opt,name=member_lose,json=memberLose,proto3" json:"member_lose,omitempty"`
	Day        uint32 `protobuf:"varint,5,opt,name=day,proto3" json:"day,omitempty"`
	Percent    uint32 `protobuf:"varint,6,opt,name=percent,proto3" json:"percent,omitempty"`
	BetAmount  uint64 `protobuf:"varint,7,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
}

func (x *CreateSubCondition) Reset() {
	*x = CreateSubCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubCondition) ProtoMessage() {}

func (x *CreateSubCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubCondition.ProtoReflect.Descriptor instead.
func (*CreateSubCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{10}
}

func (x *CreateSubCondition) GetWin() uint64 {
	if x != nil {
		return x.Win
	}
	return 0
}

func (x *CreateSubCondition) GetLose() uint64 {
	if x != nil {
		return x.Lose
	}
	return 0
}

func (x *CreateSubCondition) GetMemberWin() uint64 {
	if x != nil {
		return x.MemberWin
	}
	return 0
}

func (x *CreateSubCondition) GetMemberLose() uint64 {
	if x != nil {
		return x.MemberLose
	}
	return 0
}

func (x *CreateSubCondition) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *CreateSubCondition) GetPercent() uint32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *CreateSubCondition) GetBetAmount() uint64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

type DeleteConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId uint32 `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	GameKind  uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *DeleteConditionRequest) Reset() {
	*x = DeleteConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConditionRequest) ProtoMessage() {}

func (x *DeleteConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConditionRequest.ProtoReflect.Descriptor instead.
func (*DeleteConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *DeleteConditionRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type GetSubConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site         string           `protobuf:"bytes,1,opt,name=site,proto3" json:"site,omitempty"`
	GroupId      uint32           `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId       uint32           `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId       uint32           `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	MonitorId    uint32           `protobuf:"varint,5,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	SubCondition *GetSubCondition `protobuf:"bytes,6,opt,name=sub_condition,json=subCondition,proto3" json:"sub_condition,omitempty"`
}

func (x *GetSubConditionRequest) Reset() {
	*x = GetSubConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubConditionRequest) ProtoMessage() {}

func (x *GetSubConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubConditionRequest.ProtoReflect.Descriptor instead.
func (*GetSubConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{12}
}

func (x *GetSubConditionRequest) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *GetSubConditionRequest) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetSubConditionRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetSubConditionRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetSubConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *GetSubConditionRequest) GetSubCondition() *GetSubCondition {
	if x != nil {
		return x.SubCondition
	}
	return nil
}

type GetSubCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Win        *Uint64Value `protobuf:"bytes,1,opt,name=win,proto3" json:"win,omitempty"`
	Lose       *Uint64Value `protobuf:"bytes,2,opt,name=lose,proto3" json:"lose,omitempty"`
	MemberWin  *Uint64Value `protobuf:"bytes,3,opt,name=member_win,json=memberWin,proto3" json:"member_win,omitempty"`
	MemberLose *Uint64Value `protobuf:"bytes,4,opt,name=member_lose,json=memberLose,proto3" json:"member_lose,omitempty"`
	Day        *Uint32Value `protobuf:"bytes,5,opt,name=day,proto3" json:"day,omitempty"`
	Percent    *Uint32Value `protobuf:"bytes,6,opt,name=percent,proto3" json:"percent,omitempty"`
	BetAmount  *Uint64Value `protobuf:"bytes,7,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
}

func (x *GetSubCondition) Reset() {
	*x = GetSubCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubCondition) ProtoMessage() {}

func (x *GetSubCondition) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubCondition.ProtoReflect.Descriptor instead.
func (*GetSubCondition) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{13}
}

func (x *GetSubCondition) GetWin() *Uint64Value {
	if x != nil {
		return x.Win
	}
	return nil
}

func (x *GetSubCondition) GetLose() *Uint64Value {
	if x != nil {
		return x.Lose
	}
	return nil
}

func (x *GetSubCondition) GetMemberWin() *Uint64Value {
	if x != nil {
		return x.MemberWin
	}
	return nil
}

func (x *GetSubCondition) GetMemberLose() *Uint64Value {
	if x != nil {
		return x.MemberLose
	}
	return nil
}

func (x *GetSubCondition) GetDay() *Uint32Value {
	if x != nil {
		return x.Day
	}
	return nil
}

func (x *GetSubCondition) GetPercent() *Uint32Value {
	if x != nil {
		return x.Percent
	}
	return nil
}

func (x *GetSubCondition) GetBetAmount() *Uint64Value {
	if x != nil {
		return x.BetAmount
	}
	return nil
}

type GetSubConditionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId uint32   `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	Site      string   `protobuf:"bytes,2,opt,name=site,proto3" json:"site,omitempty"`
	GroupId   uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId    uint32   `protobuf:"varint,4,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId    uint32   `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind  []uint32 `protobuf:"varint,6,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GetSubConditionResponse) Reset() {
	*x = GetSubConditionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubConditionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubConditionResponse) ProtoMessage() {}

func (x *GetSubConditionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubConditionResponse.ProtoReflect.Descriptor instead.
func (*GetSubConditionResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{14}
}

func (x *GetSubConditionResponse) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *GetSubConditionResponse) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *GetSubConditionResponse) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetSubConditionResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetSubConditionResponse) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetSubConditionResponse) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

type UpdateConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId         uint32   `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	GameKinds         []uint32 `protobuf:"varint,2,rep,packed,name=game_kinds,json=gameKinds,proto3" json:"game_kinds,omitempty"`
	AffectedMonitorId []uint32 `protobuf:"varint,3,rep,packed,name=affected_monitor_id,json=affectedMonitorId,proto3" json:"affected_monitor_id,omitempty"`
}

func (x *UpdateConditionRequest) Reset() {
	*x = UpdateConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConditionRequest) ProtoMessage() {}

func (x *UpdateConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConditionRequest.ProtoReflect.Descriptor instead.
func (*UpdateConditionRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateConditionRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *UpdateConditionRequest) GetGameKinds() []uint32 {
	if x != nil {
		return x.GameKinds
	}
	return nil
}

func (x *UpdateConditionRequest) GetAffectedMonitorId() []uint32 {
	if x != nil {
		return x.AffectedMonitorId
	}
	return nil
}

type SendTelegramRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	Msg         string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SendTelegramRequest) Reset() {
	*x = SendTelegramRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendTelegramRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTelegramRequest) ProtoMessage() {}

func (x *SendTelegramRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTelegramRequest.ProtoReflect.Descriptor instead.
func (*SendTelegramRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{16}
}

func (x *SendTelegramRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *SendTelegramRequest) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SetPayOffLobbyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorId          uint32   `protobuf:"varint,1,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	GameKind           []uint32 `protobuf:"varint,2,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	InfluenceMonitorId []uint32 `protobuf:"varint,3,rep,packed,name=influence_monitor_id,json=influenceMonitorId,proto3" json:"influence_monitor_id,omitempty"`
}

func (x *SetPayOffLobbyRequest) Reset() {
	*x = SetPayOffLobbyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPayOffLobbyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPayOffLobbyRequest) ProtoMessage() {}

func (x *SetPayOffLobbyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPayOffLobbyRequest.ProtoReflect.Descriptor instead.
func (*SetPayOffLobbyRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{17}
}

func (x *SetPayOffLobbyRequest) GetMonitorId() uint32 {
	if x != nil {
		return x.MonitorId
	}
	return 0
}

func (x *SetPayOffLobbyRequest) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

func (x *SetPayOffLobbyRequest) GetInfluenceMonitorId() []uint32 {
	if x != nil {
		return x.InfluenceMonitorId
	}
	return nil
}

type GetMonitorDBPayoffAlertTableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartDate           string     `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate             string     `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	UserId              uint32     `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	HallId              uint32     `protobuf:"varint,4,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	LobbyId             uint32     `protobuf:"varint,5,opt,name=lobby_id,json=lobbyId,proto3" json:"lobby_id,omitempty"`
	GroupId             uint32     `protobuf:"varint,6,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Category            string     `protobuf:"bytes,7,opt,name=category,proto3" json:"category,omitempty"`
	IsPayoffGreaterThan *BoolValue `protobuf:"bytes,8,opt,name=is_payoff_greater_than,json=isPayoffGreaterThan,proto3" json:"is_payoff_greater_than,omitempty"`
	ConditionType       string     `protobuf:"bytes,9,opt,name=condition_type,json=conditionType,proto3" json:"condition_type,omitempty"`
	Sort                string     `protobuf:"bytes,10,opt,name=sort,proto3" json:"sort,omitempty"`
	Order               string     `protobuf:"bytes,11,opt,name=order,proto3" json:"order,omitempty"`
	Page                uint32     `protobuf:"varint,12,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit           uint32     `protobuf:"varint,13,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetMonitorDBPayoffAlertTableRequest) Reset() {
	*x = GetMonitorDBPayoffAlertTableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonitorDBPayoffAlertTableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonitorDBPayoffAlertTableRequest) ProtoMessage() {}

func (x *GetMonitorDBPayoffAlertTableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonitorDBPayoffAlertTableRequest.ProtoReflect.Descriptor instead.
func (*GetMonitorDBPayoffAlertTableRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{18}
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetLobbyId() uint32 {
	if x != nil {
		return x.LobbyId
	}
	return 0
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetIsPayoffGreaterThan() *BoolValue {
	if x != nil {
		return x.IsPayoffGreaterThan
	}
	return nil
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetConditionType() string {
	if x != nil {
		return x.ConditionType
	}
	return ""
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetMonitorDBPayoffAlertTableRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type GetMonitorDBPayoffAlertTableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*FetchMonitorDBPayoffAlertTable `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination                       `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetMonitorDBPayoffAlertTableResponse) Reset() {
	*x = GetMonitorDBPayoffAlertTableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonitorDBPayoffAlertTableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonitorDBPayoffAlertTableResponse) ProtoMessage() {}

func (x *GetMonitorDBPayoffAlertTableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonitorDBPayoffAlertTableResponse.ProtoReflect.Descriptor instead.
func (*GetMonitorDBPayoffAlertTableResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{19}
}

func (x *GetMonitorDBPayoffAlertTableResponse) GetList() []*FetchMonitorDBPayoffAlertTable {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetMonitorDBPayoffAlertTableResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type FetchMonitorDBPayoffAlertTable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32  `protobuf:"zigzag32,1,opt,name=id,proto3" json:"id,omitempty"`
	GroupId          int32  `protobuf:"zigzag32,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId           int64  `protobuf:"zigzag64,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId           int64  `protobuf:"zigzag64,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	LobbyId          int32  `protobuf:"zigzag32,5,opt,name=lobby_id,json=lobbyId,proto3" json:"lobby_id,omitempty"`
	Category         string `protobuf:"bytes,6,opt,name=category,proto3" json:"category,omitempty"`
	ConditionType    string `protobuf:"bytes,7,opt,name=condition_type,json=conditionType,proto3" json:"condition_type,omitempty"`
	Win              int32  `protobuf:"zigzag32,8,opt,name=win,proto3" json:"win,omitempty"`
	Lost             int32  `protobuf:"zigzag32,9,opt,name=lost,proto3" json:"lost,omitempty"`
	Day              int32  `protobuf:"zigzag32,10,opt,name=day,proto3" json:"day,omitempty"`
	Percent          int32  `protobuf:"zigzag32,11,opt,name=percent,proto3" json:"percent,omitempty"`
	BetAmountSetting int32  `protobuf:"zigzag32,12,opt,name=bet_amount_setting,json=betAmountSetting,proto3" json:"bet_amount_setting,omitempty"`
	Payoff           int32  `protobuf:"zigzag32,13,opt,name=payoff,proto3" json:"payoff,omitempty"`
	BetAmount        int32  `protobuf:"zigzag32,14,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Growth           int32  `protobuf:"zigzag32,15,opt,name=growth,proto3" json:"growth,omitempty"`
	Time             string `protobuf:"bytes,16,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *FetchMonitorDBPayoffAlertTable) Reset() {
	*x = FetchMonitorDBPayoffAlertTable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchMonitorDBPayoffAlertTable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchMonitorDBPayoffAlertTable) ProtoMessage() {}

func (x *FetchMonitorDBPayoffAlertTable) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchMonitorDBPayoffAlertTable.ProtoReflect.Descriptor instead.
func (*FetchMonitorDBPayoffAlertTable) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{20}
}

func (x *FetchMonitorDBPayoffAlertTable) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetHallId() int64 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetLobbyId() int32 {
	if x != nil {
		return x.LobbyId
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *FetchMonitorDBPayoffAlertTable) GetConditionType() string {
	if x != nil {
		return x.ConditionType
	}
	return ""
}

func (x *FetchMonitorDBPayoffAlertTable) GetWin() int32 {
	if x != nil {
		return x.Win
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetLost() int32 {
	if x != nil {
		return x.Lost
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetBetAmountSetting() int32 {
	if x != nil {
		return x.BetAmountSetting
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetPayoff() int32 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetBetAmount() int32 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetGrowth() int32 {
	if x != nil {
		return x.Growth
	}
	return 0
}

func (x *FetchMonitorDBPayoffAlertTable) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   uint32 `protobuf:"varint,2,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Total       uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPage   uint32 `protobuf:"varint,4,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{21}
}

func (x *Pagination) GetCurrentPage() uint32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *Pagination) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Pagination) GetTotalPage() uint32 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetProfitLossByGameTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"zigzag32,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetProfitLossByGameTypeRequest) Reset() {
	*x = GetProfitLossByGameTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfitLossByGameTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfitLossByGameTypeRequest) ProtoMessage() {}

func (x *GetProfitLossByGameTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfitLossByGameTypeRequest.ProtoReflect.Descriptor instead.
func (*GetProfitLossByGameTypeRequest) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{22}
}

func (x *GetProfitLossByGameTypeRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetProfitLossByGameTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []*GetProfitLossByGameType `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	SubTotal *Total                     `protobuf:"bytes,2,opt,name=subTotal,proto3" json:"subTotal,omitempty"`
}

func (x *GetProfitLossByGameTypeResponse) Reset() {
	*x = GetProfitLossByGameTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfitLossByGameTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfitLossByGameTypeResponse) ProtoMessage() {}

func (x *GetProfitLossByGameTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfitLossByGameTypeResponse.ProtoReflect.Descriptor instead.
func (*GetProfitLossByGameTypeResponse) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{23}
}

func (x *GetProfitLossByGameTypeResponse) GetList() []*GetProfitLossByGameType {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetProfitLossByGameTypeResponse) GetSubTotal() *Total {
	if x != nil {
		return x.SubTotal
	}
	return nil
}

type GetProfitLossByGameType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32   `protobuf:"zigzag32,1,opt,name=id,proto3" json:"id,omitempty"`
	GameId    string  `protobuf:"bytes,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PayOff    float64 `protobuf:"fixed64,3,opt,name=pay_off,json=payOff,proto3" json:"pay_off,omitempty"`
	BetAmount float64 `protobuf:"fixed64,4,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Growth    int32   `protobuf:"varint,5,opt,name=growth,proto3" json:"growth,omitempty"`
}

func (x *GetProfitLossByGameType) Reset() {
	*x = GetProfitLossByGameType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfitLossByGameType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfitLossByGameType) ProtoMessage() {}

func (x *GetProfitLossByGameType) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfitLossByGameType.ProtoReflect.Descriptor instead.
func (*GetProfitLossByGameType) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{24}
}

func (x *GetProfitLossByGameType) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetProfitLossByGameType) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *GetProfitLossByGameType) GetPayOff() float64 {
	if x != nil {
		return x.PayOff
	}
	return 0
}

func (x *GetProfitLossByGameType) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *GetProfitLossByGameType) GetGrowth() int32 {
	if x != nil {
		return x.Growth
	}
	return 0
}

type Total struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number    uint32  `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
	PayOff    float64 `protobuf:"fixed64,2,opt,name=pay_off,json=payOff,proto3" json:"pay_off,omitempty"`
	BetAmount float64 `protobuf:"fixed64,3,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
}

func (x *Total) Reset() {
	*x = Total{}
	if protoimpl.UnsafeEnabled {
		mi := &file_monitor_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Total) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Total) ProtoMessage() {}

func (x *Total) ProtoReflect() protoreflect.Message {
	mi := &file_monitor_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Total.ProtoReflect.Descriptor instead.
func (*Total) Descriptor() ([]byte, []int) {
	return file_monitor_proto_rawDescGZIP(), []int{25}
}

func (x *Total) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *Total) GetPayOff() float64 {
	if x != nil {
		return x.PayOff
	}
	return 0
}

func (x *Total) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

var File_monitor_proto protoreflect.FileDescriptor

var file_monitor_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x22, 0x23, 0x0a, 0x0b, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a,
	0x0b, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xa6, 0x01,
	0x0a, 0x0c, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x73, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x6c, 0x6f, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64,
	0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x65, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe4, 0x01, 0x0a, 0x10, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4d, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x08, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x22, 0xb1, 0x01, 0x0a,
	0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x40, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x73,
	0x22, 0x72, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0xc5, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x77,
	0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x6c, 0x6f, 0x73,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x69, 0x6e,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x73, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x6f, 0x73,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x64, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x16,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x22, 0xd7, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a,
	0x0d, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c,
	0x73, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xdc, 0x02, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x26, 0x0a, 0x03, 0x77, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x6f, 0x73, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6c, 0x6f,
	0x73, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x57, 0x69, 0x6e, 0x12, 0x35, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x6f, 0x73, 0x65, 0x12, 0x26,
	0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb6, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x4b, 0x69, 0x6e, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x73, 0x12, 0x2e, 0x0a,
	0x13, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x11, 0x61, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x4a, 0x0a,
	0x13, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x85, 0x01, 0x0a, 0x15, 0x53, 0x65,
	0x74, 0x50, 0x61, 0x79, 0x4f, 0x66, 0x66, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12,
	0x30, 0x0a, 0x14, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x12, 0x69,
	0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x22, 0xb0, 0x03, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x44, 0x42, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x47, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x70, 0x61,
	0x79, 0x6f, 0x66, 0x66, 0x5f, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x61,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x69, 0x73, 0x50,
	0x61, 0x79, 0x6f, 0x66, 0x66, 0x47, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x54, 0x68, 0x61, 0x6e,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0x98, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x44, 0x42, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x44, 0x42, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xbe, 0x03, 0x0a, 0x1e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x44, 0x42, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x11, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x12, 0x52, 0x06,
	0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x12, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x11, 0x52, 0x07, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x77, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x11, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x11, 0x52, 0x04, 0x6c,
	0x6f, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x11,
	0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x11, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12,
	0x2c, 0x0a, 0x12, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x11, 0x52, 0x10, 0x62, 0x65, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x11, 0x52, 0x06, 0x70,
	0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x11, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x11, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x22, 0x83, 0x01, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x22, 0x30, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x11, 0x52, 0x02, 0x69, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x4c, 0x6f,
	0x73, 0x73, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x92,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x4c, 0x6f, 0x73, 0x73,
	0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x4f, 0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a,
	0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x72, 0x6f,
	0x77, 0x74, 0x68, 0x22, 0x57, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x4f, 0x66, 0x66, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0x8b, 0x06, 0x0a,
	0x07, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x4a, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4a, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1f, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x20, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x44, 0x0a, 0x0c, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x12,
	0x1c, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65,
	0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x50, 0x61, 0x79, 0x4f,
	0x66, 0x66, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x12, 0x1e, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x61, 0x79, 0x4f, 0x66, 0x66, 0x4c, 0x6f, 0x62, 0x62, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x7b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x42, 0x50,
	0x61, 0x79, 0x6f, 0x66, 0x66, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x2c, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x44, 0x42, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x44, 0x42, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x42, 0x79, 0x47,
	0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x42,
	0x79, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x74, 0x4c, 0x6f, 0x73, 0x73, 0x42, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0f, 0x5a, 0x0d, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_monitor_proto_rawDescOnce sync.Once
	file_monitor_proto_rawDescData = file_monitor_proto_rawDesc
)

func file_monitor_proto_rawDescGZIP() []byte {
	file_monitor_proto_rawDescOnce.Do(func() {
		file_monitor_proto_rawDescData = protoimpl.X.CompressGZIP(file_monitor_proto_rawDescData)
	})
	return file_monitor_proto_rawDescData
}

var file_monitor_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_monitor_proto_goTypes = []interface{}{
	(*Uint32Value)(nil),                          // 0: monitor.Uint32Value
	(*Uint64Value)(nil),                          // 1: monitor.Uint64Value
	(*BoolValue)(nil),                            // 2: monitor.BoolValue
	(*EmptyResponse)(nil),                        // 3: monitor.EmptyResponse
	(*GetConditionRequest)(nil),                  // 4: monitor.GetConditionRequest
	(*SubCondition)(nil),                         // 5: monitor.SubCondition
	(*MonitorCondition)(nil),                     // 6: monitor.MonitorCondition
	(*GetConditionResponse)(nil),                 // 7: monitor.GetConditionResponse
	(*CreateConditionRequest)(nil),               // 8: monitor.CreateConditionRequest
	(*CreateCondition)(nil),                      // 9: monitor.CreateCondition
	(*CreateSubCondition)(nil),                   // 10: monitor.CreateSubCondition
	(*DeleteConditionRequest)(nil),               // 11: monitor.DeleteConditionRequest
	(*GetSubConditionRequest)(nil),               // 12: monitor.GetSubConditionRequest
	(*GetSubCondition)(nil),                      // 13: monitor.GetSubCondition
	(*GetSubConditionResponse)(nil),              // 14: monitor.GetSubConditionResponse
	(*UpdateConditionRequest)(nil),               // 15: monitor.UpdateConditionRequest
	(*SendTelegramRequest)(nil),                  // 16: monitor.SendTelegramRequest
	(*SetPayOffLobbyRequest)(nil),                // 17: monitor.SetPayOffLobbyRequest
	(*GetMonitorDBPayoffAlertTableRequest)(nil),  // 18: monitor.GetMonitorDBPayoffAlertTableRequest
	(*GetMonitorDBPayoffAlertTableResponse)(nil), // 19: monitor.GetMonitorDBPayoffAlertTableResponse
	(*FetchMonitorDBPayoffAlertTable)(nil),       // 20: monitor.FetchMonitorDBPayoffAlertTable
	(*Pagination)(nil),                           // 21: monitor.Pagination
	(*GetProfitLossByGameTypeRequest)(nil),       // 22: monitor.GetProfitLossByGameTypeRequest
	(*GetProfitLossByGameTypeResponse)(nil),      // 23: monitor.GetProfitLossByGameTypeResponse
	(*GetProfitLossByGameType)(nil),              // 24: monitor.GetProfitLossByGameType
	(*Total)(nil),                                // 25: monitor.Total
}
var file_monitor_proto_depIdxs = []int32{
	5,  // 0: monitor.MonitorCondition.condition:type_name -> monitor.SubCondition
	6,  // 1: monitor.GetConditionResponse.monitors:type_name -> monitor.MonitorCondition
	9,  // 2: monitor.CreateConditionRequest.condition:type_name -> monitor.CreateCondition
	10, // 3: monitor.CreateConditionRequest.sub_condition:type_name -> monitor.CreateSubCondition
	13, // 4: monitor.GetSubConditionRequest.sub_condition:type_name -> monitor.GetSubCondition
	1,  // 5: monitor.GetSubCondition.win:type_name -> monitor.Uint64Value
	1,  // 6: monitor.GetSubCondition.lose:type_name -> monitor.Uint64Value
	1,  // 7: monitor.GetSubCondition.member_win:type_name -> monitor.Uint64Value
	1,  // 8: monitor.GetSubCondition.member_lose:type_name -> monitor.Uint64Value
	0,  // 9: monitor.GetSubCondition.day:type_name -> monitor.Uint32Value
	0,  // 10: monitor.GetSubCondition.percent:type_name -> monitor.Uint32Value
	1,  // 11: monitor.GetSubCondition.bet_amount:type_name -> monitor.Uint64Value
	2,  // 12: monitor.GetMonitorDBPayoffAlertTableRequest.is_payoff_greater_than:type_name -> monitor.BoolValue
	20, // 13: monitor.GetMonitorDBPayoffAlertTableResponse.list:type_name -> monitor.FetchMonitorDBPayoffAlertTable
	21, // 14: monitor.GetMonitorDBPayoffAlertTableResponse.pagination:type_name -> monitor.Pagination
	24, // 15: monitor.GetProfitLossByGameTypeResponse.list:type_name -> monitor.GetProfitLossByGameType
	25, // 16: monitor.GetProfitLossByGameTypeResponse.subTotal:type_name -> monitor.Total
	8,  // 17: monitor.Monitor.CreateCondition:input_type -> monitor.CreateConditionRequest
	4,  // 18: monitor.Monitor.GetCondition:input_type -> monitor.GetConditionRequest
	11, // 19: monitor.Monitor.DeleteCondition:input_type -> monitor.DeleteConditionRequest
	12, // 20: monitor.Monitor.GetSubCondition:input_type -> monitor.GetSubConditionRequest
	15, // 21: monitor.Monitor.UpdateCondition:input_type -> monitor.UpdateConditionRequest
	16, // 22: monitor.Monitor.SendTelegram:input_type -> monitor.SendTelegramRequest
	17, // 23: monitor.Monitor.SetPayOffLobby:input_type -> monitor.SetPayOffLobbyRequest
	18, // 24: monitor.Monitor.GetMonitorDBPayoffAlertTable:input_type -> monitor.GetMonitorDBPayoffAlertTableRequest
	22, // 25: monitor.Monitor.GetProfitLossByGameType:input_type -> monitor.GetProfitLossByGameTypeRequest
	3,  // 26: monitor.Monitor.CreateCondition:output_type -> monitor.EmptyResponse
	7,  // 27: monitor.Monitor.GetCondition:output_type -> monitor.GetConditionResponse
	3,  // 28: monitor.Monitor.DeleteCondition:output_type -> monitor.EmptyResponse
	14, // 29: monitor.Monitor.GetSubCondition:output_type -> monitor.GetSubConditionResponse
	3,  // 30: monitor.Monitor.UpdateCondition:output_type -> monitor.EmptyResponse
	3,  // 31: monitor.Monitor.SendTelegram:output_type -> monitor.EmptyResponse
	3,  // 32: monitor.Monitor.SetPayOffLobby:output_type -> monitor.EmptyResponse
	19, // 33: monitor.Monitor.GetMonitorDBPayoffAlertTable:output_type -> monitor.GetMonitorDBPayoffAlertTableResponse
	23, // 34: monitor.Monitor.GetProfitLossByGameType:output_type -> monitor.GetProfitLossByGameTypeResponse
	26, // [26:35] is the sub-list for method output_type
	17, // [17:26] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_monitor_proto_init() }
func file_monitor_proto_init() {
	if File_monitor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_monitor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint64Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoolValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConditionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubConditionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendTelegramRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPayOffLobbyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMonitorDBPayoffAlertTableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMonitorDBPayoffAlertTableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchMonitorDBPayoffAlertTable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pagination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfitLossByGameTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfitLossByGameTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfitLossByGameType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_monitor_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Total); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_monitor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_monitor_proto_goTypes,
		DependencyIndexes: file_monitor_proto_depIdxs,
		MessageInfos:      file_monitor_proto_msgTypes,
	}.Build()
	File_monitor_proto = out.File
	file_monitor_proto_rawDesc = nil
	file_monitor_proto_goTypes = nil
	file_monitor_proto_depIdxs = nil
}
