// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: monitor.proto

package monitor

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Monitor_CreateCondition_FullMethodName              = "/monitor.Monitor/CreateCondition"
	Monitor_GetCondition_FullMethodName                 = "/monitor.Monitor/GetCondition"
	Monitor_DeleteCondition_FullMethodName              = "/monitor.Monitor/DeleteCondition"
	Monitor_GetSubCondition_FullMethodName              = "/monitor.Monitor/GetSubCondition"
	Monitor_UpdateCondition_FullMethodName              = "/monitor.Monitor/UpdateCondition"
	Monitor_SendTelegram_FullMethodName                 = "/monitor.Monitor/SendTelegram"
	Monitor_SetPayOffLobby_FullMethodName               = "/monitor.Monitor/SetPayOffLobby"
	Monitor_GetMonitorDBPayoffAlertTable_FullMethodName = "/monitor.Monitor/GetMonitorDBPayoffAlertTable"
	Monitor_GetProfitLossByGameType_FullMethodName      = "/monitor.Monitor/GetProfitLossByGameType"
)

// MonitorClient is the client API for Monitor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MonitorClient interface {
	CreateCondition(ctx context.Context, in *CreateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetCondition(ctx context.Context, in *GetConditionRequest, opts ...grpc.CallOption) (*GetConditionResponse, error)
	DeleteCondition(ctx context.Context, in *DeleteConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetSubCondition(ctx context.Context, in *GetSubConditionRequest, opts ...grpc.CallOption) (*GetSubConditionResponse, error)
	UpdateCondition(ctx context.Context, in *UpdateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SendTelegram(ctx context.Context, in *SendTelegramRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetPayOffLobby(ctx context.Context, in *SetPayOffLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetMonitorDBPayoffAlertTable(ctx context.Context, in *GetMonitorDBPayoffAlertTableRequest, opts ...grpc.CallOption) (*GetMonitorDBPayoffAlertTableResponse, error)
	GetProfitLossByGameType(ctx context.Context, in *GetProfitLossByGameTypeRequest, opts ...grpc.CallOption) (*GetProfitLossByGameTypeResponse, error)
}

type monitorClient struct {
	cc grpc.ClientConnInterface
}

func NewMonitorClient(cc grpc.ClientConnInterface) MonitorClient {
	return &monitorClient{cc}
}

func (c *monitorClient) CreateCondition(ctx context.Context, in *CreateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Monitor_CreateCondition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) GetCondition(ctx context.Context, in *GetConditionRequest, opts ...grpc.CallOption) (*GetConditionResponse, error) {
	out := new(GetConditionResponse)
	err := c.cc.Invoke(ctx, Monitor_GetCondition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) DeleteCondition(ctx context.Context, in *DeleteConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Monitor_DeleteCondition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) GetSubCondition(ctx context.Context, in *GetSubConditionRequest, opts ...grpc.CallOption) (*GetSubConditionResponse, error) {
	out := new(GetSubConditionResponse)
	err := c.cc.Invoke(ctx, Monitor_GetSubCondition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) UpdateCondition(ctx context.Context, in *UpdateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Monitor_UpdateCondition_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) SendTelegram(ctx context.Context, in *SendTelegramRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Monitor_SendTelegram_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) SetPayOffLobby(ctx context.Context, in *SetPayOffLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Monitor_SetPayOffLobby_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) GetMonitorDBPayoffAlertTable(ctx context.Context, in *GetMonitorDBPayoffAlertTableRequest, opts ...grpc.CallOption) (*GetMonitorDBPayoffAlertTableResponse, error) {
	out := new(GetMonitorDBPayoffAlertTableResponse)
	err := c.cc.Invoke(ctx, Monitor_GetMonitorDBPayoffAlertTable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) GetProfitLossByGameType(ctx context.Context, in *GetProfitLossByGameTypeRequest, opts ...grpc.CallOption) (*GetProfitLossByGameTypeResponse, error) {
	out := new(GetProfitLossByGameTypeResponse)
	err := c.cc.Invoke(ctx, Monitor_GetProfitLossByGameType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MonitorServer is the server API for Monitor service.
// All implementations must embed UnimplementedMonitorServer
// for forward compatibility
type MonitorServer interface {
	CreateCondition(context.Context, *CreateConditionRequest) (*EmptyResponse, error)
	GetCondition(context.Context, *GetConditionRequest) (*GetConditionResponse, error)
	DeleteCondition(context.Context, *DeleteConditionRequest) (*EmptyResponse, error)
	GetSubCondition(context.Context, *GetSubConditionRequest) (*GetSubConditionResponse, error)
	UpdateCondition(context.Context, *UpdateConditionRequest) (*EmptyResponse, error)
	SendTelegram(context.Context, *SendTelegramRequest) (*EmptyResponse, error)
	SetPayOffLobby(context.Context, *SetPayOffLobbyRequest) (*EmptyResponse, error)
	GetMonitorDBPayoffAlertTable(context.Context, *GetMonitorDBPayoffAlertTableRequest) (*GetMonitorDBPayoffAlertTableResponse, error)
	GetProfitLossByGameType(context.Context, *GetProfitLossByGameTypeRequest) (*GetProfitLossByGameTypeResponse, error)
	mustEmbedUnimplementedMonitorServer()
}

// UnimplementedMonitorServer must be embedded to have forward compatible implementations.
type UnimplementedMonitorServer struct {
}

func (UnimplementedMonitorServer) CreateCondition(context.Context, *CreateConditionRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCondition not implemented")
}
func (UnimplementedMonitorServer) GetCondition(context.Context, *GetConditionRequest) (*GetConditionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCondition not implemented")
}
func (UnimplementedMonitorServer) DeleteCondition(context.Context, *DeleteConditionRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCondition not implemented")
}
func (UnimplementedMonitorServer) GetSubCondition(context.Context, *GetSubConditionRequest) (*GetSubConditionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubCondition not implemented")
}
func (UnimplementedMonitorServer) UpdateCondition(context.Context, *UpdateConditionRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCondition not implemented")
}
func (UnimplementedMonitorServer) SendTelegram(context.Context, *SendTelegramRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTelegram not implemented")
}
func (UnimplementedMonitorServer) SetPayOffLobby(context.Context, *SetPayOffLobbyRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPayOffLobby not implemented")
}
func (UnimplementedMonitorServer) GetMonitorDBPayoffAlertTable(context.Context, *GetMonitorDBPayoffAlertTableRequest) (*GetMonitorDBPayoffAlertTableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonitorDBPayoffAlertTable not implemented")
}
func (UnimplementedMonitorServer) GetProfitLossByGameType(context.Context, *GetProfitLossByGameTypeRequest) (*GetProfitLossByGameTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfitLossByGameType not implemented")
}
func (UnimplementedMonitorServer) mustEmbedUnimplementedMonitorServer() {}

// UnsafeMonitorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MonitorServer will
// result in compilation errors.
type UnsafeMonitorServer interface {
	mustEmbedUnimplementedMonitorServer()
}

func RegisterMonitorServer(s grpc.ServiceRegistrar, srv MonitorServer) {
	s.RegisterService(&Monitor_ServiceDesc, srv)
}

func _Monitor_CreateCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).CreateCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_CreateCondition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).CreateCondition(ctx, req.(*CreateConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_GetCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).GetCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_GetCondition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).GetCondition(ctx, req.(*GetConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_DeleteCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).DeleteCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_DeleteCondition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).DeleteCondition(ctx, req.(*DeleteConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_GetSubCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).GetSubCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_GetSubCondition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).GetSubCondition(ctx, req.(*GetSubConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_UpdateCondition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).UpdateCondition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_UpdateCondition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).UpdateCondition(ctx, req.(*UpdateConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_SendTelegram_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTelegramRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).SendTelegram(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_SendTelegram_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).SendTelegram(ctx, req.(*SendTelegramRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_SetPayOffLobby_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPayOffLobbyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).SetPayOffLobby(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_SetPayOffLobby_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).SetPayOffLobby(ctx, req.(*SetPayOffLobbyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_GetMonitorDBPayoffAlertTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonitorDBPayoffAlertTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).GetMonitorDBPayoffAlertTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_GetMonitorDBPayoffAlertTable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).GetMonitorDBPayoffAlertTable(ctx, req.(*GetMonitorDBPayoffAlertTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_GetProfitLossByGameType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfitLossByGameTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).GetProfitLossByGameType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_GetProfitLossByGameType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).GetProfitLossByGameType(ctx, req.(*GetProfitLossByGameTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Monitor_ServiceDesc is the grpc.ServiceDesc for Monitor service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Monitor_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "monitor.Monitor",
	HandlerType: (*MonitorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCondition",
			Handler:    _Monitor_CreateCondition_Handler,
		},
		{
			MethodName: "GetCondition",
			Handler:    _Monitor_GetCondition_Handler,
		},
		{
			MethodName: "DeleteCondition",
			Handler:    _Monitor_DeleteCondition_Handler,
		},
		{
			MethodName: "GetSubCondition",
			Handler:    _Monitor_GetSubCondition_Handler,
		},
		{
			MethodName: "UpdateCondition",
			Handler:    _Monitor_UpdateCondition_Handler,
		},
		{
			MethodName: "SendTelegram",
			Handler:    _Monitor_SendTelegram_Handler,
		},
		{
			MethodName: "SetPayOffLobby",
			Handler:    _Monitor_SetPayOffLobby_Handler,
		},
		{
			MethodName: "GetMonitorDBPayoffAlertTable",
			Handler:    _Monitor_GetMonitorDBPayoffAlertTable_Handler,
		},
		{
			MethodName: "GetProfitLossByGameType",
			Handler:    _Monitor_GetProfitLossByGameType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "monitor.proto",
}
