// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: wallet.proto

package wallet

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Wallet_EntryStatus_FullMethodName               = "/wallet.Wallet/EntryStatus"
	Wallet_GetUsersBalance_FullMethodName           = "/wallet.Wallet/GetUsersBalance"
	Wallet_SlotMachineBalance_FullMethodName        = "/wallet.Wallet/SlotMachineBalance"
	Wallet_Withdraw_FullMethodName                  = "/wallet.Wallet/Withdraw"
	Wallet_Deposit_FullMethodName                   = "/wallet.Wallet/Deposit"
	Wallet_TransferEntry_FullMethodName             = "/wallet.Wallet/TransferEntry"
	Wallet_TransferEntryList_FullMethodName         = "/wallet.Wallet/TransferEntryList"
	Wallet_FishMachineBalance_FullMethodName        = "/wallet.Wallet/FishMachineBalance"
	Wallet_GetUsersBalanceByAgent_FullMethodName    = "/wallet.Wallet/GetUsersBalanceByAgent"
	Wallet_GetUserBalance_FullMethodName            = "/wallet.Wallet/GetUserBalance"
	Wallet_GetEntryList_FullMethodName              = "/wallet.Wallet/GetEntryList"
	Wallet_GetTotalAmount_FullMethodName            = "/wallet.Wallet/GetTotalAmount"
	Wallet_GetOpCodeList_FullMethodName             = "/wallet.Wallet/GetOpCodeList"
	Wallet_GetOpCodeDict_FullMethodName             = "/wallet.Wallet/GetOpCodeDict"
	Wallet_GetAccountCloseDate_FullMethodName       = "/wallet.Wallet/GetAccountCloseDate"
	Wallet_GetTransferEntryDetail_FullMethodName    = "/wallet.Wallet/GetTransferEntryDetail"
	Wallet_GetUserCashFake_FullMethodName           = "/wallet.Wallet/GetUserCashFake"
	Wallet_GetOpcodeListByACC_FullMethodName        = "/wallet.Wallet/GetOpcodeListByACC"
	Wallet_GetExchangeRate_FullMethodName           = "/wallet.Wallet/GetExchangeRate"
	Wallet_ExchangeRateList_FullMethodName          = "/wallet.Wallet/ExchangeRateList"
	Wallet_GetOpcodeGroupsList_FullMethodName       = "/wallet.Wallet/GetOpcodeGroupsList"
	Wallet_GetTransferEntryDetailAll_FullMethodName = "/wallet.Wallet/GetTransferEntryDetailAll"
	Wallet_GetCurrencyList_FullMethodName           = "/wallet.Wallet/GetCurrencyList"
)

// WalletClient is the client API for Wallet service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WalletClient interface {
	EntryStatus(ctx context.Context, in *EntryStatusRequest, opts ...grpc.CallOption) (*EntryStatusResponse, error)
	GetUsersBalance(ctx context.Context, in *UsersBalanceRequest, opts ...grpc.CallOption) (*UsersBalanceResponse, error)
	SlotMachineBalance(ctx context.Context, in *SlotMachineBalanceRequest, opts ...grpc.CallOption) (*SlotMachineBalanceResponse, error)
	Withdraw(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error)
	Deposit(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error)
	TransferEntry(ctx context.Context, in *TransferEntryRequest, opts ...grpc.CallOption) (*TransferEntryResponse, error)
	TransferEntryList(ctx context.Context, in *TransferEntryListRequest, opts ...grpc.CallOption) (*TransferEntryListResponse, error)
	FishMachineBalance(ctx context.Context, in *FishMachineBalanceRequest, opts ...grpc.CallOption) (*FishMachineBalanceResponse, error)
	GetUsersBalanceByAgent(ctx context.Context, in *UsersBalanceByAgentRequest, opts ...grpc.CallOption) (*UsersBalanceByAgentResponse, error)
	GetUserBalance(ctx context.Context, in *GetUserBalanceRequest, opts ...grpc.CallOption) (*GetUserBalanceResponse, error)
	GetEntryList(ctx context.Context, in *GetEntryListRequest, opts ...grpc.CallOption) (*GetEntryListResponse, error)
	GetTotalAmount(ctx context.Context, in *GetTotalAmountRequest, opts ...grpc.CallOption) (*GetTotalAmountResponse, error)
	GetOpCodeList(ctx context.Context, in *GetOpCodeListRequest, opts ...grpc.CallOption) (*GetOpCodeListResponse, error)
	GetOpCodeDict(ctx context.Context, in *GetOpCodeDictRequest, opts ...grpc.CallOption) (*GetOpCodeDictResponse, error)
	GetAccountCloseDate(ctx context.Context, in *AccountCloseDateRequest, opts ...grpc.CallOption) (*AccountCloseDateResponse, error)
	GetTransferEntryDetail(ctx context.Context, in *GetTransferEntryDetailRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailResponse, error)
	GetUserCashFake(ctx context.Context, in *GetUserCashFakeRequest, opts ...grpc.CallOption) (*GetUserCashFakeResponse, error)
	GetOpcodeListByACC(ctx context.Context, in *GetOpcodeListByACCRequest, opts ...grpc.CallOption) (*GetOpcodeListByACCResponse, error)
	GetExchangeRate(ctx context.Context, in *GetExchangeRateRequest, opts ...grpc.CallOption) (*GetExchangeRateResponse, error)
	ExchangeRateList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ExchangeRateListResponse, error)
	GetOpcodeGroupsList(ctx context.Context, in *GetOpcodeGroupsListRequest, opts ...grpc.CallOption) (*GetOpcodeGroupsListResponse, error)
	GetTransferEntryDetailAll(ctx context.Context, in *GetTransferEntryDetailAllRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailAllResponse, error)
	GetCurrencyList(ctx context.Context, in *GetCurrencyListRequest, opts ...grpc.CallOption) (*GetCurrencyListResponse, error)
}

type walletClient struct {
	cc grpc.ClientConnInterface
}

func NewWalletClient(cc grpc.ClientConnInterface) WalletClient {
	return &walletClient{cc}
}

func (c *walletClient) EntryStatus(ctx context.Context, in *EntryStatusRequest, opts ...grpc.CallOption) (*EntryStatusResponse, error) {
	out := new(EntryStatusResponse)
	err := c.cc.Invoke(ctx, Wallet_EntryStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetUsersBalance(ctx context.Context, in *UsersBalanceRequest, opts ...grpc.CallOption) (*UsersBalanceResponse, error) {
	out := new(UsersBalanceResponse)
	err := c.cc.Invoke(ctx, Wallet_GetUsersBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) SlotMachineBalance(ctx context.Context, in *SlotMachineBalanceRequest, opts ...grpc.CallOption) (*SlotMachineBalanceResponse, error) {
	out := new(SlotMachineBalanceResponse)
	err := c.cc.Invoke(ctx, Wallet_SlotMachineBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) Withdraw(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error) {
	out := new(WithdrawResponse)
	err := c.cc.Invoke(ctx, Wallet_Withdraw_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) Deposit(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error) {
	out := new(DepositResponse)
	err := c.cc.Invoke(ctx, Wallet_Deposit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) TransferEntry(ctx context.Context, in *TransferEntryRequest, opts ...grpc.CallOption) (*TransferEntryResponse, error) {
	out := new(TransferEntryResponse)
	err := c.cc.Invoke(ctx, Wallet_TransferEntry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) TransferEntryList(ctx context.Context, in *TransferEntryListRequest, opts ...grpc.CallOption) (*TransferEntryListResponse, error) {
	out := new(TransferEntryListResponse)
	err := c.cc.Invoke(ctx, Wallet_TransferEntryList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) FishMachineBalance(ctx context.Context, in *FishMachineBalanceRequest, opts ...grpc.CallOption) (*FishMachineBalanceResponse, error) {
	out := new(FishMachineBalanceResponse)
	err := c.cc.Invoke(ctx, Wallet_FishMachineBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetUsersBalanceByAgent(ctx context.Context, in *UsersBalanceByAgentRequest, opts ...grpc.CallOption) (*UsersBalanceByAgentResponse, error) {
	out := new(UsersBalanceByAgentResponse)
	err := c.cc.Invoke(ctx, Wallet_GetUsersBalanceByAgent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetUserBalance(ctx context.Context, in *GetUserBalanceRequest, opts ...grpc.CallOption) (*GetUserBalanceResponse, error) {
	out := new(GetUserBalanceResponse)
	err := c.cc.Invoke(ctx, Wallet_GetUserBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetEntryList(ctx context.Context, in *GetEntryListRequest, opts ...grpc.CallOption) (*GetEntryListResponse, error) {
	out := new(GetEntryListResponse)
	err := c.cc.Invoke(ctx, Wallet_GetEntryList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetTotalAmount(ctx context.Context, in *GetTotalAmountRequest, opts ...grpc.CallOption) (*GetTotalAmountResponse, error) {
	out := new(GetTotalAmountResponse)
	err := c.cc.Invoke(ctx, Wallet_GetTotalAmount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetOpCodeList(ctx context.Context, in *GetOpCodeListRequest, opts ...grpc.CallOption) (*GetOpCodeListResponse, error) {
	out := new(GetOpCodeListResponse)
	err := c.cc.Invoke(ctx, Wallet_GetOpCodeList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetOpCodeDict(ctx context.Context, in *GetOpCodeDictRequest, opts ...grpc.CallOption) (*GetOpCodeDictResponse, error) {
	out := new(GetOpCodeDictResponse)
	err := c.cc.Invoke(ctx, Wallet_GetOpCodeDict_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetAccountCloseDate(ctx context.Context, in *AccountCloseDateRequest, opts ...grpc.CallOption) (*AccountCloseDateResponse, error) {
	out := new(AccountCloseDateResponse)
	err := c.cc.Invoke(ctx, Wallet_GetAccountCloseDate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetTransferEntryDetail(ctx context.Context, in *GetTransferEntryDetailRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailResponse, error) {
	out := new(GetTransferEntryDetailResponse)
	err := c.cc.Invoke(ctx, Wallet_GetTransferEntryDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetUserCashFake(ctx context.Context, in *GetUserCashFakeRequest, opts ...grpc.CallOption) (*GetUserCashFakeResponse, error) {
	out := new(GetUserCashFakeResponse)
	err := c.cc.Invoke(ctx, Wallet_GetUserCashFake_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetOpcodeListByACC(ctx context.Context, in *GetOpcodeListByACCRequest, opts ...grpc.CallOption) (*GetOpcodeListByACCResponse, error) {
	out := new(GetOpcodeListByACCResponse)
	err := c.cc.Invoke(ctx, Wallet_GetOpcodeListByACC_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetExchangeRate(ctx context.Context, in *GetExchangeRateRequest, opts ...grpc.CallOption) (*GetExchangeRateResponse, error) {
	out := new(GetExchangeRateResponse)
	err := c.cc.Invoke(ctx, Wallet_GetExchangeRate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) ExchangeRateList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ExchangeRateListResponse, error) {
	out := new(ExchangeRateListResponse)
	err := c.cc.Invoke(ctx, Wallet_ExchangeRateList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetOpcodeGroupsList(ctx context.Context, in *GetOpcodeGroupsListRequest, opts ...grpc.CallOption) (*GetOpcodeGroupsListResponse, error) {
	out := new(GetOpcodeGroupsListResponse)
	err := c.cc.Invoke(ctx, Wallet_GetOpcodeGroupsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetTransferEntryDetailAll(ctx context.Context, in *GetTransferEntryDetailAllRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailAllResponse, error) {
	out := new(GetTransferEntryDetailAllResponse)
	err := c.cc.Invoke(ctx, Wallet_GetTransferEntryDetailAll_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *walletClient) GetCurrencyList(ctx context.Context, in *GetCurrencyListRequest, opts ...grpc.CallOption) (*GetCurrencyListResponse, error) {
	out := new(GetCurrencyListResponse)
	err := c.cc.Invoke(ctx, Wallet_GetCurrencyList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WalletServer is the server API for Wallet service.
// All implementations must embed UnimplementedWalletServer
// for forward compatibility
type WalletServer interface {
	EntryStatus(context.Context, *EntryStatusRequest) (*EntryStatusResponse, error)
	GetUsersBalance(context.Context, *UsersBalanceRequest) (*UsersBalanceResponse, error)
	SlotMachineBalance(context.Context, *SlotMachineBalanceRequest) (*SlotMachineBalanceResponse, error)
	Withdraw(context.Context, *WithdrawRequest) (*WithdrawResponse, error)
	Deposit(context.Context, *DepositRequest) (*DepositResponse, error)
	TransferEntry(context.Context, *TransferEntryRequest) (*TransferEntryResponse, error)
	TransferEntryList(context.Context, *TransferEntryListRequest) (*TransferEntryListResponse, error)
	FishMachineBalance(context.Context, *FishMachineBalanceRequest) (*FishMachineBalanceResponse, error)
	GetUsersBalanceByAgent(context.Context, *UsersBalanceByAgentRequest) (*UsersBalanceByAgentResponse, error)
	GetUserBalance(context.Context, *GetUserBalanceRequest) (*GetUserBalanceResponse, error)
	GetEntryList(context.Context, *GetEntryListRequest) (*GetEntryListResponse, error)
	GetTotalAmount(context.Context, *GetTotalAmountRequest) (*GetTotalAmountResponse, error)
	GetOpCodeList(context.Context, *GetOpCodeListRequest) (*GetOpCodeListResponse, error)
	GetOpCodeDict(context.Context, *GetOpCodeDictRequest) (*GetOpCodeDictResponse, error)
	GetAccountCloseDate(context.Context, *AccountCloseDateRequest) (*AccountCloseDateResponse, error)
	GetTransferEntryDetail(context.Context, *GetTransferEntryDetailRequest) (*GetTransferEntryDetailResponse, error)
	GetUserCashFake(context.Context, *GetUserCashFakeRequest) (*GetUserCashFakeResponse, error)
	GetOpcodeListByACC(context.Context, *GetOpcodeListByACCRequest) (*GetOpcodeListByACCResponse, error)
	GetExchangeRate(context.Context, *GetExchangeRateRequest) (*GetExchangeRateResponse, error)
	ExchangeRateList(context.Context, *EmptyRequest) (*ExchangeRateListResponse, error)
	GetOpcodeGroupsList(context.Context, *GetOpcodeGroupsListRequest) (*GetOpcodeGroupsListResponse, error)
	GetTransferEntryDetailAll(context.Context, *GetTransferEntryDetailAllRequest) (*GetTransferEntryDetailAllResponse, error)
	GetCurrencyList(context.Context, *GetCurrencyListRequest) (*GetCurrencyListResponse, error)
	mustEmbedUnimplementedWalletServer()
}

// UnimplementedWalletServer must be embedded to have forward compatible implementations.
type UnimplementedWalletServer struct {
}

func (UnimplementedWalletServer) EntryStatus(context.Context, *EntryStatusRequest) (*EntryStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntryStatus not implemented")
}
func (UnimplementedWalletServer) GetUsersBalance(context.Context, *UsersBalanceRequest) (*UsersBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersBalance not implemented")
}
func (UnimplementedWalletServer) SlotMachineBalance(context.Context, *SlotMachineBalanceRequest) (*SlotMachineBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SlotMachineBalance not implemented")
}
func (UnimplementedWalletServer) Withdraw(context.Context, *WithdrawRequest) (*WithdrawResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Withdraw not implemented")
}
func (UnimplementedWalletServer) Deposit(context.Context, *DepositRequest) (*DepositResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Deposit not implemented")
}
func (UnimplementedWalletServer) TransferEntry(context.Context, *TransferEntryRequest) (*TransferEntryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransferEntry not implemented")
}
func (UnimplementedWalletServer) TransferEntryList(context.Context, *TransferEntryListRequest) (*TransferEntryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransferEntryList not implemented")
}
func (UnimplementedWalletServer) FishMachineBalance(context.Context, *FishMachineBalanceRequest) (*FishMachineBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FishMachineBalance not implemented")
}
func (UnimplementedWalletServer) GetUsersBalanceByAgent(context.Context, *UsersBalanceByAgentRequest) (*UsersBalanceByAgentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersBalanceByAgent not implemented")
}
func (UnimplementedWalletServer) GetUserBalance(context.Context, *GetUserBalanceRequest) (*GetUserBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserBalance not implemented")
}
func (UnimplementedWalletServer) GetEntryList(context.Context, *GetEntryListRequest) (*GetEntryListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntryList not implemented")
}
func (UnimplementedWalletServer) GetTotalAmount(context.Context, *GetTotalAmountRequest) (*GetTotalAmountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTotalAmount not implemented")
}
func (UnimplementedWalletServer) GetOpCodeList(context.Context, *GetOpCodeListRequest) (*GetOpCodeListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpCodeList not implemented")
}
func (UnimplementedWalletServer) GetOpCodeDict(context.Context, *GetOpCodeDictRequest) (*GetOpCodeDictResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpCodeDict not implemented")
}
func (UnimplementedWalletServer) GetAccountCloseDate(context.Context, *AccountCloseDateRequest) (*AccountCloseDateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountCloseDate not implemented")
}
func (UnimplementedWalletServer) GetTransferEntryDetail(context.Context, *GetTransferEntryDetailRequest) (*GetTransferEntryDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransferEntryDetail not implemented")
}
func (UnimplementedWalletServer) GetUserCashFake(context.Context, *GetUserCashFakeRequest) (*GetUserCashFakeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserCashFake not implemented")
}
func (UnimplementedWalletServer) GetOpcodeListByACC(context.Context, *GetOpcodeListByACCRequest) (*GetOpcodeListByACCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpcodeListByACC not implemented")
}
func (UnimplementedWalletServer) GetExchangeRate(context.Context, *GetExchangeRateRequest) (*GetExchangeRateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExchangeRate not implemented")
}
func (UnimplementedWalletServer) ExchangeRateList(context.Context, *EmptyRequest) (*ExchangeRateListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExchangeRateList not implemented")
}
func (UnimplementedWalletServer) GetOpcodeGroupsList(context.Context, *GetOpcodeGroupsListRequest) (*GetOpcodeGroupsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpcodeGroupsList not implemented")
}
func (UnimplementedWalletServer) GetTransferEntryDetailAll(context.Context, *GetTransferEntryDetailAllRequest) (*GetTransferEntryDetailAllResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransferEntryDetailAll not implemented")
}
func (UnimplementedWalletServer) GetCurrencyList(context.Context, *GetCurrencyListRequest) (*GetCurrencyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrencyList not implemented")
}
func (UnimplementedWalletServer) mustEmbedUnimplementedWalletServer() {}

// UnsafeWalletServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WalletServer will
// result in compilation errors.
type UnsafeWalletServer interface {
	mustEmbedUnimplementedWalletServer()
}

func RegisterWalletServer(s grpc.ServiceRegistrar, srv WalletServer) {
	s.RegisterService(&Wallet_ServiceDesc, srv)
}

func _Wallet_EntryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).EntryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_EntryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).EntryStatus(ctx, req.(*EntryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetUsersBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UsersBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetUsersBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetUsersBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetUsersBalance(ctx, req.(*UsersBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_SlotMachineBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SlotMachineBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).SlotMachineBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_SlotMachineBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).SlotMachineBalance(ctx, req.(*SlotMachineBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_Withdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithdrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).Withdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_Withdraw_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).Withdraw(ctx, req.(*WithdrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_Deposit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepositRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).Deposit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_Deposit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).Deposit(ctx, req.(*DepositRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_TransferEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).TransferEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_TransferEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).TransferEntry(ctx, req.(*TransferEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_TransferEntryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferEntryListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).TransferEntryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_TransferEntryList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).TransferEntryList(ctx, req.(*TransferEntryListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_FishMachineBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FishMachineBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).FishMachineBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_FishMachineBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).FishMachineBalance(ctx, req.(*FishMachineBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetUsersBalanceByAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UsersBalanceByAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetUsersBalanceByAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetUsersBalanceByAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetUsersBalanceByAgent(ctx, req.(*UsersBalanceByAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetUserBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetUserBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetUserBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetUserBalance(ctx, req.(*GetUserBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetEntryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEntryListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetEntryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetEntryList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetEntryList(ctx, req.(*GetEntryListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetTotalAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTotalAmountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetTotalAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetTotalAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetTotalAmount(ctx, req.(*GetTotalAmountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetOpCodeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpCodeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetOpCodeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetOpCodeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetOpCodeList(ctx, req.(*GetOpCodeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetOpCodeDict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpCodeDictRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetOpCodeDict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetOpCodeDict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetOpCodeDict(ctx, req.(*GetOpCodeDictRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetAccountCloseDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountCloseDateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetAccountCloseDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetAccountCloseDate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetAccountCloseDate(ctx, req.(*AccountCloseDateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetTransferEntryDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransferEntryDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetTransferEntryDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetTransferEntryDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetTransferEntryDetail(ctx, req.(*GetTransferEntryDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetUserCashFake_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCashFakeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetUserCashFake(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetUserCashFake_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetUserCashFake(ctx, req.(*GetUserCashFakeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetOpcodeListByACC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpcodeListByACCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetOpcodeListByACC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetOpcodeListByACC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetOpcodeListByACC(ctx, req.(*GetOpcodeListByACCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetExchangeRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangeRateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetExchangeRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetExchangeRate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetExchangeRate(ctx, req.(*GetExchangeRateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_ExchangeRateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).ExchangeRateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_ExchangeRateList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).ExchangeRateList(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetOpcodeGroupsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpcodeGroupsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetOpcodeGroupsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetOpcodeGroupsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetOpcodeGroupsList(ctx, req.(*GetOpcodeGroupsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetTransferEntryDetailAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransferEntryDetailAllRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetTransferEntryDetailAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetTransferEntryDetailAll_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetTransferEntryDetailAll(ctx, req.(*GetTransferEntryDetailAllRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wallet_GetCurrencyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrencyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WalletServer).GetCurrencyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Wallet_GetCurrencyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WalletServer).GetCurrencyList(ctx, req.(*GetCurrencyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Wallet_ServiceDesc is the grpc.ServiceDesc for Wallet service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Wallet_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "wallet.Wallet",
	HandlerType: (*WalletServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EntryStatus",
			Handler:    _Wallet_EntryStatus_Handler,
		},
		{
			MethodName: "GetUsersBalance",
			Handler:    _Wallet_GetUsersBalance_Handler,
		},
		{
			MethodName: "SlotMachineBalance",
			Handler:    _Wallet_SlotMachineBalance_Handler,
		},
		{
			MethodName: "Withdraw",
			Handler:    _Wallet_Withdraw_Handler,
		},
		{
			MethodName: "Deposit",
			Handler:    _Wallet_Deposit_Handler,
		},
		{
			MethodName: "TransferEntry",
			Handler:    _Wallet_TransferEntry_Handler,
		},
		{
			MethodName: "TransferEntryList",
			Handler:    _Wallet_TransferEntryList_Handler,
		},
		{
			MethodName: "FishMachineBalance",
			Handler:    _Wallet_FishMachineBalance_Handler,
		},
		{
			MethodName: "GetUsersBalanceByAgent",
			Handler:    _Wallet_GetUsersBalanceByAgent_Handler,
		},
		{
			MethodName: "GetUserBalance",
			Handler:    _Wallet_GetUserBalance_Handler,
		},
		{
			MethodName: "GetEntryList",
			Handler:    _Wallet_GetEntryList_Handler,
		},
		{
			MethodName: "GetTotalAmount",
			Handler:    _Wallet_GetTotalAmount_Handler,
		},
		{
			MethodName: "GetOpCodeList",
			Handler:    _Wallet_GetOpCodeList_Handler,
		},
		{
			MethodName: "GetOpCodeDict",
			Handler:    _Wallet_GetOpCodeDict_Handler,
		},
		{
			MethodName: "GetAccountCloseDate",
			Handler:    _Wallet_GetAccountCloseDate_Handler,
		},
		{
			MethodName: "GetTransferEntryDetail",
			Handler:    _Wallet_GetTransferEntryDetail_Handler,
		},
		{
			MethodName: "GetUserCashFake",
			Handler:    _Wallet_GetUserCashFake_Handler,
		},
		{
			MethodName: "GetOpcodeListByACC",
			Handler:    _Wallet_GetOpcodeListByACC_Handler,
		},
		{
			MethodName: "GetExchangeRate",
			Handler:    _Wallet_GetExchangeRate_Handler,
		},
		{
			MethodName: "ExchangeRateList",
			Handler:    _Wallet_ExchangeRateList_Handler,
		},
		{
			MethodName: "GetOpcodeGroupsList",
			Handler:    _Wallet_GetOpcodeGroupsList_Handler,
		},
		{
			MethodName: "GetTransferEntryDetailAll",
			Handler:    _Wallet_GetTransferEntryDetailAll_Handler,
		},
		{
			MethodName: "GetCurrencyList",
			Handler:    _Wallet_GetCurrencyList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "wallet.proto",
}
