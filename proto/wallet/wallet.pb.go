// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.19.4
// source: wallet.proto

package wallet

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StringValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StringValue) Reset() {
	*x = StringValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringValue) ProtoMessage() {}

func (x *StringValue) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringValue.ProtoReflect.Descriptor instead.
func (*StringValue) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{0}
}

func (x *StringValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{1}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type BoolValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value bool `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{2}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

type RepeatedUint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []uint32 `protobuf:"varint,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *RepeatedUint32Value) Reset() {
	*x = RepeatedUint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedUint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedUint32Value) ProtoMessage() {}

func (x *RepeatedUint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedUint32Value.ProtoReflect.Descriptor instead.
func (*RepeatedUint32Value) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{3}
}

func (x *RepeatedUint32Value) GetValue() []uint32 {
	if x != nil {
		return x.Value
	}
	return nil
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{4}
}

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstResult uint32 `protobuf:"varint,1,opt,name=first_result,json=firstResult,proto3" json:"first_result,omitempty"`
	MaxResults  uint32 `protobuf:"varint,2,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
	Total       uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{5}
}

func (x *Pagination) GetFirstResult() uint32 {
	if x != nil {
		return x.FirstResult
	}
	return 0
}

func (x *Pagination) GetMaxResults() uint32 {
	if x != nil {
		return x.MaxResults
	}
	return 0
}

func (x *Pagination) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type EntryStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	RefId  string `protobuf:"bytes,2,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
}

func (x *EntryStatusRequest) Reset() {
	*x = EntryStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntryStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntryStatusRequest) ProtoMessage() {}

func (x *EntryStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntryStatusRequest.ProtoReflect.Descriptor instead.
func (*EntryStatusRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{6}
}

func (x *EntryStatusRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *EntryStatusRequest) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

type EntryStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint32  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username string  `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	RefId    string  `protobuf:"bytes,3,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	Amount   float64 `protobuf:"fixed64,4,opt,name=amount,proto3" json:"amount,omitempty"`
	Status   int32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *EntryStatusResponse) Reset() {
	*x = EntryStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntryStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntryStatusResponse) ProtoMessage() {}

func (x *EntryStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntryStatusResponse.ProtoReflect.Descriptor instead.
func (*EntryStatusResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{7}
}

func (x *EntryStatusResponse) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *EntryStatusResponse) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *EntryStatusResponse) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *EntryStatusResponse) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *EntryStatusResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type UsersBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users    []uint32 `protobuf:"varint,1,rep,packed,name=users,proto3" json:"users,omitempty"`
	ClientIp string   `protobuf:"bytes,2,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
}

func (x *UsersBalanceRequest) Reset() {
	*x = UsersBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersBalanceRequest) ProtoMessage() {}

func (x *UsersBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersBalanceRequest.ProtoReflect.Descriptor instead.
func (*UsersBalanceRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{8}
}

func (x *UsersBalanceRequest) GetUsers() []uint32 {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *UsersBalanceRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

type UsersBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*BalanceInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *UsersBalanceResponse) Reset() {
	*x = UsersBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersBalanceResponse) ProtoMessage() {}

func (x *UsersBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersBalanceResponse.ProtoReflect.Descriptor instead.
func (*UsersBalanceResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{9}
}

func (x *UsersBalanceResponse) GetUsers() []*BalanceInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

type BalanceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId  uint32  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Balance float64 `protobuf:"fixed64,2,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *BalanceInfo) Reset() {
	*x = BalanceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceInfo) ProtoMessage() {}

func (x *BalanceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceInfo.ProtoReflect.Descriptor instead.
func (*BalanceInfo) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{10}
}

func (x *BalanceInfo) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BalanceInfo) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type SlotMachineBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *SlotMachineBalanceRequest) Reset() {
	*x = SlotMachineBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotMachineBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotMachineBalanceRequest) ProtoMessage() {}

func (x *SlotMachineBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotMachineBalanceRequest.ProtoReflect.Descriptor instead.
func (*SlotMachineBalanceRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{11}
}

func (x *SlotMachineBalanceRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type SlotMachineBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance float64 `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *SlotMachineBalanceResponse) Reset() {
	*x = SlotMachineBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotMachineBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotMachineBalanceResponse) ProtoMessage() {}

func (x *SlotMachineBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotMachineBalanceResponse.ProtoReflect.Descriptor instead.
func (*SlotMachineBalanceResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{12}
}

func (x *SlotMachineBalanceResponse) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type WithdrawRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	RefId  string  `protobuf:"bytes,3,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
}

func (x *WithdrawRequest) Reset() {
	*x = WithdrawRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithdrawRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithdrawRequest) ProtoMessage() {}

func (x *WithdrawRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithdrawRequest.ProtoReflect.Descriptor instead.
func (*WithdrawRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{13}
}

func (x *WithdrawRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *WithdrawRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *WithdrawRequest) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

type WithdrawResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance float64 `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *WithdrawResponse) Reset() {
	*x = WithdrawResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithdrawResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithdrawResponse) ProtoMessage() {}

func (x *WithdrawResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithdrawResponse.ProtoReflect.Descriptor instead.
func (*WithdrawResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{14}
}

func (x *WithdrawResponse) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type DepositRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	RefId  string  `protobuf:"bytes,3,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
}

func (x *DepositRequest) Reset() {
	*x = DepositRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositRequest) ProtoMessage() {}

func (x *DepositRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositRequest.ProtoReflect.Descriptor instead.
func (*DepositRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{15}
}

func (x *DepositRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DepositRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *DepositRequest) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

type DepositResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance float64 `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *DepositResponse) Reset() {
	*x = DepositResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositResponse) ProtoMessage() {}

func (x *DepositResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositResponse.ProtoReflect.Descriptor instead.
func (*DepositResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{16}
}

func (x *DepositResponse) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type TransferEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         uint32       `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RefId          *StringValue `protobuf:"bytes,2,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	TransferAction *StringValue `protobuf:"bytes,3,opt,name=transfer_action,json=transferAction,proto3" json:"transfer_action,omitempty"`
	StartTime      string       `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime        string       `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	FirstResult    *Uint32Value `protobuf:"bytes,6,opt,name=first_result,json=firstResult,proto3" json:"first_result,omitempty"`
	MaxResults     *Uint32Value `protobuf:"bytes,7,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
}

func (x *TransferEntryRequest) Reset() {
	*x = TransferEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferEntryRequest) ProtoMessage() {}

func (x *TransferEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferEntryRequest.ProtoReflect.Descriptor instead.
func (*TransferEntryRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{17}
}

func (x *TransferEntryRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TransferEntryRequest) GetRefId() *StringValue {
	if x != nil {
		return x.RefId
	}
	return nil
}

func (x *TransferEntryRequest) GetTransferAction() *StringValue {
	if x != nil {
		return x.TransferAction
	}
	return nil
}

func (x *TransferEntryRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *TransferEntryRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *TransferEntryRequest) GetFirstResult() *Uint32Value {
	if x != nil {
		return x.FirstResult
	}
	return nil
}

func (x *TransferEntryRequest) GetMaxResults() *Uint32Value {
	if x != nil {
		return x.MaxResults
	}
	return nil
}

type TransferEntryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entry      []*TransferEntry `protobuf:"bytes,1,rep,name=entry,proto3" json:"entry,omitempty"`
	Pagination *Pagination      `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *TransferEntryResponse) Reset() {
	*x = TransferEntryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferEntryResponse) ProtoMessage() {}

func (x *TransferEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferEntryResponse.ProtoReflect.Descriptor instead.
func (*TransferEntryResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{18}
}

func (x *TransferEntryResponse) GetEntry() []*TransferEntry {
	if x != nil {
		return x.Entry
	}
	return nil
}

func (x *TransferEntryResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type TransferEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId    uint32  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username  string  `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Currency  string  `protobuf:"bytes,4,opt,name=currency,proto3" json:"currency,omitempty"`
	RefId     string  `protobuf:"bytes,5,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	Amount    float64 `protobuf:"fixed64,6,opt,name=amount,proto3" json:"amount,omitempty"`
	Balance   float64 `protobuf:"fixed64,7,opt,name=balance,proto3" json:"balance,omitempty"`
	CreatedAt string  `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Memo      string  `protobuf:"bytes,9,opt,name=memo,proto3" json:"memo,omitempty"`
}

func (x *TransferEntry) Reset() {
	*x = TransferEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferEntry) ProtoMessage() {}

func (x *TransferEntry) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferEntry.ProtoReflect.Descriptor instead.
func (*TransferEntry) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{19}
}

func (x *TransferEntry) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TransferEntry) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TransferEntry) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *TransferEntry) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *TransferEntry) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *TransferEntry) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *TransferEntry) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *TransferEntry) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *TransferEntry) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

type TransferEntryListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentId    uint32               `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Level       *Uint32Value         `protobuf:"bytes,2,opt,name=level,proto3" json:"level,omitempty"`
	RefId       *StringValue         `protobuf:"bytes,3,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	Opcode      *RepeatedUint32Value `protobuf:"bytes,4,opt,name=opcode,proto3" json:"opcode,omitempty"`
	StartTime   *StringValue         `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime     *StringValue         `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	FirstResult *Uint32Value         `protobuf:"bytes,7,opt,name=first_result,json=firstResult,proto3" json:"first_result,omitempty"`
	MaxResults  *Uint32Value         `protobuf:"bytes,8,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
}

func (x *TransferEntryListRequest) Reset() {
	*x = TransferEntryListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferEntryListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferEntryListRequest) ProtoMessage() {}

func (x *TransferEntryListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferEntryListRequest.ProtoReflect.Descriptor instead.
func (*TransferEntryListRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{20}
}

func (x *TransferEntryListRequest) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *TransferEntryListRequest) GetLevel() *Uint32Value {
	if x != nil {
		return x.Level
	}
	return nil
}

func (x *TransferEntryListRequest) GetRefId() *StringValue {
	if x != nil {
		return x.RefId
	}
	return nil
}

func (x *TransferEntryListRequest) GetOpcode() *RepeatedUint32Value {
	if x != nil {
		return x.Opcode
	}
	return nil
}

func (x *TransferEntryListRequest) GetStartTime() *StringValue {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *TransferEntryListRequest) GetEndTime() *StringValue {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *TransferEntryListRequest) GetFirstResult() *Uint32Value {
	if x != nil {
		return x.FirstResult
	}
	return nil
}

func (x *TransferEntryListRequest) GetMaxResults() *Uint32Value {
	if x != nil {
		return x.MaxResults
	}
	return nil
}

type TransferEntryListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entry      []*TransferEntry `protobuf:"bytes,1,rep,name=entry,proto3" json:"entry,omitempty"`
	Pagination *Pagination      `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *TransferEntryListResponse) Reset() {
	*x = TransferEntryListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferEntryListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferEntryListResponse) ProtoMessage() {}

func (x *TransferEntryListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferEntryListResponse.ProtoReflect.Descriptor instead.
func (*TransferEntryListResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{21}
}

func (x *TransferEntryListResponse) GetEntry() []*TransferEntry {
	if x != nil {
		return x.Entry
	}
	return nil
}

func (x *TransferEntryListResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type UsersBalanceByAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentId    uint32       `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Username    *StringValue `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	FirstResult *Uint32Value `protobuf:"bytes,3,opt,name=first_result,json=firstResult,proto3" json:"first_result,omitempty"`
	MaxResults  *Uint32Value `protobuf:"bytes,4,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
}

func (x *UsersBalanceByAgentRequest) Reset() {
	*x = UsersBalanceByAgentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersBalanceByAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersBalanceByAgentRequest) ProtoMessage() {}

func (x *UsersBalanceByAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersBalanceByAgentRequest.ProtoReflect.Descriptor instead.
func (*UsersBalanceByAgentRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{22}
}

func (x *UsersBalanceByAgentRequest) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *UsersBalanceByAgentRequest) GetUsername() *StringValue {
	if x != nil {
		return x.Username
	}
	return nil
}

func (x *UsersBalanceByAgentRequest) GetFirstResult() *Uint32Value {
	if x != nil {
		return x.FirstResult
	}
	return nil
}

func (x *UsersBalanceByAgentRequest) GetMaxResults() *Uint32Value {
	if x != nil {
		return x.MaxResults
	}
	return nil
}

type UsersBalanceByAgentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users      []*BalanceByAgentInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Pagination *Pagination           `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *UsersBalanceByAgentResponse) Reset() {
	*x = UsersBalanceByAgentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersBalanceByAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersBalanceByAgentResponse) ProtoMessage() {}

func (x *UsersBalanceByAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersBalanceByAgentResponse.ProtoReflect.Descriptor instead.
func (*UsersBalanceByAgentResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{23}
}

func (x *UsersBalanceByAgentResponse) GetUsers() []*BalanceByAgentInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *UsersBalanceByAgentResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BalanceByAgentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    uint32  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName  string  `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Balance   float64 `protobuf:"fixed64,3,opt,name=balance,proto3" json:"balance,omitempty"`
	ParentId  uint32  `protobuf:"varint,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Enable    bool    `protobuf:"varint,5,opt,name=enable,proto3" json:"enable,omitempty"`
	CreatedAt string  `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *BalanceByAgentInfo) Reset() {
	*x = BalanceByAgentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceByAgentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceByAgentInfo) ProtoMessage() {}

func (x *BalanceByAgentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceByAgentInfo.ProtoReflect.Descriptor instead.
func (*BalanceByAgentInfo) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{24}
}

func (x *BalanceByAgentInfo) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BalanceByAgentInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *BalanceByAgentInfo) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *BalanceByAgentInfo) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *BalanceByAgentInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *BalanceByAgentInfo) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type FishMachineBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *FishMachineBalanceRequest) Reset() {
	*x = FishMachineBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishMachineBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishMachineBalanceRequest) ProtoMessage() {}

func (x *FishMachineBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishMachineBalanceRequest.ProtoReflect.Descriptor instead.
func (*FishMachineBalanceRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{25}
}

func (x *FishMachineBalanceRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type FishMachineBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance float64 `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *FishMachineBalanceResponse) Reset() {
	*x = FishMachineBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishMachineBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishMachineBalanceResponse) ProtoMessage() {}

func (x *FishMachineBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishMachineBalanceResponse.ProtoReflect.Descriptor instead.
func (*FishMachineBalanceResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{26}
}

func (x *FishMachineBalanceResponse) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type GetUserBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetUserBalanceRequest) Reset() {
	*x = GetUserBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBalanceRequest) ProtoMessage() {}

func (x *GetUserBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetUserBalanceRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{27}
}

func (x *GetUserBalanceRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance float64 `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *GetUserBalanceResponse) Reset() {
	*x = GetUserBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBalanceResponse) ProtoMessage() {}

func (x *GetUserBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetUserBalanceResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{28}
}

func (x *GetUserBalanceResponse) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type GetEntryListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartTime   string   `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime     string   `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OpCodeGroup []string `protobuf:"bytes,4,rep,name=op_code_group,json=opCodeGroup,proto3" json:"op_code_group,omitempty"`
	Sort        string   `protobuf:"bytes,5,opt,name=sort,proto3" json:"sort,omitempty"`
	FirstResult uint32   `protobuf:"varint,6,opt,name=first_result,json=firstResult,proto3" json:"first_result,omitempty"`
	MaxResults  uint32   `protobuf:"varint,7,opt,name=max_results,json=maxResults,proto3" json:"max_results,omitempty"`
}

func (x *GetEntryListRequest) Reset() {
	*x = GetEntryListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntryListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntryListRequest) ProtoMessage() {}

func (x *GetEntryListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntryListRequest.ProtoReflect.Descriptor instead.
func (*GetEntryListRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{29}
}

func (x *GetEntryListRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetEntryListRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetEntryListRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetEntryListRequest) GetOpCodeGroup() []string {
	if x != nil {
		return x.OpCodeGroup
	}
	return nil
}

func (x *GetEntryListRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *GetEntryListRequest) GetFirstResult() uint32 {
	if x != nil {
		return x.FirstResult
	}
	return 0
}

func (x *GetEntryListRequest) GetMaxResults() uint32 {
	if x != nil {
		return x.MaxResults
	}
	return 0
}

type GetEntryListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntryInfo  []*EntryDetail `protobuf:"bytes,1,rep,name=entry_info,json=entryInfo,proto3" json:"entry_info,omitempty"`
	Pagination *Pagination    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetEntryListResponse) Reset() {
	*x = GetEntryListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntryListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntryListResponse) ProtoMessage() {}

func (x *GetEntryListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntryListResponse.ProtoReflect.Descriptor instead.
func (*GetEntryListResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{30}
}

func (x *GetEntryListResponse) GetEntryInfo() []*EntryDetail {
	if x != nil {
		return x.EntryInfo
	}
	return nil
}

func (x *GetEntryListResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type EntryDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpCode      uint32  `protobuf:"varint,1,opt,name=op_code,json=opCode,proto3" json:"op_code,omitempty"`
	CreatedTime string  `protobuf:"bytes,2,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	Amount      float64 `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Balance     float64 `protobuf:"fixed64,4,opt,name=balance,proto3" json:"balance,omitempty"`
	RefId       string  `protobuf:"bytes,5,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	Memo        string  `protobuf:"bytes,6,opt,name=memo,proto3" json:"memo,omitempty"`
}

func (x *EntryDetail) Reset() {
	*x = EntryDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntryDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntryDetail) ProtoMessage() {}

func (x *EntryDetail) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntryDetail.ProtoReflect.Descriptor instead.
func (*EntryDetail) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{31}
}

func (x *EntryDetail) GetOpCode() uint32 {
	if x != nil {
		return x.OpCode
	}
	return 0
}

func (x *EntryDetail) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *EntryDetail) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *EntryDetail) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *EntryDetail) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *EntryDetail) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

type GetTotalAmountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartTime   string   `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime     string   `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OpCodeGroup []string `protobuf:"bytes,4,rep,name=op_code_group,json=opCodeGroup,proto3" json:"op_code_group,omitempty"`
}

func (x *GetTotalAmountRequest) Reset() {
	*x = GetTotalAmountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTotalAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTotalAmountRequest) ProtoMessage() {}

func (x *GetTotalAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTotalAmountRequest.ProtoReflect.Descriptor instead.
func (*GetTotalAmountRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{32}
}

func (x *GetTotalAmountRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetTotalAmountRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetTotalAmountRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetTotalAmountRequest) GetOpCodeGroup() []string {
	if x != nil {
		return x.OpCodeGroup
	}
	return nil
}

type GetTotalAmountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total float64 `protobuf:"fixed64,1,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetTotalAmountResponse) Reset() {
	*x = GetTotalAmountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTotalAmountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTotalAmountResponse) ProtoMessage() {}

func (x *GetTotalAmountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTotalAmountResponse.ProtoReflect.Descriptor instead.
func (*GetTotalAmountResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{33}
}

func (x *GetTotalAmountResponse) GetTotal() float64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetOpCodeListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultType *StringValue `protobuf:"bytes,1,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	GameKind   *Uint32Value `protobuf:"bytes,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	DetailType *StringValue `protobuf:"bytes,3,opt,name=detail_type,json=detailType,proto3" json:"detail_type,omitempty"`
}

func (x *GetOpCodeListRequest) Reset() {
	*x = GetOpCodeListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpCodeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpCodeListRequest) ProtoMessage() {}

func (x *GetOpCodeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpCodeListRequest.ProtoReflect.Descriptor instead.
func (*GetOpCodeListRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{34}
}

func (x *GetOpCodeListRequest) GetResultType() *StringValue {
	if x != nil {
		return x.ResultType
	}
	return nil
}

func (x *GetOpCodeListRequest) GetGameKind() *Uint32Value {
	if x != nil {
		return x.GameKind
	}
	return nil
}

func (x *GetOpCodeListRequest) GetDetailType() *StringValue {
	if x != nil {
		return x.DetailType
	}
	return nil
}

type GetOpCodeListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpcodeInfo []*OpCodeDetail `protobuf:"bytes,1,rep,name=opcode_info,json=opcodeInfo,proto3" json:"opcode_info,omitempty"`
}

func (x *GetOpCodeListResponse) Reset() {
	*x = GetOpCodeListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpCodeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpCodeListResponse) ProtoMessage() {}

func (x *GetOpCodeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpCodeListResponse.ProtoReflect.Descriptor instead.
func (*GetOpCodeListResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{35}
}

func (x *GetOpCodeListResponse) GetOpcodeInfo() []*OpCodeDetail {
	if x != nil {
		return x.OpcodeInfo
	}
	return nil
}

type OpCodeDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CategoryId string `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ResultType string `protobuf:"bytes,3,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	GameKind   uint32 `protobuf:"varint,4,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId     string `protobuf:"bytes,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Note       string `protobuf:"bytes,6,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *OpCodeDetail) Reset() {
	*x = OpCodeDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpCodeDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpCodeDetail) ProtoMessage() {}

func (x *OpCodeDetail) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpCodeDetail.ProtoReflect.Descriptor instead.
func (*OpCodeDetail) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{36}
}

func (x *OpCodeDetail) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OpCodeDetail) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *OpCodeDetail) GetResultType() string {
	if x != nil {
		return x.ResultType
	}
	return ""
}

func (x *OpCodeDetail) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *OpCodeDetail) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *OpCodeDetail) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

type GetOpCodeDictRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *GetOpCodeDictRequest) Reset() {
	*x = GetOpCodeDictRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpCodeDictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpCodeDictRequest) ProtoMessage() {}

func (x *GetOpCodeDictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpCodeDictRequest.ProtoReflect.Descriptor instead.
func (*GetOpCodeDictRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{37}
}

func (x *GetOpCodeDictRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetOpCodeDictRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type GetOpCodeDictResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Dictionary string `protobuf:"bytes,2,opt,name=dictionary,proto3" json:"dictionary,omitempty"`
}

func (x *GetOpCodeDictResponse) Reset() {
	*x = GetOpCodeDictResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpCodeDictResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpCodeDictResponse) ProtoMessage() {}

func (x *GetOpCodeDictResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpCodeDictResponse.ProtoReflect.Descriptor instead.
func (*GetOpCodeDictResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{38}
}

func (x *GetOpCodeDictResponse) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetOpCodeDictResponse) GetDictionary() string {
	if x != nil {
		return x.Dictionary
	}
	return ""
}

type AccountCloseDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AccountCloseDateRequest) Reset() {
	*x = AccountCloseDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountCloseDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountCloseDateRequest) ProtoMessage() {}

func (x *AccountCloseDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountCloseDateRequest.ProtoReflect.Descriptor instead.
func (*AccountCloseDateRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{39}
}

type AccountCloseDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *AccountCloseDateResponse) Reset() {
	*x = AccountCloseDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountCloseDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountCloseDateResponse) ProtoMessage() {}

func (x *AccountCloseDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountCloseDateResponse.ProtoReflect.Descriptor instead.
func (*AccountCloseDateResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{40}
}

func (x *AccountCloseDateResponse) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

type Int64Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value int64 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Int64Value) Reset() {
	*x = Int64Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64Value) ProtoMessage() {}

func (x *Int64Value) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64Value.ProtoReflect.Descriptor instead.
func (*Int64Value) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{41}
}

func (x *Int64Value) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type GetTransferEntryDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency         string      `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"`
	StartTime        string      `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime          string      `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ParentId         uint32      `protobuf:"varint,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	ParentRole       uint32      `protobuf:"varint,5,opt,name=parent_role,json=parentRole,proto3" json:"parent_role,omitempty"`
	UserId           uint32      `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RefId            uint64      `protobuf:"varint,7,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	Opcode           []uint32    `protobuf:"varint,8,rep,packed,name=opcode,proto3" json:"opcode,omitempty"`
	GroupName        []string    `protobuf:"bytes,9,rep,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	DisableGroupName []string    `protobuf:"bytes,10,rep,name=disable_group_name,json=disableGroupName,proto3" json:"disable_group_name,omitempty"`
	GteAmount        *Int64Value `protobuf:"bytes,11,opt,name=gte_amount,json=gteAmount,proto3" json:"gte_amount,omitempty"`
	LteAmount        *Int64Value `protobuf:"bytes,12,opt,name=lte_amount,json=lteAmount,proto3" json:"lte_amount,omitempty"`
	Page             uint32      `protobuf:"varint,13,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit        uint32      `protobuf:"varint,14,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Sort             []string    `protobuf:"bytes,15,rep,name=sort,proto3" json:"sort,omitempty"`
	Order            []string    `protobuf:"bytes,16,rep,name=order,proto3" json:"order,omitempty"`
}

func (x *GetTransferEntryDetailRequest) Reset() {
	*x = GetTransferEntryDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransferEntryDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferEntryDetailRequest) ProtoMessage() {}

func (x *GetTransferEntryDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferEntryDetailRequest.ProtoReflect.Descriptor instead.
func (*GetTransferEntryDetailRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{42}
}

func (x *GetTransferEntryDetailRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetTransferEntryDetailRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetTransferEntryDetailRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetTransferEntryDetailRequest) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *GetTransferEntryDetailRequest) GetParentRole() uint32 {
	if x != nil {
		return x.ParentRole
	}
	return 0
}

func (x *GetTransferEntryDetailRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetTransferEntryDetailRequest) GetRefId() uint64 {
	if x != nil {
		return x.RefId
	}
	return 0
}

func (x *GetTransferEntryDetailRequest) GetOpcode() []uint32 {
	if x != nil {
		return x.Opcode
	}
	return nil
}

func (x *GetTransferEntryDetailRequest) GetGroupName() []string {
	if x != nil {
		return x.GroupName
	}
	return nil
}

func (x *GetTransferEntryDetailRequest) GetDisableGroupName() []string {
	if x != nil {
		return x.DisableGroupName
	}
	return nil
}

func (x *GetTransferEntryDetailRequest) GetGteAmount() *Int64Value {
	if x != nil {
		return x.GteAmount
	}
	return nil
}

func (x *GetTransferEntryDetailRequest) GetLteAmount() *Int64Value {
	if x != nil {
		return x.LteAmount
	}
	return nil
}

func (x *GetTransferEntryDetailRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetTransferEntryDetailRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *GetTransferEntryDetailRequest) GetSort() []string {
	if x != nil {
		return x.Sort
	}
	return nil
}

func (x *GetTransferEntryDetailRequest) GetOrder() []string {
	if x != nil {
		return x.Order
	}
	return nil
}

type TransferEntryDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	HallId    uint32  `protobuf:"varint,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	ParentId  uint32  `protobuf:"varint,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	UserId    uint32  `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Amount    float64 `protobuf:"fixed64,5,opt,name=amount,proto3" json:"amount,omitempty"`
	Balance   float64 `protobuf:"fixed64,6,opt,name=balance,proto3" json:"balance,omitempty"`
	CreatedAt string  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Currency  string  `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency,omitempty"`
	Memo      string  `protobuf:"bytes,9,opt,name=memo,proto3" json:"memo,omitempty"`
	Opcode    uint32  `protobuf:"varint,10,opt,name=opcode,proto3" json:"opcode,omitempty"`
	RefId     uint64  `protobuf:"varint,11,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
}

func (x *TransferEntryDetail) Reset() {
	*x = TransferEntryDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferEntryDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferEntryDetail) ProtoMessage() {}

func (x *TransferEntryDetail) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferEntryDetail.ProtoReflect.Descriptor instead.
func (*TransferEntryDetail) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{43}
}

func (x *TransferEntryDetail) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TransferEntryDetail) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *TransferEntryDetail) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *TransferEntryDetail) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TransferEntryDetail) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *TransferEntryDetail) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *TransferEntryDetail) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *TransferEntryDetail) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *TransferEntryDetail) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *TransferEntryDetail) GetOpcode() uint32 {
	if x != nil {
		return x.Opcode
	}
	return 0
}

func (x *TransferEntryDetail) GetRefId() uint64 {
	if x != nil {
		return x.RefId
	}
	return 0
}

type GetTransferEntryDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntryDetail []*TransferEntryDetail `protobuf:"bytes,1,rep,name=entry_detail,json=entryDetail,proto3" json:"entry_detail,omitempty"`
	Total       uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetTransferEntryDetailResponse) Reset() {
	*x = GetTransferEntryDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransferEntryDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferEntryDetailResponse) ProtoMessage() {}

func (x *GetTransferEntryDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferEntryDetailResponse.ProtoReflect.Descriptor instead.
func (*GetTransferEntryDetailResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{44}
}

func (x *GetTransferEntryDetailResponse) GetEntryDetail() []*TransferEntryDetail {
	if x != nil {
		return x.EntryDetail
	}
	return nil
}

func (x *GetTransferEntryDetailResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetUserCashFakeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetUserCashFakeRequest) Reset() {
	*x = GetUserCashFakeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCashFakeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCashFakeRequest) ProtoMessage() {}

func (x *GetUserCashFakeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCashFakeRequest.ProtoReflect.Descriptor instead.
func (*GetUserCashFakeRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{45}
}

func (x *GetUserCashFakeRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserCashFakeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId    uint32  `protobuf:"varint,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Balance     float64 `protobuf:"fixed64,2,opt,name=balance,proto3" json:"balance,omitempty"`
	Currency    string  `protobuf:"bytes,3,opt,name=currency,proto3" json:"currency,omitempty"`
	Enable      bool    `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`
	LastEntryAt string  `protobuf:"bytes,5,opt,name=last_entry_at,json=lastEntryAt,proto3" json:"last_entry_at,omitempty"`
}

func (x *GetUserCashFakeResponse) Reset() {
	*x = GetUserCashFakeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCashFakeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCashFakeResponse) ProtoMessage() {}

func (x *GetUserCashFakeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCashFakeResponse.ProtoReflect.Descriptor instead.
func (*GetUserCashFakeResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{46}
}

func (x *GetUserCashFakeResponse) GetWalletId() uint32 {
	if x != nil {
		return x.WalletId
	}
	return 0
}

func (x *GetUserCashFakeResponse) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *GetUserCashFakeResponse) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetUserCashFakeResponse) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *GetUserCashFakeResponse) GetLastEntryAt() string {
	if x != nil {
		return x.LastEntryAt
	}
	return ""
}

type GetOpcodeListByACCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Opcode               []uint32   `protobuf:"varint,1,rep,packed,name=opcode,proto3" json:"opcode,omitempty"`
	AllowBalanceNegative *BoolValue `protobuf:"bytes,2,opt,name=allow_balance_negative,json=allowBalanceNegative,proto3" json:"allow_balance_negative,omitempty"`
	AllowAmountZero      *BoolValue `protobuf:"bytes,3,opt,name=allow_amount_zero,json=allowAmountZero,proto3" json:"allow_amount_zero,omitempty"`
	DisableNotAllow      *BoolValue `protobuf:"bytes,4,opt,name=disable_not_allow,json=disableNotAllow,proto3" json:"disable_not_allow,omitempty"`
	DisableForCashFake   *BoolValue `protobuf:"bytes,5,opt,name=disable_for_cash_fake,json=disableForCashFake,proto3" json:"disable_for_cash_fake,omitempty"`
	CashDepositOpcode    *BoolValue `protobuf:"bytes,6,opt,name=cash_deposit_opcode,json=cashDepositOpcode,proto3" json:"cash_deposit_opcode,omitempty"`
	IsEnabled            *BoolValue `protobuf:"bytes,7,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Tw                   string     `protobuf:"bytes,8,opt,name=tw,proto3" json:"tw,omitempty"`
	Cn                   string     `protobuf:"bytes,9,opt,name=cn,proto3" json:"cn,omitempty"`
	En                   string     `protobuf:"bytes,10,opt,name=en,proto3" json:"en,omitempty"`
	Sort                 []string   `protobuf:"bytes,11,rep,name=sort,proto3" json:"sort,omitempty"`
	Order                []string   `protobuf:"bytes,12,rep,name=order,proto3" json:"order,omitempty"`
	Page                 uint32     `protobuf:"varint,13,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit            uint32     `protobuf:"varint,14,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetOpcodeListByACCRequest) Reset() {
	*x = GetOpcodeListByACCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpcodeListByACCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpcodeListByACCRequest) ProtoMessage() {}

func (x *GetOpcodeListByACCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpcodeListByACCRequest.ProtoReflect.Descriptor instead.
func (*GetOpcodeListByACCRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{47}
}

func (x *GetOpcodeListByACCRequest) GetOpcode() []uint32 {
	if x != nil {
		return x.Opcode
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetAllowBalanceNegative() *BoolValue {
	if x != nil {
		return x.AllowBalanceNegative
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetAllowAmountZero() *BoolValue {
	if x != nil {
		return x.AllowAmountZero
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetDisableNotAllow() *BoolValue {
	if x != nil {
		return x.DisableNotAllow
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetDisableForCashFake() *BoolValue {
	if x != nil {
		return x.DisableForCashFake
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetCashDepositOpcode() *BoolValue {
	if x != nil {
		return x.CashDepositOpcode
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetIsEnabled() *BoolValue {
	if x != nil {
		return x.IsEnabled
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetTw() string {
	if x != nil {
		return x.Tw
	}
	return ""
}

func (x *GetOpcodeListByACCRequest) GetCn() string {
	if x != nil {
		return x.Cn
	}
	return ""
}

func (x *GetOpcodeListByACCRequest) GetEn() string {
	if x != nil {
		return x.En
	}
	return ""
}

func (x *GetOpcodeListByACCRequest) GetSort() []string {
	if x != nil {
		return x.Sort
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetOrder() []string {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *GetOpcodeListByACCRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetOpcodeListByACCRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type GetOpcodeListByACCResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpcodeInfo []*OpcodeDetailByACC `protobuf:"bytes,1,rep,name=opcode_info,json=opcodeInfo,proto3" json:"opcode_info,omitempty"`
	Pagination *PaginationByGTI     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetOpcodeListByACCResponse) Reset() {
	*x = GetOpcodeListByACCResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpcodeListByACCResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpcodeListByACCResponse) ProtoMessage() {}

func (x *GetOpcodeListByACCResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpcodeListByACCResponse.ProtoReflect.Descriptor instead.
func (*GetOpcodeListByACCResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{48}
}

func (x *GetOpcodeListByACCResponse) GetOpcodeInfo() []*OpcodeDetailByACC {
	if x != nil {
		return x.OpcodeInfo
	}
	return nil
}

func (x *GetOpcodeListByACCResponse) GetPagination() *PaginationByGTI {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type OpcodeDetailByACC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Opcode               uint32 `protobuf:"varint,1,opt,name=opcode,proto3" json:"opcode,omitempty"`
	AllowBalanceNegative bool   `protobuf:"varint,2,opt,name=allow_balance_negative,json=allowBalanceNegative,proto3" json:"allow_balance_negative,omitempty"`
	AllowAmountZero      bool   `protobuf:"varint,3,opt,name=allow_amount_zero,json=allowAmountZero,proto3" json:"allow_amount_zero,omitempty"`
	DisableNotAllow      bool   `protobuf:"varint,4,opt,name=disable_not_allow,json=disableNotAllow,proto3" json:"disable_not_allow,omitempty"`
	DisableForCashFake   bool   `protobuf:"varint,5,opt,name=disable_for_cash_fake,json=disableForCashFake,proto3" json:"disable_for_cash_fake,omitempty"`
	CashDepositOpcode    bool   `protobuf:"varint,6,opt,name=cash_deposit_opcode,json=cashDepositOpcode,proto3" json:"cash_deposit_opcode,omitempty"`
	IsEnabled            bool   `protobuf:"varint,7,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Tw                   string `protobuf:"bytes,8,opt,name=tw,proto3" json:"tw,omitempty"`
	Cn                   string `protobuf:"bytes,9,opt,name=cn,proto3" json:"cn,omitempty"`
	En                   string `protobuf:"bytes,10,opt,name=en,proto3" json:"en,omitempty"`
}

func (x *OpcodeDetailByACC) Reset() {
	*x = OpcodeDetailByACC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpcodeDetailByACC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpcodeDetailByACC) ProtoMessage() {}

func (x *OpcodeDetailByACC) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpcodeDetailByACC.ProtoReflect.Descriptor instead.
func (*OpcodeDetailByACC) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{49}
}

func (x *OpcodeDetailByACC) GetOpcode() uint32 {
	if x != nil {
		return x.Opcode
	}
	return 0
}

func (x *OpcodeDetailByACC) GetAllowBalanceNegative() bool {
	if x != nil {
		return x.AllowBalanceNegative
	}
	return false
}

func (x *OpcodeDetailByACC) GetAllowAmountZero() bool {
	if x != nil {
		return x.AllowAmountZero
	}
	return false
}

func (x *OpcodeDetailByACC) GetDisableNotAllow() bool {
	if x != nil {
		return x.DisableNotAllow
	}
	return false
}

func (x *OpcodeDetailByACC) GetDisableForCashFake() bool {
	if x != nil {
		return x.DisableForCashFake
	}
	return false
}

func (x *OpcodeDetailByACC) GetCashDepositOpcode() bool {
	if x != nil {
		return x.CashDepositOpcode
	}
	return false
}

func (x *OpcodeDetailByACC) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *OpcodeDetailByACC) GetTw() string {
	if x != nil {
		return x.Tw
	}
	return ""
}

func (x *OpcodeDetailByACC) GetCn() string {
	if x != nil {
		return x.Cn
	}
	return ""
}

func (x *OpcodeDetailByACC) GetEn() string {
	if x != nil {
		return x.En
	}
	return ""
}

type PaginationByGTI struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   uint32 `protobuf:"varint,2,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Total       uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPage   uint32 `protobuf:"varint,4,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
}

func (x *PaginationByGTI) Reset() {
	*x = PaginationByGTI{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaginationByGTI) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationByGTI) ProtoMessage() {}

func (x *PaginationByGTI) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationByGTI.ProtoReflect.Descriptor instead.
func (*PaginationByGTI) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{50}
}

func (x *PaginationByGTI) GetCurrentPage() uint32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *PaginationByGTI) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *PaginationByGTI) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PaginationByGTI) GetTotalPage() uint32 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetExchangeRateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency    string   `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"`
	Sort        []string `protobuf:"bytes,2,rep,name=sort,proto3" json:"sort,omitempty"`
	Order       []string `protobuf:"bytes,3,rep,name=order,proto3" json:"order,omitempty"`
	CurrentPage uint32   `protobuf:"varint,4,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   uint32   `protobuf:"varint,5,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetExchangeRateRequest) Reset() {
	*x = GetExchangeRateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeRateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeRateRequest) ProtoMessage() {}

func (x *GetExchangeRateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeRateRequest.ProtoReflect.Descriptor instead.
func (*GetExchangeRateRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{51}
}

func (x *GetExchangeRateRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetExchangeRateRequest) GetSort() []string {
	if x != nil {
		return x.Sort
	}
	return nil
}

func (x *GetExchangeRateRequest) GetOrder() []string {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *GetExchangeRateRequest) GetCurrentPage() uint32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *GetExchangeRateRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type ExchangeRate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Currency string  `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency,omitempty"`
	Buy      float64 `protobuf:"fixed64,3,opt,name=buy,proto3" json:"buy,omitempty"`
	Sell     float64 `protobuf:"fixed64,4,opt,name=sell,proto3" json:"sell,omitempty"`
	Basic    float64 `protobuf:"fixed64,5,opt,name=basic,proto3" json:"basic,omitempty"`
	ActiveAt string  `protobuf:"bytes,6,opt,name=active_at,json=activeAt,proto3" json:"active_at,omitempty"`
}

func (x *ExchangeRate) Reset() {
	*x = ExchangeRate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRate) ProtoMessage() {}

func (x *ExchangeRate) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRate.ProtoReflect.Descriptor instead.
func (*ExchangeRate) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{52}
}

func (x *ExchangeRate) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExchangeRate) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *ExchangeRate) GetBuy() float64 {
	if x != nil {
		return x.Buy
	}
	return 0
}

func (x *ExchangeRate) GetSell() float64 {
	if x != nil {
		return x.Sell
	}
	return 0
}

func (x *ExchangeRate) GetBasic() float64 {
	if x != nil {
		return x.Basic
	}
	return 0
}

func (x *ExchangeRate) GetActiveAt() string {
	if x != nil {
		return x.ActiveAt
	}
	return ""
}

type GetExchangeRateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExchangeRate []*ExchangeRate  `protobuf:"bytes,1,rep,name=exchange_rate,json=exchangeRate,proto3" json:"exchange_rate,omitempty"`
	Pagination   *PaginationByGTI `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetExchangeRateResponse) Reset() {
	*x = GetExchangeRateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeRateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeRateResponse) ProtoMessage() {}

func (x *GetExchangeRateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeRateResponse.ProtoReflect.Descriptor instead.
func (*GetExchangeRateResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{53}
}

func (x *GetExchangeRateResponse) GetExchangeRate() []*ExchangeRate {
	if x != nil {
		return x.ExchangeRate
	}
	return nil
}

func (x *GetExchangeRateResponse) GetPagination() *PaginationByGTI {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type ExchangeRateListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ExchangeRate `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ExchangeRateListResponse) Reset() {
	*x = ExchangeRateListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeRateListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRateListResponse) ProtoMessage() {}

func (x *ExchangeRateListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRateListResponse.ProtoReflect.Descriptor instead.
func (*ExchangeRateListResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{54}
}

func (x *ExchangeRateListResponse) GetList() []*ExchangeRate {
	if x != nil {
		return x.List
	}
	return nil
}

type GetOpcodeGroupsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupName       string   `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	ParentGroupName string   `protobuf:"bytes,2,opt,name=parent_group_name,json=parentGroupName,proto3" json:"parent_group_name,omitempty"`
	Opcode          uint32   `protobuf:"varint,3,opt,name=opcode,proto3" json:"opcode,omitempty"`
	Sort            []string `protobuf:"bytes,11,rep,name=sort,proto3" json:"sort,omitempty"`
	Order           []string `protobuf:"bytes,12,rep,name=order,proto3" json:"order,omitempty"`
	Page            uint32   `protobuf:"varint,13,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit       uint32   `protobuf:"varint,14,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetOpcodeGroupsListRequest) Reset() {
	*x = GetOpcodeGroupsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpcodeGroupsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpcodeGroupsListRequest) ProtoMessage() {}

func (x *GetOpcodeGroupsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpcodeGroupsListRequest.ProtoReflect.Descriptor instead.
func (*GetOpcodeGroupsListRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{55}
}

func (x *GetOpcodeGroupsListRequest) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *GetOpcodeGroupsListRequest) GetParentGroupName() string {
	if x != nil {
		return x.ParentGroupName
	}
	return ""
}

func (x *GetOpcodeGroupsListRequest) GetOpcode() uint32 {
	if x != nil {
		return x.Opcode
	}
	return 0
}

func (x *GetOpcodeGroupsListRequest) GetSort() []string {
	if x != nil {
		return x.Sort
	}
	return nil
}

func (x *GetOpcodeGroupsListRequest) GetOrder() []string {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *GetOpcodeGroupsListRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetOpcodeGroupsListRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type GetOpcodeGroupsListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpcodeGroupsList []*OpcodeGroupsList `protobuf:"bytes,1,rep,name=opcode_groups_list,json=opcodeGroupsList,proto3" json:"opcode_groups_list,omitempty"`
	Pagination       *PaginationByGTI    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetOpcodeGroupsListResponse) Reset() {
	*x = GetOpcodeGroupsListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpcodeGroupsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpcodeGroupsListResponse) ProtoMessage() {}

func (x *GetOpcodeGroupsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpcodeGroupsListResponse.ProtoReflect.Descriptor instead.
func (*GetOpcodeGroupsListResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{56}
}

func (x *GetOpcodeGroupsListResponse) GetOpcodeGroupsList() []*OpcodeGroupsList {
	if x != nil {
		return x.OpcodeGroupsList
	}
	return nil
}

func (x *GetOpcodeGroupsListResponse) GetPagination() *PaginationByGTI {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type OpcodeGroupsList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GroupName       string `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	ParentGroupName string `protobuf:"bytes,3,opt,name=parent_group_name,json=parentGroupName,proto3" json:"parent_group_name,omitempty"`
	Opcode          uint32 `protobuf:"varint,4,opt,name=opcode,proto3" json:"opcode,omitempty"`
}

func (x *OpcodeGroupsList) Reset() {
	*x = OpcodeGroupsList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpcodeGroupsList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpcodeGroupsList) ProtoMessage() {}

func (x *OpcodeGroupsList) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpcodeGroupsList.ProtoReflect.Descriptor instead.
func (*OpcodeGroupsList) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{57}
}

func (x *OpcodeGroupsList) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OpcodeGroupsList) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *OpcodeGroupsList) GetParentGroupName() string {
	if x != nil {
		return x.ParentGroupName
	}
	return ""
}

func (x *OpcodeGroupsList) GetOpcode() uint32 {
	if x != nil {
		return x.Opcode
	}
	return 0
}

type GetTransferEntryDetailAllRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency     string     `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"`
	StartTime    string     `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime      string     `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	UserId       uint32     `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FilterOpcode *BoolValue `protobuf:"bytes,5,opt,name=filter_opcode,json=filterOpcode,proto3" json:"filter_opcode,omitempty"`
	GroupName    []string   `protobuf:"bytes,6,rep,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Opcode       []uint32   `protobuf:"varint,7,rep,packed,name=opcode,proto3" json:"opcode,omitempty"`
	RefId        uint64     `protobuf:"varint,8,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	Order        []string   `protobuf:"bytes,9,rep,name=order,proto3" json:"order,omitempty"`
	Sort         []string   `protobuf:"bytes,10,rep,name=sort,proto3" json:"sort,omitempty"`
	Page         uint32     `protobuf:"varint,11,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit    uint32     `protobuf:"varint,12,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetTransferEntryDetailAllRequest) Reset() {
	*x = GetTransferEntryDetailAllRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransferEntryDetailAllRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferEntryDetailAllRequest) ProtoMessage() {}

func (x *GetTransferEntryDetailAllRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferEntryDetailAllRequest.ProtoReflect.Descriptor instead.
func (*GetTransferEntryDetailAllRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{58}
}

func (x *GetTransferEntryDetailAllRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetTransferEntryDetailAllRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetTransferEntryDetailAllRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetTransferEntryDetailAllRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetTransferEntryDetailAllRequest) GetFilterOpcode() *BoolValue {
	if x != nil {
		return x.FilterOpcode
	}
	return nil
}

func (x *GetTransferEntryDetailAllRequest) GetGroupName() []string {
	if x != nil {
		return x.GroupName
	}
	return nil
}

func (x *GetTransferEntryDetailAllRequest) GetOpcode() []uint32 {
	if x != nil {
		return x.Opcode
	}
	return nil
}

func (x *GetTransferEntryDetailAllRequest) GetRefId() uint64 {
	if x != nil {
		return x.RefId
	}
	return 0
}

func (x *GetTransferEntryDetailAllRequest) GetOrder() []string {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *GetTransferEntryDetailAllRequest) GetSort() []string {
	if x != nil {
		return x.Sort
	}
	return nil
}

func (x *GetTransferEntryDetailAllRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetTransferEntryDetailAllRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type GetTransferEntryDetailAllResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntryDetail []*TransferEntryDetail `protobuf:"bytes,1,rep,name=entry_detail,json=entryDetail,proto3" json:"entry_detail,omitempty"`
	Pagination  *PaginationByGTI       `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetTransferEntryDetailAllResponse) Reset() {
	*x = GetTransferEntryDetailAllResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransferEntryDetailAllResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransferEntryDetailAllResponse) ProtoMessage() {}

func (x *GetTransferEntryDetailAllResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransferEntryDetailAllResponse.ProtoReflect.Descriptor instead.
func (*GetTransferEntryDetailAllResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{59}
}

func (x *GetTransferEntryDetailAllResponse) GetEntryDetail() []*TransferEntryDetail {
	if x != nil {
		return x.EntryDetail
	}
	return nil
}

func (x *GetTransferEntryDetailAllResponse) GetPagination() *PaginationByGTI {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetCurrencyListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsVirtual bool `protobuf:"varint,1,opt,name=is_virtual,json=isVirtual,proto3" json:"is_virtual,omitempty"`
}

func (x *GetCurrencyListRequest) Reset() {
	*x = GetCurrencyListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyListRequest) ProtoMessage() {}

func (x *GetCurrencyListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyListRequest.ProtoReflect.Descriptor instead.
func (*GetCurrencyListRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{60}
}

func (x *GetCurrencyListRequest) GetIsVirtual() bool {
	if x != nil {
		return x.IsVirtual
	}
	return false
}

type GetCurrencyListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency []string `protobuf:"bytes,1,rep,name=currency,proto3" json:"currency,omitempty"`
}

func (x *GetCurrencyListResponse) Reset() {
	*x = GetCurrencyListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyListResponse) ProtoMessage() {}

func (x *GetCurrencyListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyListResponse.ProtoReflect.Descriptor instead.
func (*GetCurrencyListResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{61}
}

func (x *GetCurrencyListResponse) GetCurrency() []string {
	if x != nil {
		return x.Currency
	}
	return nil
}

var File_wallet_proto protoreflect.FileDescriptor

var file_wallet_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a, 0x0b, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x2b, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x66, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21,
	0x0a, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x66, 0x69, 0x72, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x12, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x22, 0x91,
	0x01, 0x0a, 0x13, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x48, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x22, 0x41, 0x0a, 0x14,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22,
	0x40, 0x0a, 0x0b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x34, 0x0a, 0x19, 0x53, 0x6c, 0x6f, 0x74, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x1a, 0x53, 0x6c, 0x6f, 0x74, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22,
	0x59, 0x0a, 0x0f, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x22, 0x2c, 0x0a, 0x10, 0x57, 0x69,
	0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x58, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66,
	0x49, 0x64, 0x22, 0x2b, 0x0a, 0x0f, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22,
	0xc1, 0x02, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x3c, 0x0a,
	0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0b, 0x66, 0x69, 0x72, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a,
	0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x73, 0x22, 0x78, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xec, 0x01,
	0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x22, 0x95, 0x03, 0x0a,
	0x18, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x2a, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x33, 0x0a,
	0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6f, 0x70, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x32, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0b, 0x66, 0x69, 0x72, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34,
	0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x22, 0x7c, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2b, 0x0a, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x32,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xd8, 0x01, 0x0a, 0x1a, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2f,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x36, 0x0a, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x83, 0x01,
	0x0a, 0x1b, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a,
	0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x32, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xb8, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x34,
	0x0a, 0x19, 0x46, 0x69, 0x73, 0x68, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x1a, 0x46, 0x69, 0x73, 0x68, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x30, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x32,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x22, 0xe4, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x0d, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d,
	0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x7e, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x32, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa6, 0x01, 0x0a, 0x0b, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x70, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x65,
	0x6d, 0x6f, 0x22, 0x8e, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0d, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x22, 0x2e, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0xb4, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x4b, 0x69, 0x6e, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x4e, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a,
	0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xaa, 0x01, 0x0a, 0x0c, 0x4f,
	0x70, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0x42, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4f, 0x70,
	0x43, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x47, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x72, 0x79, 0x22, 0x19, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x2e, 0x0a, 0x18, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22,
	0x22, 0x0a, 0x0a, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x8b, 0x04, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31,
	0x0a, 0x0a, 0x67, 0x74, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x67, 0x74, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6c, 0x74, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x6c, 0x74, 0x65, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x22, 0xa4, 0x02, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0x31, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61, 0x73, 0x68, 0x46,
	0x61, 0x6b, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x61, 0x73, 0x68, 0x46, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x41, 0x74, 0x22, 0xc2,
	0x04, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x41, 0x43, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x70,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x47, 0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x3d, 0x0a,
	0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x7a, 0x65,
	0x72, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5a, 0x65, 0x72, 0x6f, 0x12, 0x3d, 0x0a, 0x11,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x44, 0x0a, 0x15, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f,
	0x66, 0x61, 0x6b, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x68, 0x46, 0x61, 0x6b,
	0x65, 0x12, 0x41, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x11, 0x63, 0x61, 0x73, 0x68, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x70,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x69, 0x73, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x77, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x74, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x6e, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x63, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x43, 0x43, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x41,
	0x43, 0x43, 0x52, 0x0a, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x47, 0x54, 0x49, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xeb, 0x02, 0x0a, 0x11, 0x4f, 0x70, 0x63, 0x6f,
	0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x41, 0x43, 0x43, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f,
	0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x7a, 0x65, 0x72, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5a, 0x65, 0x72, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x41, 0x6c,
	0x6c, 0x6f, 0x77, 0x12, 0x31, 0x0a, 0x15, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66,
	0x6f, 0x72, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x66, 0x61, 0x6b, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x61,
	0x73, 0x68, 0x46, 0x61, 0x6b, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x73, 0x68, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x74, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x63, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x65, 0x6e, 0x22, 0x88, 0x01, 0x0a, 0x0f, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x47, 0x54, 0x49, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65,
	0x22, 0xa0, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x22, 0x93, 0x01, 0x0a, 0x0c, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x62, 0x75, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x62,
	0x75, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x04, 0x73, 0x65, 0x6c, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x61, 0x73, 0x69, 0x63, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x62, 0x61, 0x73, 0x69, 0x63, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x47, 0x54, 0x49, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x44, 0x0a, 0x18, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0xdc, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x9e,
	0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46,
	0x0a, 0x12, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x10, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79,
	0x47, 0x54, 0x49, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x85, 0x01, 0x0a, 0x10, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xf4, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0d, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x06, 0x6f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x9c,
	0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x47, 0x54,
	0x49, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x37, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69,
	0x72, 0x74, 0x75, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x56,
	0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x22, 0x35, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x32, 0xa1, 0x0f,
	0x0a, 0x06, 0x57, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x46, 0x0a, 0x0b, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4c, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x1b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b,
	0x0a, 0x12, 0x53, 0x6c, 0x6f, 0x74, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x53, 0x6c,
	0x6f, 0x74, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x57,
	0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x12, 0x17, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x18, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72,
	0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x12, 0x16, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1c, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b,
	0x0a, 0x12, 0x46, 0x69, 0x73, 0x68, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x46, 0x69,
	0x73, 0x68, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x46, 0x69, 0x73, 0x68, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x1d, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x49, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1b, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x2e, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x63, 0x74, 0x12, 0x1c, 0x2e, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x63, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x67, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x2e, 0x77, 0x61,
	0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61, 0x73, 0x68, 0x46, 0x61, 0x6b, 0x65, 0x12, 0x1e, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61,
	0x73, 0x68, 0x46, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61,
	0x73, 0x68, 0x46, 0x61, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x41, 0x43, 0x43, 0x12, 0x21, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x43, 0x43,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x41, 0x43, 0x43, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1e,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4a, 0x0a, 0x10, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x77, 0x61, 0x6c, 0x6c,
	0x65, 0x74, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x22, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x41, 0x6c, 0x6c, 0x12, 0x28, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1e, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x0e, 0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x77, 0x61, 0x6c, 0x6c, 0x65,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_wallet_proto_rawDescOnce sync.Once
	file_wallet_proto_rawDescData = file_wallet_proto_rawDesc
)

func file_wallet_proto_rawDescGZIP() []byte {
	file_wallet_proto_rawDescOnce.Do(func() {
		file_wallet_proto_rawDescData = protoimpl.X.CompressGZIP(file_wallet_proto_rawDescData)
	})
	return file_wallet_proto_rawDescData
}

var file_wallet_proto_msgTypes = make([]protoimpl.MessageInfo, 62)
var file_wallet_proto_goTypes = []interface{}{
	(*StringValue)(nil),                       // 0: wallet.StringValue
	(*Uint32Value)(nil),                       // 1: wallet.Uint32Value
	(*BoolValue)(nil),                         // 2: wallet.BoolValue
	(*RepeatedUint32Value)(nil),               // 3: wallet.RepeatedUint32Value
	(*EmptyRequest)(nil),                      // 4: wallet.EmptyRequest
	(*Pagination)(nil),                        // 5: wallet.Pagination
	(*EntryStatusRequest)(nil),                // 6: wallet.EntryStatusRequest
	(*EntryStatusResponse)(nil),               // 7: wallet.EntryStatusResponse
	(*UsersBalanceRequest)(nil),               // 8: wallet.UsersBalanceRequest
	(*UsersBalanceResponse)(nil),              // 9: wallet.UsersBalanceResponse
	(*BalanceInfo)(nil),                       // 10: wallet.BalanceInfo
	(*SlotMachineBalanceRequest)(nil),         // 11: wallet.SlotMachineBalanceRequest
	(*SlotMachineBalanceResponse)(nil),        // 12: wallet.SlotMachineBalanceResponse
	(*WithdrawRequest)(nil),                   // 13: wallet.WithdrawRequest
	(*WithdrawResponse)(nil),                  // 14: wallet.WithdrawResponse
	(*DepositRequest)(nil),                    // 15: wallet.DepositRequest
	(*DepositResponse)(nil),                   // 16: wallet.DepositResponse
	(*TransferEntryRequest)(nil),              // 17: wallet.TransferEntryRequest
	(*TransferEntryResponse)(nil),             // 18: wallet.TransferEntryResponse
	(*TransferEntry)(nil),                     // 19: wallet.TransferEntry
	(*TransferEntryListRequest)(nil),          // 20: wallet.TransferEntryListRequest
	(*TransferEntryListResponse)(nil),         // 21: wallet.TransferEntryListResponse
	(*UsersBalanceByAgentRequest)(nil),        // 22: wallet.UsersBalanceByAgentRequest
	(*UsersBalanceByAgentResponse)(nil),       // 23: wallet.UsersBalanceByAgentResponse
	(*BalanceByAgentInfo)(nil),                // 24: wallet.BalanceByAgentInfo
	(*FishMachineBalanceRequest)(nil),         // 25: wallet.FishMachineBalanceRequest
	(*FishMachineBalanceResponse)(nil),        // 26: wallet.FishMachineBalanceResponse
	(*GetUserBalanceRequest)(nil),             // 27: wallet.GetUserBalanceRequest
	(*GetUserBalanceResponse)(nil),            // 28: wallet.GetUserBalanceResponse
	(*GetEntryListRequest)(nil),               // 29: wallet.GetEntryListRequest
	(*GetEntryListResponse)(nil),              // 30: wallet.GetEntryListResponse
	(*EntryDetail)(nil),                       // 31: wallet.EntryDetail
	(*GetTotalAmountRequest)(nil),             // 32: wallet.GetTotalAmountRequest
	(*GetTotalAmountResponse)(nil),            // 33: wallet.GetTotalAmountResponse
	(*GetOpCodeListRequest)(nil),              // 34: wallet.GetOpCodeListRequest
	(*GetOpCodeListResponse)(nil),             // 35: wallet.GetOpCodeListResponse
	(*OpCodeDetail)(nil),                      // 36: wallet.OpCodeDetail
	(*GetOpCodeDictRequest)(nil),              // 37: wallet.GetOpCodeDictRequest
	(*GetOpCodeDictResponse)(nil),             // 38: wallet.GetOpCodeDictResponse
	(*AccountCloseDateRequest)(nil),           // 39: wallet.AccountCloseDateRequest
	(*AccountCloseDateResponse)(nil),          // 40: wallet.AccountCloseDateResponse
	(*Int64Value)(nil),                        // 41: wallet.Int64Value
	(*GetTransferEntryDetailRequest)(nil),     // 42: wallet.GetTransferEntryDetailRequest
	(*TransferEntryDetail)(nil),               // 43: wallet.TransferEntryDetail
	(*GetTransferEntryDetailResponse)(nil),    // 44: wallet.GetTransferEntryDetailResponse
	(*GetUserCashFakeRequest)(nil),            // 45: wallet.GetUserCashFakeRequest
	(*GetUserCashFakeResponse)(nil),           // 46: wallet.GetUserCashFakeResponse
	(*GetOpcodeListByACCRequest)(nil),         // 47: wallet.GetOpcodeListByACCRequest
	(*GetOpcodeListByACCResponse)(nil),        // 48: wallet.GetOpcodeListByACCResponse
	(*OpcodeDetailByACC)(nil),                 // 49: wallet.OpcodeDetailByACC
	(*PaginationByGTI)(nil),                   // 50: wallet.PaginationByGTI
	(*GetExchangeRateRequest)(nil),            // 51: wallet.GetExchangeRateRequest
	(*ExchangeRate)(nil),                      // 52: wallet.ExchangeRate
	(*GetExchangeRateResponse)(nil),           // 53: wallet.GetExchangeRateResponse
	(*ExchangeRateListResponse)(nil),          // 54: wallet.ExchangeRateListResponse
	(*GetOpcodeGroupsListRequest)(nil),        // 55: wallet.GetOpcodeGroupsListRequest
	(*GetOpcodeGroupsListResponse)(nil),       // 56: wallet.GetOpcodeGroupsListResponse
	(*OpcodeGroupsList)(nil),                  // 57: wallet.OpcodeGroupsList
	(*GetTransferEntryDetailAllRequest)(nil),  // 58: wallet.GetTransferEntryDetailAllRequest
	(*GetTransferEntryDetailAllResponse)(nil), // 59: wallet.GetTransferEntryDetailAllResponse
	(*GetCurrencyListRequest)(nil),            // 60: wallet.GetCurrencyListRequest
	(*GetCurrencyListResponse)(nil),           // 61: wallet.GetCurrencyListResponse
}
var file_wallet_proto_depIdxs = []int32{
	10, // 0: wallet.UsersBalanceResponse.users:type_name -> wallet.BalanceInfo
	0,  // 1: wallet.TransferEntryRequest.ref_id:type_name -> wallet.StringValue
	0,  // 2: wallet.TransferEntryRequest.transfer_action:type_name -> wallet.StringValue
	1,  // 3: wallet.TransferEntryRequest.first_result:type_name -> wallet.Uint32Value
	1,  // 4: wallet.TransferEntryRequest.max_results:type_name -> wallet.Uint32Value
	19, // 5: wallet.TransferEntryResponse.entry:type_name -> wallet.TransferEntry
	5,  // 6: wallet.TransferEntryResponse.pagination:type_name -> wallet.Pagination
	1,  // 7: wallet.TransferEntryListRequest.level:type_name -> wallet.Uint32Value
	0,  // 8: wallet.TransferEntryListRequest.ref_id:type_name -> wallet.StringValue
	3,  // 9: wallet.TransferEntryListRequest.opcode:type_name -> wallet.RepeatedUint32Value
	0,  // 10: wallet.TransferEntryListRequest.start_time:type_name -> wallet.StringValue
	0,  // 11: wallet.TransferEntryListRequest.end_time:type_name -> wallet.StringValue
	1,  // 12: wallet.TransferEntryListRequest.first_result:type_name -> wallet.Uint32Value
	1,  // 13: wallet.TransferEntryListRequest.max_results:type_name -> wallet.Uint32Value
	19, // 14: wallet.TransferEntryListResponse.entry:type_name -> wallet.TransferEntry
	5,  // 15: wallet.TransferEntryListResponse.pagination:type_name -> wallet.Pagination
	0,  // 16: wallet.UsersBalanceByAgentRequest.username:type_name -> wallet.StringValue
	1,  // 17: wallet.UsersBalanceByAgentRequest.first_result:type_name -> wallet.Uint32Value
	1,  // 18: wallet.UsersBalanceByAgentRequest.max_results:type_name -> wallet.Uint32Value
	24, // 19: wallet.UsersBalanceByAgentResponse.users:type_name -> wallet.BalanceByAgentInfo
	5,  // 20: wallet.UsersBalanceByAgentResponse.pagination:type_name -> wallet.Pagination
	31, // 21: wallet.GetEntryListResponse.entry_info:type_name -> wallet.EntryDetail
	5,  // 22: wallet.GetEntryListResponse.pagination:type_name -> wallet.Pagination
	0,  // 23: wallet.GetOpCodeListRequest.result_type:type_name -> wallet.StringValue
	1,  // 24: wallet.GetOpCodeListRequest.game_kind:type_name -> wallet.Uint32Value
	0,  // 25: wallet.GetOpCodeListRequest.detail_type:type_name -> wallet.StringValue
	36, // 26: wallet.GetOpCodeListResponse.opcode_info:type_name -> wallet.OpCodeDetail
	41, // 27: wallet.GetTransferEntryDetailRequest.gte_amount:type_name -> wallet.Int64Value
	41, // 28: wallet.GetTransferEntryDetailRequest.lte_amount:type_name -> wallet.Int64Value
	43, // 29: wallet.GetTransferEntryDetailResponse.entry_detail:type_name -> wallet.TransferEntryDetail
	2,  // 30: wallet.GetOpcodeListByACCRequest.allow_balance_negative:type_name -> wallet.BoolValue
	2,  // 31: wallet.GetOpcodeListByACCRequest.allow_amount_zero:type_name -> wallet.BoolValue
	2,  // 32: wallet.GetOpcodeListByACCRequest.disable_not_allow:type_name -> wallet.BoolValue
	2,  // 33: wallet.GetOpcodeListByACCRequest.disable_for_cash_fake:type_name -> wallet.BoolValue
	2,  // 34: wallet.GetOpcodeListByACCRequest.cash_deposit_opcode:type_name -> wallet.BoolValue
	2,  // 35: wallet.GetOpcodeListByACCRequest.is_enabled:type_name -> wallet.BoolValue
	49, // 36: wallet.GetOpcodeListByACCResponse.opcode_info:type_name -> wallet.OpcodeDetailByACC
	50, // 37: wallet.GetOpcodeListByACCResponse.pagination:type_name -> wallet.PaginationByGTI
	52, // 38: wallet.GetExchangeRateResponse.exchange_rate:type_name -> wallet.ExchangeRate
	50, // 39: wallet.GetExchangeRateResponse.pagination:type_name -> wallet.PaginationByGTI
	52, // 40: wallet.ExchangeRateListResponse.list:type_name -> wallet.ExchangeRate
	57, // 41: wallet.GetOpcodeGroupsListResponse.opcode_groups_list:type_name -> wallet.OpcodeGroupsList
	50, // 42: wallet.GetOpcodeGroupsListResponse.pagination:type_name -> wallet.PaginationByGTI
	2,  // 43: wallet.GetTransferEntryDetailAllRequest.filter_opcode:type_name -> wallet.BoolValue
	43, // 44: wallet.GetTransferEntryDetailAllResponse.entry_detail:type_name -> wallet.TransferEntryDetail
	50, // 45: wallet.GetTransferEntryDetailAllResponse.pagination:type_name -> wallet.PaginationByGTI
	6,  // 46: wallet.Wallet.EntryStatus:input_type -> wallet.EntryStatusRequest
	8,  // 47: wallet.Wallet.GetUsersBalance:input_type -> wallet.UsersBalanceRequest
	11, // 48: wallet.Wallet.SlotMachineBalance:input_type -> wallet.SlotMachineBalanceRequest
	13, // 49: wallet.Wallet.Withdraw:input_type -> wallet.WithdrawRequest
	15, // 50: wallet.Wallet.Deposit:input_type -> wallet.DepositRequest
	17, // 51: wallet.Wallet.TransferEntry:input_type -> wallet.TransferEntryRequest
	20, // 52: wallet.Wallet.TransferEntryList:input_type -> wallet.TransferEntryListRequest
	25, // 53: wallet.Wallet.FishMachineBalance:input_type -> wallet.FishMachineBalanceRequest
	22, // 54: wallet.Wallet.GetUsersBalanceByAgent:input_type -> wallet.UsersBalanceByAgentRequest
	27, // 55: wallet.Wallet.GetUserBalance:input_type -> wallet.GetUserBalanceRequest
	29, // 56: wallet.Wallet.GetEntryList:input_type -> wallet.GetEntryListRequest
	32, // 57: wallet.Wallet.GetTotalAmount:input_type -> wallet.GetTotalAmountRequest
	34, // 58: wallet.Wallet.GetOpCodeList:input_type -> wallet.GetOpCodeListRequest
	37, // 59: wallet.Wallet.GetOpCodeDict:input_type -> wallet.GetOpCodeDictRequest
	39, // 60: wallet.Wallet.GetAccountCloseDate:input_type -> wallet.AccountCloseDateRequest
	42, // 61: wallet.Wallet.GetTransferEntryDetail:input_type -> wallet.GetTransferEntryDetailRequest
	45, // 62: wallet.Wallet.GetUserCashFake:input_type -> wallet.GetUserCashFakeRequest
	47, // 63: wallet.Wallet.GetOpcodeListByACC:input_type -> wallet.GetOpcodeListByACCRequest
	51, // 64: wallet.Wallet.GetExchangeRate:input_type -> wallet.GetExchangeRateRequest
	4,  // 65: wallet.Wallet.ExchangeRateList:input_type -> wallet.EmptyRequest
	55, // 66: wallet.Wallet.GetOpcodeGroupsList:input_type -> wallet.GetOpcodeGroupsListRequest
	58, // 67: wallet.Wallet.GetTransferEntryDetailAll:input_type -> wallet.GetTransferEntryDetailAllRequest
	60, // 68: wallet.Wallet.GetCurrencyList:input_type -> wallet.GetCurrencyListRequest
	7,  // 69: wallet.Wallet.EntryStatus:output_type -> wallet.EntryStatusResponse
	9,  // 70: wallet.Wallet.GetUsersBalance:output_type -> wallet.UsersBalanceResponse
	12, // 71: wallet.Wallet.SlotMachineBalance:output_type -> wallet.SlotMachineBalanceResponse
	14, // 72: wallet.Wallet.Withdraw:output_type -> wallet.WithdrawResponse
	16, // 73: wallet.Wallet.Deposit:output_type -> wallet.DepositResponse
	18, // 74: wallet.Wallet.TransferEntry:output_type -> wallet.TransferEntryResponse
	21, // 75: wallet.Wallet.TransferEntryList:output_type -> wallet.TransferEntryListResponse
	26, // 76: wallet.Wallet.FishMachineBalance:output_type -> wallet.FishMachineBalanceResponse
	23, // 77: wallet.Wallet.GetUsersBalanceByAgent:output_type -> wallet.UsersBalanceByAgentResponse
	28, // 78: wallet.Wallet.GetUserBalance:output_type -> wallet.GetUserBalanceResponse
	30, // 79: wallet.Wallet.GetEntryList:output_type -> wallet.GetEntryListResponse
	33, // 80: wallet.Wallet.GetTotalAmount:output_type -> wallet.GetTotalAmountResponse
	35, // 81: wallet.Wallet.GetOpCodeList:output_type -> wallet.GetOpCodeListResponse
	38, // 82: wallet.Wallet.GetOpCodeDict:output_type -> wallet.GetOpCodeDictResponse
	40, // 83: wallet.Wallet.GetAccountCloseDate:output_type -> wallet.AccountCloseDateResponse
	44, // 84: wallet.Wallet.GetTransferEntryDetail:output_type -> wallet.GetTransferEntryDetailResponse
	46, // 85: wallet.Wallet.GetUserCashFake:output_type -> wallet.GetUserCashFakeResponse
	48, // 86: wallet.Wallet.GetOpcodeListByACC:output_type -> wallet.GetOpcodeListByACCResponse
	53, // 87: wallet.Wallet.GetExchangeRate:output_type -> wallet.GetExchangeRateResponse
	54, // 88: wallet.Wallet.ExchangeRateList:output_type -> wallet.ExchangeRateListResponse
	56, // 89: wallet.Wallet.GetOpcodeGroupsList:output_type -> wallet.GetOpcodeGroupsListResponse
	59, // 90: wallet.Wallet.GetTransferEntryDetailAll:output_type -> wallet.GetTransferEntryDetailAllResponse
	61, // 91: wallet.Wallet.GetCurrencyList:output_type -> wallet.GetCurrencyListResponse
	69, // [69:92] is the sub-list for method output_type
	46, // [46:69] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_wallet_proto_init() }
func file_wallet_proto_init() {
	if File_wallet_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_wallet_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoolValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedUint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pagination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntryStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntryStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotMachineBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotMachineBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithdrawRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithdrawResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferEntryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferEntryListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferEntryListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersBalanceByAgentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersBalanceByAgentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceByAgentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishMachineBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishMachineBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntryListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntryListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntryDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTotalAmountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTotalAmountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpCodeListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpCodeListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpCodeDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpCodeDictRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpCodeDictResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountCloseDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountCloseDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int64Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransferEntryDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferEntryDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransferEntryDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCashFakeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCashFakeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpcodeListByACCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpcodeListByACCResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpcodeDetailByACC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaginationByGTI); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeRateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeRate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeRateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeRateListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpcodeGroupsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpcodeGroupsListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpcodeGroupsList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransferEntryDetailAllRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransferEntryDetailAllResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrencyListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wallet_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrencyListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wallet_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   62,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_wallet_proto_goTypes,
		DependencyIndexes: file_wallet_proto_depIdxs,
		MessageInfos:      file_wallet_proto_msgTypes,
	}.Build()
	File_wallet_proto = out.File
	file_wallet_proto_rawDesc = nil
	file_wallet_proto_goTypes = nil
	file_wallet_proto_depIdxs = nil
}
