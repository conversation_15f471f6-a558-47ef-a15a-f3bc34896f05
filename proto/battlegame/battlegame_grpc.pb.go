// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: battlegame.proto

package battlegame

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	BattleGame_GetUserPermissions_FullMethodName    = "/battlegame.BattleGame/GetUserPermissions"
	BattleGame_EnableUserPermissions_FullMethodName = "/battlegame.BattleGame/EnableUserPermissions"
	BattleGame_WagersByBetTime_FullMethodName       = "/battlegame.BattleGame/WagersByBetTime"
	BattleGame_WagersByModifiedTime_FullMethodName  = "/battlegame.BattleGame/WagersByModifiedTime"
	BattleGame_LinkList_FullMethodName              = "/battlegame.BattleGame/LinkList"
	BattleGame_SubWagersURL_FullMethodName          = "/battlegame.BattleGame/SubWagersURL"
	BattleGame_WagersByUserID_FullMethodName        = "/battlegame.BattleGame/WagersByUserID"
	BattleGame_GetWagers_FullMethodName             = "/battlegame.BattleGame/GetWagers"
	BattleGame_UpdateGameSwitch_FullMethodName      = "/battlegame.BattleGame/UpdateGameSwitch"
	BattleGame_GetMultiSubWagersURL_FullMethodName  = "/battlegame.BattleGame/GetMultiSubWagersURL"
	BattleGame_LogoutByHall_FullMethodName          = "/battlegame.BattleGame/LogoutByHall"
)

// BattleGameClient is the client API for BattleGame service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BattleGameClient interface {
	GetUserPermissions(ctx context.Context, in *GetUserPermissionsRequest, opts ...grpc.CallOption) (*GetUserPermissionsResponse, error)
	EnableUserPermissions(ctx context.Context, in *EnableUserPermissionsRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	WagersByBetTime(ctx context.Context, in *WagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
	WagersByModifiedTime(ctx context.Context, in *WagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
	LinkList(ctx context.Context, in *LinkListRequest, opts ...grpc.CallOption) (*LinkListResponse, error)
	SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error)
	WagersByUserID(ctx context.Context, in *WagersByUserIDRequest, opts ...grpc.CallOption) (*WagersByUserIDResponse, error)
	GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error)
	UpdateGameSwitch(ctx context.Context, in *UpdateGameSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetMultiSubWagersURL(ctx context.Context, in *GetMultiSubWagersURLRequest, opts ...grpc.CallOption) (*GetMultiSubWagersURLResponse, error)
	LogoutByHall(ctx context.Context, in *LogoutByHallRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
}

type battleGameClient struct {
	cc grpc.ClientConnInterface
}

func NewBattleGameClient(cc grpc.ClientConnInterface) BattleGameClient {
	return &battleGameClient{cc}
}

func (c *battleGameClient) GetUserPermissions(ctx context.Context, in *GetUserPermissionsRequest, opts ...grpc.CallOption) (*GetUserPermissionsResponse, error) {
	out := new(GetUserPermissionsResponse)
	err := c.cc.Invoke(ctx, BattleGame_GetUserPermissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) EnableUserPermissions(ctx context.Context, in *EnableUserPermissionsRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, BattleGame_EnableUserPermissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) WagersByBetTime(ctx context.Context, in *WagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	out := new(WagersResponse)
	err := c.cc.Invoke(ctx, BattleGame_WagersByBetTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) WagersByModifiedTime(ctx context.Context, in *WagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	out := new(WagersResponse)
	err := c.cc.Invoke(ctx, BattleGame_WagersByModifiedTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) LinkList(ctx context.Context, in *LinkListRequest, opts ...grpc.CallOption) (*LinkListResponse, error) {
	out := new(LinkListResponse)
	err := c.cc.Invoke(ctx, BattleGame_LinkList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error) {
	out := new(SubWagersURLResponse)
	err := c.cc.Invoke(ctx, BattleGame_SubWagersURL_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) WagersByUserID(ctx context.Context, in *WagersByUserIDRequest, opts ...grpc.CallOption) (*WagersByUserIDResponse, error) {
	out := new(WagersByUserIDResponse)
	err := c.cc.Invoke(ctx, BattleGame_WagersByUserID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error) {
	out := new(GetWagersResponse)
	err := c.cc.Invoke(ctx, BattleGame_GetWagers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) UpdateGameSwitch(ctx context.Context, in *UpdateGameSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, BattleGame_UpdateGameSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) GetMultiSubWagersURL(ctx context.Context, in *GetMultiSubWagersURLRequest, opts ...grpc.CallOption) (*GetMultiSubWagersURLResponse, error) {
	out := new(GetMultiSubWagersURLResponse)
	err := c.cc.Invoke(ctx, BattleGame_GetMultiSubWagersURL_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleGameClient) LogoutByHall(ctx context.Context, in *LogoutByHallRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, BattleGame_LogoutByHall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BattleGameServer is the server API for BattleGame service.
// All implementations must embed UnimplementedBattleGameServer
// for forward compatibility
type BattleGameServer interface {
	GetUserPermissions(context.Context, *GetUserPermissionsRequest) (*GetUserPermissionsResponse, error)
	EnableUserPermissions(context.Context, *EnableUserPermissionsRequest) (*EmptyResponse, error)
	WagersByBetTime(context.Context, *WagersByBetTimeRequest) (*WagersResponse, error)
	WagersByModifiedTime(context.Context, *WagersByModifiedTimeRequest) (*WagersResponse, error)
	LinkList(context.Context, *LinkListRequest) (*LinkListResponse, error)
	SubWagersURL(context.Context, *SubWagersURLRequest) (*SubWagersURLResponse, error)
	WagersByUserID(context.Context, *WagersByUserIDRequest) (*WagersByUserIDResponse, error)
	GetWagers(context.Context, *GetWagersRequest) (*GetWagersResponse, error)
	UpdateGameSwitch(context.Context, *UpdateGameSwitchRequest) (*EmptyResponse, error)
	GetMultiSubWagersURL(context.Context, *GetMultiSubWagersURLRequest) (*GetMultiSubWagersURLResponse, error)
	LogoutByHall(context.Context, *LogoutByHallRequest) (*EmptyResponse, error)
	mustEmbedUnimplementedBattleGameServer()
}

// UnimplementedBattleGameServer must be embedded to have forward compatible implementations.
type UnimplementedBattleGameServer struct {
}

func (UnimplementedBattleGameServer) GetUserPermissions(context.Context, *GetUserPermissionsRequest) (*GetUserPermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPermissions not implemented")
}
func (UnimplementedBattleGameServer) EnableUserPermissions(context.Context, *EnableUserPermissionsRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnableUserPermissions not implemented")
}
func (UnimplementedBattleGameServer) WagersByBetTime(context.Context, *WagersByBetTimeRequest) (*WagersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WagersByBetTime not implemented")
}
func (UnimplementedBattleGameServer) WagersByModifiedTime(context.Context, *WagersByModifiedTimeRequest) (*WagersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WagersByModifiedTime not implemented")
}
func (UnimplementedBattleGameServer) LinkList(context.Context, *LinkListRequest) (*LinkListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkList not implemented")
}
func (UnimplementedBattleGameServer) SubWagersURL(context.Context, *SubWagersURLRequest) (*SubWagersURLResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubWagersURL not implemented")
}
func (UnimplementedBattleGameServer) WagersByUserID(context.Context, *WagersByUserIDRequest) (*WagersByUserIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WagersByUserID not implemented")
}
func (UnimplementedBattleGameServer) GetWagers(context.Context, *GetWagersRequest) (*GetWagersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWagers not implemented")
}
func (UnimplementedBattleGameServer) UpdateGameSwitch(context.Context, *UpdateGameSwitchRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGameSwitch not implemented")
}
func (UnimplementedBattleGameServer) GetMultiSubWagersURL(context.Context, *GetMultiSubWagersURLRequest) (*GetMultiSubWagersURLResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMultiSubWagersURL not implemented")
}
func (UnimplementedBattleGameServer) LogoutByHall(context.Context, *LogoutByHallRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogoutByHall not implemented")
}
func (UnimplementedBattleGameServer) mustEmbedUnimplementedBattleGameServer() {}

// UnsafeBattleGameServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BattleGameServer will
// result in compilation errors.
type UnsafeBattleGameServer interface {
	mustEmbedUnimplementedBattleGameServer()
}

func RegisterBattleGameServer(s grpc.ServiceRegistrar, srv BattleGameServer) {
	s.RegisterService(&BattleGame_ServiceDesc, srv)
}

func _BattleGame_GetUserPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).GetUserPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_GetUserPermissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).GetUserPermissions(ctx, req.(*GetUserPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_EnableUserPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableUserPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).EnableUserPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_EnableUserPermissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).EnableUserPermissions(ctx, req.(*EnableUserPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_WagersByBetTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WagersByBetTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).WagersByBetTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_WagersByBetTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).WagersByBetTime(ctx, req.(*WagersByBetTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_WagersByModifiedTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WagersByModifiedTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).WagersByModifiedTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_WagersByModifiedTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).WagersByModifiedTime(ctx, req.(*WagersByModifiedTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_LinkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).LinkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_LinkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).LinkList(ctx, req.(*LinkListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_SubWagersURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubWagersURLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).SubWagersURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_SubWagersURL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).SubWagersURL(ctx, req.(*SubWagersURLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_WagersByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WagersByUserIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).WagersByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_WagersByUserID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).WagersByUserID(ctx, req.(*WagersByUserIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_GetWagers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWagersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).GetWagers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_GetWagers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).GetWagers(ctx, req.(*GetWagersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_UpdateGameSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGameSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).UpdateGameSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_UpdateGameSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).UpdateGameSwitch(ctx, req.(*UpdateGameSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_GetMultiSubWagersURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiSubWagersURLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).GetMultiSubWagersURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_GetMultiSubWagersURL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).GetMultiSubWagersURL(ctx, req.(*GetMultiSubWagersURLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BattleGame_LogoutByHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutByHallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleGameServer).LogoutByHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BattleGame_LogoutByHall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleGameServer).LogoutByHall(ctx, req.(*LogoutByHallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BattleGame_ServiceDesc is the grpc.ServiceDesc for BattleGame service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BattleGame_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "battlegame.BattleGame",
	HandlerType: (*BattleGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserPermissions",
			Handler:    _BattleGame_GetUserPermissions_Handler,
		},
		{
			MethodName: "EnableUserPermissions",
			Handler:    _BattleGame_EnableUserPermissions_Handler,
		},
		{
			MethodName: "WagersByBetTime",
			Handler:    _BattleGame_WagersByBetTime_Handler,
		},
		{
			MethodName: "WagersByModifiedTime",
			Handler:    _BattleGame_WagersByModifiedTime_Handler,
		},
		{
			MethodName: "LinkList",
			Handler:    _BattleGame_LinkList_Handler,
		},
		{
			MethodName: "SubWagersURL",
			Handler:    _BattleGame_SubWagersURL_Handler,
		},
		{
			MethodName: "WagersByUserID",
			Handler:    _BattleGame_WagersByUserID_Handler,
		},
		{
			MethodName: "GetWagers",
			Handler:    _BattleGame_GetWagers_Handler,
		},
		{
			MethodName: "UpdateGameSwitch",
			Handler:    _BattleGame_UpdateGameSwitch_Handler,
		},
		{
			MethodName: "GetMultiSubWagersURL",
			Handler:    _BattleGame_GetMultiSubWagersURL_Handler,
		},
		{
			MethodName: "LogoutByHall",
			Handler:    _BattleGame_LogoutByHall_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "battlegame.proto",
}
