// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v3.19.4
// source: battlegame.proto

package battlegame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{0}
}

type GetUserPermissionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []uint32 `protobuf:"varint,1,rep,packed,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
}

func (x *GetUserPermissionsRequest) Reset() {
	*x = GetUserPermissionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPermissionsRequest) ProtoMessage() {}

func (x *GetUserPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPermissionsRequest.ProtoReflect.Descriptor instead.
func (*GetUserPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserPermissionsRequest) GetUserIds() []uint32 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{2}
}

func (x *User) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type GetUserPermissionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *GetUserPermissionsResponse) Reset() {
	*x = GetUserPermissionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPermissionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPermissionsResponse) ProtoMessage() {}

func (x *GetUserPermissionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPermissionsResponse.ProtoReflect.Descriptor instead.
func (*GetUserPermissionsResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserPermissionsResponse) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

type EnableUserPermissionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []uint32 `protobuf:"varint,1,rep,packed,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	Enable  bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *EnableUserPermissionsRequest) Reset() {
	*x = EnableUserPermissionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableUserPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableUserPermissionsRequest) ProtoMessage() {}

func (x *EnableUserPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableUserPermissionsRequest.ProtoReflect.Descriptor instead.
func (*EnableUserPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{4}
}

func (x *EnableUserPermissionsRequest) GetUserIds() []uint32 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *EnableUserPermissionsRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{5}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type WagersByBetTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime   string       `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime     string       `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	HallId      uint32       `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	AgentId     uint32       `protobuf:"varint,4,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	GameId      *Uint32Value `protobuf:"bytes,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CurrentPage *Uint32Value `protobuf:"bytes,6,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   *Uint32Value `protobuf:"bytes,7,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *WagersByBetTimeRequest) Reset() {
	*x = WagersByBetTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersByBetTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersByBetTimeRequest) ProtoMessage() {}

func (x *WagersByBetTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersByBetTimeRequest.ProtoReflect.Descriptor instead.
func (*WagersByBetTimeRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{6}
}

func (x *WagersByBetTimeRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *WagersByBetTimeRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *WagersByBetTimeRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *WagersByBetTimeRequest) GetAgentId() uint32 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *WagersByBetTimeRequest) GetGameId() *Uint32Value {
	if x != nil {
		return x.GameId
	}
	return nil
}

func (x *WagersByBetTimeRequest) GetCurrentPage() *Uint32Value {
	if x != nil {
		return x.CurrentPage
	}
	return nil
}

func (x *WagersByBetTimeRequest) GetPageLimit() *Uint32Value {
	if x != nil {
		return x.PageLimit
	}
	return nil
}

type Wager struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId         uint32  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameId         uint32  `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	BetAmount      float64 `protobuf:"fixed64,4,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Payoff         float64 `protobuf:"fixed64,5,opt,name=payoff,proto3" json:"payoff,omitempty"`
	Commissionable float64 `protobuf:"fixed64,6,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
	Revenue        float64 `protobuf:"fixed64,7,opt,name=revenue,proto3" json:"revenue,omitempty"`
	Currency       string  `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency,omitempty"`
	ExchangeRate   float64 `protobuf:"fixed64,9,opt,name=exchange_rate,json=exchangeRate,proto3" json:"exchange_rate,omitempty"`
	ResultStatus   int32   `protobuf:"varint,10,opt,name=result_status,json=resultStatus,proto3" json:"result_status,omitempty"`
	Platform       uint32  `protobuf:"varint,11,opt,name=platform,proto3" json:"platform,omitempty"`
	Client         uint32  `protobuf:"varint,12,opt,name=client,proto3" json:"client,omitempty"`
	Portal         uint32  `protobuf:"varint,13,opt,name=portal,proto3" json:"portal,omitempty"`
	WagersDate     string  `protobuf:"bytes,14,opt,name=wagers_date,json=wagersDate,proto3" json:"wagers_date,omitempty"`
	ModifiedDate   string  `protobuf:"bytes,15,opt,name=modified_date,json=modifiedDate,proto3" json:"modified_date,omitempty"`
}

func (x *Wager) Reset() {
	*x = Wager{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Wager) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Wager) ProtoMessage() {}

func (x *Wager) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Wager.ProtoReflect.Descriptor instead.
func (*Wager) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{7}
}

func (x *Wager) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Wager) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Wager) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *Wager) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *Wager) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *Wager) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

func (x *Wager) GetRevenue() float64 {
	if x != nil {
		return x.Revenue
	}
	return 0
}

func (x *Wager) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Wager) GetExchangeRate() float64 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *Wager) GetResultStatus() int32 {
	if x != nil {
		return x.ResultStatus
	}
	return 0
}

func (x *Wager) GetPlatform() uint32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *Wager) GetClient() uint32 {
	if x != nil {
		return x.Client
	}
	return 0
}

func (x *Wager) GetPortal() uint32 {
	if x != nil {
		return x.Portal
	}
	return 0
}

func (x *Wager) GetWagersDate() string {
	if x != nil {
		return x.WagersDate
	}
	return ""
}

func (x *Wager) GetModifiedDate() string {
	if x != nil {
		return x.ModifiedDate
	}
	return ""
}

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   uint32 `protobuf:"varint,2,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Total       uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPage   uint32 `protobuf:"varint,4,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{8}
}

func (x *Pagination) GetCurrentPage() uint32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *Pagination) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Pagination) GetTotalPage() uint32 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type WagersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wagers     []*Wager    `protobuf:"bytes,1,rep,name=wagers,proto3" json:"wagers,omitempty"`
	Pagination *Pagination `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *WagersResponse) Reset() {
	*x = WagersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersResponse) ProtoMessage() {}

func (x *WagersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersResponse.ProtoReflect.Descriptor instead.
func (*WagersResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{9}
}

func (x *WagersResponse) GetWagers() []*Wager {
	if x != nil {
		return x.Wagers
	}
	return nil
}

func (x *WagersResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type WagersByModifiedTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime   string       `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime     string       `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	HallId      uint32       `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	AgentId     *Uint32Value `protobuf:"bytes,4,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	GameId      *Uint32Value `protobuf:"bytes,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CurrentPage *Uint32Value `protobuf:"bytes,6,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   *Uint32Value `protobuf:"bytes,7,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *WagersByModifiedTimeRequest) Reset() {
	*x = WagersByModifiedTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersByModifiedTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersByModifiedTimeRequest) ProtoMessage() {}

func (x *WagersByModifiedTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersByModifiedTimeRequest.ProtoReflect.Descriptor instead.
func (*WagersByModifiedTimeRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{10}
}

func (x *WagersByModifiedTimeRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *WagersByModifiedTimeRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *WagersByModifiedTimeRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *WagersByModifiedTimeRequest) GetAgentId() *Uint32Value {
	if x != nil {
		return x.AgentId
	}
	return nil
}

func (x *WagersByModifiedTimeRequest) GetGameId() *Uint32Value {
	if x != nil {
		return x.GameId
	}
	return nil
}

func (x *WagersByModifiedTimeRequest) GetCurrentPage() *Uint32Value {
	if x != nil {
		return x.CurrentPage
	}
	return nil
}

func (x *WagersByModifiedTimeRequest) GetPageLimit() *Uint32Value {
	if x != nil {
		return x.PageLimit
	}
	return nil
}

type StringValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StringValue) Reset() {
	*x = StringValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringValue) ProtoMessage() {}

func (x *StringValue) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringValue.ProtoReflect.Descriptor instead.
func (*StringValue) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{11}
}

func (x *StringValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type LinkListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Session    string       `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	ClientIp   string       `protobuf:"bytes,2,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	Lang       *StringValue `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
	ExitOption *Uint32Value `protobuf:"bytes,4,opt,name=exit_option,json=exitOption,proto3" json:"exit_option,omitempty"`
	ExitParam  *StringValue `protobuf:"bytes,5,opt,name=exit_param,json=exitParam,proto3" json:"exit_param,omitempty"`
}

func (x *LinkListRequest) Reset() {
	*x = LinkListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkListRequest) ProtoMessage() {}

func (x *LinkListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkListRequest.ProtoReflect.Descriptor instead.
func (*LinkListRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{12}
}

func (x *LinkListRequest) GetSession() string {
	if x != nil {
		return x.Session
	}
	return ""
}

func (x *LinkListRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *LinkListRequest) GetLang() *StringValue {
	if x != nil {
		return x.Lang
	}
	return nil
}

func (x *LinkListRequest) GetExitOption() *Uint32Value {
	if x != nil {
		return x.ExitOption
	}
	return nil
}

func (x *LinkListRequest) GetExitParam() *StringValue {
	if x != nil {
		return x.ExitParam
	}
	return nil
}

type Link struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Pc     string `protobuf:"bytes,2,opt,name=pc,proto3" json:"pc,omitempty"`
	Mobile string `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Rwd    string `protobuf:"bytes,4,opt,name=rwd,proto3" json:"rwd,omitempty"`
}

func (x *Link) Reset() {
	*x = Link{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Link) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Link) ProtoMessage() {}

func (x *Link) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Link.ProtoReflect.Descriptor instead.
func (*Link) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{13}
}

func (x *Link) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *Link) GetPc() string {
	if x != nil {
		return x.Pc
	}
	return ""
}

func (x *Link) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *Link) GetRwd() string {
	if x != nil {
		return x.Rwd
	}
	return ""
}

type LinkListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Links []*Link `protobuf:"bytes,1,rep,name=links,proto3" json:"links,omitempty"`
}

func (x *LinkListResponse) Reset() {
	*x = LinkListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkListResponse) ProtoMessage() {}

func (x *LinkListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkListResponse.ProtoReflect.Descriptor instead.
func (*LinkListResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{14}
}

func (x *LinkListResponse) GetLinks() []*Link {
	if x != nil {
		return x.Links
	}
	return nil
}

type SubWagersURLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang     string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
	UserId   uint32 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WagersId uint64 `protobuf:"varint,3,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
}

func (x *SubWagersURLRequest) Reset() {
	*x = SubWagersURLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubWagersURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubWagersURLRequest) ProtoMessage() {}

func (x *SubWagersURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubWagersURLRequest.ProtoReflect.Descriptor instead.
func (*SubWagersURLRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{15}
}

func (x *SubWagersURLRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SubWagersURLRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SubWagersURLRequest) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

type SubWagersURLResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *SubWagersURLResponse) Reset() {
	*x = SubWagersURLResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubWagersURLResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubWagersURLResponse) ProtoMessage() {}

func (x *SubWagersURLResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubWagersURLResponse.ProtoReflect.Descriptor instead.
func (*SubWagersURLResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{16}
}

func (x *SubWagersURLResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type WagersByUserIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId         uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId         uint32 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameId         uint32 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	StartRoundDate string `protobuf:"bytes,4,opt,name=start_round_date,json=startRoundDate,proto3" json:"start_round_date,omitempty"`
	EndRoundDate   string `protobuf:"bytes,5,opt,name=end_round_date,json=endRoundDate,proto3" json:"end_round_date,omitempty"`
	CurrentPage    uint32 `protobuf:"varint,6,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit      uint32 `protobuf:"varint,7,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *WagersByUserIDRequest) Reset() {
	*x = WagersByUserIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersByUserIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersByUserIDRequest) ProtoMessage() {}

func (x *WagersByUserIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersByUserIDRequest.ProtoReflect.Descriptor instead.
func (*WagersByUserIDRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{17}
}

func (x *WagersByUserIDRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *WagersByUserIDRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *WagersByUserIDRequest) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *WagersByUserIDRequest) GetStartRoundDate() string {
	if x != nil {
		return x.StartRoundDate
	}
	return ""
}

func (x *WagersByUserIDRequest) GetEndRoundDate() string {
	if x != nil {
		return x.EndRoundDate
	}
	return ""
}

func (x *WagersByUserIDRequest) GetCurrentPage() uint32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *WagersByUserIDRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type WagersByUserID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundDate    string  `protobuf:"bytes,1,opt,name=round_date,json=roundDate,proto3" json:"round_date,omitempty"`
	WagersId     uint64  `protobuf:"varint,2,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	GameId       uint32  `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ResultStatus int32   `protobuf:"varint,4,opt,name=result_status,json=resultStatus,proto3" json:"result_status,omitempty"`
	BetAmount    float64 `protobuf:"fixed64,5,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Payoff       float64 `protobuf:"fixed64,6,opt,name=payoff,proto3" json:"payoff,omitempty"`
}

func (x *WagersByUserID) Reset() {
	*x = WagersByUserID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersByUserID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersByUserID) ProtoMessage() {}

func (x *WagersByUserID) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersByUserID.ProtoReflect.Descriptor instead.
func (*WagersByUserID) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{18}
}

func (x *WagersByUserID) GetRoundDate() string {
	if x != nil {
		return x.RoundDate
	}
	return ""
}

func (x *WagersByUserID) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *WagersByUserID) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *WagersByUserID) GetResultStatus() int32 {
	if x != nil {
		return x.ResultStatus
	}
	return 0
}

func (x *WagersByUserID) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *WagersByUserID) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

type WagersSumByUserID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalBetAmount float64 `protobuf:"fixed64,2,opt,name=total_bet_amount,json=totalBetAmount,proto3" json:"total_bet_amount,omitempty"`
	TotalPayoff    float64 `protobuf:"fixed64,3,opt,name=total_payoff,json=totalPayoff,proto3" json:"total_payoff,omitempty"`
}

func (x *WagersSumByUserID) Reset() {
	*x = WagersSumByUserID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersSumByUserID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersSumByUserID) ProtoMessage() {}

func (x *WagersSumByUserID) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersSumByUserID.ProtoReflect.Descriptor instead.
func (*WagersSumByUserID) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{19}
}

func (x *WagersSumByUserID) GetTotalBetAmount() float64 {
	if x != nil {
		return x.TotalBetAmount
	}
	return 0
}

func (x *WagersSumByUserID) GetTotalPayoff() float64 {
	if x != nil {
		return x.TotalPayoff
	}
	return 0
}

type WagersByUserIDResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wagers     []*WagersByUserID  `protobuf:"bytes,1,rep,name=wagers,proto3" json:"wagers,omitempty"`
	WagersSum  *WagersSumByUserID `protobuf:"bytes,2,opt,name=wagers_sum,json=wagersSum,proto3" json:"wagers_sum,omitempty"`
	Pagination *Pagination        `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *WagersByUserIDResponse) Reset() {
	*x = WagersByUserIDResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersByUserIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersByUserIDResponse) ProtoMessage() {}

func (x *WagersByUserIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersByUserIDResponse.ProtoReflect.Descriptor instead.
func (*WagersByUserIDResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{20}
}

func (x *WagersByUserIDResponse) GetWagers() []*WagersByUserID {
	if x != nil {
		return x.Wagers
	}
	return nil
}

func (x *WagersByUserIDResponse) GetWagersSum() *WagersSumByUserID {
	if x != nil {
		return x.WagersSum
	}
	return nil
}

func (x *WagersByUserIDResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetWagersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartRoundDate string   `protobuf:"bytes,1,opt,name=start_round_date,json=startRoundDate,proto3" json:"start_round_date,omitempty"`
	EndRoundDate   string   `protobuf:"bytes,2,opt,name=end_round_date,json=endRoundDate,proto3" json:"end_round_date,omitempty"`
	HallId         uint32   `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId         []uint32 `protobuf:"varint,4,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WagersId       uint64   `protobuf:"varint,5,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	GameId         []uint32 `protobuf:"varint,6,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	RoundSerial    uint32   `protobuf:"varint,7,opt,name=round_serial,json=roundSerial,proto3" json:"round_serial,omitempty"`
	WagersType     uint32   `protobuf:"varint,8,opt,name=wagers_type,json=wagersType,proto3" json:"wagers_type,omitempty"`
	MinBetAmount   float64  `protobuf:"fixed64,9,opt,name=min_bet_amount,json=minBetAmount,proto3" json:"min_bet_amount,omitempty"`
	MaxBetAmount   float64  `protobuf:"fixed64,10,opt,name=max_bet_amount,json=maxBetAmount,proto3" json:"max_bet_amount,omitempty"`
	MinPayoff      float64  `protobuf:"fixed64,11,opt,name=min_payoff,json=minPayoff,proto3" json:"min_payoff,omitempty"`
	MaxPayoff      float64  `protobuf:"fixed64,12,opt,name=max_payoff,json=maxPayoff,proto3" json:"max_payoff,omitempty"`
	CloseDate      string   `protobuf:"bytes,13,opt,name=close_date,json=closeDate,proto3" json:"close_date,omitempty"`
	ReferenceId    uint64   `protobuf:"varint,14,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	Page           uint32   `protobuf:"varint,15,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit      uint32   `protobuf:"varint,16,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Order          string   `protobuf:"bytes,17,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *GetWagersRequest) Reset() {
	*x = GetWagersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWagersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWagersRequest) ProtoMessage() {}

func (x *GetWagersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWagersRequest.ProtoReflect.Descriptor instead.
func (*GetWagersRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{21}
}

func (x *GetWagersRequest) GetStartRoundDate() string {
	if x != nil {
		return x.StartRoundDate
	}
	return ""
}

func (x *GetWagersRequest) GetEndRoundDate() string {
	if x != nil {
		return x.EndRoundDate
	}
	return ""
}

func (x *GetWagersRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetWagersRequest) GetUserId() []uint32 {
	if x != nil {
		return x.UserId
	}
	return nil
}

func (x *GetWagersRequest) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *GetWagersRequest) GetGameId() []uint32 {
	if x != nil {
		return x.GameId
	}
	return nil
}

func (x *GetWagersRequest) GetRoundSerial() uint32 {
	if x != nil {
		return x.RoundSerial
	}
	return 0
}

func (x *GetWagersRequest) GetWagersType() uint32 {
	if x != nil {
		return x.WagersType
	}
	return 0
}

func (x *GetWagersRequest) GetMinBetAmount() float64 {
	if x != nil {
		return x.MinBetAmount
	}
	return 0
}

func (x *GetWagersRequest) GetMaxBetAmount() float64 {
	if x != nil {
		return x.MaxBetAmount
	}
	return 0
}

func (x *GetWagersRequest) GetMinPayoff() float64 {
	if x != nil {
		return x.MinPayoff
	}
	return 0
}

func (x *GetWagersRequest) GetMaxPayoff() float64 {
	if x != nil {
		return x.MaxPayoff
	}
	return 0
}

func (x *GetWagersRequest) GetCloseDate() string {
	if x != nil {
		return x.CloseDate
	}
	return ""
}

func (x *GetWagersRequest) GetReferenceId() uint64 {
	if x != nil {
		return x.ReferenceId
	}
	return 0
}

func (x *GetWagersRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWagersRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *GetWagersRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

type FetchWagersByDB struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WagersId            uint64  `protobuf:"varint,1,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	WagersTime          string  `protobuf:"bytes,2,opt,name=wagers_time,json=wagersTime,proto3" json:"wagers_time,omitempty"`
	Hierarchy           uint32  `protobuf:"varint,3,opt,name=hierarchy,proto3" json:"hierarchy,omitempty"`
	Portal              uint32  `protobuf:"varint,4,opt,name=portal,proto3" json:"portal,omitempty"`
	WagersType          uint32  `protobuf:"varint,5,opt,name=wagers_type,json=wagersType,proto3" json:"wagers_type,omitempty"`
	Platform            uint32  `protobuf:"varint,6,opt,name=platform,proto3" json:"platform,omitempty"`
	Client              uint32  `protobuf:"varint,7,opt,name=client,proto3" json:"client,omitempty"`
	GameId              uint32  `protobuf:"varint,8,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	UserId              uint32  `protobuf:"varint,9,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RoundDate           string  `protobuf:"bytes,10,opt,name=round_date,json=roundDate,proto3" json:"round_date,omitempty"`
	RoundTime           string  `protobuf:"bytes,11,opt,name=round_time,json=roundTime,proto3" json:"round_time,omitempty"`
	BetAmount           float64 `protobuf:"fixed64,12,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Commissionable      float64 `protobuf:"fixed64,13,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
	Currency            string  `protobuf:"bytes,14,opt,name=currency,proto3" json:"currency,omitempty"`
	ExchangeRate        float64 `protobuf:"fixed64,15,opt,name=exchange_rate,json=exchangeRate,proto3" json:"exchange_rate,omitempty"`
	Result              int32   `protobuf:"zigzag32,16,opt,name=result,proto3" json:"result,omitempty"`
	Payoff              float64 `protobuf:"fixed64,17,opt,name=payoff,proto3" json:"payoff,omitempty"`
	Revenue             float64 `protobuf:"fixed64,18,opt,name=revenue,proto3" json:"revenue,omitempty"`
	JackpotContribution float64 `protobuf:"fixed64,19,opt,name=jackpot_contribution,json=jackpotContribution,proto3" json:"jackpot_contribution,omitempty"`
	HallId              uint32  `protobuf:"varint,20,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	RoundSerial         uint32  `protobuf:"varint,21,opt,name=round_serial,json=roundSerial,proto3" json:"round_serial,omitempty"`
	ReferenceId         uint64  `protobuf:"varint,22,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	SettledTime         string  `protobuf:"bytes,23,opt,name=settled_time,json=settledTime,proto3" json:"settled_time,omitempty"`
	ModifiedDate        string  `protobuf:"bytes,24,opt,name=modified_date,json=modifiedDate,proto3" json:"modified_date,omitempty"`
}

func (x *FetchWagersByDB) Reset() {
	*x = FetchWagersByDB{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchWagersByDB) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchWagersByDB) ProtoMessage() {}

func (x *FetchWagersByDB) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchWagersByDB.ProtoReflect.Descriptor instead.
func (*FetchWagersByDB) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{22}
}

func (x *FetchWagersByDB) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *FetchWagersByDB) GetWagersTime() string {
	if x != nil {
		return x.WagersTime
	}
	return ""
}

func (x *FetchWagersByDB) GetHierarchy() uint32 {
	if x != nil {
		return x.Hierarchy
	}
	return 0
}

func (x *FetchWagersByDB) GetPortal() uint32 {
	if x != nil {
		return x.Portal
	}
	return 0
}

func (x *FetchWagersByDB) GetWagersType() uint32 {
	if x != nil {
		return x.WagersType
	}
	return 0
}

func (x *FetchWagersByDB) GetPlatform() uint32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *FetchWagersByDB) GetClient() uint32 {
	if x != nil {
		return x.Client
	}
	return 0
}

func (x *FetchWagersByDB) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *FetchWagersByDB) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FetchWagersByDB) GetRoundDate() string {
	if x != nil {
		return x.RoundDate
	}
	return ""
}

func (x *FetchWagersByDB) GetRoundTime() string {
	if x != nil {
		return x.RoundTime
	}
	return ""
}

func (x *FetchWagersByDB) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *FetchWagersByDB) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

func (x *FetchWagersByDB) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *FetchWagersByDB) GetExchangeRate() float64 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *FetchWagersByDB) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *FetchWagersByDB) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *FetchWagersByDB) GetRevenue() float64 {
	if x != nil {
		return x.Revenue
	}
	return 0
}

func (x *FetchWagersByDB) GetJackpotContribution() float64 {
	if x != nil {
		return x.JackpotContribution
	}
	return 0
}

func (x *FetchWagersByDB) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *FetchWagersByDB) GetRoundSerial() uint32 {
	if x != nil {
		return x.RoundSerial
	}
	return 0
}

func (x *FetchWagersByDB) GetReferenceId() uint64 {
	if x != nil {
		return x.ReferenceId
	}
	return 0
}

func (x *FetchWagersByDB) GetSettledTime() string {
	if x != nil {
		return x.SettledTime
	}
	return ""
}

func (x *FetchWagersByDB) GetModifiedDate() string {
	if x != nil {
		return x.ModifiedDate
	}
	return ""
}

type Total struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number         uint32  `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
	BetAmount      float64 `protobuf:"fixed64,2,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Commissionable float64 `protobuf:"fixed64,3,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
	Payoff         float64 `protobuf:"fixed64,4,opt,name=payoff,proto3" json:"payoff,omitempty"`
	Revenue        float64 `protobuf:"fixed64,5,opt,name=revenue,proto3" json:"revenue,omitempty"`
}

func (x *Total) Reset() {
	*x = Total{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Total) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Total) ProtoMessage() {}

func (x *Total) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Total.ProtoReflect.Descriptor instead.
func (*Total) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{23}
}

func (x *Total) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *Total) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *Total) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

func (x *Total) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *Total) GetRevenue() float64 {
	if x != nil {
		return x.Revenue
	}
	return 0
}

type GetWagersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wagers     []*FetchWagersByDB `protobuf:"bytes,1,rep,name=wagers,proto3" json:"wagers,omitempty"`
	Pagination *Pagination        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	SubTotal   *Total             `protobuf:"bytes,3,opt,name=sub_total,json=subTotal,proto3" json:"sub_total,omitempty"`
	Total      *Total             `protobuf:"bytes,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetWagersResponse) Reset() {
	*x = GetWagersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWagersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWagersResponse) ProtoMessage() {}

func (x *GetWagersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWagersResponse.ProtoReflect.Descriptor instead.
func (*GetWagersResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{24}
}

func (x *GetWagersResponse) GetWagers() []*FetchWagersByDB {
	if x != nil {
		return x.Wagers
	}
	return nil
}

func (x *GetWagersResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetWagersResponse) GetSubTotal() *Total {
	if x != nil {
		return x.SubTotal
	}
	return nil
}

func (x *GetWagersResponse) GetTotal() *Total {
	if x != nil {
		return x.Total
	}
	return nil
}

type UpdateGameSwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameId []uint32 `protobuf:"varint,1,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Enable bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UpdateGameSwitchRequest) Reset() {
	*x = UpdateGameSwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGameSwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGameSwitchRequest) ProtoMessage() {}

func (x *UpdateGameSwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGameSwitchRequest.ProtoReflect.Descriptor instead.
func (*UpdateGameSwitchRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateGameSwitchRequest) GetGameId() []uint32 {
	if x != nil {
		return x.GameId
	}
	return nil
}

func (x *UpdateGameSwitchRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type GetMultiSubWagersURLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WagersIds []uint64 `protobuf:"varint,1,rep,packed,name=wagers_ids,json=wagersIds,proto3" json:"wagers_ids,omitempty"`
	Lang      string   `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetMultiSubWagersURLRequest) Reset() {
	*x = GetMultiSubWagersURLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMultiSubWagersURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMultiSubWagersURLRequest) ProtoMessage() {}

func (x *GetMultiSubWagersURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMultiSubWagersURLRequest.ProtoReflect.Descriptor instead.
func (*GetMultiSubWagersURLRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{26}
}

func (x *GetMultiSubWagersURLRequest) GetWagersIds() []uint64 {
	if x != nil {
		return x.WagersIds
	}
	return nil
}

func (x *GetMultiSubWagersURLRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type SubWagersURL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WagersId uint64 `protobuf:"varint,1,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	Url      string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *SubWagersURL) Reset() {
	*x = SubWagersURL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubWagersURL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubWagersURL) ProtoMessage() {}

func (x *SubWagersURL) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubWagersURL.ProtoReflect.Descriptor instead.
func (*SubWagersURL) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{27}
}

func (x *SubWagersURL) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *SubWagersURL) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetMultiSubWagersURLResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubWagersUrl []*SubWagersURL `protobuf:"bytes,1,rep,name=sub_wagers_url,json=subWagersUrl,proto3" json:"sub_wagers_url,omitempty"`
}

func (x *GetMultiSubWagersURLResponse) Reset() {
	*x = GetMultiSubWagersURLResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMultiSubWagersURLResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMultiSubWagersURLResponse) ProtoMessage() {}

func (x *GetMultiSubWagersURLResponse) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMultiSubWagersURLResponse.ProtoReflect.Descriptor instead.
func (*GetMultiSubWagersURLResponse) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{28}
}

func (x *GetMultiSubWagersURLResponse) GetSubWagersUrl() []*SubWagersURL {
	if x != nil {
		return x.SubWagersUrl
	}
	return nil
}

type LogoutByHallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *LogoutByHallRequest) Reset() {
	*x = LogoutByHallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_battlegame_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogoutByHallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutByHallRequest) ProtoMessage() {}

func (x *LogoutByHallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_battlegame_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutByHallRequest.ProtoReflect.Descriptor instead.
func (*LogoutByHallRequest) Descriptor() ([]byte, []int) {
	return file_battlegame_proto_rawDescGZIP(), []int{29}
}

func (x *LogoutByHallRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

var File_battlegame_proto protoreflect.FileDescriptor

var file_battlegame_proto_rawDesc = []byte{
	0x0a, 0x10, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x0f,
	0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x36, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x2e, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x44, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x51, 0x0a,
	0x1c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x23, 0x0a, 0x0b, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xac, 0x02, 0x0a, 0x16, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x42, 0x79, 0x42, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0xba, 0x03, 0x0a, 0x05, 0x57, 0x61, 0x67, 0x65, 0x72, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x22, 0x83, 0x01, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x22, 0x73, 0x0a, 0x0e, 0x57, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x77, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x52, 0x06, 0x77, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xca, 0x02, 0x0a,
	0x1b, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x32, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x67,
	0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xe7,
	0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x0b, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x65, 0x78, 0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x36, 0x0a, 0x0a, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x65,
	0x78, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x22, 0x59, 0x0a, 0x04, 0x4c, 0x69, 0x6e, 0x6b,
	0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x70, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x77, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x72, 0x77, 0x64, 0x22, 0x3a, 0x0a, 0x10, 0x4c, 0x69, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x6b, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x22,
	0x5f, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64,
	0x22, 0x28, 0x0a, 0x14, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xf4, 0x01, 0x0a, 0x15, 0x57,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x52, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x64,
	0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x22, 0xc1, 0x01, 0x0a, 0x0e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70,
	0x61, 0x79, 0x6f, 0x66, 0x66, 0x22, 0x60, 0x0a, 0x11, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x53,
	0x75, 0x6d, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x65, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61,
	0x79, 0x6f, 0x66, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x22, 0xc2, 0x01, 0x0a, 0x16, 0x57, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x06,
	0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x5f, 0x73, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x53, 0x75,
	0x6d, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x53, 0x75, 0x6d, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x04, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x65,
	0x6e, 0x64, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x6d, 0x69, 0x6e, 0x5f, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x42, 0x65, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x65, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6d, 0x61, 0x78,
	0x42, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e,
	0x5f, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6d,
	0x69, 0x6e, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f,
	0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x6f,
	0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x22, 0xf6, 0x05, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x57, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x42, 0x79, 0x44, 0x42, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63,
	0x68, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x11, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e,
	0x75, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75,
	0x65, 0x12, 0x31, 0x0a, 0x14, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x13, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x98, 0x01, 0x0a, 0x05,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x72,
	0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x06,
	0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x57,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x44, 0x42, 0x52, 0x06, 0x77, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0x4a, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06,
	0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x50,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67,
	0x22, 0x3d, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c,
	0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22,
	0x5e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53, 0x75, 0x62, 0x57, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3e, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x5f, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52,
	0x4c, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x72, 0x6c, 0x22,
	0x2e, 0x0a, 0x13, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x32,
	0xc7, 0x07, 0x0a, 0x0a, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x12, 0x63,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x62, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x15, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x2e, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x51, 0x0a, 0x0f, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x42, 0x65, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x42, 0x65, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x14, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x2e, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x42, 0x79, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x45, 0x0a, 0x08, 0x4c, 0x69, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x2e,
	0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x57,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x12, 0x1f, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55,
	0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x62, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x55, 0x52, 0x4c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x57,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x21, 0x2e,
	0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x12, 0x1c, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x23, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x69, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53, 0x75,
	0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x12, 0x27, 0x2e, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a,
	0x0c, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x1f, 0x2e,
	0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x6f, 0x75,
	0x74, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19,
	0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x12, 0x5a, 0x10, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_battlegame_proto_rawDescOnce sync.Once
	file_battlegame_proto_rawDescData = file_battlegame_proto_rawDesc
)

func file_battlegame_proto_rawDescGZIP() []byte {
	file_battlegame_proto_rawDescOnce.Do(func() {
		file_battlegame_proto_rawDescData = protoimpl.X.CompressGZIP(file_battlegame_proto_rawDescData)
	})
	return file_battlegame_proto_rawDescData
}

var file_battlegame_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_battlegame_proto_goTypes = []any{
	(*EmptyResponse)(nil),                // 0: battlegame.EmptyResponse
	(*GetUserPermissionsRequest)(nil),    // 1: battlegame.GetUserPermissionsRequest
	(*User)(nil),                         // 2: battlegame.User
	(*GetUserPermissionsResponse)(nil),   // 3: battlegame.GetUserPermissionsResponse
	(*EnableUserPermissionsRequest)(nil), // 4: battlegame.EnableUserPermissionsRequest
	(*Uint32Value)(nil),                  // 5: battlegame.Uint32Value
	(*WagersByBetTimeRequest)(nil),       // 6: battlegame.WagersByBetTimeRequest
	(*Wager)(nil),                        // 7: battlegame.Wager
	(*Pagination)(nil),                   // 8: battlegame.Pagination
	(*WagersResponse)(nil),               // 9: battlegame.WagersResponse
	(*WagersByModifiedTimeRequest)(nil),  // 10: battlegame.WagersByModifiedTimeRequest
	(*StringValue)(nil),                  // 11: battlegame.StringValue
	(*LinkListRequest)(nil),              // 12: battlegame.LinkListRequest
	(*Link)(nil),                         // 13: battlegame.Link
	(*LinkListResponse)(nil),             // 14: battlegame.LinkListResponse
	(*SubWagersURLRequest)(nil),          // 15: battlegame.SubWagersURLRequest
	(*SubWagersURLResponse)(nil),         // 16: battlegame.SubWagersURLResponse
	(*WagersByUserIDRequest)(nil),        // 17: battlegame.WagersByUserIDRequest
	(*WagersByUserID)(nil),               // 18: battlegame.WagersByUserID
	(*WagersSumByUserID)(nil),            // 19: battlegame.WagersSumByUserID
	(*WagersByUserIDResponse)(nil),       // 20: battlegame.WagersByUserIDResponse
	(*GetWagersRequest)(nil),             // 21: battlegame.GetWagersRequest
	(*FetchWagersByDB)(nil),              // 22: battlegame.FetchWagersByDB
	(*Total)(nil),                        // 23: battlegame.Total
	(*GetWagersResponse)(nil),            // 24: battlegame.GetWagersResponse
	(*UpdateGameSwitchRequest)(nil),      // 25: battlegame.UpdateGameSwitchRequest
	(*GetMultiSubWagersURLRequest)(nil),  // 26: battlegame.GetMultiSubWagersURLRequest
	(*SubWagersURL)(nil),                 // 27: battlegame.SubWagersURL
	(*GetMultiSubWagersURLResponse)(nil), // 28: battlegame.GetMultiSubWagersURLResponse
	(*LogoutByHallRequest)(nil),          // 29: battlegame.LogoutByHallRequest
}
var file_battlegame_proto_depIdxs = []int32{
	2,  // 0: battlegame.GetUserPermissionsResponse.users:type_name -> battlegame.User
	5,  // 1: battlegame.WagersByBetTimeRequest.game_id:type_name -> battlegame.Uint32Value
	5,  // 2: battlegame.WagersByBetTimeRequest.current_page:type_name -> battlegame.Uint32Value
	5,  // 3: battlegame.WagersByBetTimeRequest.page_limit:type_name -> battlegame.Uint32Value
	7,  // 4: battlegame.WagersResponse.wagers:type_name -> battlegame.Wager
	8,  // 5: battlegame.WagersResponse.pagination:type_name -> battlegame.Pagination
	5,  // 6: battlegame.WagersByModifiedTimeRequest.agent_id:type_name -> battlegame.Uint32Value
	5,  // 7: battlegame.WagersByModifiedTimeRequest.game_id:type_name -> battlegame.Uint32Value
	5,  // 8: battlegame.WagersByModifiedTimeRequest.current_page:type_name -> battlegame.Uint32Value
	5,  // 9: battlegame.WagersByModifiedTimeRequest.page_limit:type_name -> battlegame.Uint32Value
	11, // 10: battlegame.LinkListRequest.lang:type_name -> battlegame.StringValue
	5,  // 11: battlegame.LinkListRequest.exit_option:type_name -> battlegame.Uint32Value
	11, // 12: battlegame.LinkListRequest.exit_param:type_name -> battlegame.StringValue
	13, // 13: battlegame.LinkListResponse.links:type_name -> battlegame.Link
	18, // 14: battlegame.WagersByUserIDResponse.wagers:type_name -> battlegame.WagersByUserID
	19, // 15: battlegame.WagersByUserIDResponse.wagers_sum:type_name -> battlegame.WagersSumByUserID
	8,  // 16: battlegame.WagersByUserIDResponse.pagination:type_name -> battlegame.Pagination
	22, // 17: battlegame.GetWagersResponse.wagers:type_name -> battlegame.FetchWagersByDB
	8,  // 18: battlegame.GetWagersResponse.pagination:type_name -> battlegame.Pagination
	23, // 19: battlegame.GetWagersResponse.sub_total:type_name -> battlegame.Total
	23, // 20: battlegame.GetWagersResponse.total:type_name -> battlegame.Total
	27, // 21: battlegame.GetMultiSubWagersURLResponse.sub_wagers_url:type_name -> battlegame.SubWagersURL
	1,  // 22: battlegame.BattleGame.GetUserPermissions:input_type -> battlegame.GetUserPermissionsRequest
	4,  // 23: battlegame.BattleGame.EnableUserPermissions:input_type -> battlegame.EnableUserPermissionsRequest
	6,  // 24: battlegame.BattleGame.WagersByBetTime:input_type -> battlegame.WagersByBetTimeRequest
	10, // 25: battlegame.BattleGame.WagersByModifiedTime:input_type -> battlegame.WagersByModifiedTimeRequest
	12, // 26: battlegame.BattleGame.LinkList:input_type -> battlegame.LinkListRequest
	15, // 27: battlegame.BattleGame.SubWagersURL:input_type -> battlegame.SubWagersURLRequest
	17, // 28: battlegame.BattleGame.WagersByUserID:input_type -> battlegame.WagersByUserIDRequest
	21, // 29: battlegame.BattleGame.GetWagers:input_type -> battlegame.GetWagersRequest
	25, // 30: battlegame.BattleGame.UpdateGameSwitch:input_type -> battlegame.UpdateGameSwitchRequest
	26, // 31: battlegame.BattleGame.GetMultiSubWagersURL:input_type -> battlegame.GetMultiSubWagersURLRequest
	29, // 32: battlegame.BattleGame.LogoutByHall:input_type -> battlegame.LogoutByHallRequest
	3,  // 33: battlegame.BattleGame.GetUserPermissions:output_type -> battlegame.GetUserPermissionsResponse
	0,  // 34: battlegame.BattleGame.EnableUserPermissions:output_type -> battlegame.EmptyResponse
	9,  // 35: battlegame.BattleGame.WagersByBetTime:output_type -> battlegame.WagersResponse
	9,  // 36: battlegame.BattleGame.WagersByModifiedTime:output_type -> battlegame.WagersResponse
	14, // 37: battlegame.BattleGame.LinkList:output_type -> battlegame.LinkListResponse
	16, // 38: battlegame.BattleGame.SubWagersURL:output_type -> battlegame.SubWagersURLResponse
	20, // 39: battlegame.BattleGame.WagersByUserID:output_type -> battlegame.WagersByUserIDResponse
	24, // 40: battlegame.BattleGame.GetWagers:output_type -> battlegame.GetWagersResponse
	0,  // 41: battlegame.BattleGame.UpdateGameSwitch:output_type -> battlegame.EmptyResponse
	28, // 42: battlegame.BattleGame.GetMultiSubWagersURL:output_type -> battlegame.GetMultiSubWagersURLResponse
	0,  // 43: battlegame.BattleGame.LogoutByHall:output_type -> battlegame.EmptyResponse
	33, // [33:44] is the sub-list for method output_type
	22, // [22:33] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_battlegame_proto_init() }
func file_battlegame_proto_init() {
	if File_battlegame_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_battlegame_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserPermissionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserPermissionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*EnableUserPermissionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*WagersByBetTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Wager); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Pagination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*WagersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*WagersByModifiedTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*StringValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*LinkListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*Link); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*LinkListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*SubWagersURLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*SubWagersURLResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*WagersByUserIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*WagersByUserID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*WagersSumByUserID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*WagersByUserIDResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*GetWagersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*FetchWagersByDB); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*Total); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*GetWagersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateGameSwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*GetMultiSubWagersURLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*SubWagersURL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*GetMultiSubWagersURLResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_battlegame_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*LogoutByHallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_battlegame_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_battlegame_proto_goTypes,
		DependencyIndexes: file_battlegame_proto_depIdxs,
		MessageInfos:      file_battlegame_proto_msgTypes,
	}.Build()
	File_battlegame_proto = out.File
	file_battlegame_proto_rawDesc = nil
	file_battlegame_proto_goTypes = nil
	file_battlegame_proto_depIdxs = nil
}
