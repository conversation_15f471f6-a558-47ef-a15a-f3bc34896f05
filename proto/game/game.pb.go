// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.19.4
// source: game.proto

package game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{0}
}

type LobbyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind []uint32 `protobuf:"varint,1,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *LobbyResponse) Reset() {
	*x = LobbyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LobbyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LobbyResponse) ProtoMessage() {}

func (x *LobbyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LobbyResponse.ProtoReflect.Descriptor instead.
func (*LobbyResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{1}
}

func (x *LobbyResponse) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{2}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type StringValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StringValue) Reset() {
	*x = StringValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringValue) ProtoMessage() {}

func (x *StringValue) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringValue.ProtoReflect.Descriptor instead.
func (*StringValue) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{3}
}

func (x *StringValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type BoolValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value bool `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{4}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

type RepeatedUint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []uint32 `protobuf:"varint,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *RepeatedUint32Value) Reset() {
	*x = RepeatedUint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedUint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedUint32Value) ProtoMessage() {}

func (x *RepeatedUint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedUint32Value.ProtoReflect.Descriptor instead.
func (*RepeatedUint32Value) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{5}
}

func (x *RepeatedUint32Value) GetValue() []uint32 {
	if x != nil {
		return x.Value
	}
	return nil
}

type EnableGameListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32       `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	HallId   *Uint32Value `protobuf:"bytes,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Lang     *StringValue `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *EnableGameListRequest) Reset() {
	*x = EnableGameListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableGameListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableGameListRequest) ProtoMessage() {}

func (x *EnableGameListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableGameListRequest.ProtoReflect.Descriptor instead.
func (*EnableGameListRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{6}
}

func (x *EnableGameListRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *EnableGameListRequest) GetHallId() *Uint32Value {
	if x != nil {
		return x.HallId
	}
	return nil
}

func (x *EnableGameListRequest) GetLang() *StringValue {
	if x != nil {
		return x.Lang
	}
	return nil
}

type EnableGame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *EnableGame) Reset() {
	*x = EnableGame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableGame) ProtoMessage() {}

func (x *EnableGame) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableGame.ProtoReflect.Descriptor instead.
func (*EnableGame) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{7}
}

func (x *EnableGame) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EnableGame) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DemoLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32       `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	HallId   *Uint32Value `protobuf:"bytes,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Lang     *StringValue `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *DemoLinkRequest) Reset() {
	*x = DemoLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DemoLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DemoLinkRequest) ProtoMessage() {}

func (x *DemoLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DemoLinkRequest.ProtoReflect.Descriptor instead.
func (*DemoLinkRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{8}
}

func (x *DemoLinkRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *DemoLinkRequest) GetHallId() *Uint32Value {
	if x != nil {
		return x.HallId
	}
	return nil
}

func (x *DemoLinkRequest) GetLang() *StringValue {
	if x != nil {
		return x.Lang
	}
	return nil
}

type DemoLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DemoLinkInfo []*DemoLinkInfo `protobuf:"bytes,1,rep,name=demo_link_info,json=demoLinkInfo,proto3" json:"demo_link_info,omitempty"`
}

func (x *DemoLinkResponse) Reset() {
	*x = DemoLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DemoLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DemoLinkResponse) ProtoMessage() {}

func (x *DemoLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DemoLinkResponse.ProtoReflect.Descriptor instead.
func (*DemoLinkResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{9}
}

func (x *DemoLinkResponse) GetDemoLinkInfo() []*DemoLinkInfo {
	if x != nil {
		return x.DemoLinkInfo
	}
	return nil
}

type DemoLinkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Url    string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *DemoLinkInfo) Reset() {
	*x = DemoLinkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DemoLinkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DemoLinkInfo) ProtoMessage() {}

func (x *DemoLinkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DemoLinkInfo.ProtoReflect.Descriptor instead.
func (*DemoLinkInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{10}
}

func (x *DemoLinkInfo) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *DemoLinkInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type EnableGameListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Games []*EnableGame `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
}

func (x *EnableGameListResponse) Reset() {
	*x = EnableGameListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnableGameListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableGameListResponse) ProtoMessage() {}

func (x *EnableGameListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableGameListResponse.ProtoReflect.Descriptor instead.
func (*EnableGameListResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{11}
}

func (x *EnableGameListResponse) GetGames() []*EnableGame {
	if x != nil {
		return x.Games
	}
	return nil
}

type GameDomainRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	HallId   uint32 `protobuf:"varint,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GameDomainRequest) Reset() {
	*x = GameDomainRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDomainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDomainRequest) ProtoMessage() {}

func (x *GameDomainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDomainRequest.ProtoReflect.Descriptor instead.
func (*GameDomainRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{12}
}

func (x *GameDomainRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameDomainRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GameDomainResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain []string `protobuf:"bytes,1,rep,name=domain,proto3" json:"domain,omitempty"`
}

func (x *GameDomainResponse) Reset() {
	*x = GameDomainResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDomainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDomainResponse) ProtoMessage() {}

func (x *GameDomainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDomainResponse.ProtoReflect.Descriptor instead.
func (*GameDomainResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{13}
}

func (x *GameDomainResponse) GetDomain() []string {
	if x != nil {
		return x.Domain
	}
	return nil
}

type LobbyLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind   uint32       `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Session    string       `protobuf:"bytes,2,opt,name=session,proto3" json:"session,omitempty"`
	Lang       *StringValue `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
	Ip         *StringValue `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip,omitempty"`
	ExitOption *Uint32Value `protobuf:"bytes,5,opt,name=exit_option,json=exitOption,proto3" json:"exit_option,omitempty"`
	ExitUrl    *StringValue `protobuf:"bytes,6,opt,name=exit_url,json=exitUrl,proto3" json:"exit_url,omitempty"`
}

func (x *LobbyLinkRequest) Reset() {
	*x = LobbyLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LobbyLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LobbyLinkRequest) ProtoMessage() {}

func (x *LobbyLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LobbyLinkRequest.ProtoReflect.Descriptor instead.
func (*LobbyLinkRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{14}
}

func (x *LobbyLinkRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *LobbyLinkRequest) GetSession() string {
	if x != nil {
		return x.Session
	}
	return ""
}

func (x *LobbyLinkRequest) GetLang() *StringValue {
	if x != nil {
		return x.Lang
	}
	return nil
}

func (x *LobbyLinkRequest) GetIp() *StringValue {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *LobbyLinkRequest) GetExitOption() *Uint32Value {
	if x != nil {
		return x.ExitOption
	}
	return nil
}

func (x *LobbyLinkRequest) GetExitUrl() *StringValue {
	if x != nil {
		return x.ExitUrl
	}
	return nil
}

type LobbyLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *LobbyLinkResponse) Reset() {
	*x = LobbyLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LobbyLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LobbyLinkResponse) ProtoMessage() {}

func (x *LobbyLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LobbyLinkResponse.ProtoReflect.Descriptor instead.
func (*LobbyLinkResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{15}
}

func (x *LobbyLinkResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetLobbySwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId    uint32       `protobuf:"varint,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AllParents []uint32     `protobuf:"varint,2,rep,packed,name=all_parents,json=allParents,proto3" json:"all_parents,omitempty"`
	UserId     *Uint32Value `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetLobbySwitchRequest) Reset() {
	*x = GetLobbySwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbySwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbySwitchRequest) ProtoMessage() {}

func (x *GetLobbySwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbySwitchRequest.ProtoReflect.Descriptor instead.
func (*GetLobbySwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{16}
}

func (x *GetLobbySwitchRequest) GetAgentId() uint32 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *GetLobbySwitchRequest) GetAllParents() []uint32 {
	if x != nil {
		return x.AllParents
	}
	return nil
}

func (x *GetLobbySwitchRequest) GetUserId() *Uint32Value {
	if x != nil {
		return x.UserId
	}
	return nil
}

type LobbySwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Switch   bool   `protobuf:"varint,2,opt,name=switch,proto3" json:"switch,omitempty"`
	UserId   uint32 `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *LobbySwitch) Reset() {
	*x = LobbySwitch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LobbySwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LobbySwitch) ProtoMessage() {}

func (x *LobbySwitch) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LobbySwitch.ProtoReflect.Descriptor instead.
func (*LobbySwitch) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{17}
}

func (x *LobbySwitch) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *LobbySwitch) GetSwitch() bool {
	if x != nil {
		return x.Switch
	}
	return false
}

func (x *LobbySwitch) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetLobbySwitchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LobbySwitch []*LobbySwitch `protobuf:"bytes,1,rep,name=lobby_switch,json=lobbySwitch,proto3" json:"lobby_switch,omitempty"`
}

func (x *GetLobbySwitchResponse) Reset() {
	*x = GetLobbySwitchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbySwitchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbySwitchResponse) ProtoMessage() {}

func (x *GetLobbySwitchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbySwitchResponse.ProtoReflect.Descriptor instead.
func (*GetLobbySwitchResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{18}
}

func (x *GetLobbySwitchResponse) GetLobbySwitch() []*LobbySwitch {
	if x != nil {
		return x.LobbySwitch
	}
	return nil
}

type GetLobbySwitchByHallIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId            uint32     `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	FilterLobbyEnable bool       `protobuf:"varint,2,opt,name=filter_lobby_enable,json=filterLobbyEnable,proto3" json:"filter_lobby_enable,omitempty"`
	LobbySwitchEnable *BoolValue `protobuf:"bytes,3,opt,name=lobby_switch_enable,json=lobbySwitchEnable,proto3" json:"lobby_switch_enable,omitempty"`
}

func (x *GetLobbySwitchByHallIDRequest) Reset() {
	*x = GetLobbySwitchByHallIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbySwitchByHallIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbySwitchByHallIDRequest) ProtoMessage() {}

func (x *GetLobbySwitchByHallIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbySwitchByHallIDRequest.ProtoReflect.Descriptor instead.
func (*GetLobbySwitchByHallIDRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{19}
}

func (x *GetLobbySwitchByHallIDRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetLobbySwitchByHallIDRequest) GetFilterLobbyEnable() bool {
	if x != nil {
		return x.FilterLobbyEnable
	}
	return false
}

func (x *GetLobbySwitchByHallIDRequest) GetLobbySwitchEnable() *BoolValue {
	if x != nil {
		return x.LobbySwitchEnable
	}
	return nil
}

type GetLobbySwitchByHallIDResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LobbySwitch []*LobbySwitch `protobuf:"bytes,1,rep,name=lobby_switch,json=lobbySwitch,proto3" json:"lobby_switch,omitempty"`
}

func (x *GetLobbySwitchByHallIDResponse) Reset() {
	*x = GetLobbySwitchByHallIDResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbySwitchByHallIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbySwitchByHallIDResponse) ProtoMessage() {}

func (x *GetLobbySwitchByHallIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbySwitchByHallIDResponse.ProtoReflect.Descriptor instead.
func (*GetLobbySwitchByHallIDResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{20}
}

func (x *GetLobbySwitchByHallIDResponse) GetLobbySwitch() []*LobbySwitch {
	if x != nil {
		return x.LobbySwitch
	}
	return nil
}

type GetCategoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GetCategoryRequest) Reset() {
	*x = GetCategoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryRequest) ProtoMessage() {}

func (x *GetCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetCategoryRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{21}
}

func (x *GetCategoryRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type CategoryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MenuId uint32 `protobuf:"varint,1,opt,name=menu_id,json=menuId,proto3" json:"menu_id,omitempty"`
	TopId  uint32 `protobuf:"varint,2,opt,name=top_id,json=topId,proto3" json:"top_id,omitempty"`
	Depth  uint32 `protobuf:"varint,3,opt,name=depth,proto3" json:"depth,omitempty"`
	Sort   uint32 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *CategoryInfo) Reset() {
	*x = CategoryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryInfo) ProtoMessage() {}

func (x *CategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryInfo.ProtoReflect.Descriptor instead.
func (*CategoryInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{22}
}

func (x *CategoryInfo) GetMenuId() uint32 {
	if x != nil {
		return x.MenuId
	}
	return 0
}

func (x *CategoryInfo) GetTopId() uint32 {
	if x != nil {
		return x.TopId
	}
	return 0
}

func (x *CategoryInfo) GetDepth() uint32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

func (x *CategoryInfo) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

type GetCategoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*CategoryInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetCategoryResponse) Reset() {
	*x = GetCategoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryResponse) ProtoMessage() {}

func (x *GetCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryResponse.ProtoReflect.Descriptor instead.
func (*GetCategoryResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{23}
}

func (x *GetCategoryResponse) GetData() []*CategoryInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetMenuSortRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GetMenuSortRequest) Reset() {
	*x = GetMenuSortRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMenuSortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMenuSortRequest) ProtoMessage() {}

func (x *GetMenuSortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMenuSortRequest.ProtoReflect.Descriptor instead.
func (*GetMenuSortRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{24}
}

func (x *GetMenuSortRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type MenuSortInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MenuId   uint32 `protobuf:"varint,1,opt,name=menu_id,json=menuId,proto3" json:"menu_id,omitempty"`
	Sort     uint32 `protobuf:"varint,2,opt,name=sort,proto3" json:"sort,omitempty"`
	GameType uint32 `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
}

func (x *MenuSortInfo) Reset() {
	*x = MenuSortInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MenuSortInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MenuSortInfo) ProtoMessage() {}

func (x *MenuSortInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MenuSortInfo.ProtoReflect.Descriptor instead.
func (*MenuSortInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{25}
}

func (x *MenuSortInfo) GetMenuId() uint32 {
	if x != nil {
		return x.MenuId
	}
	return 0
}

func (x *MenuSortInfo) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *MenuSortInfo) GetGameType() uint32 {
	if x != nil {
		return x.GameType
	}
	return 0
}

type GetMenuSortResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*MenuSortInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMenuSortResponse) Reset() {
	*x = GetMenuSortResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMenuSortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMenuSortResponse) ProtoMessage() {}

func (x *GetMenuSortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMenuSortResponse.ProtoReflect.Descriptor instead.
func (*GetMenuSortResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{26}
}

func (x *GetMenuSortResponse) GetData() []*MenuSortInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetGameInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId   uint32 `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
}

func (x *GetGameInfoRequest) Reset() {
	*x = GetGameInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameInfoRequest) ProtoMessage() {}

func (x *GetGameInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameInfoRequest.ProtoReflect.Descriptor instead.
func (*GetGameInfoRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{27}
}

func (x *GetGameInfoRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GetGameInfoRequest) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

type GameInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lobby        uint32   `protobuf:"varint,1,opt,name=lobby,proto3" json:"lobby,omitempty"`
	Device       uint32   `protobuf:"varint,2,opt,name=device,proto3" json:"device,omitempty"`
	GameType     uint32   `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	Name         string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Enable       bool     `protobuf:"varint,5,opt,name=enable,proto3" json:"enable,omitempty"`
	PcEnable     bool     `protobuf:"varint,6,opt,name=pc_enable,json=pcEnable,proto3" json:"pc_enable,omitempty"`
	MobileEnable bool     `protobuf:"varint,7,opt,name=mobile_enable,json=mobileEnable,proto3" json:"mobile_enable,omitempty"`
	DemoEnable   bool     `protobuf:"varint,8,opt,name=demo_enable,json=demoEnable,proto3" json:"demo_enable,omitempty"`
	IsJackpot    bool     `protobuf:"varint,9,opt,name=is_jackpot,json=isJackpot,proto3" json:"is_jackpot,omitempty"`
	IconKind     string   `protobuf:"bytes,10,opt,name=icon_kind,json=iconKind,proto3" json:"icon_kind,omitempty"`
	OpenDate     string   `protobuf:"bytes,11,opt,name=open_date,json=openDate,proto3" json:"open_date,omitempty"`
	AllowList    []uint32 `protobuf:"varint,12,rep,packed,name=allow_list,json=allowList,proto3" json:"allow_list,omitempty"`
	BlockList    []uint32 `protobuf:"varint,13,rep,packed,name=block_list,json=blockList,proto3" json:"block_list,omitempty"`
	ExternalId   string   `protobuf:"bytes,14,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
}

func (x *GameInfo) Reset() {
	*x = GameInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameInfo) ProtoMessage() {}

func (x *GameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameInfo.ProtoReflect.Descriptor instead.
func (*GameInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{28}
}

func (x *GameInfo) GetLobby() uint32 {
	if x != nil {
		return x.Lobby
	}
	return 0
}

func (x *GameInfo) GetDevice() uint32 {
	if x != nil {
		return x.Device
	}
	return 0
}

func (x *GameInfo) GetGameType() uint32 {
	if x != nil {
		return x.GameType
	}
	return 0
}

func (x *GameInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *GameInfo) GetPcEnable() bool {
	if x != nil {
		return x.PcEnable
	}
	return false
}

func (x *GameInfo) GetMobileEnable() bool {
	if x != nil {
		return x.MobileEnable
	}
	return false
}

func (x *GameInfo) GetDemoEnable() bool {
	if x != nil {
		return x.DemoEnable
	}
	return false
}

func (x *GameInfo) GetIsJackpot() bool {
	if x != nil {
		return x.IsJackpot
	}
	return false
}

func (x *GameInfo) GetIconKind() string {
	if x != nil {
		return x.IconKind
	}
	return ""
}

func (x *GameInfo) GetOpenDate() string {
	if x != nil {
		return x.OpenDate
	}
	return ""
}

func (x *GameInfo) GetAllowList() []uint32 {
	if x != nil {
		return x.AllowList
	}
	return nil
}

func (x *GameInfo) GetBlockList() []uint32 {
	if x != nil {
		return x.BlockList
	}
	return nil
}

func (x *GameInfo) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

type ExternalList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalId string `protobuf:"bytes,1,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	GameType   uint32 `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
}

func (x *ExternalList) Reset() {
	*x = ExternalList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExternalList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalList) ProtoMessage() {}

func (x *ExternalList) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalList.ProtoReflect.Descriptor instead.
func (*ExternalList) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{29}
}

func (x *ExternalList) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *ExternalList) GetGameType() uint32 {
	if x != nil {
		return x.GameType
	}
	return 0
}

type GetGameInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data         []*GameInfo     `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	ExternalList []*ExternalList `protobuf:"bytes,2,rep,name=external_list,json=externalList,proto3" json:"external_list,omitempty"`
}

func (x *GetGameInfoResponse) Reset() {
	*x = GetGameInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameInfoResponse) ProtoMessage() {}

func (x *GetGameInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameInfoResponse.ProtoReflect.Descriptor instead.
func (*GetGameInfoResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{30}
}

func (x *GetGameInfoResponse) GetData() []*GameInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetGameInfoResponse) GetExternalList() []*ExternalList {
	if x != nil {
		return x.ExternalList
	}
	return nil
}

type GetLobbyGameEntranceSwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	HallId   uint32 `protobuf:"varint,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetLobbyGameEntranceSwitchRequest) Reset() {
	*x = GetLobbyGameEntranceSwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbyGameEntranceSwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbyGameEntranceSwitchRequest) ProtoMessage() {}

func (x *GetLobbyGameEntranceSwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbyGameEntranceSwitchRequest.ProtoReflect.Descriptor instead.
func (*GetLobbyGameEntranceSwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{31}
}

func (x *GetLobbyGameEntranceSwitchRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GetLobbyGameEntranceSwitchRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type LobbyGameEntranceSwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameType     string `protobuf:"bytes,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	PcEnable     bool   `protobuf:"varint,2,opt,name=pc_enable,json=pcEnable,proto3" json:"pc_enable,omitempty"`
	MobileEnable bool   `protobuf:"varint,3,opt,name=mobile_enable,json=mobileEnable,proto3" json:"mobile_enable,omitempty"`
}

func (x *LobbyGameEntranceSwitch) Reset() {
	*x = LobbyGameEntranceSwitch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LobbyGameEntranceSwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LobbyGameEntranceSwitch) ProtoMessage() {}

func (x *LobbyGameEntranceSwitch) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LobbyGameEntranceSwitch.ProtoReflect.Descriptor instead.
func (*LobbyGameEntranceSwitch) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{32}
}

func (x *LobbyGameEntranceSwitch) GetGameType() string {
	if x != nil {
		return x.GameType
	}
	return ""
}

func (x *LobbyGameEntranceSwitch) GetPcEnable() bool {
	if x != nil {
		return x.PcEnable
	}
	return false
}

func (x *LobbyGameEntranceSwitch) GetMobileEnable() bool {
	if x != nil {
		return x.MobileEnable
	}
	return false
}

type GetLobbyGameEntranceSwitchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*LobbyGameEntranceSwitch `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetLobbyGameEntranceSwitchResponse) Reset() {
	*x = GetLobbyGameEntranceSwitchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbyGameEntranceSwitchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbyGameEntranceSwitchResponse) ProtoMessage() {}

func (x *GetLobbyGameEntranceSwitchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbyGameEntranceSwitchResponse.ProtoReflect.Descriptor instead.
func (*GetLobbyGameEntranceSwitchResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{33}
}

func (x *GetLobbyGameEntranceSwitchResponse) GetData() []*LobbyGameEntranceSwitch {
	if x != nil {
		return x.Data
	}
	return nil
}

type BulletinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartDate string `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate   string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Lang      string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *BulletinRequest) Reset() {
	*x = BulletinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulletinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulletinRequest) ProtoMessage() {}

func (x *BulletinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulletinRequest.ProtoReflect.Descriptor instead.
func (*BulletinRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{34}
}

func (x *BulletinRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *BulletinRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *BulletinRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type BulletinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bulletin []*BulletinData `protobuf:"bytes,1,rep,name=bulletin,proto3" json:"bulletin,omitempty"`
}

func (x *BulletinResponse) Reset() {
	*x = BulletinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulletinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulletinResponse) ProtoMessage() {}

func (x *BulletinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulletinResponse.ProtoReflect.Descriptor instead.
func (*BulletinResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{35}
}

func (x *BulletinResponse) GetBulletin() []*BulletinData {
	if x != nil {
		return x.Bulletin
	}
	return nil
}

type BulletinData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date    string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *BulletinData) Reset() {
	*x = BulletinData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulletinData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulletinData) ProtoMessage() {}

func (x *BulletinData) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulletinData.ProtoReflect.Descriptor instead.
func (*BulletinData) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{36}
}

func (x *BulletinData) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *BulletinData) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type GameKindList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameCode string `protobuf:"bytes,2,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
}

func (x *GameKindList) Reset() {
	*x = GameKindList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameKindList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameKindList) ProtoMessage() {}

func (x *GameKindList) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameKindList.ProtoReflect.Descriptor instead.
func (*GameKindList) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{37}
}

func (x *GameKindList) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameKindList) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

type GameKindListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKindList []*GameKindList `protobuf:"bytes,1,rep,name=game_kind_list,json=gameKindList,proto3" json:"game_kind_list,omitempty"`
}

func (x *GameKindListResponse) Reset() {
	*x = GameKindListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameKindListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameKindListResponse) ProtoMessage() {}

func (x *GameKindListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameKindListResponse.ProtoReflect.Descriptor instead.
func (*GameKindListResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{38}
}

func (x *GameKindListResponse) GetGameKindList() []*GameKindList {
	if x != nil {
		return x.GameKindList
	}
	return nil
}

type GameIconKindInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind uint32 `protobuf:"varint,1,opt,name=kind,proto3" json:"kind,omitempty"`
	Icon string `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
}

func (x *GameIconKindInfo) Reset() {
	*x = GameIconKindInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameIconKindInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameIconKindInfo) ProtoMessage() {}

func (x *GameIconKindInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameIconKindInfo.ProtoReflect.Descriptor instead.
func (*GameIconKindInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{39}
}

func (x *GameIconKindInfo) GetKind() uint32 {
	if x != nil {
		return x.Kind
	}
	return 0
}

func (x *GameIconKindInfo) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

type GameIconKindResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IconKind []*GameIconKindInfo `protobuf:"bytes,1,rep,name=icon_kind,json=iconKind,proto3" json:"icon_kind,omitempty"`
}

func (x *GameIconKindResponse) Reset() {
	*x = GameIconKindResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameIconKindResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameIconKindResponse) ProtoMessage() {}

func (x *GameIconKindResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameIconKindResponse.ProtoReflect.Descriptor instead.
func (*GameIconKindResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{40}
}

func (x *GameIconKindResponse) GetIconKind() []*GameIconKindInfo {
	if x != nil {
		return x.IconKind
	}
	return nil
}

type AddUserFavoriteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId   uint32 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
}

func (x *AddUserFavoriteRequest) Reset() {
	*x = AddUserFavoriteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserFavoriteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserFavoriteRequest) ProtoMessage() {}

func (x *AddUserFavoriteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserFavoriteRequest.ProtoReflect.Descriptor instead.
func (*AddUserFavoriteRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{41}
}

func (x *AddUserFavoriteRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserFavoriteRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *AddUserFavoriteRequest) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{42}
}

type GetMenuNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetMenuNameRequest) Reset() {
	*x = GetMenuNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMenuNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMenuNameRequest) ProtoMessage() {}

func (x *GetMenuNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMenuNameRequest.ProtoReflect.Descriptor instead.
func (*GetMenuNameRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{43}
}

func (x *GetMenuNameRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type MenuInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MenuId   uint32 `protobuf:"varint,1,opt,name=menu_id,json=menuId,proto3" json:"menu_id,omitempty"`
	MenuName string `protobuf:"bytes,2,opt,name=menu_name,json=menuName,proto3" json:"menu_name,omitempty"`
}

func (x *MenuInfo) Reset() {
	*x = MenuInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MenuInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MenuInfo) ProtoMessage() {}

func (x *MenuInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MenuInfo.ProtoReflect.Descriptor instead.
func (*MenuInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{44}
}

func (x *MenuInfo) GetMenuId() uint32 {
	if x != nil {
		return x.MenuId
	}
	return 0
}

func (x *MenuInfo) GetMenuName() string {
	if x != nil {
		return x.MenuName
	}
	return ""
}

type GetMenuNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MenuList []*MenuInfo `protobuf:"bytes,1,rep,name=menu_list,json=menuList,proto3" json:"menu_list,omitempty"`
}

func (x *GetMenuNameResponse) Reset() {
	*x = GetMenuNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMenuNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMenuNameResponse) ProtoMessage() {}

func (x *GetMenuNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMenuNameResponse.ProtoReflect.Descriptor instead.
func (*GetMenuNameResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{45}
}

func (x *GetMenuNameResponse) GetMenuList() []*MenuInfo {
	if x != nil {
		return x.MenuList
	}
	return nil
}

type GetUserFavoriteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GetUserFavoriteRequest) Reset() {
	*x = GetUserFavoriteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFavoriteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFavoriteRequest) ProtoMessage() {}

func (x *GetUserFavoriteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFavoriteRequest.ProtoReflect.Descriptor instead.
func (*GetUserFavoriteRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{46}
}

func (x *GetUserFavoriteRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserFavoriteRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type UserFavorite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameType uint32 `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
}

func (x *UserFavorite) Reset() {
	*x = UserFavorite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserFavorite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFavorite) ProtoMessage() {}

func (x *UserFavorite) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFavorite.ProtoReflect.Descriptor instead.
func (*UserFavorite) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{47}
}

func (x *UserFavorite) GetGameType() uint32 {
	if x != nil {
		return x.GameType
	}
	return 0
}

type GetUserFavoriteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserFavorite []*UserFavorite `protobuf:"bytes,1,rep,name=user_favorite,json=userFavorite,proto3" json:"user_favorite,omitempty"`
}

func (x *GetUserFavoriteResponse) Reset() {
	*x = GetUserFavoriteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFavoriteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFavoriteResponse) ProtoMessage() {}

func (x *GetUserFavoriteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFavoriteResponse.ProtoReflect.Descriptor instead.
func (*GetUserFavoriteResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{48}
}

func (x *GetUserFavoriteResponse) GetUserFavorite() []*UserFavorite {
	if x != nil {
		return x.UserFavorite
	}
	return nil
}

type DeleteUserFavoriteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId   uint32 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
}

func (x *DeleteUserFavoriteRequest) Reset() {
	*x = DeleteUserFavoriteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserFavoriteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserFavoriteRequest) ProtoMessage() {}

func (x *DeleteUserFavoriteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserFavoriteRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserFavoriteRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{49}
}

func (x *DeleteUserFavoriteRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DeleteUserFavoriteRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *DeleteUserFavoriteRequest) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

type GameListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Lang     string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GameListRequest) Reset() {
	*x = GameListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameListRequest) ProtoMessage() {}

func (x *GameListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameListRequest.ProtoReflect.Descriptor instead.
func (*GameListRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{50}
}

func (x *GameListRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameListRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GameList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GameList) Reset() {
	*x = GameList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameList) ProtoMessage() {}

func (x *GameList) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameList.ProtoReflect.Descriptor instead.
func (*GameList) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{51}
}

func (x *GameList) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GameList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GameListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GameList `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GameListResponse) Reset() {
	*x = GameListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameListResponse) ProtoMessage() {}

func (x *GameListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameListResponse.ProtoReflect.Descriptor instead.
func (*GameListResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{52}
}

func (x *GameListResponse) GetData() []*GameList {
	if x != nil {
		return x.Data
	}
	return nil
}

type GameMaintainLabelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind []uint32 `protobuf:"varint,1,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GameMaintainLabelRequest) Reset() {
	*x = GameMaintainLabelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameMaintainLabelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameMaintainLabelRequest) ProtoMessage() {}

func (x *GameMaintainLabelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameMaintainLabelRequest.ProtoReflect.Descriptor instead.
func (*GameMaintainLabelRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{53}
}

func (x *GameMaintainLabelRequest) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

type GameMaintainLabelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GameMaintainLabel `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GameMaintainLabelResponse) Reset() {
	*x = GameMaintainLabelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameMaintainLabelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameMaintainLabelResponse) ProtoMessage() {}

func (x *GameMaintainLabelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameMaintainLabelResponse.ProtoReflect.Descriptor instead.
func (*GameMaintainLabelResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{54}
}

func (x *GameMaintainLabelResponse) GetData() []*GameMaintainLabel {
	if x != nil {
		return x.Data
	}
	return nil
}

type GameMaintainLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId   string `protobuf:"bytes,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Label    uint32 `protobuf:"varint,3,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *GameMaintainLabel) Reset() {
	*x = GameMaintainLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameMaintainLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameMaintainLabel) ProtoMessage() {}

func (x *GameMaintainLabel) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameMaintainLabel.ProtoReflect.Descriptor instead.
func (*GameMaintainLabel) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{55}
}

func (x *GameMaintainLabel) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameMaintainLabel) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *GameMaintainLabel) GetLabel() uint32 {
	if x != nil {
		return x.Label
	}
	return 0
}

type GameURLListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind uint32       `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	HallId   *Uint32Value `protobuf:"bytes,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GameURLListRequest) Reset() {
	*x = GameURLListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameURLListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameURLListRequest) ProtoMessage() {}

func (x *GameURLListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameURLListRequest.ProtoReflect.Descriptor instead.
func (*GameURLListRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{56}
}

func (x *GameURLListRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameURLListRequest) GetHallId() *Uint32Value {
	if x != nil {
		return x.HallId
	}
	return nil
}

type GameURLListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GameURL `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GameURLListResponse) Reset() {
	*x = GameURLListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameURLListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameURLListResponse) ProtoMessage() {}

func (x *GameURLListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameURLListResponse.ProtoReflect.Descriptor instead.
func (*GameURLListResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{57}
}

func (x *GameURLListResponse) GetData() []*GameURL {
	if x != nil {
		return x.Data
	}
	return nil
}

type GameURL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *GameURL) Reset() {
	*x = GameURL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameURL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameURL) ProtoMessage() {}

func (x *GameURL) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameURL.ProtoReflect.Descriptor instead.
func (*GameURL) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{58}
}

func (x *GameURL) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GameURL) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type LobbyCategoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BbTip              bool       `protobuf:"varint,1,opt,name=bb_tip,json=bbTip,proto3" json:"bb_tip,omitempty"`
	TransferWagersType bool       `protobuf:"varint,2,opt,name=transfer_wagers_type,json=transferWagersType,proto3" json:"transfer_wagers_type,omitempty"`
	Enable             *BoolValue `protobuf:"bytes,3,opt,name=enable,proto3" json:"enable,omitempty"`
	Category           string     `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	Report             *BoolValue `protobuf:"bytes,5,opt,name=report,proto3" json:"report,omitempty"`
	Commissionable     *BoolValue `protobuf:"bytes,6,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
	Jp                 *BoolValue `protobuf:"bytes,7,opt,name=jp,proto3" json:"jp,omitempty"`
	External           *BoolValue `protobuf:"bytes,8,opt,name=external,proto3" json:"external,omitempty"`
	LastBet            string     `protobuf:"bytes,9,opt,name=last_bet,json=lastBet,proto3" json:"last_bet,omitempty"`
	Series             string     `protobuf:"bytes,10,opt,name=series,proto3" json:"series,omitempty"`
}

func (x *LobbyCategoryRequest) Reset() {
	*x = LobbyCategoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LobbyCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LobbyCategoryRequest) ProtoMessage() {}

func (x *LobbyCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LobbyCategoryRequest.ProtoReflect.Descriptor instead.
func (*LobbyCategoryRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{59}
}

func (x *LobbyCategoryRequest) GetBbTip() bool {
	if x != nil {
		return x.BbTip
	}
	return false
}

func (x *LobbyCategoryRequest) GetTransferWagersType() bool {
	if x != nil {
		return x.TransferWagersType
	}
	return false
}

func (x *LobbyCategoryRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *LobbyCategoryRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *LobbyCategoryRequest) GetReport() *BoolValue {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *LobbyCategoryRequest) GetCommissionable() *BoolValue {
	if x != nil {
		return x.Commissionable
	}
	return nil
}

func (x *LobbyCategoryRequest) GetJp() *BoolValue {
	if x != nil {
		return x.Jp
	}
	return nil
}

func (x *LobbyCategoryRequest) GetExternal() *BoolValue {
	if x != nil {
		return x.External
	}
	return nil
}

func (x *LobbyCategoryRequest) GetLastBet() string {
	if x != nil {
		return x.LastBet
	}
	return ""
}

func (x *LobbyCategoryRequest) GetSeries() string {
	if x != nil {
		return x.Series
	}
	return ""
}

type LobbyCategoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Live    []string `protobuf:"bytes,1,rep,name=live,proto3" json:"live,omitempty"`
	Prob    []string `protobuf:"bytes,2,rep,name=prob,proto3" json:"prob,omitempty"`
	Sport   []string `protobuf:"bytes,3,rep,name=sport,proto3" json:"sport,omitempty"`
	Lottery []string `protobuf:"bytes,4,rep,name=lottery,proto3" json:"lottery,omitempty"`
	Card    []string `protobuf:"bytes,5,rep,name=card,proto3" json:"card,omitempty"`
}

func (x *LobbyCategoryResponse) Reset() {
	*x = LobbyCategoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LobbyCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LobbyCategoryResponse) ProtoMessage() {}

func (x *LobbyCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LobbyCategoryResponse.ProtoReflect.Descriptor instead.
func (*LobbyCategoryResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{60}
}

func (x *LobbyCategoryResponse) GetLive() []string {
	if x != nil {
		return x.Live
	}
	return nil
}

func (x *LobbyCategoryResponse) GetProb() []string {
	if x != nil {
		return x.Prob
	}
	return nil
}

func (x *LobbyCategoryResponse) GetSport() []string {
	if x != nil {
		return x.Sport
	}
	return nil
}

func (x *LobbyCategoryResponse) GetLottery() []string {
	if x != nil {
		return x.Lottery
	}
	return nil
}

func (x *LobbyCategoryResponse) GetCard() []string {
	if x != nil {
		return x.Card
	}
	return nil
}

type GetGameListWithSwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind            []uint32     `protobuf:"varint,1,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId              []uint32     `protobuf:"varint,2,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ExternalId          []string     `protobuf:"bytes,3,rep,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	IconKind            string       `protobuf:"bytes,4,opt,name=icon_kind,json=iconKind,proto3" json:"icon_kind,omitempty"`
	CommissionableGroup *StringValue `protobuf:"bytes,5,opt,name=commissionable_group,json=commissionableGroup,proto3" json:"commissionable_group,omitempty"`
	OpenStartDate       string       `protobuf:"bytes,6,opt,name=open_start_date,json=openStartDate,proto3" json:"open_start_date,omitempty"`
	OpenEndDate         string       `protobuf:"bytes,7,opt,name=open_end_date,json=openEndDate,proto3" json:"open_end_date,omitempty"`
	Device              *Uint32Value `protobuf:"bytes,8,opt,name=device,proto3" json:"device,omitempty"`
	PlatformEnable      *BoolValue   `protobuf:"bytes,9,opt,name=platform_enable,json=platformEnable,proto3" json:"platform_enable,omitempty"`
	PcEnable            *BoolValue   `protobuf:"bytes,10,opt,name=pc_enable,json=pcEnable,proto3" json:"pc_enable,omitempty"`
	MobileEnable        *BoolValue   `protobuf:"bytes,11,opt,name=mobile_enable,json=mobileEnable,proto3" json:"mobile_enable,omitempty"`
	DemoEnable          *BoolValue   `protobuf:"bytes,12,opt,name=demo_enable,json=demoEnable,proto3" json:"demo_enable,omitempty"`
	IsJackpot           *BoolValue   `protobuf:"bytes,13,opt,name=is_jackpot,json=isJackpot,proto3" json:"is_jackpot,omitempty"`
}

func (x *GetGameListWithSwitchRequest) Reset() {
	*x = GetGameListWithSwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameListWithSwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameListWithSwitchRequest) ProtoMessage() {}

func (x *GetGameListWithSwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameListWithSwitchRequest.ProtoReflect.Descriptor instead.
func (*GetGameListWithSwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{61}
}

func (x *GetGameListWithSwitchRequest) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetGameId() []uint32 {
	if x != nil {
		return x.GameId
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetExternalId() []string {
	if x != nil {
		return x.ExternalId
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetIconKind() string {
	if x != nil {
		return x.IconKind
	}
	return ""
}

func (x *GetGameListWithSwitchRequest) GetCommissionableGroup() *StringValue {
	if x != nil {
		return x.CommissionableGroup
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetOpenStartDate() string {
	if x != nil {
		return x.OpenStartDate
	}
	return ""
}

func (x *GetGameListWithSwitchRequest) GetOpenEndDate() string {
	if x != nil {
		return x.OpenEndDate
	}
	return ""
}

func (x *GetGameListWithSwitchRequest) GetDevice() *Uint32Value {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetPlatformEnable() *BoolValue {
	if x != nil {
		return x.PlatformEnable
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetPcEnable() *BoolValue {
	if x != nil {
		return x.PcEnable
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetMobileEnable() *BoolValue {
	if x != nil {
		return x.MobileEnable
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetDemoEnable() *BoolValue {
	if x != nil {
		return x.DemoEnable
	}
	return nil
}

func (x *GetGameListWithSwitchRequest) GetIsJackpot() *BoolValue {
	if x != nil {
		return x.IsJackpot
	}
	return nil
}

type GameListWithSwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind               uint32   `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId                 uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Name                   string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Device                 uint32   `protobuf:"varint,4,opt,name=device,proto3" json:"device,omitempty"`
	PlatformEnable         bool     `protobuf:"varint,5,opt,name=platform_enable,json=platformEnable,proto3" json:"platform_enable,omitempty"`
	PcEnable               bool     `protobuf:"varint,6,opt,name=pc_enable,json=pcEnable,proto3" json:"pc_enable,omitempty"`
	MobileEnable           bool     `protobuf:"varint,7,opt,name=mobile_enable,json=mobileEnable,proto3" json:"mobile_enable,omitempty"`
	DemoEnable             bool     `protobuf:"varint,8,opt,name=demo_enable,json=demoEnable,proto3" json:"demo_enable,omitempty"`
	IsJackpot              bool     `protobuf:"varint,9,opt,name=is_jackpot,json=isJackpot,proto3" json:"is_jackpot,omitempty"`
	CommissionableGroup    string   `protobuf:"bytes,10,opt,name=commissionable_group,json=commissionableGroup,proto3" json:"commissionable_group,omitempty"`
	OpenDate               string   `protobuf:"bytes,11,opt,name=open_date,json=openDate,proto3" json:"open_date,omitempty"`
	UpdatedAt              string   `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	ExternalId             string   `protobuf:"bytes,13,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	IconKind               string   `protobuf:"bytes,14,opt,name=icon_kind,json=iconKind,proto3" json:"icon_kind,omitempty"`
	WhiteList              []uint32 `protobuf:"varint,15,rep,packed,name=white_list,json=whiteList,proto3" json:"white_list,omitempty"`
	DisableEntranceHallIds []uint32 `protobuf:"varint,16,rep,packed,name=disable_entrance_hall_ids,json=disableEntranceHallIds,proto3" json:"disable_entrance_hall_ids,omitempty"`
	DisablePlatformHallIds []uint32 `protobuf:"varint,17,rep,packed,name=disable_platform_hall_ids,json=disablePlatformHallIds,proto3" json:"disable_platform_hall_ids,omitempty"`
}

func (x *GameListWithSwitch) Reset() {
	*x = GameListWithSwitch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameListWithSwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameListWithSwitch) ProtoMessage() {}

func (x *GameListWithSwitch) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameListWithSwitch.ProtoReflect.Descriptor instead.
func (*GameListWithSwitch) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{62}
}

func (x *GameListWithSwitch) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameListWithSwitch) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *GameListWithSwitch) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameListWithSwitch) GetDevice() uint32 {
	if x != nil {
		return x.Device
	}
	return 0
}

func (x *GameListWithSwitch) GetPlatformEnable() bool {
	if x != nil {
		return x.PlatformEnable
	}
	return false
}

func (x *GameListWithSwitch) GetPcEnable() bool {
	if x != nil {
		return x.PcEnable
	}
	return false
}

func (x *GameListWithSwitch) GetMobileEnable() bool {
	if x != nil {
		return x.MobileEnable
	}
	return false
}

func (x *GameListWithSwitch) GetDemoEnable() bool {
	if x != nil {
		return x.DemoEnable
	}
	return false
}

func (x *GameListWithSwitch) GetIsJackpot() bool {
	if x != nil {
		return x.IsJackpot
	}
	return false
}

func (x *GameListWithSwitch) GetCommissionableGroup() string {
	if x != nil {
		return x.CommissionableGroup
	}
	return ""
}

func (x *GameListWithSwitch) GetOpenDate() string {
	if x != nil {
		return x.OpenDate
	}
	return ""
}

func (x *GameListWithSwitch) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *GameListWithSwitch) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *GameListWithSwitch) GetIconKind() string {
	if x != nil {
		return x.IconKind
	}
	return ""
}

func (x *GameListWithSwitch) GetWhiteList() []uint32 {
	if x != nil {
		return x.WhiteList
	}
	return nil
}

func (x *GameListWithSwitch) GetDisableEntranceHallIds() []uint32 {
	if x != nil {
		return x.DisableEntranceHallIds
	}
	return nil
}

func (x *GameListWithSwitch) GetDisablePlatformHallIds() []uint32 {
	if x != nil {
		return x.DisablePlatformHallIds
	}
	return nil
}

type GetGameListWithSwitchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GameListWithSwitch `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetGameListWithSwitchResponse) Reset() {
	*x = GetGameListWithSwitchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameListWithSwitchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameListWithSwitchResponse) ProtoMessage() {}

func (x *GetGameListWithSwitchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameListWithSwitchResponse.ProtoReflect.Descriptor instead.
func (*GetGameListWithSwitchResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{63}
}

func (x *GetGameListWithSwitchResponse) GetData() []*GameListWithSwitch {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetUserLobbySwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     uint32     `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind   uint32     `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	AllParents []uint32   `protobuf:"varint,3,rep,packed,name=all_parents,json=allParents,proto3" json:"all_parents,omitempty"`
	Role       uint32     `protobuf:"varint,4,opt,name=role,proto3" json:"role,omitempty"`
	Sub        *BoolValue `protobuf:"bytes,5,opt,name=sub,proto3" json:"sub,omitempty"`
}

func (x *GetUserLobbySwitchRequest) Reset() {
	*x = GetUserLobbySwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserLobbySwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserLobbySwitchRequest) ProtoMessage() {}

func (x *GetUserLobbySwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserLobbySwitchRequest.ProtoReflect.Descriptor instead.
func (*GetUserLobbySwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{64}
}

func (x *GetUserLobbySwitchRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserLobbySwitchRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GetUserLobbySwitchRequest) GetAllParents() []uint32 {
	if x != nil {
		return x.AllParents
	}
	return nil
}

func (x *GetUserLobbySwitchRequest) GetRole() uint32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *GetUserLobbySwitchRequest) GetSub() *BoolValue {
	if x != nil {
		return x.Sub
	}
	return nil
}

type GetUserLobbySwitchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LobbySwitch []*LobbySwitch `protobuf:"bytes,1,rep,name=lobby_switch,json=lobbySwitch,proto3" json:"lobby_switch,omitempty"`
}

func (x *GetUserLobbySwitchResponse) Reset() {
	*x = GetUserLobbySwitchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserLobbySwitchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserLobbySwitchResponse) ProtoMessage() {}

func (x *GetUserLobbySwitchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserLobbySwitchResponse.ProtoReflect.Descriptor instead.
func (*GetUserLobbySwitchResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{65}
}

func (x *GetUserLobbySwitchResponse) GetLobbySwitch() []*LobbySwitch {
	if x != nil {
		return x.LobbySwitch
	}
	return nil
}

type GetGameDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     []uint32 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
	HallId []uint32 `protobuf:"varint,2,rep,packed,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetGameDetailRequest) Reset() {
	*x = GetGameDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameDetailRequest) ProtoMessage() {}

func (x *GetGameDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameDetailRequest.ProtoReflect.Descriptor instead.
func (*GetGameDetailRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{66}
}

func (x *GetGameDetailRequest) GetId() []uint32 {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *GetGameDetailRequest) GetHallId() []uint32 {
	if x != nil {
		return x.HallId
	}
	return nil
}

type GetGameDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameDetail []*GameDetail `protobuf:"bytes,1,rep,name=game_detail,json=gameDetail,proto3" json:"game_detail,omitempty"`
}

func (x *GetGameDetailResponse) Reset() {
	*x = GetGameDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameDetailResponse) ProtoMessage() {}

func (x *GetGameDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameDetailResponse.ProtoReflect.Descriptor instead.
func (*GetGameDetailResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{67}
}

func (x *GetGameDetailResponse) GetGameDetail() []*GameDetail {
	if x != nil {
		return x.GameDetail
	}
	return nil
}

type GameDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	HallId   uint32 `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Switch   bool   `protobuf:"varint,4,opt,name=switch,proto3" json:"switch,omitempty"`
}

func (x *GameDetail) Reset() {
	*x = GameDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDetail) ProtoMessage() {}

func (x *GameDetail) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDetail.ProtoReflect.Descriptor instead.
func (*GameDetail) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{68}
}

func (x *GameDetail) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GameDetail) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameDetail) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GameDetail) GetSwitch() bool {
	if x != nil {
		return x.Switch
	}
	return false
}

type CreateGameDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Id       uint32 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Enable   bool   `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *CreateGameDetailRequest) Reset() {
	*x = CreateGameDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameDetailRequest) ProtoMessage() {}

func (x *CreateGameDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameDetailRequest.ProtoReflect.Descriptor instead.
func (*CreateGameDetailRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{69}
}

func (x *CreateGameDetailRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *CreateGameDetailRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *CreateGameDetailRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateGameDetailRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type GetOnlineMemberMinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
}

func (x *GetOnlineMemberMinRequest) Reset() {
	*x = GetOnlineMemberMinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineMemberMinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineMemberMinRequest) ProtoMessage() {}

func (x *GetOnlineMemberMinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineMemberMinRequest.ProtoReflect.Descriptor instead.
func (*GetOnlineMemberMinRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{70}
}

func (x *GetOnlineMemberMinRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetOnlineMemberMinRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

type GetOnlineMemberMinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OnlineMemberSummary        []*OnlineMember               `protobuf:"bytes,1,rep,name=online_member_summary,json=onlineMemberSummary,proto3" json:"online_member_summary,omitempty"`
	OnlineMemberIngressSummary []*OnlineMemberIngressSummary `protobuf:"bytes,2,rep,name=online_member_ingress_summary,json=onlineMemberIngressSummary,proto3" json:"online_member_ingress_summary,omitempty"`
}

func (x *GetOnlineMemberMinResponse) Reset() {
	*x = GetOnlineMemberMinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineMemberMinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineMemberMinResponse) ProtoMessage() {}

func (x *GetOnlineMemberMinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineMemberMinResponse.ProtoReflect.Descriptor instead.
func (*GetOnlineMemberMinResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{71}
}

func (x *GetOnlineMemberMinResponse) GetOnlineMemberSummary() []*OnlineMember {
	if x != nil {
		return x.OnlineMemberSummary
	}
	return nil
}

func (x *GetOnlineMemberMinResponse) GetOnlineMemberIngressSummary() []*OnlineMemberIngressSummary {
	if x != nil {
		return x.OnlineMemberIngressSummary
	}
	return nil
}

type OnlineMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Datetime string `protobuf:"bytes,1,opt,name=datetime,proto3" json:"datetime,omitempty"`
	Count    uint32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *OnlineMember) Reset() {
	*x = OnlineMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnlineMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineMember) ProtoMessage() {}

func (x *OnlineMember) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineMember.ProtoReflect.Descriptor instead.
func (*OnlineMember) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{72}
}

func (x *OnlineMember) GetDatetime() string {
	if x != nil {
		return x.Datetime
	}
	return ""
}

func (x *OnlineMember) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type OnlineMemberIngressSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IngressId               uint32                     `protobuf:"varint,1,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	OnlineMemberIngressInfo []*OnlineMemberIngressInfo `protobuf:"bytes,2,rep,name=online_member_ingress_info,json=onlineMemberIngressInfo,proto3" json:"online_member_ingress_info,omitempty"`
}

func (x *OnlineMemberIngressSummary) Reset() {
	*x = OnlineMemberIngressSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnlineMemberIngressSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineMemberIngressSummary) ProtoMessage() {}

func (x *OnlineMemberIngressSummary) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineMemberIngressSummary.ProtoReflect.Descriptor instead.
func (*OnlineMemberIngressSummary) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{73}
}

func (x *OnlineMemberIngressSummary) GetIngressId() uint32 {
	if x != nil {
		return x.IngressId
	}
	return 0
}

func (x *OnlineMemberIngressSummary) GetOnlineMemberIngressInfo() []*OnlineMemberIngressInfo {
	if x != nil {
		return x.OnlineMemberIngressInfo
	}
	return nil
}

type OnlineMemberIngressInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Datetime   string `protobuf:"bytes,1,opt,name=datetime,proto3" json:"datetime,omitempty"`
	Count      uint32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Percentage uint32 `protobuf:"varint,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *OnlineMemberIngressInfo) Reset() {
	*x = OnlineMemberIngressInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnlineMemberIngressInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineMemberIngressInfo) ProtoMessage() {}

func (x *OnlineMemberIngressInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineMemberIngressInfo.ProtoReflect.Descriptor instead.
func (*OnlineMemberIngressInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{74}
}

func (x *OnlineMemberIngressInfo) GetDatetime() string {
	if x != nil {
		return x.Datetime
	}
	return ""
}

func (x *OnlineMemberIngressInfo) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *OnlineMemberIngressInfo) GetPercentage() uint32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type CoverCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameType string `protobuf:"bytes,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	Lobby    uint32 `protobuf:"varint,2,opt,name=lobby,proto3" json:"lobby,omitempty"`
}

func (x *CoverCondition) Reset() {
	*x = CoverCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoverCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoverCondition) ProtoMessage() {}

func (x *CoverCondition) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoverCondition.ProtoReflect.Descriptor instead.
func (*CoverCondition) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{75}
}

func (x *CoverCondition) GetGameType() string {
	if x != nil {
		return x.GameType
	}
	return ""
}

func (x *CoverCondition) GetLobby() uint32 {
	if x != nil {
		return x.Lobby
	}
	return 0
}

type DeleteAPISynchronizeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiFacade      string          `protobuf:"bytes,1,opt,name=api_facade,json=apiFacade,proto3" json:"api_facade,omitempty"`
	FunctionName   string          `protobuf:"bytes,2,opt,name=function_name,json=functionName,proto3" json:"function_name,omitempty"`
	CoverCondition *CoverCondition `protobuf:"bytes,3,opt,name=cover_condition,json=coverCondition,proto3" json:"cover_condition,omitempty"`
}

func (x *DeleteAPISynchronizeRequest) Reset() {
	*x = DeleteAPISynchronizeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAPISynchronizeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAPISynchronizeRequest) ProtoMessage() {}

func (x *DeleteAPISynchronizeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAPISynchronizeRequest.ProtoReflect.Descriptor instead.
func (*DeleteAPISynchronizeRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{76}
}

func (x *DeleteAPISynchronizeRequest) GetApiFacade() string {
	if x != nil {
		return x.ApiFacade
	}
	return ""
}

func (x *DeleteAPISynchronizeRequest) GetFunctionName() string {
	if x != nil {
		return x.FunctionName
	}
	return ""
}

func (x *DeleteAPISynchronizeRequest) GetCoverCondition() *CoverCondition {
	if x != nil {
		return x.CoverCondition
	}
	return nil
}

type ModifyGameTypeSwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameTypeInfo []*GameTypeInfo `protobuf:"bytes,1,rep,name=game_type_info,json=gameTypeInfo,proto3" json:"game_type_info,omitempty"`
}

func (x *ModifyGameTypeSwitchRequest) Reset() {
	*x = ModifyGameTypeSwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyGameTypeSwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyGameTypeSwitchRequest) ProtoMessage() {}

func (x *ModifyGameTypeSwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyGameTypeSwitchRequest.ProtoReflect.Descriptor instead.
func (*ModifyGameTypeSwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{77}
}

func (x *ModifyGameTypeSwitchRequest) GetGameTypeInfo() []*GameTypeInfo {
	if x != nil {
		return x.GameTypeInfo
	}
	return nil
}

type GameTypeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind       uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId         string `protobuf:"bytes,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PlatformEnable uint32 `protobuf:"varint,3,opt,name=platform_enable,json=platformEnable,proto3" json:"platform_enable,omitempty"`
	PcEnable       uint32 `protobuf:"varint,4,opt,name=pc_enable,json=pcEnable,proto3" json:"pc_enable,omitempty"`
	MobileEnable   uint32 `protobuf:"varint,5,opt,name=mobile_enable,json=mobileEnable,proto3" json:"mobile_enable,omitempty"`
	Label          uint32 `protobuf:"varint,6,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *GameTypeInfo) Reset() {
	*x = GameTypeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameTypeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameTypeInfo) ProtoMessage() {}

func (x *GameTypeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameTypeInfo.ProtoReflect.Descriptor instead.
func (*GameTypeInfo) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{78}
}

func (x *GameTypeInfo) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameTypeInfo) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *GameTypeInfo) GetPlatformEnable() uint32 {
	if x != nil {
		return x.PlatformEnable
	}
	return 0
}

func (x *GameTypeInfo) GetPcEnable() uint32 {
	if x != nil {
		return x.PcEnable
	}
	return 0
}

func (x *GameTypeInfo) GetMobileEnable() uint32 {
	if x != nil {
		return x.MobileEnable
	}
	return 0
}

func (x *GameTypeInfo) GetLabel() uint32 {
	if x != nil {
		return x.Label
	}
	return 0
}

type DeleteGameTypeSynchronizeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId       []uint32 `protobuf:"varint,1,rep,packed,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameType     []string `protobuf:"bytes,2,rep,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	SyncFunction string   `protobuf:"bytes,3,opt,name=sync_function,json=syncFunction,proto3" json:"sync_function,omitempty"`
}

func (x *DeleteGameTypeSynchronizeRequest) Reset() {
	*x = DeleteGameTypeSynchronizeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGameTypeSynchronizeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGameTypeSynchronizeRequest) ProtoMessage() {}

func (x *DeleteGameTypeSynchronizeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGameTypeSynchronizeRequest.ProtoReflect.Descriptor instead.
func (*DeleteGameTypeSynchronizeRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{79}
}

func (x *DeleteGameTypeSynchronizeRequest) GetHallId() []uint32 {
	if x != nil {
		return x.HallId
	}
	return nil
}

func (x *DeleteGameTypeSynchronizeRequest) GetGameType() []string {
	if x != nil {
		return x.GameType
	}
	return nil
}

func (x *DeleteGameTypeSynchronizeRequest) GetSyncFunction() string {
	if x != nil {
		return x.SyncFunction
	}
	return ""
}

type ManageGameHallSwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GameHallSwitch `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ManageGameHallSwitchRequest) Reset() {
	*x = ManageGameHallSwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManageGameHallSwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManageGameHallSwitchRequest) ProtoMessage() {}

func (x *ManageGameHallSwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManageGameHallSwitchRequest.ProtoReflect.Descriptor instead.
func (*ManageGameHallSwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{80}
}

func (x *ManageGameHallSwitchRequest) GetData() []*GameHallSwitch {
	if x != nil {
		return x.Data
	}
	return nil
}

type GameHallSwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind       uint32 `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	HallId         uint32 `protobuf:"varint,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameId         string `protobuf:"bytes,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PlatformEnable bool   `protobuf:"varint,4,opt,name=platform_enable,json=platformEnable,proto3" json:"platform_enable,omitempty"`
	EntranceEnable bool   `protobuf:"varint,5,opt,name=entrance_enable,json=entranceEnable,proto3" json:"entrance_enable,omitempty"`
	NotSupport     bool   `protobuf:"varint,6,opt,name=not_support,json=notSupport,proto3" json:"not_support,omitempty"`
}

func (x *GameHallSwitch) Reset() {
	*x = GameHallSwitch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameHallSwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameHallSwitch) ProtoMessage() {}

func (x *GameHallSwitch) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameHallSwitch.ProtoReflect.Descriptor instead.
func (*GameHallSwitch) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{81}
}

func (x *GameHallSwitch) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameHallSwitch) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GameHallSwitch) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *GameHallSwitch) GetPlatformEnable() bool {
	if x != nil {
		return x.PlatformEnable
	}
	return false
}

func (x *GameHallSwitch) GetEntranceEnable() bool {
	if x != nil {
		return x.EntranceEnable
	}
	return false
}

func (x *GameHallSwitch) GetNotSupport() bool {
	if x != nil {
		return x.NotSupport
	}
	return false
}

type UpdateGameInfoHallListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind  uint32               `protobuf:"varint,1,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId    uint32               `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	AllowList *RepeatedUint32Value `protobuf:"bytes,3,opt,name=allow_list,json=allowList,proto3" json:"allow_list,omitempty"`
	BlockList *RepeatedUint32Value `protobuf:"bytes,4,opt,name=block_list,json=blockList,proto3" json:"block_list,omitempty"`
}

func (x *UpdateGameInfoHallListRequest) Reset() {
	*x = UpdateGameInfoHallListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGameInfoHallListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGameInfoHallListRequest) ProtoMessage() {}

func (x *UpdateGameInfoHallListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGameInfoHallListRequest.ProtoReflect.Descriptor instead.
func (*UpdateGameInfoHallListRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{82}
}

func (x *UpdateGameInfoHallListRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *UpdateGameInfoHallListRequest) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *UpdateGameInfoHallListRequest) GetAllowList() *RepeatedUint32Value {
	if x != nil {
		return x.AllowList
	}
	return nil
}

func (x *UpdateGameInfoHallListRequest) GetBlockList() *RepeatedUint32Value {
	if x != nil {
		return x.BlockList
	}
	return nil
}

type DeleteUserLobbyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *DeleteUserLobbyRequest) Reset() {
	*x = DeleteUserLobbyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserLobbyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserLobbyRequest) ProtoMessage() {}

func (x *DeleteUserLobbyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserLobbyRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserLobbyRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{83}
}

func (x *DeleteUserLobbyRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetOnlineMemberHourRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
}

func (x *GetOnlineMemberHourRequest) Reset() {
	*x = GetOnlineMemberHourRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineMemberHourRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineMemberHourRequest) ProtoMessage() {}

func (x *GetOnlineMemberHourRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineMemberHourRequest.ProtoReflect.Descriptor instead.
func (*GetOnlineMemberHourRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{84}
}

func (x *GetOnlineMemberHourRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetOnlineMemberHourRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

type GetOnlineMemberHourResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OnlineMemberSummary        []*OnlineMember               `protobuf:"bytes,1,rep,name=online_member_summary,json=onlineMemberSummary,proto3" json:"online_member_summary,omitempty"`
	OnlineMemberIngressSummary []*OnlineMemberIngressSummary `protobuf:"bytes,2,rep,name=online_member_ingress_summary,json=onlineMemberIngressSummary,proto3" json:"online_member_ingress_summary,omitempty"`
}

func (x *GetOnlineMemberHourResponse) Reset() {
	*x = GetOnlineMemberHourResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineMemberHourResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineMemberHourResponse) ProtoMessage() {}

func (x *GetOnlineMemberHourResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineMemberHourResponse.ProtoReflect.Descriptor instead.
func (*GetOnlineMemberHourResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{85}
}

func (x *GetOnlineMemberHourResponse) GetOnlineMemberSummary() []*OnlineMember {
	if x != nil {
		return x.OnlineMemberSummary
	}
	return nil
}

func (x *GetOnlineMemberHourResponse) GetOnlineMemberIngressSummary() []*OnlineMemberIngressSummary {
	if x != nil {
		return x.OnlineMemberIngressSummary
	}
	return nil
}

type UpdateHallLobbySwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Enable   bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UpdateHallLobbySwitchRequest) Reset() {
	*x = UpdateHallLobbySwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateHallLobbySwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateHallLobbySwitchRequest) ProtoMessage() {}

func (x *UpdateHallLobbySwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateHallLobbySwitchRequest.ProtoReflect.Descriptor instead.
func (*UpdateHallLobbySwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{86}
}

func (x *UpdateHallLobbySwitchRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *UpdateHallLobbySwitchRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *UpdateHallLobbySwitchRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type DeleteHallLowerAccountLobbySwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *DeleteHallLowerAccountLobbySwitchRequest) Reset() {
	*x = DeleteHallLowerAccountLobbySwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteHallLowerAccountLobbySwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHallLowerAccountLobbySwitchRequest) ProtoMessage() {}

func (x *DeleteHallLowerAccountLobbySwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHallLowerAccountLobbySwitchRequest.ProtoReflect.Descriptor instead.
func (*DeleteHallLowerAccountLobbySwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{87}
}

func (x *DeleteHallLowerAccountLobbySwitchRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *DeleteHallLowerAccountLobbySwitchRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type CreateUserLobbySwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserLobbySwitch []*UserLobbySwitch `protobuf:"bytes,1,rep,name=user_lobby_switch,json=userLobbySwitch,proto3" json:"user_lobby_switch,omitempty"`
}

func (x *CreateUserLobbySwitchRequest) Reset() {
	*x = CreateUserLobbySwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserLobbySwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserLobbySwitchRequest) ProtoMessage() {}

func (x *CreateUserLobbySwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserLobbySwitchRequest.ProtoReflect.Descriptor instead.
func (*CreateUserLobbySwitchRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{88}
}

func (x *CreateUserLobbySwitchRequest) GetUserLobbySwitch() []*UserLobbySwitch {
	if x != nil {
		return x.UserLobbySwitch
	}
	return nil
}

type UserLobbySwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId   uint32 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameKind uint32 `protobuf:"varint,3,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Enable   bool   `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UserLobbySwitch) Reset() {
	*x = UserLobbySwitch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserLobbySwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLobbySwitch) ProtoMessage() {}

func (x *UserLobbySwitch) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLobbySwitch.ProtoReflect.Descriptor instead.
func (*UserLobbySwitch) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{89}
}

func (x *UserLobbySwitch) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *UserLobbySwitch) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserLobbySwitch) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *UserLobbySwitch) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type GetOnlineMemberDayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *GetOnlineMemberDayRequest) Reset() {
	*x = GetOnlineMemberDayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineMemberDayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineMemberDayRequest) ProtoMessage() {}

func (x *GetOnlineMemberDayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineMemberDayRequest.ProtoReflect.Descriptor instead.
func (*GetOnlineMemberDayRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{90}
}

func (x *GetOnlineMemberDayRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetOnlineMemberDayRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetOnlineMemberDayRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type GetOnlineMemberDayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OnlineMemberSummary        []*OnlineMember               `protobuf:"bytes,1,rep,name=online_member_summary,json=onlineMemberSummary,proto3" json:"online_member_summary,omitempty"`
	OnlineMemberIngressSummary []*OnlineMemberIngressSummary `protobuf:"bytes,2,rep,name=online_member_ingress_summary,json=onlineMemberIngressSummary,proto3" json:"online_member_ingress_summary,omitempty"`
}

func (x *GetOnlineMemberDayResponse) Reset() {
	*x = GetOnlineMemberDayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineMemberDayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineMemberDayResponse) ProtoMessage() {}

func (x *GetOnlineMemberDayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineMemberDayResponse.ProtoReflect.Descriptor instead.
func (*GetOnlineMemberDayResponse) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{91}
}

func (x *GetOnlineMemberDayResponse) GetOnlineMemberSummary() []*OnlineMember {
	if x != nil {
		return x.OnlineMemberSummary
	}
	return nil
}

func (x *GetOnlineMemberDayResponse) GetOnlineMemberIngressSummary() []*OnlineMemberIngressSummary {
	if x != nil {
		return x.OnlineMemberIngressSummary
	}
	return nil
}

type DeleteHallLobbyCloseTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *DeleteHallLobbyCloseTimeRequest) Reset() {
	*x = DeleteHallLobbyCloseTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteHallLobbyCloseTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHallLobbyCloseTimeRequest) ProtoMessage() {}

func (x *DeleteHallLobbyCloseTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHallLobbyCloseTimeRequest.ProtoReflect.Descriptor instead.
func (*DeleteHallLobbyCloseTimeRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{92}
}

func (x *DeleteHallLobbyCloseTimeRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *DeleteHallLobbyCloseTimeRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

type SetHallLobbyCloseTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *SetHallLobbyCloseTimeRequest) Reset() {
	*x = SetHallLobbyCloseTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_game_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetHallLobbyCloseTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetHallLobbyCloseTimeRequest) ProtoMessage() {}

func (x *SetHallLobbyCloseTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_game_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetHallLobbyCloseTimeRequest.ProtoReflect.Descriptor instead.
func (*SetHallLobbyCloseTimeRequest) Descriptor() ([]byte, []int) {
	return file_game_proto_rawDescGZIP(), []int{93}
}

func (x *SetHallLobbyCloseTimeRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetHallLobbyCloseTimeRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

var File_game_proto protoreflect.FileDescriptor

var file_game_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61,
	0x6d, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x2c, 0x0a, 0x0d, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64,
	0x22, 0x23, 0x0a, 0x0b, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x21, 0x0a, 0x09, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x2b, 0x0a,
	0x13, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x15, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x12, 0x2a, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x22, 0x30, 0x0a, 0x0a, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x61,
	0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6d, 0x6f, 0x4c,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67,
	0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x4c, 0x0a, 0x10, 0x44, 0x65,
	0x6d, 0x6f, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x0e, 0x64, 0x65, 0x6d, 0x6f, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65,
	0x6d, 0x6f, 0x4c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x64, 0x65, 0x6d, 0x6f,
	0x4c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x39, 0x0a, 0x0c, 0x44, 0x65, 0x6d, 0x6f,
	0x4c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x22, 0x40, 0x0a, 0x16, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a,
	0x05, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x05,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x49, 0x0a, 0x11, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67,
	0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x22, 0x2c, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0xf5,
	0x01, 0x0a, 0x10, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x61,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x12, 0x21, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x32, 0x0a, 0x0b, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x65, 0x78,
	0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x78, 0x69, 0x74,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x65,
	0x78, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x25, 0x0a, 0x11, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x4c,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x7f, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x2a, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5b,
	0x0a, 0x0b, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x5f, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x0b,
	0x6c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0xa9, 0x01, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x79,
	0x48, 0x61, 0x6c, 0x6c, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x13, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x5f,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x56, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x49,
	0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x6c, 0x6f, 0x62,
	0x62, 0x79, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x52, 0x0b, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22,
	0x31, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x22, 0x68, 0x0a, 0x0c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x65, 0x6e, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x65, 0x6e, 0x75, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74,
	0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x22, 0x3d, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x31, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x58,
	0x0a, 0x0c, 0x4d, 0x65, 0x6e, 0x75, 0x53, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x07, 0x6d, 0x65, 0x6e, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6d, 0x65, 0x6e, 0x75, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x6e, 0x75, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x26, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x65, 0x6e, 0x75, 0x53, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x61,
	0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x22, 0x9c, 0x03, 0x0a, 0x08, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x63, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70, 0x63, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6d,
	0x6f, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x64, 0x65, 0x6d, 0x6f, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x63,
	0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x22, 0x4c, 0x0a, 0x0c, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x72, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0d, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x59, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79,
	0x47, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22,
	0x78, 0x0a, 0x17, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67,
	0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x63, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70, 0x63, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x57, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x5f, 0x0a, 0x0f, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c,
	0x61, 0x6e, 0x67, 0x22, 0x42, 0x0a, 0x10, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x08, 0x62, 0x75, 0x6c, 0x6c, 0x65,
	0x74, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x62,
	0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x22, 0x3c, 0x0a, 0x0c, 0x42, 0x75, 0x6c, 0x6c, 0x65,
	0x74, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x48, 0x0a, 0x0c, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22,
	0x50, 0x0a, 0x14, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x0e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x69, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x0c, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x3a, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x4b, 0x69, 0x6e,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x22, 0x4b, 0x0a,
	0x14, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x67, 0x0a, 0x16, 0x41, 0x64,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x40,
	0x0a, 0x08, 0x4d, 0x65, 0x6e, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x65,
	0x6e, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x65, 0x6e,
	0x75, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6e, 0x75, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6e, 0x75, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x42, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x6d, 0x65, 0x6e, 0x75, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x4d, 0x65, 0x6e, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6d, 0x65, 0x6e, 0x75,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x4e, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x4b, 0x69, 0x6e, 0x64, 0x22, 0x2b, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x52, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0d,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76,
	0x6f, 0x72, 0x69, 0x74, 0x65, 0x22, 0x6a, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49,
	0x64, 0x22, 0x42, 0x0a, 0x0f, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x2e, 0x0a, 0x08, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x36, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x37, 0x0a,
	0x18, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x48, 0x0a, 0x19, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x69,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x5f, 0x0a, 0x11, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x22, 0x5d, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x4b, 0x69, 0x6e, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x22, 0x38, 0x0a, 0x13, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d,
	0x65, 0x55, 0x52, 0x4c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2b, 0x0a, 0x07, 0x47, 0x61,
	0x6d, 0x65, 0x55, 0x52, 0x4c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x87, 0x03, 0x0a, 0x14, 0x4c, 0x6f, 0x62, 0x62,
	0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x62, 0x62, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x62, 0x62, 0x54, 0x69, 0x70, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x5f, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x57,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x27,
	0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x37, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x1f, 0x0a, 0x02, 0x6a, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x02, 0x6a,
	0x70, 0x12, 0x2b, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x19,
	0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x62, 0x65, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6c, 0x61, 0x73, 0x74, 0x42, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x22, 0x83, 0x01, 0x0a, 0x15, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c,
	0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x76, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x72, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x72, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x22, 0xcf, 0x04, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x47,
	0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x44, 0x0a, 0x14,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x65,
	0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x29,
	0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x0f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x09, 0x70, 0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x70, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x34, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x0b, 0x64, 0x65, 0x6d, 0x6f, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x64,
	0x65, 0x6d, 0x6f, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09,
	0x69, 0x73, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x22, 0xe3, 0x04, 0x0a, 0x12, 0x47, 0x61,
	0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x70, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x65, 0x6d, 0x6f, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x6d, 0x6f, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4a, 0x61, 0x63, 0x6b, 0x70, 0x6f, 0x74, 0x12, 0x31, 0x0a,
	0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x16, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x16, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x73, 0x22,
	0x4d, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa9,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x50, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x03, 0x73, 0x75, 0x62, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x73, 0x75, 0x62, 0x22, 0x52, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x6c, 0x6f, 0x62, 0x62,
	0x79, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x0b, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0x3f,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22,
	0x4a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x0b, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x0a, 0x67, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x6a, 0x0a, 0x0a, 0x47,
	0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0x77, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x53, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x15, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x13, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x63, 0x0a, 0x1d,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x1a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x22, 0x40, 0x0a, 0x0c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x97, 0x01, 0x0a, 0x1a, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x5a, 0x0a, 0x1a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x17, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6b, 0x0a,
	0x17, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x43, 0x0a, 0x0e, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x62,
	0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x22,
	0xa0, 0x01, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x50, 0x49, 0x53, 0x79, 0x6e,
	0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x5f, 0x66, 0x61, 0x63, 0x61, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x69, 0x46, 0x61, 0x63, 0x61, 0x64, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x57, 0x0a, 0x1b, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x47, 0x61, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x38, 0x0a, 0x0e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x67,
	0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xc5, 0x01, 0x0a, 0x0c,
	0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65,
	0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x70, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x22, 0x7d, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x79, 0x6e, 0x63, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x1b, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x47, 0x61, 0x6d, 0x65,
	0x48, 0x61, 0x6c, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd2, 0x01, 0x0a, 0x0e,
	0x47, 0x61, 0x6d, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61,
	0x6c, 0x6c, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x22, 0xc9, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x38, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x31, 0x0a, 0x16,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x54, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xca, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x15, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x13, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x63, 0x0a,
	0x1d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x1a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x22, 0x6c, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x60, 0x0a, 0x28, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x6f,
	0x77, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x22, 0x61, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x41, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x62, 0x62, 0x79,
	0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0x78, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62,
	0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67,
	0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22,
	0x6e, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x44, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0xc9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x44, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46,
	0x0a, 0x15, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x13, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x63, 0x0a, 0x1d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x1a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x57, 0x0a, 0x1f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x4b, 0x69, 0x6e, 0x64, 0x22, 0x54, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x4c,
	0x6f, 0x62, 0x62, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x32, 0xaf, 0x18, 0x0a, 0x04, 0x47,
	0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x05, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x12, 0x12, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47,
	0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x44, 0x65, 0x6d, 0x6f, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x15,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x6d, 0x6f, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x6d,
	0x6f, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a,
	0x0a, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x17, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c,
	0x0a, 0x09, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x16, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79,
	0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1b,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x79, 0x48, 0x61, 0x6c,
	0x6c, 0x49, 0x44, 0x12, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x79, 0x48, 0x61, 0x6c, 0x6c, 0x49,
	0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x79,
	0x48, 0x61, 0x6c, 0x6c, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x53, 0x6f, 0x72,
	0x74, 0x12, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75,
	0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x27, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62,
	0x79, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x42,
	0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74,
	0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x47, 0x61,
	0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x47, 0x61,
	0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x4b, 0x69,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x41, 0x64,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x1c, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x6e, 0x75, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x1c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x39, 0x0a, 0x08, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x1e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x55,
	0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x52, 0x4c, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0d, 0x4c,
	0x6f, 0x62, 0x62, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x22,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1f, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62,
	0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62,
	0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x48, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x10, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x12, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4d, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x14, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x50, 0x49, 0x53, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e,
	0x69, 0x7a, 0x65, 0x12, 0x21, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x50, 0x49, 0x53, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x14, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x12, 0x21, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x19, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x79, 0x6e,
	0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x79,
	0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x47,
	0x61, 0x6d, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x21, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x48,
	0x61, 0x6c, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47,
	0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x12, 0x1c, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f,
	0x62, 0x62, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5a, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x6f, 0x75,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x48,
	0x6f, 0x75, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x15, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x12, 0x22, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a,
	0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x77, 0x65, 0x72,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x2e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x12, 0x22, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x79, 0x12,
	0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x56, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x15, 0x53, 0x65,
	0x74, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x22, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x48, 0x61,
	0x6c, 0x6c, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0c, 0x5a, 0x0a,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_game_proto_rawDescOnce sync.Once
	file_game_proto_rawDescData = file_game_proto_rawDesc
)

func file_game_proto_rawDescGZIP() []byte {
	file_game_proto_rawDescOnce.Do(func() {
		file_game_proto_rawDescData = protoimpl.X.CompressGZIP(file_game_proto_rawDescData)
	})
	return file_game_proto_rawDescData
}

var file_game_proto_msgTypes = make([]protoimpl.MessageInfo, 94)
var file_game_proto_goTypes = []interface{}{
	(*EmptyRequest)(nil),                             // 0: game.EmptyRequest
	(*LobbyResponse)(nil),                            // 1: game.LobbyResponse
	(*Uint32Value)(nil),                              // 2: game.Uint32Value
	(*StringValue)(nil),                              // 3: game.StringValue
	(*BoolValue)(nil),                                // 4: game.BoolValue
	(*RepeatedUint32Value)(nil),                      // 5: game.RepeatedUint32Value
	(*EnableGameListRequest)(nil),                    // 6: game.EnableGameListRequest
	(*EnableGame)(nil),                               // 7: game.EnableGame
	(*DemoLinkRequest)(nil),                          // 8: game.DemoLinkRequest
	(*DemoLinkResponse)(nil),                         // 9: game.DemoLinkResponse
	(*DemoLinkInfo)(nil),                             // 10: game.DemoLinkInfo
	(*EnableGameListResponse)(nil),                   // 11: game.EnableGameListResponse
	(*GameDomainRequest)(nil),                        // 12: game.GameDomainRequest
	(*GameDomainResponse)(nil),                       // 13: game.GameDomainResponse
	(*LobbyLinkRequest)(nil),                         // 14: game.LobbyLinkRequest
	(*LobbyLinkResponse)(nil),                        // 15: game.LobbyLinkResponse
	(*GetLobbySwitchRequest)(nil),                    // 16: game.GetLobbySwitchRequest
	(*LobbySwitch)(nil),                              // 17: game.LobbySwitch
	(*GetLobbySwitchResponse)(nil),                   // 18: game.GetLobbySwitchResponse
	(*GetLobbySwitchByHallIDRequest)(nil),            // 19: game.GetLobbySwitchByHallIDRequest
	(*GetLobbySwitchByHallIDResponse)(nil),           // 20: game.GetLobbySwitchByHallIDResponse
	(*GetCategoryRequest)(nil),                       // 21: game.GetCategoryRequest
	(*CategoryInfo)(nil),                             // 22: game.CategoryInfo
	(*GetCategoryResponse)(nil),                      // 23: game.GetCategoryResponse
	(*GetMenuSortRequest)(nil),                       // 24: game.GetMenuSortRequest
	(*MenuSortInfo)(nil),                             // 25: game.MenuSortInfo
	(*GetMenuSortResponse)(nil),                      // 26: game.GetMenuSortResponse
	(*GetGameInfoRequest)(nil),                       // 27: game.GetGameInfoRequest
	(*GameInfo)(nil),                                 // 28: game.GameInfo
	(*ExternalList)(nil),                             // 29: game.ExternalList
	(*GetGameInfoResponse)(nil),                      // 30: game.GetGameInfoResponse
	(*GetLobbyGameEntranceSwitchRequest)(nil),        // 31: game.GetLobbyGameEntranceSwitchRequest
	(*LobbyGameEntranceSwitch)(nil),                  // 32: game.LobbyGameEntranceSwitch
	(*GetLobbyGameEntranceSwitchResponse)(nil),       // 33: game.GetLobbyGameEntranceSwitchResponse
	(*BulletinRequest)(nil),                          // 34: game.BulletinRequest
	(*BulletinResponse)(nil),                         // 35: game.BulletinResponse
	(*BulletinData)(nil),                             // 36: game.BulletinData
	(*GameKindList)(nil),                             // 37: game.GameKindList
	(*GameKindListResponse)(nil),                     // 38: game.GameKindListResponse
	(*GameIconKindInfo)(nil),                         // 39: game.GameIconKindInfo
	(*GameIconKindResponse)(nil),                     // 40: game.GameIconKindResponse
	(*AddUserFavoriteRequest)(nil),                   // 41: game.AddUserFavoriteRequest
	(*EmptyResponse)(nil),                            // 42: game.EmptyResponse
	(*GetMenuNameRequest)(nil),                       // 43: game.GetMenuNameRequest
	(*MenuInfo)(nil),                                 // 44: game.MenuInfo
	(*GetMenuNameResponse)(nil),                      // 45: game.GetMenuNameResponse
	(*GetUserFavoriteRequest)(nil),                   // 46: game.GetUserFavoriteRequest
	(*UserFavorite)(nil),                             // 47: game.UserFavorite
	(*GetUserFavoriteResponse)(nil),                  // 48: game.GetUserFavoriteResponse
	(*DeleteUserFavoriteRequest)(nil),                // 49: game.DeleteUserFavoriteRequest
	(*GameListRequest)(nil),                          // 50: game.GameListRequest
	(*GameList)(nil),                                 // 51: game.GameList
	(*GameListResponse)(nil),                         // 52: game.GameListResponse
	(*GameMaintainLabelRequest)(nil),                 // 53: game.GameMaintainLabelRequest
	(*GameMaintainLabelResponse)(nil),                // 54: game.GameMaintainLabelResponse
	(*GameMaintainLabel)(nil),                        // 55: game.GameMaintainLabel
	(*GameURLListRequest)(nil),                       // 56: game.GameURLListRequest
	(*GameURLListResponse)(nil),                      // 57: game.GameURLListResponse
	(*GameURL)(nil),                                  // 58: game.GameURL
	(*LobbyCategoryRequest)(nil),                     // 59: game.LobbyCategoryRequest
	(*LobbyCategoryResponse)(nil),                    // 60: game.LobbyCategoryResponse
	(*GetGameListWithSwitchRequest)(nil),             // 61: game.GetGameListWithSwitchRequest
	(*GameListWithSwitch)(nil),                       // 62: game.GameListWithSwitch
	(*GetGameListWithSwitchResponse)(nil),            // 63: game.GetGameListWithSwitchResponse
	(*GetUserLobbySwitchRequest)(nil),                // 64: game.GetUserLobbySwitchRequest
	(*GetUserLobbySwitchResponse)(nil),               // 65: game.GetUserLobbySwitchResponse
	(*GetGameDetailRequest)(nil),                     // 66: game.GetGameDetailRequest
	(*GetGameDetailResponse)(nil),                    // 67: game.GetGameDetailResponse
	(*GameDetail)(nil),                               // 68: game.GameDetail
	(*CreateGameDetailRequest)(nil),                  // 69: game.CreateGameDetailRequest
	(*GetOnlineMemberMinRequest)(nil),                // 70: game.GetOnlineMemberMinRequest
	(*GetOnlineMemberMinResponse)(nil),               // 71: game.GetOnlineMemberMinResponse
	(*OnlineMember)(nil),                             // 72: game.OnlineMember
	(*OnlineMemberIngressSummary)(nil),               // 73: game.OnlineMemberIngressSummary
	(*OnlineMemberIngressInfo)(nil),                  // 74: game.OnlineMemberIngressInfo
	(*CoverCondition)(nil),                           // 75: game.CoverCondition
	(*DeleteAPISynchronizeRequest)(nil),              // 76: game.DeleteAPISynchronizeRequest
	(*ModifyGameTypeSwitchRequest)(nil),              // 77: game.ModifyGameTypeSwitchRequest
	(*GameTypeInfo)(nil),                             // 78: game.GameTypeInfo
	(*DeleteGameTypeSynchronizeRequest)(nil),         // 79: game.DeleteGameTypeSynchronizeRequest
	(*ManageGameHallSwitchRequest)(nil),              // 80: game.ManageGameHallSwitchRequest
	(*GameHallSwitch)(nil),                           // 81: game.GameHallSwitch
	(*UpdateGameInfoHallListRequest)(nil),            // 82: game.UpdateGameInfoHallListRequest
	(*DeleteUserLobbyRequest)(nil),                   // 83: game.DeleteUserLobbyRequest
	(*GetOnlineMemberHourRequest)(nil),               // 84: game.GetOnlineMemberHourRequest
	(*GetOnlineMemberHourResponse)(nil),              // 85: game.GetOnlineMemberHourResponse
	(*UpdateHallLobbySwitchRequest)(nil),             // 86: game.UpdateHallLobbySwitchRequest
	(*DeleteHallLowerAccountLobbySwitchRequest)(nil), // 87: game.DeleteHallLowerAccountLobbySwitchRequest
	(*CreateUserLobbySwitchRequest)(nil),             // 88: game.CreateUserLobbySwitchRequest
	(*UserLobbySwitch)(nil),                          // 89: game.UserLobbySwitch
	(*GetOnlineMemberDayRequest)(nil),                // 90: game.GetOnlineMemberDayRequest
	(*GetOnlineMemberDayResponse)(nil),               // 91: game.GetOnlineMemberDayResponse
	(*DeleteHallLobbyCloseTimeRequest)(nil),          // 92: game.DeleteHallLobbyCloseTimeRequest
	(*SetHallLobbyCloseTimeRequest)(nil),             // 93: game.SetHallLobbyCloseTimeRequest
}
var file_game_proto_depIdxs = []int32{
	2,  // 0: game.EnableGameListRequest.hall_id:type_name -> game.Uint32Value
	3,  // 1: game.EnableGameListRequest.lang:type_name -> game.StringValue
	2,  // 2: game.DemoLinkRequest.hall_id:type_name -> game.Uint32Value
	3,  // 3: game.DemoLinkRequest.lang:type_name -> game.StringValue
	10, // 4: game.DemoLinkResponse.demo_link_info:type_name -> game.DemoLinkInfo
	7,  // 5: game.EnableGameListResponse.games:type_name -> game.EnableGame
	3,  // 6: game.LobbyLinkRequest.lang:type_name -> game.StringValue
	3,  // 7: game.LobbyLinkRequest.ip:type_name -> game.StringValue
	2,  // 8: game.LobbyLinkRequest.exit_option:type_name -> game.Uint32Value
	3,  // 9: game.LobbyLinkRequest.exit_url:type_name -> game.StringValue
	2,  // 10: game.GetLobbySwitchRequest.user_id:type_name -> game.Uint32Value
	17, // 11: game.GetLobbySwitchResponse.lobby_switch:type_name -> game.LobbySwitch
	4,  // 12: game.GetLobbySwitchByHallIDRequest.lobby_switch_enable:type_name -> game.BoolValue
	17, // 13: game.GetLobbySwitchByHallIDResponse.lobby_switch:type_name -> game.LobbySwitch
	22, // 14: game.GetCategoryResponse.data:type_name -> game.CategoryInfo
	25, // 15: game.GetMenuSortResponse.data:type_name -> game.MenuSortInfo
	28, // 16: game.GetGameInfoResponse.data:type_name -> game.GameInfo
	29, // 17: game.GetGameInfoResponse.external_list:type_name -> game.ExternalList
	32, // 18: game.GetLobbyGameEntranceSwitchResponse.data:type_name -> game.LobbyGameEntranceSwitch
	36, // 19: game.BulletinResponse.bulletin:type_name -> game.BulletinData
	37, // 20: game.GameKindListResponse.game_kind_list:type_name -> game.GameKindList
	39, // 21: game.GameIconKindResponse.icon_kind:type_name -> game.GameIconKindInfo
	44, // 22: game.GetMenuNameResponse.menu_list:type_name -> game.MenuInfo
	47, // 23: game.GetUserFavoriteResponse.user_favorite:type_name -> game.UserFavorite
	51, // 24: game.GameListResponse.data:type_name -> game.GameList
	55, // 25: game.GameMaintainLabelResponse.data:type_name -> game.GameMaintainLabel
	2,  // 26: game.GameURLListRequest.hall_id:type_name -> game.Uint32Value
	58, // 27: game.GameURLListResponse.data:type_name -> game.GameURL
	4,  // 28: game.LobbyCategoryRequest.enable:type_name -> game.BoolValue
	4,  // 29: game.LobbyCategoryRequest.report:type_name -> game.BoolValue
	4,  // 30: game.LobbyCategoryRequest.commissionable:type_name -> game.BoolValue
	4,  // 31: game.LobbyCategoryRequest.jp:type_name -> game.BoolValue
	4,  // 32: game.LobbyCategoryRequest.external:type_name -> game.BoolValue
	3,  // 33: game.GetGameListWithSwitchRequest.commissionable_group:type_name -> game.StringValue
	2,  // 34: game.GetGameListWithSwitchRequest.device:type_name -> game.Uint32Value
	4,  // 35: game.GetGameListWithSwitchRequest.platform_enable:type_name -> game.BoolValue
	4,  // 36: game.GetGameListWithSwitchRequest.pc_enable:type_name -> game.BoolValue
	4,  // 37: game.GetGameListWithSwitchRequest.mobile_enable:type_name -> game.BoolValue
	4,  // 38: game.GetGameListWithSwitchRequest.demo_enable:type_name -> game.BoolValue
	4,  // 39: game.GetGameListWithSwitchRequest.is_jackpot:type_name -> game.BoolValue
	62, // 40: game.GetGameListWithSwitchResponse.data:type_name -> game.GameListWithSwitch
	4,  // 41: game.GetUserLobbySwitchRequest.sub:type_name -> game.BoolValue
	17, // 42: game.GetUserLobbySwitchResponse.lobby_switch:type_name -> game.LobbySwitch
	68, // 43: game.GetGameDetailResponse.game_detail:type_name -> game.GameDetail
	72, // 44: game.GetOnlineMemberMinResponse.online_member_summary:type_name -> game.OnlineMember
	73, // 45: game.GetOnlineMemberMinResponse.online_member_ingress_summary:type_name -> game.OnlineMemberIngressSummary
	74, // 46: game.OnlineMemberIngressSummary.online_member_ingress_info:type_name -> game.OnlineMemberIngressInfo
	75, // 47: game.DeleteAPISynchronizeRequest.cover_condition:type_name -> game.CoverCondition
	78, // 48: game.ModifyGameTypeSwitchRequest.game_type_info:type_name -> game.GameTypeInfo
	81, // 49: game.ManageGameHallSwitchRequest.data:type_name -> game.GameHallSwitch
	5,  // 50: game.UpdateGameInfoHallListRequest.allow_list:type_name -> game.RepeatedUint32Value
	5,  // 51: game.UpdateGameInfoHallListRequest.block_list:type_name -> game.RepeatedUint32Value
	72, // 52: game.GetOnlineMemberHourResponse.online_member_summary:type_name -> game.OnlineMember
	73, // 53: game.GetOnlineMemberHourResponse.online_member_ingress_summary:type_name -> game.OnlineMemberIngressSummary
	89, // 54: game.CreateUserLobbySwitchRequest.user_lobby_switch:type_name -> game.UserLobbySwitch
	72, // 55: game.GetOnlineMemberDayResponse.online_member_summary:type_name -> game.OnlineMember
	73, // 56: game.GetOnlineMemberDayResponse.online_member_ingress_summary:type_name -> game.OnlineMemberIngressSummary
	0,  // 57: game.Game.Lobby:input_type -> game.EmptyRequest
	6,  // 58: game.Game.EnableGameList:input_type -> game.EnableGameListRequest
	8,  // 59: game.Game.DemoLink:input_type -> game.DemoLinkRequest
	12, // 60: game.Game.GameDomain:input_type -> game.GameDomainRequest
	14, // 61: game.Game.LobbyLink:input_type -> game.LobbyLinkRequest
	16, // 62: game.Game.GetLobbySwitch:input_type -> game.GetLobbySwitchRequest
	19, // 63: game.Game.GetLobbySwitchByHallID:input_type -> game.GetLobbySwitchByHallIDRequest
	21, // 64: game.Game.GetCategory:input_type -> game.GetCategoryRequest
	24, // 65: game.Game.GetMenuSort:input_type -> game.GetMenuSortRequest
	27, // 66: game.Game.GetGameInfo:input_type -> game.GetGameInfoRequest
	31, // 67: game.Game.GetLobbyGameEntranceSwitch:input_type -> game.GetLobbyGameEntranceSwitchRequest
	34, // 68: game.Game.BulletinList:input_type -> game.BulletinRequest
	0,  // 69: game.Game.GameKindList:input_type -> game.EmptyRequest
	0,  // 70: game.Game.GameIconKind:input_type -> game.EmptyRequest
	41, // 71: game.Game.AddUserFavorite:input_type -> game.AddUserFavoriteRequest
	43, // 72: game.Game.GetMenuName:input_type -> game.GetMenuNameRequest
	46, // 73: game.Game.GetUserFavorite:input_type -> game.GetUserFavoriteRequest
	49, // 74: game.Game.DeleteUserFavorite:input_type -> game.DeleteUserFavoriteRequest
	50, // 75: game.Game.GameList:input_type -> game.GameListRequest
	53, // 76: game.Game.GetGameMaintainLabel:input_type -> game.GameMaintainLabelRequest
	56, // 77: game.Game.GetGameURLList:input_type -> game.GameURLListRequest
	59, // 78: game.Game.LobbyCategory:input_type -> game.LobbyCategoryRequest
	61, // 79: game.Game.GetGameListWithSwitch:input_type -> game.GetGameListWithSwitchRequest
	64, // 80: game.Game.GetUserLobbySwitch:input_type -> game.GetUserLobbySwitchRequest
	66, // 81: game.Game.GetGameDetail:input_type -> game.GetGameDetailRequest
	69, // 82: game.Game.CreateGameDetail:input_type -> game.CreateGameDetailRequest
	70, // 83: game.Game.GetOnlineMemberMin:input_type -> game.GetOnlineMemberMinRequest
	76, // 84: game.Game.DeleteAPISynchronize:input_type -> game.DeleteAPISynchronizeRequest
	77, // 85: game.Game.ModifyGameTypeSwitch:input_type -> game.ModifyGameTypeSwitchRequest
	79, // 86: game.Game.DeleteGameTypeSynchronize:input_type -> game.DeleteGameTypeSynchronizeRequest
	80, // 87: game.Game.ManageGameHallSwitch:input_type -> game.ManageGameHallSwitchRequest
	82, // 88: game.Game.UpdateGameInfoHallList:input_type -> game.UpdateGameInfoHallListRequest
	83, // 89: game.Game.DeleteUserLobby:input_type -> game.DeleteUserLobbyRequest
	84, // 90: game.Game.GetOnlineMemberHour:input_type -> game.GetOnlineMemberHourRequest
	86, // 91: game.Game.UpdateHallLobbySwitch:input_type -> game.UpdateHallLobbySwitchRequest
	87, // 92: game.Game.DeleteHallLowerAccountLobbySwitch:input_type -> game.DeleteHallLowerAccountLobbySwitchRequest
	88, // 93: game.Game.CreateUserLobbySwitch:input_type -> game.CreateUserLobbySwitchRequest
	90, // 94: game.Game.GetOnlineMemberDay:input_type -> game.GetOnlineMemberDayRequest
	92, // 95: game.Game.DeleteHallLobbyCloseTime:input_type -> game.DeleteHallLobbyCloseTimeRequest
	93, // 96: game.Game.SetHallLobbyCloseTime:input_type -> game.SetHallLobbyCloseTimeRequest
	1,  // 97: game.Game.Lobby:output_type -> game.LobbyResponse
	11, // 98: game.Game.EnableGameList:output_type -> game.EnableGameListResponse
	9,  // 99: game.Game.DemoLink:output_type -> game.DemoLinkResponse
	13, // 100: game.Game.GameDomain:output_type -> game.GameDomainResponse
	15, // 101: game.Game.LobbyLink:output_type -> game.LobbyLinkResponse
	18, // 102: game.Game.GetLobbySwitch:output_type -> game.GetLobbySwitchResponse
	20, // 103: game.Game.GetLobbySwitchByHallID:output_type -> game.GetLobbySwitchByHallIDResponse
	23, // 104: game.Game.GetCategory:output_type -> game.GetCategoryResponse
	26, // 105: game.Game.GetMenuSort:output_type -> game.GetMenuSortResponse
	30, // 106: game.Game.GetGameInfo:output_type -> game.GetGameInfoResponse
	33, // 107: game.Game.GetLobbyGameEntranceSwitch:output_type -> game.GetLobbyGameEntranceSwitchResponse
	35, // 108: game.Game.BulletinList:output_type -> game.BulletinResponse
	38, // 109: game.Game.GameKindList:output_type -> game.GameKindListResponse
	40, // 110: game.Game.GameIconKind:output_type -> game.GameIconKindResponse
	42, // 111: game.Game.AddUserFavorite:output_type -> game.EmptyResponse
	45, // 112: game.Game.GetMenuName:output_type -> game.GetMenuNameResponse
	48, // 113: game.Game.GetUserFavorite:output_type -> game.GetUserFavoriteResponse
	42, // 114: game.Game.DeleteUserFavorite:output_type -> game.EmptyResponse
	52, // 115: game.Game.GameList:output_type -> game.GameListResponse
	54, // 116: game.Game.GetGameMaintainLabel:output_type -> game.GameMaintainLabelResponse
	57, // 117: game.Game.GetGameURLList:output_type -> game.GameURLListResponse
	60, // 118: game.Game.LobbyCategory:output_type -> game.LobbyCategoryResponse
	63, // 119: game.Game.GetGameListWithSwitch:output_type -> game.GetGameListWithSwitchResponse
	65, // 120: game.Game.GetUserLobbySwitch:output_type -> game.GetUserLobbySwitchResponse
	67, // 121: game.Game.GetGameDetail:output_type -> game.GetGameDetailResponse
	42, // 122: game.Game.CreateGameDetail:output_type -> game.EmptyResponse
	71, // 123: game.Game.GetOnlineMemberMin:output_type -> game.GetOnlineMemberMinResponse
	42, // 124: game.Game.DeleteAPISynchronize:output_type -> game.EmptyResponse
	42, // 125: game.Game.ModifyGameTypeSwitch:output_type -> game.EmptyResponse
	42, // 126: game.Game.DeleteGameTypeSynchronize:output_type -> game.EmptyResponse
	42, // 127: game.Game.ManageGameHallSwitch:output_type -> game.EmptyResponse
	42, // 128: game.Game.UpdateGameInfoHallList:output_type -> game.EmptyResponse
	42, // 129: game.Game.DeleteUserLobby:output_type -> game.EmptyResponse
	85, // 130: game.Game.GetOnlineMemberHour:output_type -> game.GetOnlineMemberHourResponse
	42, // 131: game.Game.UpdateHallLobbySwitch:output_type -> game.EmptyResponse
	42, // 132: game.Game.DeleteHallLowerAccountLobbySwitch:output_type -> game.EmptyResponse
	42, // 133: game.Game.CreateUserLobbySwitch:output_type -> game.EmptyResponse
	91, // 134: game.Game.GetOnlineMemberDay:output_type -> game.GetOnlineMemberDayResponse
	42, // 135: game.Game.DeleteHallLobbyCloseTime:output_type -> game.EmptyResponse
	42, // 136: game.Game.SetHallLobbyCloseTime:output_type -> game.EmptyResponse
	97, // [97:137] is the sub-list for method output_type
	57, // [57:97] is the sub-list for method input_type
	57, // [57:57] is the sub-list for extension type_name
	57, // [57:57] is the sub-list for extension extendee
	0,  // [0:57] is the sub-list for field type_name
}

func init() { file_game_proto_init() }
func file_game_proto_init() {
	if File_game_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_game_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LobbyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoolValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedUint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableGameListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableGame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DemoLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DemoLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DemoLinkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnableGameListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDomainRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDomainResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LobbyLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LobbyLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbySwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LobbySwitch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbySwitchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbySwitchByHallIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbySwitchByHallIDResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMenuSortRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MenuSortInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMenuSortResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExternalList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbyGameEntranceSwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LobbyGameEntranceSwitch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbyGameEntranceSwitchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulletinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulletinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulletinData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameKindList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameKindListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameIconKindInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameIconKindResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserFavoriteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMenuNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MenuInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMenuNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFavoriteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserFavorite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFavoriteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUserFavoriteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameMaintainLabelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameMaintainLabelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameMaintainLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameURLListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameURLListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameURL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LobbyCategoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LobbyCategoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameListWithSwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameListWithSwitch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameListWithSwitchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserLobbySwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserLobbySwitchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineMemberMinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineMemberMinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnlineMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnlineMemberIngressSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnlineMemberIngressInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoverCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAPISynchronizeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyGameTypeSwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameTypeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGameTypeSynchronizeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManageGameHallSwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameHallSwitch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGameInfoHallListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUserLobbyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineMemberHourRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineMemberHourResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateHallLobbySwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteHallLowerAccountLobbySwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserLobbySwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserLobbySwitch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineMemberDayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineMemberDayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteHallLobbyCloseTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_game_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetHallLobbyCloseTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_game_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   94,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_game_proto_goTypes,
		DependencyIndexes: file_game_proto_depIdxs,
		MessageInfos:      file_game_proto_msgTypes,
	}.Build()
	File_game_proto = out.File
	file_game_proto_rawDesc = nil
	file_game_proto_goTypes = nil
	file_game_proto_depIdxs = nil
}
