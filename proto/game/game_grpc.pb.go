// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: game.proto

package game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Game_Lobby_FullMethodName                             = "/game.Game/Lobby"
	Game_EnableGameList_FullMethodName                    = "/game.Game/EnableGameList"
	Game_DemoLink_FullMethodName                          = "/game.Game/DemoLink"
	Game_GameDomain_FullMethodName                        = "/game.Game/GameDomain"
	Game_LobbyLink_FullMethodName                         = "/game.Game/LobbyLink"
	Game_GetLobbySwitch_FullMethodName                    = "/game.Game/GetLobbySwitch"
	Game_GetLobbySwitchByHallID_FullMethodName            = "/game.Game/GetLobbySwitchByHallID"
	Game_GetCategory_FullMethodName                       = "/game.Game/GetCategory"
	Game_GetMenuSort_FullMethodName                       = "/game.Game/GetMenuSort"
	Game_GetGameInfo_FullMethodName                       = "/game.Game/GetGameInfo"
	Game_GetLobbyGameEntranceSwitch_FullMethodName        = "/game.Game/GetLobbyGameEntranceSwitch"
	Game_BulletinList_FullMethodName                      = "/game.Game/BulletinList"
	Game_GameKindList_FullMethodName                      = "/game.Game/GameKindList"
	Game_GameIconKind_FullMethodName                      = "/game.Game/GameIconKind"
	Game_AddUserFavorite_FullMethodName                   = "/game.Game/AddUserFavorite"
	Game_GetMenuName_FullMethodName                       = "/game.Game/GetMenuName"
	Game_GetUserFavorite_FullMethodName                   = "/game.Game/GetUserFavorite"
	Game_DeleteUserFavorite_FullMethodName                = "/game.Game/DeleteUserFavorite"
	Game_GameList_FullMethodName                          = "/game.Game/GameList"
	Game_GetGameMaintainLabel_FullMethodName              = "/game.Game/GetGameMaintainLabel"
	Game_GetGameURLList_FullMethodName                    = "/game.Game/GetGameURLList"
	Game_LobbyCategory_FullMethodName                     = "/game.Game/LobbyCategory"
	Game_GetGameListWithSwitch_FullMethodName             = "/game.Game/GetGameListWithSwitch"
	Game_GetUserLobbySwitch_FullMethodName                = "/game.Game/GetUserLobbySwitch"
	Game_GetGameDetail_FullMethodName                     = "/game.Game/GetGameDetail"
	Game_CreateGameDetail_FullMethodName                  = "/game.Game/CreateGameDetail"
	Game_GetOnlineMemberMin_FullMethodName                = "/game.Game/GetOnlineMemberMin"
	Game_DeleteAPISynchronize_FullMethodName              = "/game.Game/DeleteAPISynchronize"
	Game_ModifyGameTypeSwitch_FullMethodName              = "/game.Game/ModifyGameTypeSwitch"
	Game_DeleteGameTypeSynchronize_FullMethodName         = "/game.Game/DeleteGameTypeSynchronize"
	Game_ManageGameHallSwitch_FullMethodName              = "/game.Game/ManageGameHallSwitch"
	Game_UpdateGameInfoHallList_FullMethodName            = "/game.Game/UpdateGameInfoHallList"
	Game_DeleteUserLobby_FullMethodName                   = "/game.Game/DeleteUserLobby"
	Game_GetOnlineMemberHour_FullMethodName               = "/game.Game/GetOnlineMemberHour"
	Game_UpdateHallLobbySwitch_FullMethodName             = "/game.Game/UpdateHallLobbySwitch"
	Game_DeleteHallLowerAccountLobbySwitch_FullMethodName = "/game.Game/DeleteHallLowerAccountLobbySwitch"
	Game_CreateUserLobbySwitch_FullMethodName             = "/game.Game/CreateUserLobbySwitch"
	Game_GetOnlineMemberDay_FullMethodName                = "/game.Game/GetOnlineMemberDay"
	Game_DeleteHallLobbyCloseTime_FullMethodName          = "/game.Game/DeleteHallLobbyCloseTime"
	Game_SetHallLobbyCloseTime_FullMethodName             = "/game.Game/SetHallLobbyCloseTime"
)

// GameClient is the client API for Game service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GameClient interface {
	Lobby(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*LobbyResponse, error)
	EnableGameList(ctx context.Context, in *EnableGameListRequest, opts ...grpc.CallOption) (*EnableGameListResponse, error)
	DemoLink(ctx context.Context, in *DemoLinkRequest, opts ...grpc.CallOption) (*DemoLinkResponse, error)
	GameDomain(ctx context.Context, in *GameDomainRequest, opts ...grpc.CallOption) (*GameDomainResponse, error)
	LobbyLink(ctx context.Context, in *LobbyLinkRequest, opts ...grpc.CallOption) (*LobbyLinkResponse, error)
	GetLobbySwitch(ctx context.Context, in *GetLobbySwitchRequest, opts ...grpc.CallOption) (*GetLobbySwitchResponse, error)
	GetLobbySwitchByHallID(ctx context.Context, in *GetLobbySwitchByHallIDRequest, opts ...grpc.CallOption) (*GetLobbySwitchByHallIDResponse, error)
	GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error)
	GetMenuSort(ctx context.Context, in *GetMenuSortRequest, opts ...grpc.CallOption) (*GetMenuSortResponse, error)
	GetGameInfo(ctx context.Context, in *GetGameInfoRequest, opts ...grpc.CallOption) (*GetGameInfoResponse, error)
	GetLobbyGameEntranceSwitch(ctx context.Context, in *GetLobbyGameEntranceSwitchRequest, opts ...grpc.CallOption) (*GetLobbyGameEntranceSwitchResponse, error)
	BulletinList(ctx context.Context, in *BulletinRequest, opts ...grpc.CallOption) (*BulletinResponse, error)
	GameKindList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameKindListResponse, error)
	GameIconKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameIconKindResponse, error)
	AddUserFavorite(ctx context.Context, in *AddUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetMenuName(ctx context.Context, in *GetMenuNameRequest, opts ...grpc.CallOption) (*GetMenuNameResponse, error)
	GetUserFavorite(ctx context.Context, in *GetUserFavoriteRequest, opts ...grpc.CallOption) (*GetUserFavoriteResponse, error)
	DeleteUserFavorite(ctx context.Context, in *DeleteUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error)
	GetGameMaintainLabel(ctx context.Context, in *GameMaintainLabelRequest, opts ...grpc.CallOption) (*GameMaintainLabelResponse, error)
	GetGameURLList(ctx context.Context, in *GameURLListRequest, opts ...grpc.CallOption) (*GameURLListResponse, error)
	LobbyCategory(ctx context.Context, in *LobbyCategoryRequest, opts ...grpc.CallOption) (*LobbyCategoryResponse, error)
	GetGameListWithSwitch(ctx context.Context, in *GetGameListWithSwitchRequest, opts ...grpc.CallOption) (*GetGameListWithSwitchResponse, error)
	GetUserLobbySwitch(ctx context.Context, in *GetUserLobbySwitchRequest, opts ...grpc.CallOption) (*GetUserLobbySwitchResponse, error)
	GetGameDetail(ctx context.Context, in *GetGameDetailRequest, opts ...grpc.CallOption) (*GetGameDetailResponse, error)
	CreateGameDetail(ctx context.Context, in *CreateGameDetailRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetOnlineMemberMin(ctx context.Context, in *GetOnlineMemberMinRequest, opts ...grpc.CallOption) (*GetOnlineMemberMinResponse, error)
	DeleteAPISynchronize(ctx context.Context, in *DeleteAPISynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	ModifyGameTypeSwitch(ctx context.Context, in *ModifyGameTypeSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	DeleteGameTypeSynchronize(ctx context.Context, in *DeleteGameTypeSynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	ManageGameHallSwitch(ctx context.Context, in *ManageGameHallSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	UpdateGameInfoHallList(ctx context.Context, in *UpdateGameInfoHallListRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	DeleteUserLobby(ctx context.Context, in *DeleteUserLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetOnlineMemberHour(ctx context.Context, in *GetOnlineMemberHourRequest, opts ...grpc.CallOption) (*GetOnlineMemberHourResponse, error)
	UpdateHallLobbySwitch(ctx context.Context, in *UpdateHallLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	DeleteHallLowerAccountLobbySwitch(ctx context.Context, in *DeleteHallLowerAccountLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	CreateUserLobbySwitch(ctx context.Context, in *CreateUserLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetOnlineMemberDay(ctx context.Context, in *GetOnlineMemberDayRequest, opts ...grpc.CallOption) (*GetOnlineMemberDayResponse, error)
	DeleteHallLobbyCloseTime(ctx context.Context, in *DeleteHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetHallLobbyCloseTime(ctx context.Context, in *SetHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
}

type gameClient struct {
	cc grpc.ClientConnInterface
}

func NewGameClient(cc grpc.ClientConnInterface) GameClient {
	return &gameClient{cc}
}

func (c *gameClient) Lobby(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*LobbyResponse, error) {
	out := new(LobbyResponse)
	err := c.cc.Invoke(ctx, Game_Lobby_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) EnableGameList(ctx context.Context, in *EnableGameListRequest, opts ...grpc.CallOption) (*EnableGameListResponse, error) {
	out := new(EnableGameListResponse)
	err := c.cc.Invoke(ctx, Game_EnableGameList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) DemoLink(ctx context.Context, in *DemoLinkRequest, opts ...grpc.CallOption) (*DemoLinkResponse, error) {
	out := new(DemoLinkResponse)
	err := c.cc.Invoke(ctx, Game_DemoLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GameDomain(ctx context.Context, in *GameDomainRequest, opts ...grpc.CallOption) (*GameDomainResponse, error) {
	out := new(GameDomainResponse)
	err := c.cc.Invoke(ctx, Game_GameDomain_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) LobbyLink(ctx context.Context, in *LobbyLinkRequest, opts ...grpc.CallOption) (*LobbyLinkResponse, error) {
	out := new(LobbyLinkResponse)
	err := c.cc.Invoke(ctx, Game_LobbyLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetLobbySwitch(ctx context.Context, in *GetLobbySwitchRequest, opts ...grpc.CallOption) (*GetLobbySwitchResponse, error) {
	out := new(GetLobbySwitchResponse)
	err := c.cc.Invoke(ctx, Game_GetLobbySwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetLobbySwitchByHallID(ctx context.Context, in *GetLobbySwitchByHallIDRequest, opts ...grpc.CallOption) (*GetLobbySwitchByHallIDResponse, error) {
	out := new(GetLobbySwitchByHallIDResponse)
	err := c.cc.Invoke(ctx, Game_GetLobbySwitchByHallID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error) {
	out := new(GetCategoryResponse)
	err := c.cc.Invoke(ctx, Game_GetCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetMenuSort(ctx context.Context, in *GetMenuSortRequest, opts ...grpc.CallOption) (*GetMenuSortResponse, error) {
	out := new(GetMenuSortResponse)
	err := c.cc.Invoke(ctx, Game_GetMenuSort_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetGameInfo(ctx context.Context, in *GetGameInfoRequest, opts ...grpc.CallOption) (*GetGameInfoResponse, error) {
	out := new(GetGameInfoResponse)
	err := c.cc.Invoke(ctx, Game_GetGameInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetLobbyGameEntranceSwitch(ctx context.Context, in *GetLobbyGameEntranceSwitchRequest, opts ...grpc.CallOption) (*GetLobbyGameEntranceSwitchResponse, error) {
	out := new(GetLobbyGameEntranceSwitchResponse)
	err := c.cc.Invoke(ctx, Game_GetLobbyGameEntranceSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) BulletinList(ctx context.Context, in *BulletinRequest, opts ...grpc.CallOption) (*BulletinResponse, error) {
	out := new(BulletinResponse)
	err := c.cc.Invoke(ctx, Game_BulletinList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GameKindList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameKindListResponse, error) {
	out := new(GameKindListResponse)
	err := c.cc.Invoke(ctx, Game_GameKindList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GameIconKind(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GameIconKindResponse, error) {
	out := new(GameIconKindResponse)
	err := c.cc.Invoke(ctx, Game_GameIconKind_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) AddUserFavorite(ctx context.Context, in *AddUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_AddUserFavorite_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetMenuName(ctx context.Context, in *GetMenuNameRequest, opts ...grpc.CallOption) (*GetMenuNameResponse, error) {
	out := new(GetMenuNameResponse)
	err := c.cc.Invoke(ctx, Game_GetMenuName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetUserFavorite(ctx context.Context, in *GetUserFavoriteRequest, opts ...grpc.CallOption) (*GetUserFavoriteResponse, error) {
	out := new(GetUserFavoriteResponse)
	err := c.cc.Invoke(ctx, Game_GetUserFavorite_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) DeleteUserFavorite(ctx context.Context, in *DeleteUserFavoriteRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_DeleteUserFavorite_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error) {
	out := new(GameListResponse)
	err := c.cc.Invoke(ctx, Game_GameList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetGameMaintainLabel(ctx context.Context, in *GameMaintainLabelRequest, opts ...grpc.CallOption) (*GameMaintainLabelResponse, error) {
	out := new(GameMaintainLabelResponse)
	err := c.cc.Invoke(ctx, Game_GetGameMaintainLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetGameURLList(ctx context.Context, in *GameURLListRequest, opts ...grpc.CallOption) (*GameURLListResponse, error) {
	out := new(GameURLListResponse)
	err := c.cc.Invoke(ctx, Game_GetGameURLList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) LobbyCategory(ctx context.Context, in *LobbyCategoryRequest, opts ...grpc.CallOption) (*LobbyCategoryResponse, error) {
	out := new(LobbyCategoryResponse)
	err := c.cc.Invoke(ctx, Game_LobbyCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetGameListWithSwitch(ctx context.Context, in *GetGameListWithSwitchRequest, opts ...grpc.CallOption) (*GetGameListWithSwitchResponse, error) {
	out := new(GetGameListWithSwitchResponse)
	err := c.cc.Invoke(ctx, Game_GetGameListWithSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetUserLobbySwitch(ctx context.Context, in *GetUserLobbySwitchRequest, opts ...grpc.CallOption) (*GetUserLobbySwitchResponse, error) {
	out := new(GetUserLobbySwitchResponse)
	err := c.cc.Invoke(ctx, Game_GetUserLobbySwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetGameDetail(ctx context.Context, in *GetGameDetailRequest, opts ...grpc.CallOption) (*GetGameDetailResponse, error) {
	out := new(GetGameDetailResponse)
	err := c.cc.Invoke(ctx, Game_GetGameDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) CreateGameDetail(ctx context.Context, in *CreateGameDetailRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_CreateGameDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetOnlineMemberMin(ctx context.Context, in *GetOnlineMemberMinRequest, opts ...grpc.CallOption) (*GetOnlineMemberMinResponse, error) {
	out := new(GetOnlineMemberMinResponse)
	err := c.cc.Invoke(ctx, Game_GetOnlineMemberMin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) DeleteAPISynchronize(ctx context.Context, in *DeleteAPISynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_DeleteAPISynchronize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) ModifyGameTypeSwitch(ctx context.Context, in *ModifyGameTypeSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_ModifyGameTypeSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) DeleteGameTypeSynchronize(ctx context.Context, in *DeleteGameTypeSynchronizeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_DeleteGameTypeSynchronize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) ManageGameHallSwitch(ctx context.Context, in *ManageGameHallSwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_ManageGameHallSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) UpdateGameInfoHallList(ctx context.Context, in *UpdateGameInfoHallListRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_UpdateGameInfoHallList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) DeleteUserLobby(ctx context.Context, in *DeleteUserLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_DeleteUserLobby_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetOnlineMemberHour(ctx context.Context, in *GetOnlineMemberHourRequest, opts ...grpc.CallOption) (*GetOnlineMemberHourResponse, error) {
	out := new(GetOnlineMemberHourResponse)
	err := c.cc.Invoke(ctx, Game_GetOnlineMemberHour_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) UpdateHallLobbySwitch(ctx context.Context, in *UpdateHallLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_UpdateHallLobbySwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) DeleteHallLowerAccountLobbySwitch(ctx context.Context, in *DeleteHallLowerAccountLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_DeleteHallLowerAccountLobbySwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) CreateUserLobbySwitch(ctx context.Context, in *CreateUserLobbySwitchRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_CreateUserLobbySwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) GetOnlineMemberDay(ctx context.Context, in *GetOnlineMemberDayRequest, opts ...grpc.CallOption) (*GetOnlineMemberDayResponse, error) {
	out := new(GetOnlineMemberDayResponse)
	err := c.cc.Invoke(ctx, Game_GetOnlineMemberDay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) DeleteHallLobbyCloseTime(ctx context.Context, in *DeleteHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_DeleteHallLobbyCloseTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) SetHallLobbyCloseTime(ctx context.Context, in *SetHallLobbyCloseTimeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Game_SetHallLobbyCloseTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameServer is the server API for Game service.
// All implementations must embed UnimplementedGameServer
// for forward compatibility
type GameServer interface {
	Lobby(context.Context, *EmptyRequest) (*LobbyResponse, error)
	EnableGameList(context.Context, *EnableGameListRequest) (*EnableGameListResponse, error)
	DemoLink(context.Context, *DemoLinkRequest) (*DemoLinkResponse, error)
	GameDomain(context.Context, *GameDomainRequest) (*GameDomainResponse, error)
	LobbyLink(context.Context, *LobbyLinkRequest) (*LobbyLinkResponse, error)
	GetLobbySwitch(context.Context, *GetLobbySwitchRequest) (*GetLobbySwitchResponse, error)
	GetLobbySwitchByHallID(context.Context, *GetLobbySwitchByHallIDRequest) (*GetLobbySwitchByHallIDResponse, error)
	GetCategory(context.Context, *GetCategoryRequest) (*GetCategoryResponse, error)
	GetMenuSort(context.Context, *GetMenuSortRequest) (*GetMenuSortResponse, error)
	GetGameInfo(context.Context, *GetGameInfoRequest) (*GetGameInfoResponse, error)
	GetLobbyGameEntranceSwitch(context.Context, *GetLobbyGameEntranceSwitchRequest) (*GetLobbyGameEntranceSwitchResponse, error)
	BulletinList(context.Context, *BulletinRequest) (*BulletinResponse, error)
	GameKindList(context.Context, *EmptyRequest) (*GameKindListResponse, error)
	GameIconKind(context.Context, *EmptyRequest) (*GameIconKindResponse, error)
	AddUserFavorite(context.Context, *AddUserFavoriteRequest) (*EmptyResponse, error)
	GetMenuName(context.Context, *GetMenuNameRequest) (*GetMenuNameResponse, error)
	GetUserFavorite(context.Context, *GetUserFavoriteRequest) (*GetUserFavoriteResponse, error)
	DeleteUserFavorite(context.Context, *DeleteUserFavoriteRequest) (*EmptyResponse, error)
	GameList(context.Context, *GameListRequest) (*GameListResponse, error)
	GetGameMaintainLabel(context.Context, *GameMaintainLabelRequest) (*GameMaintainLabelResponse, error)
	GetGameURLList(context.Context, *GameURLListRequest) (*GameURLListResponse, error)
	LobbyCategory(context.Context, *LobbyCategoryRequest) (*LobbyCategoryResponse, error)
	GetGameListWithSwitch(context.Context, *GetGameListWithSwitchRequest) (*GetGameListWithSwitchResponse, error)
	GetUserLobbySwitch(context.Context, *GetUserLobbySwitchRequest) (*GetUserLobbySwitchResponse, error)
	GetGameDetail(context.Context, *GetGameDetailRequest) (*GetGameDetailResponse, error)
	CreateGameDetail(context.Context, *CreateGameDetailRequest) (*EmptyResponse, error)
	GetOnlineMemberMin(context.Context, *GetOnlineMemberMinRequest) (*GetOnlineMemberMinResponse, error)
	DeleteAPISynchronize(context.Context, *DeleteAPISynchronizeRequest) (*EmptyResponse, error)
	ModifyGameTypeSwitch(context.Context, *ModifyGameTypeSwitchRequest) (*EmptyResponse, error)
	DeleteGameTypeSynchronize(context.Context, *DeleteGameTypeSynchronizeRequest) (*EmptyResponse, error)
	ManageGameHallSwitch(context.Context, *ManageGameHallSwitchRequest) (*EmptyResponse, error)
	UpdateGameInfoHallList(context.Context, *UpdateGameInfoHallListRequest) (*EmptyResponse, error)
	DeleteUserLobby(context.Context, *DeleteUserLobbyRequest) (*EmptyResponse, error)
	GetOnlineMemberHour(context.Context, *GetOnlineMemberHourRequest) (*GetOnlineMemberHourResponse, error)
	UpdateHallLobbySwitch(context.Context, *UpdateHallLobbySwitchRequest) (*EmptyResponse, error)
	DeleteHallLowerAccountLobbySwitch(context.Context, *DeleteHallLowerAccountLobbySwitchRequest) (*EmptyResponse, error)
	CreateUserLobbySwitch(context.Context, *CreateUserLobbySwitchRequest) (*EmptyResponse, error)
	GetOnlineMemberDay(context.Context, *GetOnlineMemberDayRequest) (*GetOnlineMemberDayResponse, error)
	DeleteHallLobbyCloseTime(context.Context, *DeleteHallLobbyCloseTimeRequest) (*EmptyResponse, error)
	SetHallLobbyCloseTime(context.Context, *SetHallLobbyCloseTimeRequest) (*EmptyResponse, error)
	mustEmbedUnimplementedGameServer()
}

// UnimplementedGameServer must be embedded to have forward compatible implementations.
type UnimplementedGameServer struct {
}

func (UnimplementedGameServer) Lobby(context.Context, *EmptyRequest) (*LobbyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Lobby not implemented")
}
func (UnimplementedGameServer) EnableGameList(context.Context, *EnableGameListRequest) (*EnableGameListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnableGameList not implemented")
}
func (UnimplementedGameServer) DemoLink(context.Context, *DemoLinkRequest) (*DemoLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DemoLink not implemented")
}
func (UnimplementedGameServer) GameDomain(context.Context, *GameDomainRequest) (*GameDomainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameDomain not implemented")
}
func (UnimplementedGameServer) LobbyLink(context.Context, *LobbyLinkRequest) (*LobbyLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LobbyLink not implemented")
}
func (UnimplementedGameServer) GetLobbySwitch(context.Context, *GetLobbySwitchRequest) (*GetLobbySwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLobbySwitch not implemented")
}
func (UnimplementedGameServer) GetLobbySwitchByHallID(context.Context, *GetLobbySwitchByHallIDRequest) (*GetLobbySwitchByHallIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLobbySwitchByHallID not implemented")
}
func (UnimplementedGameServer) GetCategory(context.Context, *GetCategoryRequest) (*GetCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCategory not implemented")
}
func (UnimplementedGameServer) GetMenuSort(context.Context, *GetMenuSortRequest) (*GetMenuSortResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMenuSort not implemented")
}
func (UnimplementedGameServer) GetGameInfo(context.Context, *GetGameInfoRequest) (*GetGameInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameInfo not implemented")
}
func (UnimplementedGameServer) GetLobbyGameEntranceSwitch(context.Context, *GetLobbyGameEntranceSwitchRequest) (*GetLobbyGameEntranceSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLobbyGameEntranceSwitch not implemented")
}
func (UnimplementedGameServer) BulletinList(context.Context, *BulletinRequest) (*BulletinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulletinList not implemented")
}
func (UnimplementedGameServer) GameKindList(context.Context, *EmptyRequest) (*GameKindListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameKindList not implemented")
}
func (UnimplementedGameServer) GameIconKind(context.Context, *EmptyRequest) (*GameIconKindResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameIconKind not implemented")
}
func (UnimplementedGameServer) AddUserFavorite(context.Context, *AddUserFavoriteRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserFavorite not implemented")
}
func (UnimplementedGameServer) GetMenuName(context.Context, *GetMenuNameRequest) (*GetMenuNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMenuName not implemented")
}
func (UnimplementedGameServer) GetUserFavorite(context.Context, *GetUserFavoriteRequest) (*GetUserFavoriteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserFavorite not implemented")
}
func (UnimplementedGameServer) DeleteUserFavorite(context.Context, *DeleteUserFavoriteRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserFavorite not implemented")
}
func (UnimplementedGameServer) GameList(context.Context, *GameListRequest) (*GameListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameList not implemented")
}
func (UnimplementedGameServer) GetGameMaintainLabel(context.Context, *GameMaintainLabelRequest) (*GameMaintainLabelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameMaintainLabel not implemented")
}
func (UnimplementedGameServer) GetGameURLList(context.Context, *GameURLListRequest) (*GameURLListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameURLList not implemented")
}
func (UnimplementedGameServer) LobbyCategory(context.Context, *LobbyCategoryRequest) (*LobbyCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LobbyCategory not implemented")
}
func (UnimplementedGameServer) GetGameListWithSwitch(context.Context, *GetGameListWithSwitchRequest) (*GetGameListWithSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameListWithSwitch not implemented")
}
func (UnimplementedGameServer) GetUserLobbySwitch(context.Context, *GetUserLobbySwitchRequest) (*GetUserLobbySwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserLobbySwitch not implemented")
}
func (UnimplementedGameServer) GetGameDetail(context.Context, *GetGameDetailRequest) (*GetGameDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameDetail not implemented")
}
func (UnimplementedGameServer) CreateGameDetail(context.Context, *CreateGameDetailRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGameDetail not implemented")
}
func (UnimplementedGameServer) GetOnlineMemberMin(context.Context, *GetOnlineMemberMinRequest) (*GetOnlineMemberMinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineMemberMin not implemented")
}
func (UnimplementedGameServer) DeleteAPISynchronize(context.Context, *DeleteAPISynchronizeRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAPISynchronize not implemented")
}
func (UnimplementedGameServer) ModifyGameTypeSwitch(context.Context, *ModifyGameTypeSwitchRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyGameTypeSwitch not implemented")
}
func (UnimplementedGameServer) DeleteGameTypeSynchronize(context.Context, *DeleteGameTypeSynchronizeRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGameTypeSynchronize not implemented")
}
func (UnimplementedGameServer) ManageGameHallSwitch(context.Context, *ManageGameHallSwitchRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManageGameHallSwitch not implemented")
}
func (UnimplementedGameServer) UpdateGameInfoHallList(context.Context, *UpdateGameInfoHallListRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGameInfoHallList not implemented")
}
func (UnimplementedGameServer) DeleteUserLobby(context.Context, *DeleteUserLobbyRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserLobby not implemented")
}
func (UnimplementedGameServer) GetOnlineMemberHour(context.Context, *GetOnlineMemberHourRequest) (*GetOnlineMemberHourResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineMemberHour not implemented")
}
func (UnimplementedGameServer) UpdateHallLobbySwitch(context.Context, *UpdateHallLobbySwitchRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHallLobbySwitch not implemented")
}
func (UnimplementedGameServer) DeleteHallLowerAccountLobbySwitch(context.Context, *DeleteHallLowerAccountLobbySwitchRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHallLowerAccountLobbySwitch not implemented")
}
func (UnimplementedGameServer) CreateUserLobbySwitch(context.Context, *CreateUserLobbySwitchRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserLobbySwitch not implemented")
}
func (UnimplementedGameServer) GetOnlineMemberDay(context.Context, *GetOnlineMemberDayRequest) (*GetOnlineMemberDayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineMemberDay not implemented")
}
func (UnimplementedGameServer) DeleteHallLobbyCloseTime(context.Context, *DeleteHallLobbyCloseTimeRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHallLobbyCloseTime not implemented")
}
func (UnimplementedGameServer) SetHallLobbyCloseTime(context.Context, *SetHallLobbyCloseTimeRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHallLobbyCloseTime not implemented")
}
func (UnimplementedGameServer) mustEmbedUnimplementedGameServer() {}

// UnsafeGameServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameServer will
// result in compilation errors.
type UnsafeGameServer interface {
	mustEmbedUnimplementedGameServer()
}

func RegisterGameServer(s grpc.ServiceRegistrar, srv GameServer) {
	s.RegisterService(&Game_ServiceDesc, srv)
}

func _Game_Lobby_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Lobby(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Lobby_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Lobby(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_EnableGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableGameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).EnableGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_EnableGameList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).EnableGameList(ctx, req.(*EnableGameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_DemoLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DemoLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).DemoLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_DemoLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).DemoLink(ctx, req.(*DemoLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GameDomain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameDomainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GameDomain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GameDomain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GameDomain(ctx, req.(*GameDomainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_LobbyLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LobbyLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).LobbyLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_LobbyLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).LobbyLink(ctx, req.(*LobbyLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetLobbySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLobbySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetLobbySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetLobbySwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetLobbySwitch(ctx, req.(*GetLobbySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetLobbySwitchByHallID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLobbySwitchByHallIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetLobbySwitchByHallID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetLobbySwitchByHallID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetLobbySwitchByHallID(ctx, req.(*GetLobbySwitchByHallIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetCategory(ctx, req.(*GetCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetMenuSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMenuSortRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetMenuSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetMenuSort_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetMenuSort(ctx, req.(*GetMenuSortRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetGameInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetGameInfo(ctx, req.(*GetGameInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetLobbyGameEntranceSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLobbyGameEntranceSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetLobbyGameEntranceSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetLobbyGameEntranceSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetLobbyGameEntranceSwitch(ctx, req.(*GetLobbyGameEntranceSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_BulletinList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulletinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).BulletinList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_BulletinList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).BulletinList(ctx, req.(*BulletinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GameKindList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GameKindList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GameKindList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GameKindList(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GameIconKind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GameIconKind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GameIconKind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GameIconKind(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_AddUserFavorite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserFavoriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).AddUserFavorite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_AddUserFavorite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).AddUserFavorite(ctx, req.(*AddUserFavoriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetMenuName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMenuNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetMenuName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetMenuName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetMenuName(ctx, req.(*GetMenuNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetUserFavorite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFavoriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetUserFavorite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetUserFavorite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetUserFavorite(ctx, req.(*GetUserFavoriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_DeleteUserFavorite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserFavoriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).DeleteUserFavorite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_DeleteUserFavorite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).DeleteUserFavorite(ctx, req.(*DeleteUserFavoriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GameList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GameList(ctx, req.(*GameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetGameMaintainLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameMaintainLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetGameMaintainLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetGameMaintainLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetGameMaintainLabel(ctx, req.(*GameMaintainLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetGameURLList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameURLListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetGameURLList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetGameURLList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetGameURLList(ctx, req.(*GameURLListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_LobbyCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LobbyCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).LobbyCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_LobbyCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).LobbyCategory(ctx, req.(*LobbyCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetGameListWithSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameListWithSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetGameListWithSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetGameListWithSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetGameListWithSwitch(ctx, req.(*GetGameListWithSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetUserLobbySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLobbySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetUserLobbySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetUserLobbySwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetUserLobbySwitch(ctx, req.(*GetUserLobbySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetGameDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetGameDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetGameDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetGameDetail(ctx, req.(*GetGameDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_CreateGameDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGameDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).CreateGameDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_CreateGameDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).CreateGameDetail(ctx, req.(*CreateGameDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetOnlineMemberMin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineMemberMinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetOnlineMemberMin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetOnlineMemberMin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetOnlineMemberMin(ctx, req.(*GetOnlineMemberMinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_DeleteAPISynchronize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAPISynchronizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).DeleteAPISynchronize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_DeleteAPISynchronize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).DeleteAPISynchronize(ctx, req.(*DeleteAPISynchronizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_ModifyGameTypeSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGameTypeSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).ModifyGameTypeSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_ModifyGameTypeSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).ModifyGameTypeSwitch(ctx, req.(*ModifyGameTypeSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_DeleteGameTypeSynchronize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGameTypeSynchronizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).DeleteGameTypeSynchronize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_DeleteGameTypeSynchronize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).DeleteGameTypeSynchronize(ctx, req.(*DeleteGameTypeSynchronizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_ManageGameHallSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManageGameHallSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).ManageGameHallSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_ManageGameHallSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).ManageGameHallSwitch(ctx, req.(*ManageGameHallSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_UpdateGameInfoHallList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGameInfoHallListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).UpdateGameInfoHallList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_UpdateGameInfoHallList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).UpdateGameInfoHallList(ctx, req.(*UpdateGameInfoHallListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_DeleteUserLobby_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserLobbyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).DeleteUserLobby(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_DeleteUserLobby_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).DeleteUserLobby(ctx, req.(*DeleteUserLobbyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetOnlineMemberHour_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineMemberHourRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetOnlineMemberHour(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetOnlineMemberHour_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetOnlineMemberHour(ctx, req.(*GetOnlineMemberHourRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_UpdateHallLobbySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHallLobbySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).UpdateHallLobbySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_UpdateHallLobbySwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).UpdateHallLobbySwitch(ctx, req.(*UpdateHallLobbySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_DeleteHallLowerAccountLobbySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHallLowerAccountLobbySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).DeleteHallLowerAccountLobbySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_DeleteHallLowerAccountLobbySwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).DeleteHallLowerAccountLobbySwitch(ctx, req.(*DeleteHallLowerAccountLobbySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_CreateUserLobbySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserLobbySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).CreateUserLobbySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_CreateUserLobbySwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).CreateUserLobbySwitch(ctx, req.(*CreateUserLobbySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_GetOnlineMemberDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineMemberDayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).GetOnlineMemberDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_GetOnlineMemberDay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).GetOnlineMemberDay(ctx, req.(*GetOnlineMemberDayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_DeleteHallLobbyCloseTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHallLobbyCloseTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).DeleteHallLobbyCloseTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_DeleteHallLobbyCloseTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).DeleteHallLobbyCloseTime(ctx, req.(*DeleteHallLobbyCloseTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_SetHallLobbyCloseTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHallLobbyCloseTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).SetHallLobbyCloseTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_SetHallLobbyCloseTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).SetHallLobbyCloseTime(ctx, req.(*SetHallLobbyCloseTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Game_ServiceDesc is the grpc.ServiceDesc for Game service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Game_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.Game",
	HandlerType: (*GameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Lobby",
			Handler:    _Game_Lobby_Handler,
		},
		{
			MethodName: "EnableGameList",
			Handler:    _Game_EnableGameList_Handler,
		},
		{
			MethodName: "DemoLink",
			Handler:    _Game_DemoLink_Handler,
		},
		{
			MethodName: "GameDomain",
			Handler:    _Game_GameDomain_Handler,
		},
		{
			MethodName: "LobbyLink",
			Handler:    _Game_LobbyLink_Handler,
		},
		{
			MethodName: "GetLobbySwitch",
			Handler:    _Game_GetLobbySwitch_Handler,
		},
		{
			MethodName: "GetLobbySwitchByHallID",
			Handler:    _Game_GetLobbySwitchByHallID_Handler,
		},
		{
			MethodName: "GetCategory",
			Handler:    _Game_GetCategory_Handler,
		},
		{
			MethodName: "GetMenuSort",
			Handler:    _Game_GetMenuSort_Handler,
		},
		{
			MethodName: "GetGameInfo",
			Handler:    _Game_GetGameInfo_Handler,
		},
		{
			MethodName: "GetLobbyGameEntranceSwitch",
			Handler:    _Game_GetLobbyGameEntranceSwitch_Handler,
		},
		{
			MethodName: "BulletinList",
			Handler:    _Game_BulletinList_Handler,
		},
		{
			MethodName: "GameKindList",
			Handler:    _Game_GameKindList_Handler,
		},
		{
			MethodName: "GameIconKind",
			Handler:    _Game_GameIconKind_Handler,
		},
		{
			MethodName: "AddUserFavorite",
			Handler:    _Game_AddUserFavorite_Handler,
		},
		{
			MethodName: "GetMenuName",
			Handler:    _Game_GetMenuName_Handler,
		},
		{
			MethodName: "GetUserFavorite",
			Handler:    _Game_GetUserFavorite_Handler,
		},
		{
			MethodName: "DeleteUserFavorite",
			Handler:    _Game_DeleteUserFavorite_Handler,
		},
		{
			MethodName: "GameList",
			Handler:    _Game_GameList_Handler,
		},
		{
			MethodName: "GetGameMaintainLabel",
			Handler:    _Game_GetGameMaintainLabel_Handler,
		},
		{
			MethodName: "GetGameURLList",
			Handler:    _Game_GetGameURLList_Handler,
		},
		{
			MethodName: "LobbyCategory",
			Handler:    _Game_LobbyCategory_Handler,
		},
		{
			MethodName: "GetGameListWithSwitch",
			Handler:    _Game_GetGameListWithSwitch_Handler,
		},
		{
			MethodName: "GetUserLobbySwitch",
			Handler:    _Game_GetUserLobbySwitch_Handler,
		},
		{
			MethodName: "GetGameDetail",
			Handler:    _Game_GetGameDetail_Handler,
		},
		{
			MethodName: "CreateGameDetail",
			Handler:    _Game_CreateGameDetail_Handler,
		},
		{
			MethodName: "GetOnlineMemberMin",
			Handler:    _Game_GetOnlineMemberMin_Handler,
		},
		{
			MethodName: "DeleteAPISynchronize",
			Handler:    _Game_DeleteAPISynchronize_Handler,
		},
		{
			MethodName: "ModifyGameTypeSwitch",
			Handler:    _Game_ModifyGameTypeSwitch_Handler,
		},
		{
			MethodName: "DeleteGameTypeSynchronize",
			Handler:    _Game_DeleteGameTypeSynchronize_Handler,
		},
		{
			MethodName: "ManageGameHallSwitch",
			Handler:    _Game_ManageGameHallSwitch_Handler,
		},
		{
			MethodName: "UpdateGameInfoHallList",
			Handler:    _Game_UpdateGameInfoHallList_Handler,
		},
		{
			MethodName: "DeleteUserLobby",
			Handler:    _Game_DeleteUserLobby_Handler,
		},
		{
			MethodName: "GetOnlineMemberHour",
			Handler:    _Game_GetOnlineMemberHour_Handler,
		},
		{
			MethodName: "UpdateHallLobbySwitch",
			Handler:    _Game_UpdateHallLobbySwitch_Handler,
		},
		{
			MethodName: "DeleteHallLowerAccountLobbySwitch",
			Handler:    _Game_DeleteHallLowerAccountLobbySwitch_Handler,
		},
		{
			MethodName: "CreateUserLobbySwitch",
			Handler:    _Game_CreateUserLobbySwitch_Handler,
		},
		{
			MethodName: "GetOnlineMemberDay",
			Handler:    _Game_GetOnlineMemberDay_Handler,
		},
		{
			MethodName: "DeleteHallLobbyCloseTime",
			Handler:    _Game_DeleteHallLobbyCloseTime_Handler,
		},
		{
			MethodName: "SetHallLobbyCloseTime",
			Handler:    _Game_SetHallLobbyCloseTime_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "game.proto",
}
