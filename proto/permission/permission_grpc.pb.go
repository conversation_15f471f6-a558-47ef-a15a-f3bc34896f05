// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: permission.proto

package permission

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Permission_GetVisitedHistory_FullMethodName            = "/permission.Permission/GetVisitedHistory"
	Permission_AdminControllerPermissionIDs_FullMethodName = "/permission.Permission/AdminControllerPermissionIDs"
	Permission_AdminPermissions_FullMethodName             = "/permission.Permission/AdminPermissions"
	Permission_ModifyVisited_FullMethodName                = "/permission.Permission/ModifyVisited"
	Permission_GetAdminPermissionEnabled_FullMethodName    = "/permission.Permission/GetAdminPermissionEnabled"
	Permission_AdminPermissionInfoList_FullMethodName      = "/permission.Permission/AdminPermissionInfoList"
	Permission_GetUserPermissionAffectList_FullMethodName  = "/permission.Permission/GetUserPermissionAffectList"
	Permission_UserPermissionConfigList_FullMethodName     = "/permission.Permission/UserPermissionConfigList"
	Permission_GetUserPermissionEnabled_FullMethodName     = "/permission.Permission/GetUserPermissionEnabled"
	Permission_GetUserAllPermission_FullMethodName         = "/permission.Permission/GetUserAllPermission"
	Permission_DeleteUserPermissionByUserID_FullMethodName = "/permission.Permission/DeleteUserPermissionByUserID"
	Permission_UserPermList_FullMethodName                 = "/permission.Permission/UserPermList"
	Permission_DeleteUserDepartmentByUserID_FullMethodName = "/permission.Permission/DeleteUserDepartmentByUserID"
	Permission_SetUserPermission_FullMethodName            = "/permission.Permission/SetUserPermission"
	Permission_SetAdminPermissionByUser_FullMethodName     = "/permission.Permission/SetAdminPermissionByUser"
	Permission_SetAdminPermission_FullMethodName           = "/permission.Permission/SetAdminPermission"
)

// PermissionClient is the client API for Permission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PermissionClient interface {
	GetVisitedHistory(ctx context.Context, in *GetVisitedHistoryRequest, opts ...grpc.CallOption) (*GetVisitedHistoryResponse, error)
	AdminControllerPermissionIDs(ctx context.Context, in *AdminControllerPermissionIDsRequest, opts ...grpc.CallOption) (*AdminControllerPermissionIDsResponse, error)
	AdminPermissions(ctx context.Context, in *AdminPermissionsRequest, opts ...grpc.CallOption) (*AdminPermissionsResponse, error)
	ModifyVisited(ctx context.Context, in *VisitedRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetAdminPermissionEnabled(ctx context.Context, in *GetAdminPermissionEnabledRequest, opts ...grpc.CallOption) (*GetAdminPermissionEnabledResponse, error)
	AdminPermissionInfoList(ctx context.Context, in *AdminPermissionInfoListRequest, opts ...grpc.CallOption) (*AdminPermissionInfoListResponse, error)
	GetUserPermissionAffectList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserPermissionAffectListResponse, error)
	UserPermissionConfigList(ctx context.Context, in *UserPermissionConfigListRequest, opts ...grpc.CallOption) (*UserPermissionConfigListResponse, error)
	GetUserPermissionEnabled(ctx context.Context, in *UserPermissionEnabledRequest, opts ...grpc.CallOption) (*UserPermissionEnabledResponse, error)
	GetUserAllPermission(ctx context.Context, in *UserAllPermissionRequest, opts ...grpc.CallOption) (*UserAllPermissionResponse, error)
	DeleteUserPermissionByUserID(ctx context.Context, in *DeleteUserPermissionByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	UserPermList(ctx context.Context, in *UserPermListRequest, opts ...grpc.CallOption) (*UserPermListResponse, error)
	DeleteUserDepartmentByUserID(ctx context.Context, in *DeleteUserDepartmentByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetUserPermission(ctx context.Context, in *SetUserPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetAdminPermissionByUser(ctx context.Context, in *SetAdminPermissionByUserRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	SetAdminPermission(ctx context.Context, in *SetAdminPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
}

type permissionClient struct {
	cc grpc.ClientConnInterface
}

func NewPermissionClient(cc grpc.ClientConnInterface) PermissionClient {
	return &permissionClient{cc}
}

func (c *permissionClient) GetVisitedHistory(ctx context.Context, in *GetVisitedHistoryRequest, opts ...grpc.CallOption) (*GetVisitedHistoryResponse, error) {
	out := new(GetVisitedHistoryResponse)
	err := c.cc.Invoke(ctx, Permission_GetVisitedHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) AdminControllerPermissionIDs(ctx context.Context, in *AdminControllerPermissionIDsRequest, opts ...grpc.CallOption) (*AdminControllerPermissionIDsResponse, error) {
	out := new(AdminControllerPermissionIDsResponse)
	err := c.cc.Invoke(ctx, Permission_AdminControllerPermissionIDs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) AdminPermissions(ctx context.Context, in *AdminPermissionsRequest, opts ...grpc.CallOption) (*AdminPermissionsResponse, error) {
	out := new(AdminPermissionsResponse)
	err := c.cc.Invoke(ctx, Permission_AdminPermissions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) ModifyVisited(ctx context.Context, in *VisitedRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Permission_ModifyVisited_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) GetAdminPermissionEnabled(ctx context.Context, in *GetAdminPermissionEnabledRequest, opts ...grpc.CallOption) (*GetAdminPermissionEnabledResponse, error) {
	out := new(GetAdminPermissionEnabledResponse)
	err := c.cc.Invoke(ctx, Permission_GetAdminPermissionEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) AdminPermissionInfoList(ctx context.Context, in *AdminPermissionInfoListRequest, opts ...grpc.CallOption) (*AdminPermissionInfoListResponse, error) {
	out := new(AdminPermissionInfoListResponse)
	err := c.cc.Invoke(ctx, Permission_AdminPermissionInfoList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) GetUserPermissionAffectList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserPermissionAffectListResponse, error) {
	out := new(GetUserPermissionAffectListResponse)
	err := c.cc.Invoke(ctx, Permission_GetUserPermissionAffectList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) UserPermissionConfigList(ctx context.Context, in *UserPermissionConfigListRequest, opts ...grpc.CallOption) (*UserPermissionConfigListResponse, error) {
	out := new(UserPermissionConfigListResponse)
	err := c.cc.Invoke(ctx, Permission_UserPermissionConfigList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) GetUserPermissionEnabled(ctx context.Context, in *UserPermissionEnabledRequest, opts ...grpc.CallOption) (*UserPermissionEnabledResponse, error) {
	out := new(UserPermissionEnabledResponse)
	err := c.cc.Invoke(ctx, Permission_GetUserPermissionEnabled_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) GetUserAllPermission(ctx context.Context, in *UserAllPermissionRequest, opts ...grpc.CallOption) (*UserAllPermissionResponse, error) {
	out := new(UserAllPermissionResponse)
	err := c.cc.Invoke(ctx, Permission_GetUserAllPermission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) DeleteUserPermissionByUserID(ctx context.Context, in *DeleteUserPermissionByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Permission_DeleteUserPermissionByUserID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) UserPermList(ctx context.Context, in *UserPermListRequest, opts ...grpc.CallOption) (*UserPermListResponse, error) {
	out := new(UserPermListResponse)
	err := c.cc.Invoke(ctx, Permission_UserPermList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) DeleteUserDepartmentByUserID(ctx context.Context, in *DeleteUserDepartmentByUserIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Permission_DeleteUserDepartmentByUserID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) SetUserPermission(ctx context.Context, in *SetUserPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Permission_SetUserPermission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) SetAdminPermissionByUser(ctx context.Context, in *SetAdminPermissionByUserRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Permission_SetAdminPermissionByUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionClient) SetAdminPermission(ctx context.Context, in *SetAdminPermissionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Permission_SetAdminPermission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PermissionServer is the server API for Permission service.
// All implementations must embed UnimplementedPermissionServer
// for forward compatibility
type PermissionServer interface {
	GetVisitedHistory(context.Context, *GetVisitedHistoryRequest) (*GetVisitedHistoryResponse, error)
	AdminControllerPermissionIDs(context.Context, *AdminControllerPermissionIDsRequest) (*AdminControllerPermissionIDsResponse, error)
	AdminPermissions(context.Context, *AdminPermissionsRequest) (*AdminPermissionsResponse, error)
	ModifyVisited(context.Context, *VisitedRequest) (*EmptyResponse, error)
	GetAdminPermissionEnabled(context.Context, *GetAdminPermissionEnabledRequest) (*GetAdminPermissionEnabledResponse, error)
	AdminPermissionInfoList(context.Context, *AdminPermissionInfoListRequest) (*AdminPermissionInfoListResponse, error)
	GetUserPermissionAffectList(context.Context, *EmptyRequest) (*GetUserPermissionAffectListResponse, error)
	UserPermissionConfigList(context.Context, *UserPermissionConfigListRequest) (*UserPermissionConfigListResponse, error)
	GetUserPermissionEnabled(context.Context, *UserPermissionEnabledRequest) (*UserPermissionEnabledResponse, error)
	GetUserAllPermission(context.Context, *UserAllPermissionRequest) (*UserAllPermissionResponse, error)
	DeleteUserPermissionByUserID(context.Context, *DeleteUserPermissionByUserIDRequest) (*EmptyResponse, error)
	UserPermList(context.Context, *UserPermListRequest) (*UserPermListResponse, error)
	DeleteUserDepartmentByUserID(context.Context, *DeleteUserDepartmentByUserIDRequest) (*EmptyResponse, error)
	SetUserPermission(context.Context, *SetUserPermissionRequest) (*EmptyResponse, error)
	SetAdminPermissionByUser(context.Context, *SetAdminPermissionByUserRequest) (*EmptyResponse, error)
	SetAdminPermission(context.Context, *SetAdminPermissionRequest) (*EmptyResponse, error)
	mustEmbedUnimplementedPermissionServer()
}

// UnimplementedPermissionServer must be embedded to have forward compatible implementations.
type UnimplementedPermissionServer struct {
}

func (UnimplementedPermissionServer) GetVisitedHistory(context.Context, *GetVisitedHistoryRequest) (*GetVisitedHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVisitedHistory not implemented")
}
func (UnimplementedPermissionServer) AdminControllerPermissionIDs(context.Context, *AdminControllerPermissionIDsRequest) (*AdminControllerPermissionIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminControllerPermissionIDs not implemented")
}
func (UnimplementedPermissionServer) AdminPermissions(context.Context, *AdminPermissionsRequest) (*AdminPermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminPermissions not implemented")
}
func (UnimplementedPermissionServer) ModifyVisited(context.Context, *VisitedRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyVisited not implemented")
}
func (UnimplementedPermissionServer) GetAdminPermissionEnabled(context.Context, *GetAdminPermissionEnabledRequest) (*GetAdminPermissionEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAdminPermissionEnabled not implemented")
}
func (UnimplementedPermissionServer) AdminPermissionInfoList(context.Context, *AdminPermissionInfoListRequest) (*AdminPermissionInfoListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminPermissionInfoList not implemented")
}
func (UnimplementedPermissionServer) GetUserPermissionAffectList(context.Context, *EmptyRequest) (*GetUserPermissionAffectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPermissionAffectList not implemented")
}
func (UnimplementedPermissionServer) UserPermissionConfigList(context.Context, *UserPermissionConfigListRequest) (*UserPermissionConfigListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserPermissionConfigList not implemented")
}
func (UnimplementedPermissionServer) GetUserPermissionEnabled(context.Context, *UserPermissionEnabledRequest) (*UserPermissionEnabledResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPermissionEnabled not implemented")
}
func (UnimplementedPermissionServer) GetUserAllPermission(context.Context, *UserAllPermissionRequest) (*UserAllPermissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserAllPermission not implemented")
}
func (UnimplementedPermissionServer) DeleteUserPermissionByUserID(context.Context, *DeleteUserPermissionByUserIDRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserPermissionByUserID not implemented")
}
func (UnimplementedPermissionServer) UserPermList(context.Context, *UserPermListRequest) (*UserPermListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserPermList not implemented")
}
func (UnimplementedPermissionServer) DeleteUserDepartmentByUserID(context.Context, *DeleteUserDepartmentByUserIDRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserDepartmentByUserID not implemented")
}
func (UnimplementedPermissionServer) SetUserPermission(context.Context, *SetUserPermissionRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserPermission not implemented")
}
func (UnimplementedPermissionServer) SetAdminPermissionByUser(context.Context, *SetAdminPermissionByUserRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAdminPermissionByUser not implemented")
}
func (UnimplementedPermissionServer) SetAdminPermission(context.Context, *SetAdminPermissionRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAdminPermission not implemented")
}
func (UnimplementedPermissionServer) mustEmbedUnimplementedPermissionServer() {}

// UnsafePermissionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PermissionServer will
// result in compilation errors.
type UnsafePermissionServer interface {
	mustEmbedUnimplementedPermissionServer()
}

func RegisterPermissionServer(s grpc.ServiceRegistrar, srv PermissionServer) {
	s.RegisterService(&Permission_ServiceDesc, srv)
}

func _Permission_GetVisitedHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVisitedHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).GetVisitedHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_GetVisitedHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).GetVisitedHistory(ctx, req.(*GetVisitedHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_AdminControllerPermissionIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminControllerPermissionIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).AdminControllerPermissionIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_AdminControllerPermissionIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).AdminControllerPermissionIDs(ctx, req.(*AdminControllerPermissionIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_AdminPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).AdminPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_AdminPermissions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).AdminPermissions(ctx, req.(*AdminPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_ModifyVisited_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VisitedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).ModifyVisited(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_ModifyVisited_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).ModifyVisited(ctx, req.(*VisitedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_GetAdminPermissionEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAdminPermissionEnabledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).GetAdminPermissionEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_GetAdminPermissionEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).GetAdminPermissionEnabled(ctx, req.(*GetAdminPermissionEnabledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_AdminPermissionInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminPermissionInfoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).AdminPermissionInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_AdminPermissionInfoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).AdminPermissionInfoList(ctx, req.(*AdminPermissionInfoListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_GetUserPermissionAffectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).GetUserPermissionAffectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_GetUserPermissionAffectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).GetUserPermissionAffectList(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_UserPermissionConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserPermissionConfigListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).UserPermissionConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_UserPermissionConfigList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).UserPermissionConfigList(ctx, req.(*UserPermissionConfigListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_GetUserPermissionEnabled_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserPermissionEnabledRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).GetUserPermissionEnabled(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_GetUserPermissionEnabled_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).GetUserPermissionEnabled(ctx, req.(*UserPermissionEnabledRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_GetUserAllPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAllPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).GetUserAllPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_GetUserAllPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).GetUserAllPermission(ctx, req.(*UserAllPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_DeleteUserPermissionByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserPermissionByUserIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).DeleteUserPermissionByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_DeleteUserPermissionByUserID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).DeleteUserPermissionByUserID(ctx, req.(*DeleteUserPermissionByUserIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_UserPermList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserPermListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).UserPermList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_UserPermList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).UserPermList(ctx, req.(*UserPermListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_DeleteUserDepartmentByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserDepartmentByUserIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).DeleteUserDepartmentByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_DeleteUserDepartmentByUserID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).DeleteUserDepartmentByUserID(ctx, req.(*DeleteUserDepartmentByUserIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_SetUserPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).SetUserPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_SetUserPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).SetUserPermission(ctx, req.(*SetUserPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_SetAdminPermissionByUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAdminPermissionByUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).SetAdminPermissionByUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_SetAdminPermissionByUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).SetAdminPermissionByUser(ctx, req.(*SetAdminPermissionByUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Permission_SetAdminPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAdminPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServer).SetAdminPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Permission_SetAdminPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServer).SetAdminPermission(ctx, req.(*SetAdminPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Permission_ServiceDesc is the grpc.ServiceDesc for Permission service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Permission_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "permission.Permission",
	HandlerType: (*PermissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVisitedHistory",
			Handler:    _Permission_GetVisitedHistory_Handler,
		},
		{
			MethodName: "AdminControllerPermissionIDs",
			Handler:    _Permission_AdminControllerPermissionIDs_Handler,
		},
		{
			MethodName: "AdminPermissions",
			Handler:    _Permission_AdminPermissions_Handler,
		},
		{
			MethodName: "ModifyVisited",
			Handler:    _Permission_ModifyVisited_Handler,
		},
		{
			MethodName: "GetAdminPermissionEnabled",
			Handler:    _Permission_GetAdminPermissionEnabled_Handler,
		},
		{
			MethodName: "AdminPermissionInfoList",
			Handler:    _Permission_AdminPermissionInfoList_Handler,
		},
		{
			MethodName: "GetUserPermissionAffectList",
			Handler:    _Permission_GetUserPermissionAffectList_Handler,
		},
		{
			MethodName: "UserPermissionConfigList",
			Handler:    _Permission_UserPermissionConfigList_Handler,
		},
		{
			MethodName: "GetUserPermissionEnabled",
			Handler:    _Permission_GetUserPermissionEnabled_Handler,
		},
		{
			MethodName: "GetUserAllPermission",
			Handler:    _Permission_GetUserAllPermission_Handler,
		},
		{
			MethodName: "DeleteUserPermissionByUserID",
			Handler:    _Permission_DeleteUserPermissionByUserID_Handler,
		},
		{
			MethodName: "UserPermList",
			Handler:    _Permission_UserPermList_Handler,
		},
		{
			MethodName: "DeleteUserDepartmentByUserID",
			Handler:    _Permission_DeleteUserDepartmentByUserID_Handler,
		},
		{
			MethodName: "SetUserPermission",
			Handler:    _Permission_SetUserPermission_Handler,
		},
		{
			MethodName: "SetAdminPermissionByUser",
			Handler:    _Permission_SetAdminPermissionByUser_Handler,
		},
		{
			MethodName: "SetAdminPermission",
			Handler:    _Permission_SetAdminPermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "permission.proto",
}
