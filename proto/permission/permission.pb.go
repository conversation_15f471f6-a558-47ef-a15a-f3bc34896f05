// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v3.19.4
// source: permission.proto

package permission

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BoolValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value bool `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{0}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{1}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type GetVisitedHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdminId uint32 `protobuf:"varint,1,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
}

func (x *GetVisitedHistoryRequest) Reset() {
	*x = GetVisitedHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVisitedHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVisitedHistoryRequest) ProtoMessage() {}

func (x *GetVisitedHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVisitedHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetVisitedHistoryRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{2}
}

func (x *GetVisitedHistoryRequest) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

type GetVisitedHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VisitedHistoryDetail []*VisitedHistoryDetail `protobuf:"bytes,1,rep,name=visited_history_detail,json=visitedHistoryDetail,proto3" json:"visited_history_detail,omitempty"`
}

func (x *GetVisitedHistoryResponse) Reset() {
	*x = GetVisitedHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVisitedHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVisitedHistoryResponse) ProtoMessage() {}

func (x *GetVisitedHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVisitedHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetVisitedHistoryResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{3}
}

func (x *GetVisitedHistoryResponse) GetVisitedHistoryDetail() []*VisitedHistoryDetail {
	if x != nil {
		return x.VisitedHistoryDetail
	}
	return nil
}

type VisitedHistoryDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Dict  string `protobuf:"bytes,2,opt,name=dict,proto3" json:"dict,omitempty"`
	IsAi  bool   `protobuf:"varint,3,opt,name=is_ai,json=isAi,proto3" json:"is_ai,omitempty"`
	IsNew bool   `protobuf:"varint,4,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
}

func (x *VisitedHistoryDetail) Reset() {
	*x = VisitedHistoryDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisitedHistoryDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisitedHistoryDetail) ProtoMessage() {}

func (x *VisitedHistoryDetail) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisitedHistoryDetail.ProtoReflect.Descriptor instead.
func (*VisitedHistoryDetail) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{4}
}

func (x *VisitedHistoryDetail) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VisitedHistoryDetail) GetDict() string {
	if x != nil {
		return x.Dict
	}
	return ""
}

func (x *VisitedHistoryDetail) GetIsAi() bool {
	if x != nil {
		return x.IsAi
	}
	return false
}

func (x *VisitedHistoryDetail) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

type AdminControllerPermissionIDsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ControllerName string `protobuf:"bytes,1,opt,name=controller_name,json=controllerName,proto3" json:"controller_name,omitempty"`
}

func (x *AdminControllerPermissionIDsRequest) Reset() {
	*x = AdminControllerPermissionIDsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminControllerPermissionIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminControllerPermissionIDsRequest) ProtoMessage() {}

func (x *AdminControllerPermissionIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminControllerPermissionIDsRequest.ProtoReflect.Descriptor instead.
func (*AdminControllerPermissionIDsRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{5}
}

func (x *AdminControllerPermissionIDsRequest) GetControllerName() string {
	if x != nil {
		return x.ControllerName
	}
	return ""
}

type AdminControllerPermissionIDsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId []uint32 `protobuf:"varint,1,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
}

func (x *AdminControllerPermissionIDsResponse) Reset() {
	*x = AdminControllerPermissionIDsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminControllerPermissionIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminControllerPermissionIDsResponse) ProtoMessage() {}

func (x *AdminControllerPermissionIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminControllerPermissionIDsResponse.ProtoReflect.Descriptor instead.
func (*AdminControllerPermissionIDsResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{6}
}

func (x *AdminControllerPermissionIDsResponse) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

type AdminPermissionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdminId      []uint32   `protobuf:"varint,1,rep,packed,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
	PermissionId []uint32   `protobuf:"varint,2,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	Enable       *BoolValue `protobuf:"bytes,3,opt,name=enable,proto3" json:"enable,omitempty"`
	Favorite     *BoolValue `protobuf:"bytes,4,opt,name=favorite,proto3" json:"favorite,omitempty"`
	Modify       *BoolValue `protobuf:"bytes,5,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *AdminPermissionsRequest) Reset() {
	*x = AdminPermissionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPermissionsRequest) ProtoMessage() {}

func (x *AdminPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPermissionsRequest.ProtoReflect.Descriptor instead.
func (*AdminPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{7}
}

func (x *AdminPermissionsRequest) GetAdminId() []uint32 {
	if x != nil {
		return x.AdminId
	}
	return nil
}

func (x *AdminPermissionsRequest) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

func (x *AdminPermissionsRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *AdminPermissionsRequest) GetFavorite() *BoolValue {
	if x != nil {
		return x.Favorite
	}
	return nil
}

func (x *AdminPermissionsRequest) GetModify() *BoolValue {
	if x != nil {
		return x.Modify
	}
	return nil
}

type AdminPermissionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionInfo []*AdminPermissionInfo `protobuf:"bytes,1,rep,name=permission_info,json=permissionInfo,proto3" json:"permission_info,omitempty"`
}

func (x *AdminPermissionsResponse) Reset() {
	*x = AdminPermissionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminPermissionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPermissionsResponse) ProtoMessage() {}

func (x *AdminPermissionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPermissionsResponse.ProtoReflect.Descriptor instead.
func (*AdminPermissionsResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{8}
}

func (x *AdminPermissionsResponse) GetPermissionInfo() []*AdminPermissionInfo {
	if x != nil {
		return x.PermissionInfo
	}
	return nil
}

type AdminPermissionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdminId      uint32 `protobuf:"varint,1,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
	PermissionId uint32 `protobuf:"varint,2,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	Enable       bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
	Favorite     bool   `protobuf:"varint,4,opt,name=favorite,proto3" json:"favorite,omitempty"`
	FavoriteSort uint32 `protobuf:"varint,5,opt,name=favorite_sort,json=favoriteSort,proto3" json:"favorite_sort,omitempty"`
	Modify       bool   `protobuf:"varint,6,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *AdminPermissionInfo) Reset() {
	*x = AdminPermissionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminPermissionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPermissionInfo) ProtoMessage() {}

func (x *AdminPermissionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPermissionInfo.ProtoReflect.Descriptor instead.
func (*AdminPermissionInfo) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{9}
}

func (x *AdminPermissionInfo) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *AdminPermissionInfo) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

func (x *AdminPermissionInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AdminPermissionInfo) GetFavorite() bool {
	if x != nil {
		return x.Favorite
	}
	return false
}

func (x *AdminPermissionInfo) GetFavoriteSort() uint32 {
	if x != nil {
		return x.FavoriteSort
	}
	return 0
}

func (x *AdminPermissionInfo) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

type VisitedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdminId      uint32 `protobuf:"varint,1,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
	PermissionId uint32 `protobuf:"varint,2,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
}

func (x *VisitedRequest) Reset() {
	*x = VisitedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisitedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisitedRequest) ProtoMessage() {}

func (x *VisitedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisitedRequest.ProtoReflect.Descriptor instead.
func (*VisitedRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{10}
}

func (x *VisitedRequest) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *VisitedRequest) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{11}
}

type GetAdminPermissionEnabledRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable *BoolValue `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *GetAdminPermissionEnabledRequest) Reset() {
	*x = GetAdminPermissionEnabledRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdminPermissionEnabledRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdminPermissionEnabledRequest) ProtoMessage() {}

func (x *GetAdminPermissionEnabledRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdminPermissionEnabledRequest.ProtoReflect.Descriptor instead.
func (*GetAdminPermissionEnabledRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{12}
}

func (x *GetAdminPermissionEnabledRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

type AdminPermissionEnabled struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ParentId uint32 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Type     string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Name     string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Weight   uint32 `protobuf:"varint,5,opt,name=weight,proto3" json:"weight,omitempty"`
	Note     string `protobuf:"bytes,6,opt,name=note,proto3" json:"note,omitempty"`
	Dict     string `protobuf:"bytes,7,opt,name=dict,proto3" json:"dict,omitempty"`
	Rd3Dict  string `protobuf:"bytes,8,opt,name=rd3_dict,json=rd3Dict,proto3" json:"rd3_dict,omitempty"`
	Partial  bool   `protobuf:"varint,9,opt,name=partial,proto3" json:"partial,omitempty"`
	Modify   bool   `protobuf:"varint,10,opt,name=modify,proto3" json:"modify,omitempty"`
	OldPerm  bool   `protobuf:"varint,11,opt,name=old_perm,json=oldPerm,proto3" json:"old_perm,omitempty"`
	Child    bool   `protobuf:"varint,12,opt,name=child,proto3" json:"child,omitempty"`
	Enable   bool   `protobuf:"varint,13,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *AdminPermissionEnabled) Reset() {
	*x = AdminPermissionEnabled{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminPermissionEnabled) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPermissionEnabled) ProtoMessage() {}

func (x *AdminPermissionEnabled) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPermissionEnabled.ProtoReflect.Descriptor instead.
func (*AdminPermissionEnabled) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{13}
}

func (x *AdminPermissionEnabled) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AdminPermissionEnabled) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *AdminPermissionEnabled) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AdminPermissionEnabled) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AdminPermissionEnabled) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *AdminPermissionEnabled) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *AdminPermissionEnabled) GetDict() string {
	if x != nil {
		return x.Dict
	}
	return ""
}

func (x *AdminPermissionEnabled) GetRd3Dict() string {
	if x != nil {
		return x.Rd3Dict
	}
	return ""
}

func (x *AdminPermissionEnabled) GetPartial() bool {
	if x != nil {
		return x.Partial
	}
	return false
}

func (x *AdminPermissionEnabled) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

func (x *AdminPermissionEnabled) GetOldPerm() bool {
	if x != nil {
		return x.OldPerm
	}
	return false
}

func (x *AdminPermissionEnabled) GetChild() bool {
	if x != nil {
		return x.Child
	}
	return false
}

func (x *AdminPermissionEnabled) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type GetAdminPermissionEnabledResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Menu    []*AdminPermissionEnabled `protobuf:"bytes,1,rep,name=menu,proto3" json:"menu,omitempty"`
	Special []*AdminPermissionEnabled `protobuf:"bytes,2,rep,name=special,proto3" json:"special,omitempty"`
	General []*AdminPermissionEnabled `protobuf:"bytes,3,rep,name=general,proto3" json:"general,omitempty"`
}

func (x *GetAdminPermissionEnabledResponse) Reset() {
	*x = GetAdminPermissionEnabledResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdminPermissionEnabledResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdminPermissionEnabledResponse) ProtoMessage() {}

func (x *GetAdminPermissionEnabledResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdminPermissionEnabledResponse.ProtoReflect.Descriptor instead.
func (*GetAdminPermissionEnabledResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{14}
}

func (x *GetAdminPermissionEnabledResponse) GetMenu() []*AdminPermissionEnabled {
	if x != nil {
		return x.Menu
	}
	return nil
}

func (x *GetAdminPermissionEnabledResponse) GetSpecial() []*AdminPermissionEnabled {
	if x != nil {
		return x.Special
	}
	return nil
}

func (x *GetAdminPermissionEnabledResponse) GetGeneral() []*AdminPermissionEnabled {
	if x != nil {
		return x.General
	}
	return nil
}

type AdminPermissionInfoListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId []uint32     `protobuf:"varint,1,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	ParentId     []uint32     `protobuf:"varint,2,rep,packed,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Type         []string     `protobuf:"bytes,3,rep,name=type,proto3" json:"type,omitempty"`
	Name         []string     `protobuf:"bytes,4,rep,name=name,proto3" json:"name,omitempty"`
	Enable       *BoolValue   `protobuf:"bytes,5,opt,name=enable,proto3" json:"enable,omitempty"`
	Weight       *Uint32Value `protobuf:"bytes,6,opt,name=weight,proto3" json:"weight,omitempty"`
	Partial      *BoolValue   `protobuf:"bytes,7,opt,name=partial,proto3" json:"partial,omitempty"`
	Modify       *BoolValue   `protobuf:"bytes,8,opt,name=modify,proto3" json:"modify,omitempty"`
	OldPerm      *BoolValue   `protobuf:"bytes,9,opt,name=old_perm,json=oldPerm,proto3" json:"old_perm,omitempty"`
	Maintenance  *BoolValue   `protobuf:"bytes,10,opt,name=maintenance,proto3" json:"maintenance,omitempty"`
}

func (x *AdminPermissionInfoListRequest) Reset() {
	*x = AdminPermissionInfoListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminPermissionInfoListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPermissionInfoListRequest) ProtoMessage() {}

func (x *AdminPermissionInfoListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPermissionInfoListRequest.ProtoReflect.Descriptor instead.
func (*AdminPermissionInfoListRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{15}
}

func (x *AdminPermissionInfoListRequest) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetParentId() []uint32 {
	if x != nil {
		return x.ParentId
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetType() []string {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetName() []string {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetWeight() *Uint32Value {
	if x != nil {
		return x.Weight
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetPartial() *BoolValue {
	if x != nil {
		return x.Partial
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetModify() *BoolValue {
	if x != nil {
		return x.Modify
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetOldPerm() *BoolValue {
	if x != nil {
		return x.OldPerm
	}
	return nil
}

func (x *AdminPermissionInfoListRequest) GetMaintenance() *BoolValue {
	if x != nil {
		return x.Maintenance
	}
	return nil
}

type AdminPermissionInfoListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionInfo []*PermissionListInfo `protobuf:"bytes,1,rep,name=permission_info,json=permissionInfo,proto3" json:"permission_info,omitempty"`
}

func (x *AdminPermissionInfoListResponse) Reset() {
	*x = AdminPermissionInfoListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminPermissionInfoListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPermissionInfoListResponse) ProtoMessage() {}

func (x *AdminPermissionInfoListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPermissionInfoListResponse.ProtoReflect.Descriptor instead.
func (*AdminPermissionInfoListResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{16}
}

func (x *AdminPermissionInfoListResponse) GetPermissionInfo() []*PermissionListInfo {
	if x != nil {
		return x.PermissionInfo
	}
	return nil
}

type PermissionListInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId  uint32                `protobuf:"varint,1,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	ParentId      uint32                `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Type          string                `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Name          string                `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Enable        bool                  `protobuf:"varint,5,opt,name=enable,proto3" json:"enable,omitempty"`
	Weight        uint32                `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	Note          string                `protobuf:"bytes,7,opt,name=note,proto3" json:"note,omitempty"`
	Dict          string                `protobuf:"bytes,8,opt,name=dict,proto3" json:"dict,omitempty"`
	Rd3Dict       string                `protobuf:"bytes,9,opt,name=rd3_dict,json=rd3Dict,proto3" json:"rd3_dict,omitempty"`
	Sort          uint32                `protobuf:"varint,10,opt,name=sort,proto3" json:"sort,omitempty"`
	Partial       bool                  `protobuf:"varint,11,opt,name=partial,proto3" json:"partial,omitempty"`
	Modify        bool                  `protobuf:"varint,12,opt,name=modify,proto3" json:"modify,omitempty"`
	OldPerm       bool                  `protobuf:"varint,13,opt,name=old_perm,json=oldPerm,proto3" json:"old_perm,omitempty"`
	Maintenance   bool                  `protobuf:"varint,14,opt,name=maintenance,proto3" json:"maintenance,omitempty"`
	DomainOnly    bool                  `protobuf:"varint,15,opt,name=domain_only,json=domainOnly,proto3" json:"domain_only,omitempty"`
	TopSubDomain  bool                  `protobuf:"varint,16,opt,name=top_sub_domain,json=topSubDomain,proto3" json:"top_sub_domain,omitempty"`
	OpenNew       bool                  `protobuf:"varint,17,opt,name=open_new,json=openNew,proto3" json:"open_new,omitempty"`
	Strict        bool                  `protobuf:"varint,18,opt,name=strict,proto3" json:"strict,omitempty"`
	Modifiable    bool                  `protobuf:"varint,19,opt,name=modifiable,proto3" json:"modifiable,omitempty"`
	HierarchyPerm bool                  `protobuf:"varint,20,opt,name=hierarchy_perm,json=hierarchyPerm,proto3" json:"hierarchy_perm,omitempty"`
	Extra         *PermissionExtra      `protobuf:"bytes,21,opt,name=extra,proto3" json:"extra,omitempty"`
	RolePerm      []*PermissionRolePerm `protobuf:"bytes,22,rep,name=role_perm,json=rolePerm,proto3" json:"role_perm,omitempty"`
	RoleSettable  []uint32              `protobuf:"varint,23,rep,packed,name=role_settable,json=roleSettable,proto3" json:"role_settable,omitempty"`
}

func (x *PermissionListInfo) Reset() {
	*x = PermissionListInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionListInfo) ProtoMessage() {}

func (x *PermissionListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionListInfo.ProtoReflect.Descriptor instead.
func (*PermissionListInfo) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{17}
}

func (x *PermissionListInfo) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

func (x *PermissionListInfo) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *PermissionListInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PermissionListInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PermissionListInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *PermissionListInfo) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *PermissionListInfo) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *PermissionListInfo) GetDict() string {
	if x != nil {
		return x.Dict
	}
	return ""
}

func (x *PermissionListInfo) GetRd3Dict() string {
	if x != nil {
		return x.Rd3Dict
	}
	return ""
}

func (x *PermissionListInfo) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *PermissionListInfo) GetPartial() bool {
	if x != nil {
		return x.Partial
	}
	return false
}

func (x *PermissionListInfo) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

func (x *PermissionListInfo) GetOldPerm() bool {
	if x != nil {
		return x.OldPerm
	}
	return false
}

func (x *PermissionListInfo) GetMaintenance() bool {
	if x != nil {
		return x.Maintenance
	}
	return false
}

func (x *PermissionListInfo) GetDomainOnly() bool {
	if x != nil {
		return x.DomainOnly
	}
	return false
}

func (x *PermissionListInfo) GetTopSubDomain() bool {
	if x != nil {
		return x.TopSubDomain
	}
	return false
}

func (x *PermissionListInfo) GetOpenNew() bool {
	if x != nil {
		return x.OpenNew
	}
	return false
}

func (x *PermissionListInfo) GetStrict() bool {
	if x != nil {
		return x.Strict
	}
	return false
}

func (x *PermissionListInfo) GetModifiable() bool {
	if x != nil {
		return x.Modifiable
	}
	return false
}

func (x *PermissionListInfo) GetHierarchyPerm() bool {
	if x != nil {
		return x.HierarchyPerm
	}
	return false
}

func (x *PermissionListInfo) GetExtra() *PermissionExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *PermissionListInfo) GetRolePerm() []*PermissionRolePerm {
	if x != nil {
		return x.RolePerm
	}
	return nil
}

func (x *PermissionListInfo) GetRoleSettable() []uint32 {
	if x != nil {
		return x.RoleSettable
	}
	return nil
}

type PermissionExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon       string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	File       string `protobuf:"bytes,2,opt,name=file,proto3" json:"file,omitempty"`
	Host       string `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`
	Qstr       string `protobuf:"bytes,4,opt,name=qstr,proto3" json:"qstr,omitempty"`
	Route      string `protobuf:"bytes,5,opt,name=route,proto3" json:"route,omitempty"`
	Conditions string `protobuf:"bytes,6,opt,name=conditions,proto3" json:"conditions,omitempty"`
	ApiName    string `protobuf:"bytes,7,opt,name=api_name,json=apiName,proto3" json:"api_name,omitempty"`
	ApiType    string `protobuf:"bytes,8,opt,name=api_type,json=apiType,proto3" json:"api_type,omitempty"`
}

func (x *PermissionExtra) Reset() {
	*x = PermissionExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionExtra) ProtoMessage() {}

func (x *PermissionExtra) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionExtra.ProtoReflect.Descriptor instead.
func (*PermissionExtra) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{18}
}

func (x *PermissionExtra) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *PermissionExtra) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *PermissionExtra) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *PermissionExtra) GetQstr() string {
	if x != nil {
		return x.Qstr
	}
	return ""
}

func (x *PermissionExtra) GetRoute() string {
	if x != nil {
		return x.Route
	}
	return ""
}

func (x *PermissionExtra) GetConditions() string {
	if x != nil {
		return x.Conditions
	}
	return ""
}

func (x *PermissionExtra) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

func (x *PermissionExtra) GetApiType() string {
	if x != nil {
		return x.ApiType
	}
	return ""
}

type PermissionRolePerm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoleId uint32 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	View   bool   `protobuf:"varint,2,opt,name=view,proto3" json:"view,omitempty"`
	Modify bool   `protobuf:"varint,3,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *PermissionRolePerm) Reset() {
	*x = PermissionRolePerm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionRolePerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionRolePerm) ProtoMessage() {}

func (x *PermissionRolePerm) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionRolePerm.ProtoReflect.Descriptor instead.
func (*PermissionRolePerm) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{19}
}

func (x *PermissionRolePerm) GetRoleId() uint32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *PermissionRolePerm) GetView() bool {
	if x != nil {
		return x.View
	}
	return false
}

func (x *PermissionRolePerm) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{20}
}

type GetUserPermissionAffectListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List map[uint32]*PermissionAffectList `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetUserPermissionAffectListResponse) Reset() {
	*x = GetUserPermissionAffectListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPermissionAffectListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPermissionAffectListResponse) ProtoMessage() {}

func (x *GetUserPermissionAffectListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPermissionAffectListResponse.ProtoReflect.Descriptor instead.
func (*GetUserPermissionAffectListResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{21}
}

func (x *GetUserPermissionAffectListResponse) GetList() map[uint32]*PermissionAffectList {
	if x != nil {
		return x.List
	}
	return nil
}

type PermissionAffectList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  *PermissionAffectListData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Count uint32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *PermissionAffectList) Reset() {
	*x = PermissionAffectList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionAffectList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionAffectList) ProtoMessage() {}

func (x *PermissionAffectList) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionAffectList.ProtoReflect.Descriptor instead.
func (*PermissionAffectList) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{22}
}

func (x *PermissionAffectList) GetData() *PermissionAffectListData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *PermissionAffectList) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type PermissionAffectListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Menu    map[uint32]*PermissionAffectData `protobuf:"bytes,1,rep,name=menu,proto3" json:"menu,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Special map[uint32]*PermissionAffectData `protobuf:"bytes,2,rep,name=special,proto3" json:"special,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Other   []*PermissionAffectData          `protobuf:"bytes,3,rep,name=other,proto3" json:"other,omitempty"`
}

func (x *PermissionAffectListData) Reset() {
	*x = PermissionAffectListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionAffectListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionAffectListData) ProtoMessage() {}

func (x *PermissionAffectListData) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionAffectListData.ProtoReflect.Descriptor instead.
func (*PermissionAffectListData) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{23}
}

func (x *PermissionAffectListData) GetMenu() map[uint32]*PermissionAffectData {
	if x != nil {
		return x.Menu
	}
	return nil
}

func (x *PermissionAffectListData) GetSpecial() map[uint32]*PermissionAffectData {
	if x != nil {
		return x.Special
	}
	return nil
}

func (x *PermissionAffectListData) GetOther() []*PermissionAffectData {
	if x != nil {
		return x.Other
	}
	return nil
}

type PermissionAffectData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Parent   uint32                  `protobuf:"varint,2,opt,name=parent,proto3" json:"parent,omitempty"`
	Type     string                  `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Name     string                  `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Dict     string                  `protobuf:"bytes,5,opt,name=dict,proto3" json:"dict,omitempty"`
	AffectId uint32                  `protobuf:"varint,6,opt,name=affect_id,json=affectId,proto3" json:"affect_id,omitempty"`
	Note     string                  `protobuf:"bytes,7,opt,name=note,proto3" json:"note,omitempty"`
	Sort     uint32                  `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
	Child    []*PermissionAffectData `protobuf:"bytes,9,rep,name=child,proto3" json:"child,omitempty"`
}

func (x *PermissionAffectData) Reset() {
	*x = PermissionAffectData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionAffectData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionAffectData) ProtoMessage() {}

func (x *PermissionAffectData) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionAffectData.ProtoReflect.Descriptor instead.
func (*PermissionAffectData) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{24}
}

func (x *PermissionAffectData) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PermissionAffectData) GetParent() uint32 {
	if x != nil {
		return x.Parent
	}
	return 0
}

func (x *PermissionAffectData) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PermissionAffectData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PermissionAffectData) GetDict() string {
	if x != nil {
		return x.Dict
	}
	return ""
}

func (x *PermissionAffectData) GetAffectId() uint32 {
	if x != nil {
		return x.AffectId
	}
	return 0
}

func (x *PermissionAffectData) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *PermissionAffectData) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *PermissionAffectData) GetChild() []*PermissionAffectData {
	if x != nil {
		return x.Child
	}
	return nil
}

type UserPermissionConfigListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId  []uint32   `protobuf:"varint,1,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	ParentId      uint32     `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Enable        *BoolValue `protobuf:"bytes,3,opt,name=enable,proto3" json:"enable,omitempty"`
	HierarchyPerm *BoolValue `protobuf:"bytes,4,opt,name=hierarchy_perm,json=hierarchyPerm,proto3" json:"hierarchy_perm,omitempty"`
	Type          []string   `protobuf:"bytes,5,rep,name=type,proto3" json:"type,omitempty"`
	Name          []string   `protobuf:"bytes,6,rep,name=name,proto3" json:"name,omitempty"`
}

func (x *UserPermissionConfigListRequest) Reset() {
	*x = UserPermissionConfigListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermissionConfigListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermissionConfigListRequest) ProtoMessage() {}

func (x *UserPermissionConfigListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermissionConfigListRequest.ProtoReflect.Descriptor instead.
func (*UserPermissionConfigListRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{25}
}

func (x *UserPermissionConfigListRequest) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

func (x *UserPermissionConfigListRequest) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *UserPermissionConfigListRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

func (x *UserPermissionConfigListRequest) GetHierarchyPerm() *BoolValue {
	if x != nil {
		return x.HierarchyPerm
	}
	return nil
}

func (x *UserPermissionConfigListRequest) GetType() []string {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *UserPermissionConfigListRequest) GetName() []string {
	if x != nil {
		return x.Name
	}
	return nil
}

type UserPermissionConfigListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionConfig []*UserPermissionConfig `protobuf:"bytes,1,rep,name=permission_config,json=permissionConfig,proto3" json:"permission_config,omitempty"`
}

func (x *UserPermissionConfigListResponse) Reset() {
	*x = UserPermissionConfigListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermissionConfigListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermissionConfigListResponse) ProtoMessage() {}

func (x *UserPermissionConfigListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermissionConfigListResponse.ProtoReflect.Descriptor instead.
func (*UserPermissionConfigListResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{26}
}

func (x *UserPermissionConfigListResponse) GetPermissionConfig() []*UserPermissionConfig {
	if x != nil {
		return x.PermissionConfig
	}
	return nil
}

type UserPermissionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId  uint32                `protobuf:"varint,1,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	ParentId      uint32                `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Type          string                `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Name          string                `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Note          string                `protobuf:"bytes,5,opt,name=note,proto3" json:"note,omitempty"`
	Dict          string                `protobuf:"bytes,6,opt,name=dict,proto3" json:"dict,omitempty"`
	Rd3Dict       string                `protobuf:"bytes,7,opt,name=rd3_dict,json=rd3Dict,proto3" json:"rd3_dict,omitempty"`
	Enable        bool                  `protobuf:"varint,8,opt,name=enable,proto3" json:"enable,omitempty"`
	Sort          uint32                `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty"`
	DomainOnly    bool                  `protobuf:"varint,10,opt,name=domain_only,json=domainOnly,proto3" json:"domain_only,omitempty"`
	TopSubDomain  bool                  `protobuf:"varint,11,opt,name=top_sub_domain,json=topSubDomain,proto3" json:"top_sub_domain,omitempty"`
	OpenNew       bool                  `protobuf:"varint,12,opt,name=open_new,json=openNew,proto3" json:"open_new,omitempty"`
	Strict        bool                  `protobuf:"varint,13,opt,name=strict,proto3" json:"strict,omitempty"`
	Modifiable    bool                  `protobuf:"varint,14,opt,name=modifiable,proto3" json:"modifiable,omitempty"`
	OldPerm       bool                  `protobuf:"varint,15,opt,name=old_perm,json=oldPerm,proto3" json:"old_perm,omitempty"`
	HierarchyPerm bool                  `protobuf:"varint,16,opt,name=hierarchy_perm,json=hierarchyPerm,proto3" json:"hierarchy_perm,omitempty"`
	Maintenance   bool                  `protobuf:"varint,17,opt,name=maintenance,proto3" json:"maintenance,omitempty"`
	Extra         *PermissionExtra      `protobuf:"bytes,18,opt,name=extra,proto3" json:"extra,omitempty"`
	RolePerm      []*PermissionRolePerm `protobuf:"bytes,19,rep,name=role_perm,json=rolePerm,proto3" json:"role_perm,omitempty"`
	RoleSettable  []uint32              `protobuf:"varint,20,rep,packed,name=role_settable,json=roleSettable,proto3" json:"role_settable,omitempty"`
}

func (x *UserPermissionConfig) Reset() {
	*x = UserPermissionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermissionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermissionConfig) ProtoMessage() {}

func (x *UserPermissionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermissionConfig.ProtoReflect.Descriptor instead.
func (*UserPermissionConfig) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{27}
}

func (x *UserPermissionConfig) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

func (x *UserPermissionConfig) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *UserPermissionConfig) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserPermissionConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserPermissionConfig) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *UserPermissionConfig) GetDict() string {
	if x != nil {
		return x.Dict
	}
	return ""
}

func (x *UserPermissionConfig) GetRd3Dict() string {
	if x != nil {
		return x.Rd3Dict
	}
	return ""
}

func (x *UserPermissionConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *UserPermissionConfig) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *UserPermissionConfig) GetDomainOnly() bool {
	if x != nil {
		return x.DomainOnly
	}
	return false
}

func (x *UserPermissionConfig) GetTopSubDomain() bool {
	if x != nil {
		return x.TopSubDomain
	}
	return false
}

func (x *UserPermissionConfig) GetOpenNew() bool {
	if x != nil {
		return x.OpenNew
	}
	return false
}

func (x *UserPermissionConfig) GetStrict() bool {
	if x != nil {
		return x.Strict
	}
	return false
}

func (x *UserPermissionConfig) GetModifiable() bool {
	if x != nil {
		return x.Modifiable
	}
	return false
}

func (x *UserPermissionConfig) GetOldPerm() bool {
	if x != nil {
		return x.OldPerm
	}
	return false
}

func (x *UserPermissionConfig) GetHierarchyPerm() bool {
	if x != nil {
		return x.HierarchyPerm
	}
	return false
}

func (x *UserPermissionConfig) GetMaintenance() bool {
	if x != nil {
		return x.Maintenance
	}
	return false
}

func (x *UserPermissionConfig) GetExtra() *PermissionExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *UserPermissionConfig) GetRolePerm() []*PermissionRolePerm {
	if x != nil {
		return x.RolePerm
	}
	return nil
}

func (x *UserPermissionConfig) GetRoleSettable() []uint32 {
	if x != nil {
		return x.RoleSettable
	}
	return nil
}

type UserPermissionEnabledRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId []uint32 `protobuf:"varint,1,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
}

func (x *UserPermissionEnabledRequest) Reset() {
	*x = UserPermissionEnabledRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermissionEnabledRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermissionEnabledRequest) ProtoMessage() {}

func (x *UserPermissionEnabledRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermissionEnabledRequest.ProtoReflect.Descriptor instead.
func (*UserPermissionEnabledRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{28}
}

func (x *UserPermissionEnabledRequest) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

type UserPermissionEnabledResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId []uint32 `protobuf:"varint,1,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
}

func (x *UserPermissionEnabledResponse) Reset() {
	*x = UserPermissionEnabledResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermissionEnabledResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermissionEnabledResponse) ProtoMessage() {}

func (x *UserPermissionEnabledResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermissionEnabledResponse.ProtoReflect.Descriptor instead.
func (*UserPermissionEnabledResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{29}
}

func (x *UserPermissionEnabledResponse) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

type UserAllPermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllParents   []uint32   `protobuf:"varint,1,rep,packed,name=all_parents,json=allParents,proto3" json:"all_parents,omitempty"`
	UserId       uint32     `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Role         uint32     `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	Sub          bool       `protobuf:"varint,4,opt,name=sub,proto3" json:"sub,omitempty"`
	PermissionId []uint32   `protobuf:"varint,5,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	Enable       *BoolValue `protobuf:"bytes,6,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UserAllPermissionRequest) Reset() {
	*x = UserAllPermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAllPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAllPermissionRequest) ProtoMessage() {}

func (x *UserAllPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAllPermissionRequest.ProtoReflect.Descriptor instead.
func (*UserAllPermissionRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{30}
}

func (x *UserAllPermissionRequest) GetAllParents() []uint32 {
	if x != nil {
		return x.AllParents
	}
	return nil
}

func (x *UserAllPermissionRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserAllPermissionRequest) GetRole() uint32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *UserAllPermissionRequest) GetSub() bool {
	if x != nil {
		return x.Sub
	}
	return false
}

func (x *UserAllPermissionRequest) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

func (x *UserAllPermissionRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

type UserAllPermissionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*UserPermission `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *UserAllPermissionResponse) Reset() {
	*x = UserAllPermissionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAllPermissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAllPermissionResponse) ProtoMessage() {}

func (x *UserAllPermissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAllPermissionResponse.ProtoReflect.Descriptor instead.
func (*UserAllPermissionResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{31}
}

func (x *UserAllPermissionResponse) GetData() []*UserPermission {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RoleId       uint32 `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	PermissionId uint32 `protobuf:"varint,3,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	Enable       bool   `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`
	Modify       bool   `protobuf:"varint,5,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *UserPermission) Reset() {
	*x = UserPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermission) ProtoMessage() {}

func (x *UserPermission) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermission.ProtoReflect.Descriptor instead.
func (*UserPermission) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{32}
}

func (x *UserPermission) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserPermission) GetRoleId() uint32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *UserPermission) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

func (x *UserPermission) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *UserPermission) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

type DeleteUserPermissionByUserIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *DeleteUserPermissionByUserIDRequest) Reset() {
	*x = DeleteUserPermissionByUserIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserPermissionByUserIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserPermissionByUserIDRequest) ProtoMessage() {}

func (x *DeleteUserPermissionByUserIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserPermissionByUserIDRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserPermissionByUserIDRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{33}
}

func (x *DeleteUserPermissionByUserIDRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type UserPermListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       []uint32   `protobuf:"varint,1,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PermissionId []uint32   `protobuf:"varint,2,rep,packed,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	RoleId       []uint32   `protobuf:"varint,3,rep,packed,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Enable       *BoolValue `protobuf:"bytes,4,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UserPermListRequest) Reset() {
	*x = UserPermListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermListRequest) ProtoMessage() {}

func (x *UserPermListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermListRequest.ProtoReflect.Descriptor instead.
func (*UserPermListRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{34}
}

func (x *UserPermListRequest) GetUserId() []uint32 {
	if x != nil {
		return x.UserId
	}
	return nil
}

func (x *UserPermListRequest) GetPermissionId() []uint32 {
	if x != nil {
		return x.PermissionId
	}
	return nil
}

func (x *UserPermListRequest) GetRoleId() []uint32 {
	if x != nil {
		return x.RoleId
	}
	return nil
}

func (x *UserPermListRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

type UserPermListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*UserPerm `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *UserPermListResponse) Reset() {
	*x = UserPermListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPermListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPermListResponse) ProtoMessage() {}

func (x *UserPermListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPermListResponse.ProtoReflect.Descriptor instead.
func (*UserPermListResponse) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{35}
}

func (x *UserPermListResponse) GetData() []*UserPerm {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserPerm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       uint32 `protobuf:"varint,1,opt,name=domain,proto3" json:"domain,omitempty"`
	UserId       uint32 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RoleId       uint32 `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	PermissionId uint32 `protobuf:"varint,4,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	Enable       bool   `protobuf:"varint,5,opt,name=enable,proto3" json:"enable,omitempty"`
	Modify       bool   `protobuf:"varint,6,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *UserPerm) Reset() {
	*x = UserPerm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPerm) ProtoMessage() {}

func (x *UserPerm) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPerm.ProtoReflect.Descriptor instead.
func (*UserPerm) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{36}
}

func (x *UserPerm) GetDomain() uint32 {
	if x != nil {
		return x.Domain
	}
	return 0
}

func (x *UserPerm) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserPerm) GetRoleId() uint32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *UserPerm) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

func (x *UserPerm) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *UserPerm) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

type DeleteUserDepartmentByUserIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *DeleteUserDepartmentByUserIDRequest) Reset() {
	*x = DeleteUserDepartmentByUserIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserDepartmentByUserIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserDepartmentByUserIDRequest) ProtoMessage() {}

func (x *DeleteUserDepartmentByUserIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserDepartmentByUserIDRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserDepartmentByUserIDRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{37}
}

func (x *DeleteUserDepartmentByUserIDRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type SetUserPermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserPermissions []*UserPerm `protobuf:"bytes,1,rep,name=user_permissions,json=userPermissions,proto3" json:"user_permissions,omitempty"`
}

func (x *SetUserPermissionRequest) Reset() {
	*x = SetUserPermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUserPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserPermissionRequest) ProtoMessage() {}

func (x *SetUserPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserPermissionRequest.ProtoReflect.Descriptor instead.
func (*SetUserPermissionRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{38}
}

func (x *SetUserPermissionRequest) GetUserPermissions() []*UserPerm {
	if x != nil {
		return x.UserPermissions
	}
	return nil
}

type SetAdminPermissionByUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdminId       uint32           `protobuf:"varint,1,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
	SetPermission []*SetPermission `protobuf:"bytes,2,rep,name=set_permission,json=setPermission,proto3" json:"set_permission,omitempty"`
	DeleteId      []uint32         `protobuf:"varint,3,rep,packed,name=delete_id,json=deleteId,proto3" json:"delete_id,omitempty"`
}

func (x *SetAdminPermissionByUserRequest) Reset() {
	*x = SetAdminPermissionByUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAdminPermissionByUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAdminPermissionByUserRequest) ProtoMessage() {}

func (x *SetAdminPermissionByUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAdminPermissionByUserRequest.ProtoReflect.Descriptor instead.
func (*SetAdminPermissionByUserRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{39}
}

func (x *SetAdminPermissionByUserRequest) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *SetAdminPermissionByUserRequest) GetSetPermission() []*SetPermission {
	if x != nil {
		return x.SetPermission
	}
	return nil
}

func (x *SetAdminPermissionByUserRequest) GetDeleteId() []uint32 {
	if x != nil {
		return x.DeleteId
	}
	return nil
}

type SetPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId uint32 `protobuf:"varint,1,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	Modify       bool   `protobuf:"varint,2,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *SetPermission) Reset() {
	*x = SetPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPermission) ProtoMessage() {}

func (x *SetPermission) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPermission.ProtoReflect.Descriptor instead.
func (*SetPermission) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{40}
}

func (x *SetPermission) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

func (x *SetPermission) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

type SetAdminPermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*AdminPermission `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SetAdminPermissionRequest) Reset() {
	*x = SetAdminPermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAdminPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAdminPermissionRequest) ProtoMessage() {}

func (x *SetAdminPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAdminPermissionRequest.ProtoReflect.Descriptor instead.
func (*SetAdminPermissionRequest) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{41}
}

func (x *SetAdminPermissionRequest) GetList() []*AdminPermission {
	if x != nil {
		return x.List
	}
	return nil
}

type AdminPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdminId      uint32     `protobuf:"varint,1,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
	PermissionId uint32     `protobuf:"varint,2,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	Modify       *BoolValue `protobuf:"bytes,3,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *AdminPermission) Reset() {
	*x = AdminPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_permission_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPermission) ProtoMessage() {}

func (x *AdminPermission) ProtoReflect() protoreflect.Message {
	mi := &file_permission_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPermission.ProtoReflect.Descriptor instead.
func (*AdminPermission) Descriptor() ([]byte, []int) {
	return file_permission_proto_rawDescGZIP(), []int{42}
}

func (x *AdminPermission) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *AdminPermission) GetPermissionId() uint32 {
	if x != nil {
		return x.PermissionId
	}
	return 0
}

func (x *AdminPermission) GetModify() *BoolValue {
	if x != nil {
		return x.Modify
	}
	return nil
}

var File_permission_proto protoreflect.FileDescriptor

var file_permission_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x21,
	0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x23, 0x0a, 0x0b, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x35, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73,
	0x69, 0x74, 0x65, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x73, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x16, 0x76, 0x69,
	0x73, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x14, 0x76, 0x69,
	0x73, 0x69, 0x74, 0x65, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x22, 0x66, 0x0a, 0x14, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69,
	0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x63, 0x74, 0x12, 0x13,
	0x0a, 0x05, 0x69, 0x73, 0x5f, 0x61, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x69,
	0x73, 0x41, 0x69, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x22, 0x4e, 0x0a, 0x23, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4b, 0x0a, 0x24, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xea, 0x01, 0x0a, 0x17, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x66, 0x61, 0x76,
	0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x22, 0x64, 0x0a, 0x18, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x48, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xc6, 0x01, 0x0a, 0x13, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x61,
	0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x61,
	0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69,
	0x74, 0x65, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x66,
	0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x22, 0x50, 0x0a, 0x0e, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x51, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xc3, 0x02, 0x0a, 0x16, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x63, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x63, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x64, 0x33,
	0x5f, 0x64, 0x69, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x64, 0x33,
	0x44, 0x69, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6f, 0x6c, 0x64, 0x50, 0x65, 0x72,
	0x6d, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22,
	0xd7, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x6d, 0x65, 0x6e, 0x75, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x04, 0x6d, 0x65, 0x6e, 0x75, 0x12, 0x3c, 0x0a,
	0x07, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x52, 0x07, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x12, 0x3c, 0x0a, 0x07, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x52, 0x07, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x22, 0xb5, 0x03, 0x0a, 0x1e, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61,
	0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07,
	0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x12, 0x30, 0x0a, 0x08, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x07, 0x6f, 0x6c, 0x64, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x37, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x6a, 0x0a, 0x1f, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xca, 0x05,
	0x0a, 0x12, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x69, 0x63, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x64, 0x33, 0x5f, 0x64, 0x69,
	0x63, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x64, 0x33, 0x44, 0x69, 0x63,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x6c, 0x64, 0x5f, 0x70,
	0x65, 0x72, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6f, 0x6c, 0x64, 0x50, 0x65,
	0x72, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6f,
	0x6e, 0x6c, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f, 0x70, 0x5f, 0x73, 0x75, 0x62,
	0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x74,
	0x6f, 0x70, 0x53, 0x75, 0x62, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x6e, 0x65, 0x77, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x4e, 0x65, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x6d,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68,
	0x79, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x31, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x3b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x70, 0x65, 0x72, 0x6d, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x52, 0x08, 0x72, 0x6f, 0x6c,
	0x65, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x6f,
	0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xcd, 0x01, 0x0a, 0x0f, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x71, 0x73,
	0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x71, 0x73, 0x74, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x54, 0x79, 0x70, 0x65, 0x22, 0x59, 0x0a, 0x12, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d,
	0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x76, 0x69, 0x65, 0x77, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xcf, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x59, 0x0a, 0x09,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x66, 0x0a, 0x14, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x9c, 0x03, 0x0a, 0x18, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x04,
	0x6d, 0x65, 0x6e, 0x75, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x4d, 0x65, 0x6e, 0x75, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6d, 0x65, 0x6e, 0x75,
	0x12, 0x4b, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x12, 0x36, 0x0a,
	0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x1a, 0x59, 0x0a, 0x09, 0x4d, 0x65, 0x6e, 0x75, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x5c, 0x0a, 0x0c, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf7,
	0x01, 0x0a, 0x14, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x63, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x63, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x12, 0x36, 0x0a, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x05, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x22, 0xf8, 0x01, 0x0a, 0x1f, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3c, 0x0a,
	0x0e, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x68, 0x69,
	0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x71, 0x0a, 0x20, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x82, 0x05, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x69, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x63,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x64, 0x33, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x64, 0x33, 0x44, 0x69, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f, 0x70,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x74, 0x6f, 0x70, 0x53, 0x75, 0x62, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x6e, 0x65, 0x77, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x4e, 0x65, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6f, 0x6c, 0x64, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x25, 0x0a,
	0x0e, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x79,
	0x50, 0x65, 0x72, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x3b, 0x0a, 0x09, 0x72, 0x6f, 0x6c,
	0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x52, 0x08, 0x72, 0x6f,
	0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x72,
	0x6f, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x43, 0x0a, 0x1c, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x22, 0x44, 0x0a, 0x1d, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xce, 0x01, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x6c, 0x6c, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x50, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x75, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03,
	0x73, 0x75, 0x62, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x4b, 0x0a, 0x19, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x6c, 0x6c, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x97, 0x01, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x22, 0x3e,
	0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x9b,
	0x01, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x40, 0x0a, 0x14,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa9,
	0x01, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x72,
	0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x22, 0x3e, 0x0a, 0x23, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x18, 0x53, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x1f, 0x53, 0x65, 0x74, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0e, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x74, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x65, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x64, 0x22, 0x4c, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x22, 0x4c, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x80, 0x01, 0x0a, 0x0f, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x32, 0xfb, 0x0c, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65,
	0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x24, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x1c, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x12, 0x2f, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x6c, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x10, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x12, 0x1a, 0x2e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x78, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2c, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x17, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x6f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x28, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x63, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x6c, 0x6c, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x2f, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x51, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1f, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x12, 0x2f, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x54, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x2b, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x12, 0x53, 0x65,
	0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x25, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65,
	0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x12, 0x5a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_permission_proto_rawDescOnce sync.Once
	file_permission_proto_rawDescData = file_permission_proto_rawDesc
)

func file_permission_proto_rawDescGZIP() []byte {
	file_permission_proto_rawDescOnce.Do(func() {
		file_permission_proto_rawDescData = protoimpl.X.CompressGZIP(file_permission_proto_rawDescData)
	})
	return file_permission_proto_rawDescData
}

var file_permission_proto_msgTypes = make([]protoimpl.MessageInfo, 46)
var file_permission_proto_goTypes = []any{
	(*BoolValue)(nil),                            // 0: permission.BoolValue
	(*Uint32Value)(nil),                          // 1: permission.Uint32Value
	(*GetVisitedHistoryRequest)(nil),             // 2: permission.GetVisitedHistoryRequest
	(*GetVisitedHistoryResponse)(nil),            // 3: permission.GetVisitedHistoryResponse
	(*VisitedHistoryDetail)(nil),                 // 4: permission.VisitedHistoryDetail
	(*AdminControllerPermissionIDsRequest)(nil),  // 5: permission.AdminControllerPermissionIDsRequest
	(*AdminControllerPermissionIDsResponse)(nil), // 6: permission.AdminControllerPermissionIDsResponse
	(*AdminPermissionsRequest)(nil),              // 7: permission.AdminPermissionsRequest
	(*AdminPermissionsResponse)(nil),             // 8: permission.AdminPermissionsResponse
	(*AdminPermissionInfo)(nil),                  // 9: permission.AdminPermissionInfo
	(*VisitedRequest)(nil),                       // 10: permission.VisitedRequest
	(*EmptyResponse)(nil),                        // 11: permission.EmptyResponse
	(*GetAdminPermissionEnabledRequest)(nil),     // 12: permission.GetAdminPermissionEnabledRequest
	(*AdminPermissionEnabled)(nil),               // 13: permission.AdminPermissionEnabled
	(*GetAdminPermissionEnabledResponse)(nil),    // 14: permission.GetAdminPermissionEnabledResponse
	(*AdminPermissionInfoListRequest)(nil),       // 15: permission.AdminPermissionInfoListRequest
	(*AdminPermissionInfoListResponse)(nil),      // 16: permission.AdminPermissionInfoListResponse
	(*PermissionListInfo)(nil),                   // 17: permission.PermissionListInfo
	(*PermissionExtra)(nil),                      // 18: permission.PermissionExtra
	(*PermissionRolePerm)(nil),                   // 19: permission.PermissionRolePerm
	(*EmptyRequest)(nil),                         // 20: permission.EmptyRequest
	(*GetUserPermissionAffectListResponse)(nil),  // 21: permission.GetUserPermissionAffectListResponse
	(*PermissionAffectList)(nil),                 // 22: permission.PermissionAffectList
	(*PermissionAffectListData)(nil),             // 23: permission.PermissionAffectListData
	(*PermissionAffectData)(nil),                 // 24: permission.PermissionAffectData
	(*UserPermissionConfigListRequest)(nil),      // 25: permission.UserPermissionConfigListRequest
	(*UserPermissionConfigListResponse)(nil),     // 26: permission.UserPermissionConfigListResponse
	(*UserPermissionConfig)(nil),                 // 27: permission.UserPermissionConfig
	(*UserPermissionEnabledRequest)(nil),         // 28: permission.UserPermissionEnabledRequest
	(*UserPermissionEnabledResponse)(nil),        // 29: permission.UserPermissionEnabledResponse
	(*UserAllPermissionRequest)(nil),             // 30: permission.UserAllPermissionRequest
	(*UserAllPermissionResponse)(nil),            // 31: permission.UserAllPermissionResponse
	(*UserPermission)(nil),                       // 32: permission.UserPermission
	(*DeleteUserPermissionByUserIDRequest)(nil),  // 33: permission.DeleteUserPermissionByUserIDRequest
	(*UserPermListRequest)(nil),                  // 34: permission.UserPermListRequest
	(*UserPermListResponse)(nil),                 // 35: permission.UserPermListResponse
	(*UserPerm)(nil),                             // 36: permission.UserPerm
	(*DeleteUserDepartmentByUserIDRequest)(nil),  // 37: permission.DeleteUserDepartmentByUserIDRequest
	(*SetUserPermissionRequest)(nil),             // 38: permission.SetUserPermissionRequest
	(*SetAdminPermissionByUserRequest)(nil),      // 39: permission.SetAdminPermissionByUserRequest
	(*SetPermission)(nil),                        // 40: permission.SetPermission
	(*SetAdminPermissionRequest)(nil),            // 41: permission.SetAdminPermissionRequest
	(*AdminPermission)(nil),                      // 42: permission.AdminPermission
	nil,                                          // 43: permission.GetUserPermissionAffectListResponse.ListEntry
	nil,                                          // 44: permission.PermissionAffectListData.MenuEntry
	nil,                                          // 45: permission.PermissionAffectListData.SpecialEntry
}
var file_permission_proto_depIdxs = []int32{
	4,  // 0: permission.GetVisitedHistoryResponse.visited_history_detail:type_name -> permission.VisitedHistoryDetail
	0,  // 1: permission.AdminPermissionsRequest.enable:type_name -> permission.BoolValue
	0,  // 2: permission.AdminPermissionsRequest.favorite:type_name -> permission.BoolValue
	0,  // 3: permission.AdminPermissionsRequest.modify:type_name -> permission.BoolValue
	9,  // 4: permission.AdminPermissionsResponse.permission_info:type_name -> permission.AdminPermissionInfo
	0,  // 5: permission.GetAdminPermissionEnabledRequest.enable:type_name -> permission.BoolValue
	13, // 6: permission.GetAdminPermissionEnabledResponse.menu:type_name -> permission.AdminPermissionEnabled
	13, // 7: permission.GetAdminPermissionEnabledResponse.special:type_name -> permission.AdminPermissionEnabled
	13, // 8: permission.GetAdminPermissionEnabledResponse.general:type_name -> permission.AdminPermissionEnabled
	0,  // 9: permission.AdminPermissionInfoListRequest.enable:type_name -> permission.BoolValue
	1,  // 10: permission.AdminPermissionInfoListRequest.weight:type_name -> permission.Uint32Value
	0,  // 11: permission.AdminPermissionInfoListRequest.partial:type_name -> permission.BoolValue
	0,  // 12: permission.AdminPermissionInfoListRequest.modify:type_name -> permission.BoolValue
	0,  // 13: permission.AdminPermissionInfoListRequest.old_perm:type_name -> permission.BoolValue
	0,  // 14: permission.AdminPermissionInfoListRequest.maintenance:type_name -> permission.BoolValue
	17, // 15: permission.AdminPermissionInfoListResponse.permission_info:type_name -> permission.PermissionListInfo
	18, // 16: permission.PermissionListInfo.extra:type_name -> permission.PermissionExtra
	19, // 17: permission.PermissionListInfo.role_perm:type_name -> permission.PermissionRolePerm
	43, // 18: permission.GetUserPermissionAffectListResponse.list:type_name -> permission.GetUserPermissionAffectListResponse.ListEntry
	23, // 19: permission.PermissionAffectList.data:type_name -> permission.PermissionAffectListData
	44, // 20: permission.PermissionAffectListData.menu:type_name -> permission.PermissionAffectListData.MenuEntry
	45, // 21: permission.PermissionAffectListData.special:type_name -> permission.PermissionAffectListData.SpecialEntry
	24, // 22: permission.PermissionAffectListData.other:type_name -> permission.PermissionAffectData
	24, // 23: permission.PermissionAffectData.child:type_name -> permission.PermissionAffectData
	0,  // 24: permission.UserPermissionConfigListRequest.enable:type_name -> permission.BoolValue
	0,  // 25: permission.UserPermissionConfigListRequest.hierarchy_perm:type_name -> permission.BoolValue
	27, // 26: permission.UserPermissionConfigListResponse.permission_config:type_name -> permission.UserPermissionConfig
	18, // 27: permission.UserPermissionConfig.extra:type_name -> permission.PermissionExtra
	19, // 28: permission.UserPermissionConfig.role_perm:type_name -> permission.PermissionRolePerm
	0,  // 29: permission.UserAllPermissionRequest.enable:type_name -> permission.BoolValue
	32, // 30: permission.UserAllPermissionResponse.data:type_name -> permission.UserPermission
	0,  // 31: permission.UserPermListRequest.enable:type_name -> permission.BoolValue
	36, // 32: permission.UserPermListResponse.data:type_name -> permission.UserPerm
	36, // 33: permission.SetUserPermissionRequest.user_permissions:type_name -> permission.UserPerm
	40, // 34: permission.SetAdminPermissionByUserRequest.set_permission:type_name -> permission.SetPermission
	42, // 35: permission.SetAdminPermissionRequest.list:type_name -> permission.AdminPermission
	0,  // 36: permission.AdminPermission.modify:type_name -> permission.BoolValue
	22, // 37: permission.GetUserPermissionAffectListResponse.ListEntry.value:type_name -> permission.PermissionAffectList
	24, // 38: permission.PermissionAffectListData.MenuEntry.value:type_name -> permission.PermissionAffectData
	24, // 39: permission.PermissionAffectListData.SpecialEntry.value:type_name -> permission.PermissionAffectData
	2,  // 40: permission.Permission.GetVisitedHistory:input_type -> permission.GetVisitedHistoryRequest
	5,  // 41: permission.Permission.AdminControllerPermissionIDs:input_type -> permission.AdminControllerPermissionIDsRequest
	7,  // 42: permission.Permission.AdminPermissions:input_type -> permission.AdminPermissionsRequest
	10, // 43: permission.Permission.ModifyVisited:input_type -> permission.VisitedRequest
	12, // 44: permission.Permission.GetAdminPermissionEnabled:input_type -> permission.GetAdminPermissionEnabledRequest
	15, // 45: permission.Permission.AdminPermissionInfoList:input_type -> permission.AdminPermissionInfoListRequest
	20, // 46: permission.Permission.GetUserPermissionAffectList:input_type -> permission.EmptyRequest
	25, // 47: permission.Permission.UserPermissionConfigList:input_type -> permission.UserPermissionConfigListRequest
	28, // 48: permission.Permission.GetUserPermissionEnabled:input_type -> permission.UserPermissionEnabledRequest
	30, // 49: permission.Permission.GetUserAllPermission:input_type -> permission.UserAllPermissionRequest
	33, // 50: permission.Permission.DeleteUserPermissionByUserID:input_type -> permission.DeleteUserPermissionByUserIDRequest
	34, // 51: permission.Permission.UserPermList:input_type -> permission.UserPermListRequest
	37, // 52: permission.Permission.DeleteUserDepartmentByUserID:input_type -> permission.DeleteUserDepartmentByUserIDRequest
	38, // 53: permission.Permission.SetUserPermission:input_type -> permission.SetUserPermissionRequest
	39, // 54: permission.Permission.SetAdminPermissionByUser:input_type -> permission.SetAdminPermissionByUserRequest
	41, // 55: permission.Permission.SetAdminPermission:input_type -> permission.SetAdminPermissionRequest
	3,  // 56: permission.Permission.GetVisitedHistory:output_type -> permission.GetVisitedHistoryResponse
	6,  // 57: permission.Permission.AdminControllerPermissionIDs:output_type -> permission.AdminControllerPermissionIDsResponse
	8,  // 58: permission.Permission.AdminPermissions:output_type -> permission.AdminPermissionsResponse
	11, // 59: permission.Permission.ModifyVisited:output_type -> permission.EmptyResponse
	14, // 60: permission.Permission.GetAdminPermissionEnabled:output_type -> permission.GetAdminPermissionEnabledResponse
	16, // 61: permission.Permission.AdminPermissionInfoList:output_type -> permission.AdminPermissionInfoListResponse
	21, // 62: permission.Permission.GetUserPermissionAffectList:output_type -> permission.GetUserPermissionAffectListResponse
	26, // 63: permission.Permission.UserPermissionConfigList:output_type -> permission.UserPermissionConfigListResponse
	29, // 64: permission.Permission.GetUserPermissionEnabled:output_type -> permission.UserPermissionEnabledResponse
	31, // 65: permission.Permission.GetUserAllPermission:output_type -> permission.UserAllPermissionResponse
	11, // 66: permission.Permission.DeleteUserPermissionByUserID:output_type -> permission.EmptyResponse
	35, // 67: permission.Permission.UserPermList:output_type -> permission.UserPermListResponse
	11, // 68: permission.Permission.DeleteUserDepartmentByUserID:output_type -> permission.EmptyResponse
	11, // 69: permission.Permission.SetUserPermission:output_type -> permission.EmptyResponse
	11, // 70: permission.Permission.SetAdminPermissionByUser:output_type -> permission.EmptyResponse
	11, // 71: permission.Permission.SetAdminPermission:output_type -> permission.EmptyResponse
	56, // [56:72] is the sub-list for method output_type
	40, // [40:56] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_permission_proto_init() }
func file_permission_proto_init() {
	if File_permission_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_permission_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BoolValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetVisitedHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetVisitedHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*VisitedHistoryDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*AdminControllerPermissionIDsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*AdminControllerPermissionIDsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*AdminPermissionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*AdminPermissionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*AdminPermissionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*VisitedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetAdminPermissionEnabledRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*AdminPermissionEnabled); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetAdminPermissionEnabledResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*AdminPermissionInfoListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*AdminPermissionInfoListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*PermissionListInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*PermissionExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*PermissionRolePerm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserPermissionAffectListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*PermissionAffectList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*PermissionAffectListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*PermissionAffectData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermissionConfigListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermissionConfigListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermissionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermissionEnabledRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermissionEnabledResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*UserAllPermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*UserAllPermissionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteUserPermissionByUserIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*UserPermListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*UserPerm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteUserDepartmentByUserIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*SetUserPermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*SetAdminPermissionByUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*SetPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*SetAdminPermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_permission_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*AdminPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_permission_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   46,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_permission_proto_goTypes,
		DependencyIndexes: file_permission_proto_depIdxs,
		MessageInfos:      file_permission_proto_msgTypes,
	}.Build()
	File_permission_proto = out.File
	file_permission_proto_rawDesc = nil
	file_permission_proto_goTypes = nil
	file_permission_proto_depIdxs = nil
}
