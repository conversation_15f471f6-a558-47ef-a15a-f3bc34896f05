// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.19.4
// source: sportgame.proto

package sportgame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GameListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GameListRequest) Reset() {
	*x = GameListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameListRequest) ProtoMessage() {}

func (x *GameListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameListRequest.ProtoReflect.Descriptor instead.
func (*GameListRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{0}
}

func (x *GameListRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GameListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameInfo []*GameInfo `protobuf:"bytes,1,rep,name=game_info,json=gameInfo,proto3" json:"game_info,omitempty"`
}

func (x *GameListResponse) Reset() {
	*x = GameListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameListResponse) ProtoMessage() {}

func (x *GameListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameListResponse.ProtoReflect.Descriptor instead.
func (*GameListResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{1}
}

func (x *GameListResponse) GetGameInfo() []*GameInfo {
	if x != nil {
		return x.GameInfo
	}
	return nil
}

type GameInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GameInfo) Reset() {
	*x = GameInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameInfo) ProtoMessage() {}

func (x *GameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameInfo.ProtoReflect.Descriptor instead.
func (*GameInfo) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{2}
}

func (x *GameInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GameInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{3}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type StringValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StringValue) Reset() {
	*x = StringValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringValue) ProtoMessage() {}

func (x *StringValue) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringValue.ProtoReflect.Descriptor instead.
func (*StringValue) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{4}
}

func (x *StringValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GameLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang         string       `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
	Device       *Uint32Value `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`
	Ots          *StringValue `protobuf:"bytes,3,opt,name=ots,proto3" json:"ots,omitempty"`
	EnterPage    *StringValue `protobuf:"bytes,4,opt,name=enter_page,json=enterPage,proto3" json:"enter_page,omitempty"`
	ExitOption   *Uint32Value `protobuf:"bytes,5,opt,name=exit_option,json=exitOption,proto3" json:"exit_option,omitempty"`
	ExitUrlParam *StringValue `protobuf:"bytes,6,opt,name=exit_url_param,json=exitUrlParam,proto3" json:"exit_url_param,omitempty"`
}

func (x *GameLinkRequest) Reset() {
	*x = GameLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameLinkRequest) ProtoMessage() {}

func (x *GameLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameLinkRequest.ProtoReflect.Descriptor instead.
func (*GameLinkRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{5}
}

func (x *GameLinkRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *GameLinkRequest) GetDevice() *Uint32Value {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *GameLinkRequest) GetOts() *StringValue {
	if x != nil {
		return x.Ots
	}
	return nil
}

func (x *GameLinkRequest) GetEnterPage() *StringValue {
	if x != nil {
		return x.EnterPage
	}
	return nil
}

func (x *GameLinkRequest) GetExitOption() *Uint32Value {
	if x != nil {
		return x.ExitOption
	}
	return nil
}

func (x *GameLinkRequest) GetExitUrlParam() *StringValue {
	if x != nil {
		return x.ExitUrlParam
	}
	return nil
}

type GameLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *GameLinkResponse) Reset() {
	*x = GameLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameLinkResponse) ProtoMessage() {}

func (x *GameLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameLinkResponse.ProtoReflect.Descriptor instead.
func (*GameLinkResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{6}
}

func (x *GameLinkResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type SportWagersByBetTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32       `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	StartTime string       `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   string       `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AgentId   uint32       `protobuf:"varint,4,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	Page      uint32       `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit uint32       `protobuf:"varint,6,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	GameId    *Uint32Value `protobuf:"bytes,7,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
}

func (x *SportWagersByBetTimeRequest) Reset() {
	*x = SportWagersByBetTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SportWagersByBetTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SportWagersByBetTimeRequest) ProtoMessage() {}

func (x *SportWagersByBetTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SportWagersByBetTimeRequest.ProtoReflect.Descriptor instead.
func (*SportWagersByBetTimeRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{7}
}

func (x *SportWagersByBetTimeRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SportWagersByBetTimeRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SportWagersByBetTimeRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SportWagersByBetTimeRequest) GetAgentId() uint32 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *SportWagersByBetTimeRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SportWagersByBetTimeRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *SportWagersByBetTimeRequest) GetGameId() *Uint32Value {
	if x != nil {
		return x.GameId
	}
	return nil
}

type SportWagersByModifiedTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32       `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	StartTime string       `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   string       `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page      uint32       `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit uint32       `protobuf:"varint,5,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	AgentId   *Uint32Value `protobuf:"bytes,6,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	GameId    *Uint32Value `protobuf:"bytes,7,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
}

func (x *SportWagersByModifiedTimeRequest) Reset() {
	*x = SportWagersByModifiedTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SportWagersByModifiedTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SportWagersByModifiedTimeRequest) ProtoMessage() {}

func (x *SportWagersByModifiedTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SportWagersByModifiedTimeRequest.ProtoReflect.Descriptor instead.
func (*SportWagersByModifiedTimeRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{8}
}

func (x *SportWagersByModifiedTimeRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SportWagersByModifiedTimeRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SportWagersByModifiedTimeRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SportWagersByModifiedTimeRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SportWagersByModifiedTimeRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *SportWagersByModifiedTimeRequest) GetAgentId() *Uint32Value {
	if x != nil {
		return x.AgentId
	}
	return nil
}

func (x *SportWagersByModifiedTimeRequest) GetGameId() *Uint32Value {
	if x != nil {
		return x.GameId
	}
	return nil
}

type Wagers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WagersId       uint64       `protobuf:"varint,1,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	RoundDate      string       `protobuf:"bytes,2,opt,name=round_date,json=roundDate,proto3" json:"round_date,omitempty"`
	ModifiedDate   string       `protobuf:"bytes,3,opt,name=modified_date,json=modifiedDate,proto3" json:"modified_date,omitempty"`
	MatchDate      string       `protobuf:"bytes,4,opt,name=match_date,json=matchDate,proto3" json:"match_date,omitempty"`
	GameId         uint32       `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Currency       string       `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	ExchangeRate   float64      `protobuf:"fixed64,7,opt,name=exchange_rate,json=exchangeRate,proto3" json:"exchange_rate,omitempty"`
	ResultStatus   int32        `protobuf:"varint,8,opt,name=result_status,json=resultStatus,proto3" json:"result_status,omitempty"`
	BetAmount      float64      `protobuf:"fixed64,9,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	AccountDate    *StringValue `protobuf:"bytes,10,opt,name=account_date,json=accountDate,proto3" json:"account_date,omitempty"`
	Platform       uint32       `protobuf:"varint,11,opt,name=platform,proto3" json:"platform,omitempty"`
	Payoff         float64      `protobuf:"fixed64,12,opt,name=payoff,proto3" json:"payoff,omitempty"`
	Commissionable float64      `protobuf:"fixed64,13,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
	UserId         uint32       `protobuf:"varint,14,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *Wagers) Reset() {
	*x = Wagers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Wagers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Wagers) ProtoMessage() {}

func (x *Wagers) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Wagers.ProtoReflect.Descriptor instead.
func (*Wagers) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{9}
}

func (x *Wagers) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *Wagers) GetRoundDate() string {
	if x != nil {
		return x.RoundDate
	}
	return ""
}

func (x *Wagers) GetModifiedDate() string {
	if x != nil {
		return x.ModifiedDate
	}
	return ""
}

func (x *Wagers) GetMatchDate() string {
	if x != nil {
		return x.MatchDate
	}
	return ""
}

func (x *Wagers) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *Wagers) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Wagers) GetExchangeRate() float64 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *Wagers) GetResultStatus() int32 {
	if x != nil {
		return x.ResultStatus
	}
	return 0
}

func (x *Wagers) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *Wagers) GetAccountDate() *StringValue {
	if x != nil {
		return x.AccountDate
	}
	return nil
}

func (x *Wagers) GetPlatform() uint32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *Wagers) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *Wagers) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

func (x *Wagers) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   uint32 `protobuf:"varint,2,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Total       uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPage   uint32 `protobuf:"varint,4,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{10}
}

func (x *Pagination) GetCurrentPage() uint32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *Pagination) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Pagination) GetTotalPage() uint32 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type WagersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wagers     []*Wagers   `protobuf:"bytes,1,rep,name=wagers,proto3" json:"wagers,omitempty"`
	Pagination *Pagination `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *WagersResponse) Reset() {
	*x = WagersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersResponse) ProtoMessage() {}

func (x *WagersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersResponse.ProtoReflect.Descriptor instead.
func (*WagersResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{11}
}

func (x *WagersResponse) GetWagers() []*Wagers {
	if x != nil {
		return x.Wagers
	}
	return nil
}

func (x *WagersResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type SubWagersURLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WagersId uint64 `protobuf:"varint,2,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	Lang     string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *SubWagersURLRequest) Reset() {
	*x = SubWagersURLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubWagersURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubWagersURLRequest) ProtoMessage() {}

func (x *SubWagersURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubWagersURLRequest.ProtoReflect.Descriptor instead.
func (*SubWagersURLRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{12}
}

func (x *SubWagersURLRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SubWagersURLRequest) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *SubWagersURLRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type SubWagersURLResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *SubWagersURLResponse) Reset() {
	*x = SubWagersURLResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubWagersURLResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubWagersURLResponse) ProtoMessage() {}

func (x *SubWagersURLResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubWagersURLResponse.ProtoReflect.Descriptor instead.
func (*SubWagersURLResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{13}
}

func (x *SubWagersURLResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CheckWagersByIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId uint32 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *CheckWagersByIDRequest) Reset() {
	*x = CheckWagersByIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckWagersByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckWagersByIDRequest) ProtoMessage() {}

func (x *CheckWagersByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckWagersByIDRequest.ProtoReflect.Descriptor instead.
func (*CheckWagersByIDRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{14}
}

func (x *CheckWagersByIDRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CheckWagersByIDRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetCategoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetCategoryRequest) Reset() {
	*x = GetCategoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryRequest) ProtoMessage() {}

func (x *GetCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetCategoryRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{15}
}

func (x *GetCategoryRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type Category struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Category) Reset() {
	*x = Category{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Category) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Category) ProtoMessage() {}

func (x *Category) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Category.ProtoReflect.Descriptor instead.
func (*Category) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{16}
}

func (x *Category) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Category) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetCategoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category []*Category `protobuf:"bytes,1,rep,name=category,proto3" json:"category,omitempty"`
}

func (x *GetCategoryResponse) Reset() {
	*x = GetCategoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryResponse) ProtoMessage() {}

func (x *GetCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryResponse.ProtoReflect.Descriptor instead.
func (*GetCategoryResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{17}
}

func (x *GetCategoryResponse) GetCategory() []*Category {
	if x != nil {
		return x.Category
	}
	return nil
}

type GameplayBetLimitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CategoryId uint32 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Lang       string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GameplayBetLimitRequest) Reset() {
	*x = GameplayBetLimitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameplayBetLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameplayBetLimitRequest) ProtoMessage() {}

func (x *GameplayBetLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameplayBetLimitRequest.ProtoReflect.Descriptor instead.
func (*GameplayBetLimitRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{18}
}

func (x *GameplayBetLimitRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GameplayBetLimitRequest) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *GameplayBetLimitRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GroupNamePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *GroupNamePair) Reset() {
	*x = GroupNamePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupNamePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupNamePair) ProtoMessage() {}

func (x *GroupNamePair) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupNamePair.ProtoReflect.Descriptor instead.
func (*GroupNamePair) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{19}
}

func (x *GroupNamePair) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *GroupNamePair) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type BetLimitSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BetLimit  float64 `protobuf:"fixed64,1,opt,name=bet_limit,json=betLimit,proto3" json:"bet_limit,omitempty"`
	GameLimit float64 `protobuf:"fixed64,2,opt,name=game_limit,json=gameLimit,proto3" json:"game_limit,omitempty"`
}

func (x *BetLimitSetting) Reset() {
	*x = BetLimitSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BetLimitSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BetLimitSetting) ProtoMessage() {}

func (x *BetLimitSetting) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BetLimitSetting.ProtoReflect.Descriptor instead.
func (*BetLimitSetting) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{20}
}

func (x *BetLimitSetting) GetBetLimit() float64 {
	if x != nil {
		return x.BetLimit
	}
	return 0
}

func (x *BetLimitSetting) GetGameLimit() float64 {
	if x != nil {
		return x.GameLimit
	}
	return 0
}

type BetLimitSettingPair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string           `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value *BetLimitSetting `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *BetLimitSettingPair) Reset() {
	*x = BetLimitSettingPair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BetLimitSettingPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BetLimitSettingPair) ProtoMessage() {}

func (x *BetLimitSettingPair) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BetLimitSettingPair.ProtoReflect.Descriptor instead.
func (*BetLimitSettingPair) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{21}
}

func (x *BetLimitSettingPair) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *BetLimitSettingPair) GetValue() *BetLimitSetting {
	if x != nil {
		return x.Value
	}
	return nil
}

type GameplayBetLimitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupName []*GroupNamePair       `protobuf:"bytes,1,rep,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Setting   []*BetLimitSettingPair `protobuf:"bytes,2,rep,name=setting,proto3" json:"setting,omitempty"`
}

func (x *GameplayBetLimitResponse) Reset() {
	*x = GameplayBetLimitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameplayBetLimitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameplayBetLimitResponse) ProtoMessage() {}

func (x *GameplayBetLimitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameplayBetLimitResponse.ProtoReflect.Descriptor instead.
func (*GameplayBetLimitResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{22}
}

func (x *GameplayBetLimitResponse) GetGroupName() []*GroupNamePair {
	if x != nil {
		return x.GroupName
	}
	return nil
}

func (x *GameplayBetLimitResponse) GetSetting() []*BetLimitSettingPair {
	if x != nil {
		return x.Setting
	}
	return nil
}

type UnfinishStatisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang   string       `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
	UserId uint32       `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GameId *Uint32Value `protobuf:"bytes,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
}

func (x *UnfinishStatisRequest) Reset() {
	*x = UnfinishStatisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnfinishStatisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfinishStatisRequest) ProtoMessage() {}

func (x *UnfinishStatisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfinishStatisRequest.ProtoReflect.Descriptor instead.
func (*UnfinishStatisRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{23}
}

func (x *UnfinishStatisRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *UnfinishStatisRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UnfinishStatisRequest) GetGameId() *Uint32Value {
	if x != nil {
		return x.GameId
	}
	return nil
}

type UnfinishStatisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameList    []*GameWagersStatis `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	TotalCount  uint32              `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalAmount float64             `protobuf:"fixed64,4,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	Wagers      []*WagersInfo       `protobuf:"bytes,5,rep,name=wagers,proto3" json:"wagers,omitempty"`
}

func (x *UnfinishStatisResponse) Reset() {
	*x = UnfinishStatisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnfinishStatisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfinishStatisResponse) ProtoMessage() {}

func (x *UnfinishStatisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfinishStatisResponse.ProtoReflect.Descriptor instead.
func (*UnfinishStatisResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{24}
}

func (x *UnfinishStatisResponse) GetGameList() []*GameWagersStatis {
	if x != nil {
		return x.GameList
	}
	return nil
}

func (x *UnfinishStatisResponse) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *UnfinishStatisResponse) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *UnfinishStatisResponse) GetWagers() []*WagersInfo {
	if x != nil {
		return x.Wagers
	}
	return nil
}

type GameWagersStatis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Count          uint32  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	BetAmount      float64 `protobuf:"fixed64,4,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Payoff         float64 `protobuf:"fixed64,5,opt,name=payoff,proto3" json:"payoff,omitempty"`
	Commissionable float64 `protobuf:"fixed64,6,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
}

func (x *GameWagersStatis) Reset() {
	*x = GameWagersStatis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameWagersStatis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameWagersStatis) ProtoMessage() {}

func (x *GameWagersStatis) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameWagersStatis.ProtoReflect.Descriptor instead.
func (*GameWagersStatis) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{25}
}

func (x *GameWagersStatis) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GameWagersStatis) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameWagersStatis) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GameWagersStatis) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *GameWagersStatis) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *GameWagersStatis) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

type WagersInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WagersId       uint64  `protobuf:"varint,1,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	SportName      string  `protobuf:"bytes,2,opt,name=sport_name,json=sportName,proto3" json:"sport_name,omitempty"`
	AddDate        string  `protobuf:"bytes,3,opt,name=add_date,json=addDate,proto3" json:"add_date,omitempty"`
	OddType        string  `protobuf:"bytes,4,opt,name=odd_type,json=oddType,proto3" json:"odd_type,omitempty"`
	BetAmount      float64 `protobuf:"fixed64,5,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	BetState       string  `protobuf:"bytes,6,opt,name=bet_state,json=betState,proto3" json:"bet_state,omitempty"`
	Payoff         float64 `protobuf:"fixed64,7,opt,name=payoff,proto3" json:"payoff,omitempty"`
	Commissionable float64 `protobuf:"fixed64,8,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
}

func (x *WagersInfo) Reset() {
	*x = WagersInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersInfo) ProtoMessage() {}

func (x *WagersInfo) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersInfo.ProtoReflect.Descriptor instead.
func (*WagersInfo) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{26}
}

func (x *WagersInfo) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *WagersInfo) GetSportName() string {
	if x != nil {
		return x.SportName
	}
	return ""
}

func (x *WagersInfo) GetAddDate() string {
	if x != nil {
		return x.AddDate
	}
	return ""
}

func (x *WagersInfo) GetOddType() string {
	if x != nil {
		return x.OddType
	}
	return ""
}

func (x *WagersInfo) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *WagersInfo) GetBetState() string {
	if x != nil {
		return x.BetState
	}
	return ""
}

func (x *WagersInfo) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *WagersInfo) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{27}
}

type WagersDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WagersId uint64 `protobuf:"varint,1,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	Lang     string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *WagersDetailRequest) Reset() {
	*x = WagersDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersDetailRequest) ProtoMessage() {}

func (x *WagersDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersDetailRequest.ProtoReflect.Descriptor instead.
func (*WagersDetailRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{28}
}

func (x *WagersDetailRequest) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *WagersDetailRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type WagersDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchDate          string  `protobuf:"bytes,1,opt,name=match_date,json=matchDate,proto3" json:"match_date,omitempty"`
	SportName          string  `protobuf:"bytes,2,opt,name=sport_name,json=sportName,proto3" json:"sport_name,omitempty"`
	CompetitionName    string  `protobuf:"bytes,3,opt,name=competition_name,json=competitionName,proto3" json:"competition_name,omitempty"`
	RegionName         string  `protobuf:"bytes,4,opt,name=region_name,json=regionName,proto3" json:"region_name,omitempty"`
	MatchName          string  `protobuf:"bytes,5,opt,name=match_name,json=matchName,proto3" json:"match_name,omitempty"`
	MarketName         string  `protobuf:"bytes,6,opt,name=market_name,json=marketName,proto3" json:"market_name,omitempty"`
	SelectionName      string  `protobuf:"bytes,7,opt,name=selection_name,json=selectionName,proto3" json:"selection_name,omitempty"`
	Price              float64 `protobuf:"fixed64,8,opt,name=price,proto3" json:"price,omitempty"`
	MatchInfo          string  `protobuf:"bytes,9,opt,name=match_info,json=matchInfo,proto3" json:"match_info,omitempty"`
	State              string  `protobuf:"bytes,10,opt,name=state,proto3" json:"state,omitempty"`
	StateName          string  `protobuf:"bytes,11,opt,name=state_name,json=stateName,proto3" json:"state_name,omitempty"`
	SelectionScore     string  `protobuf:"bytes,12,opt,name=selection_score,json=selectionScore,proto3" json:"selection_score,omitempty"`
	ResettlementReason string  `protobuf:"bytes,13,opt,name=resettlement_reason,json=resettlementReason,proto3" json:"resettlement_reason,omitempty"`
}

func (x *WagersDetail) Reset() {
	*x = WagersDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersDetail) ProtoMessage() {}

func (x *WagersDetail) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersDetail.ProtoReflect.Descriptor instead.
func (*WagersDetail) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{29}
}

func (x *WagersDetail) GetMatchDate() string {
	if x != nil {
		return x.MatchDate
	}
	return ""
}

func (x *WagersDetail) GetSportName() string {
	if x != nil {
		return x.SportName
	}
	return ""
}

func (x *WagersDetail) GetCompetitionName() string {
	if x != nil {
		return x.CompetitionName
	}
	return ""
}

func (x *WagersDetail) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *WagersDetail) GetMatchName() string {
	if x != nil {
		return x.MatchName
	}
	return ""
}

func (x *WagersDetail) GetMarketName() string {
	if x != nil {
		return x.MarketName
	}
	return ""
}

func (x *WagersDetail) GetSelectionName() string {
	if x != nil {
		return x.SelectionName
	}
	return ""
}

func (x *WagersDetail) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *WagersDetail) GetMatchInfo() string {
	if x != nil {
		return x.MatchInfo
	}
	return ""
}

func (x *WagersDetail) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *WagersDetail) GetStateName() string {
	if x != nil {
		return x.StateName
	}
	return ""
}

func (x *WagersDetail) GetSelectionScore() string {
	if x != nil {
		return x.SelectionScore
	}
	return ""
}

func (x *WagersDetail) GetResettlementReason() string {
	if x != nil {
		return x.ResettlementReason
	}
	return ""
}

type WagersDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*WagersDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *WagersDetailResponse) Reset() {
	*x = WagersDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersDetailResponse) ProtoMessage() {}

func (x *WagersDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersDetailResponse.ProtoReflect.Descriptor instead.
func (*WagersDetailResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{30}
}

func (x *WagersDetailResponse) GetData() []*WagersDetail {
	if x != nil {
		return x.Data
	}
	return nil
}

type FinishStatisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartDate string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate   string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Lang      string `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *FinishStatisRequest) Reset() {
	*x = FinishStatisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishStatisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishStatisRequest) ProtoMessage() {}

func (x *FinishStatisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishStatisRequest.ProtoReflect.Descriptor instead.
func (*FinishStatisRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{31}
}

func (x *FinishStatisRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FinishStatisRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *FinishStatisRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *FinishStatisRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type FinishStatisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wagers []*DateWagersStatis `protobuf:"bytes,1,rep,name=wagers,proto3" json:"wagers,omitempty"`
	Total  *WagersStatis       `protobuf:"bytes,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *FinishStatisResponse) Reset() {
	*x = FinishStatisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishStatisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishStatisResponse) ProtoMessage() {}

func (x *FinishStatisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishStatisResponse.ProtoReflect.Descriptor instead.
func (*FinishStatisResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{32}
}

func (x *FinishStatisResponse) GetWagers() []*DateWagersStatis {
	if x != nil {
		return x.Wagers
	}
	return nil
}

func (x *FinishStatisResponse) GetTotal() *WagersStatis {
	if x != nil {
		return x.Total
	}
	return nil
}

type DateWagersStatis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date     string              `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Weekday  string              `protobuf:"bytes,2,opt,name=weekday,proto3" json:"weekday,omitempty"`
	GameList []*GameWagersStatis `protobuf:"bytes,3,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	Subtotal *WagersStatis       `protobuf:"bytes,4,opt,name=subtotal,proto3" json:"subtotal,omitempty"`
}

func (x *DateWagersStatis) Reset() {
	*x = DateWagersStatis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateWagersStatis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateWagersStatis) ProtoMessage() {}

func (x *DateWagersStatis) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateWagersStatis.ProtoReflect.Descriptor instead.
func (*DateWagersStatis) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{33}
}

func (x *DateWagersStatis) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *DateWagersStatis) GetWeekday() string {
	if x != nil {
		return x.Weekday
	}
	return ""
}

func (x *DateWagersStatis) GetGameList() []*GameWagersStatis {
	if x != nil {
		return x.GameList
	}
	return nil
}

func (x *DateWagersStatis) GetSubtotal() *WagersStatis {
	if x != nil {
		return x.Subtotal
	}
	return nil
}

type WagersStatis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BetAmount      float64 `protobuf:"fixed64,1,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Payoff         float64 `protobuf:"fixed64,2,opt,name=payoff,proto3" json:"payoff,omitempty"`
	Commissionable float64 `protobuf:"fixed64,3,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
}

func (x *WagersStatis) Reset() {
	*x = WagersStatis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WagersStatis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WagersStatis) ProtoMessage() {}

func (x *WagersStatis) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WagersStatis.ProtoReflect.Descriptor instead.
func (*WagersStatis) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{34}
}

func (x *WagersStatis) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *WagersStatis) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *WagersStatis) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

type FinishWagersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartDate string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate   string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Lang      string `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`
	GameId    uint32 `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
}

func (x *FinishWagersRequest) Reset() {
	*x = FinishWagersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishWagersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishWagersRequest) ProtoMessage() {}

func (x *FinishWagersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishWagersRequest.ProtoReflect.Descriptor instead.
func (*FinishWagersRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{35}
}

func (x *FinishWagersRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FinishWagersRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *FinishWagersRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *FinishWagersRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *FinishWagersRequest) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

type FinishWagersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameList []*GameInfo   `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	Wagers   []*WagersInfo `protobuf:"bytes,2,rep,name=wagers,proto3" json:"wagers,omitempty"`
	Total    *WagersStatis `protobuf:"bytes,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *FinishWagersResponse) Reset() {
	*x = FinishWagersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishWagersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishWagersResponse) ProtoMessage() {}

func (x *FinishWagersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishWagersResponse.ProtoReflect.Descriptor instead.
func (*FinishWagersResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{36}
}

func (x *FinishWagersResponse) GetGameList() []*GameInfo {
	if x != nil {
		return x.GameList
	}
	return nil
}

func (x *FinishWagersResponse) GetWagers() []*WagersInfo {
	if x != nil {
		return x.Wagers
	}
	return nil
}

func (x *FinishWagersResponse) GetTotal() *WagersStatis {
	if x != nil {
		return x.Total
	}
	return nil
}

type GetWagersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartRoundDate string       `protobuf:"bytes,1,opt,name=start_round_date,json=startRoundDate,proto3" json:"start_round_date,omitempty"`
	EndRoundDate   string       `protobuf:"bytes,2,opt,name=end_round_date,json=endRoundDate,proto3" json:"end_round_date,omitempty"`
	HallId         uint32       `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	UserId         []uint32     `protobuf:"varint,4,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WagersId       uint64       `protobuf:"varint,5,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	GameId         []string     `protobuf:"bytes,6,rep,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Result         *Uint32Value `protobuf:"bytes,7,opt,name=result,proto3" json:"result,omitempty"`
	MinBetAmount   float64      `protobuf:"fixed64,8,opt,name=min_bet_amount,json=minBetAmount,proto3" json:"min_bet_amount,omitempty"`
	MaxBetAmount   float64      `protobuf:"fixed64,9,opt,name=max_bet_amount,json=maxBetAmount,proto3" json:"max_bet_amount,omitempty"`
	MinPayoff      float64      `protobuf:"fixed64,10,opt,name=min_payoff,json=minPayoff,proto3" json:"min_payoff,omitempty"`
	MaxPayoff      float64      `protobuf:"fixed64,11,opt,name=max_payoff,json=maxPayoff,proto3" json:"max_payoff,omitempty"`
	CloseDate      string       `protobuf:"bytes,12,opt,name=close_date,json=closeDate,proto3" json:"close_date,omitempty"`
	Page           uint32       `protobuf:"varint,13,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit      uint32       `protobuf:"varint,14,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Order          string       `protobuf:"bytes,15,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *GetWagersRequest) Reset() {
	*x = GetWagersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWagersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWagersRequest) ProtoMessage() {}

func (x *GetWagersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWagersRequest.ProtoReflect.Descriptor instead.
func (*GetWagersRequest) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{37}
}

func (x *GetWagersRequest) GetStartRoundDate() string {
	if x != nil {
		return x.StartRoundDate
	}
	return ""
}

func (x *GetWagersRequest) GetEndRoundDate() string {
	if x != nil {
		return x.EndRoundDate
	}
	return ""
}

func (x *GetWagersRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetWagersRequest) GetUserId() []uint32 {
	if x != nil {
		return x.UserId
	}
	return nil
}

func (x *GetWagersRequest) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *GetWagersRequest) GetGameId() []string {
	if x != nil {
		return x.GameId
	}
	return nil
}

func (x *GetWagersRequest) GetResult() *Uint32Value {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetWagersRequest) GetMinBetAmount() float64 {
	if x != nil {
		return x.MinBetAmount
	}
	return 0
}

func (x *GetWagersRequest) GetMaxBetAmount() float64 {
	if x != nil {
		return x.MaxBetAmount
	}
	return 0
}

func (x *GetWagersRequest) GetMinPayoff() float64 {
	if x != nil {
		return x.MinPayoff
	}
	return 0
}

func (x *GetWagersRequest) GetMaxPayoff() float64 {
	if x != nil {
		return x.MaxPayoff
	}
	return 0
}

func (x *GetWagersRequest) GetCloseDate() string {
	if x != nil {
		return x.CloseDate
	}
	return ""
}

func (x *GetWagersRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWagersRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *GetWagersRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

type GetWagersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wagers     []*FetchWagersByDB `protobuf:"bytes,1,rep,name=wagers,proto3" json:"wagers,omitempty"`
	Pagination *Pagination        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	SubTotal   *Total             `protobuf:"bytes,3,opt,name=sub_total,json=subTotal,proto3" json:"sub_total,omitempty"`
	Total      *Total             `protobuf:"bytes,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetWagersResponse) Reset() {
	*x = GetWagersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWagersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWagersResponse) ProtoMessage() {}

func (x *GetWagersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWagersResponse.ProtoReflect.Descriptor instead.
func (*GetWagersResponse) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{38}
}

func (x *GetWagersResponse) GetWagers() []*FetchWagersByDB {
	if x != nil {
		return x.Wagers
	}
	return nil
}

func (x *GetWagersResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetWagersResponse) GetSubTotal() *Total {
	if x != nil {
		return x.SubTotal
	}
	return nil
}

func (x *GetWagersResponse) GetTotal() *Total {
	if x != nil {
		return x.Total
	}
	return nil
}

type FetchWagersByDB struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WagersId       uint64  `protobuf:"varint,1,opt,name=wagers_id,json=wagersId,proto3" json:"wagers_id,omitempty"`
	WagersTime     string  `protobuf:"bytes,2,opt,name=wagers_time,json=wagersTime,proto3" json:"wagers_time,omitempty"`
	Hierarchy      uint32  `protobuf:"varint,3,opt,name=hierarchy,proto3" json:"hierarchy,omitempty"`
	Portal         uint32  `protobuf:"varint,4,opt,name=portal,proto3" json:"portal,omitempty"`
	WagersType     int32   `protobuf:"zigzag32,5,opt,name=wagers_type,json=wagersType,proto3" json:"wagers_type,omitempty"`
	Platform       uint32  `protobuf:"varint,6,opt,name=platform,proto3" json:"platform,omitempty"`
	Client         uint32  `protobuf:"varint,7,opt,name=client,proto3" json:"client,omitempty"`
	GameId         uint32  `protobuf:"varint,8,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	UserId         uint32  `protobuf:"varint,9,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RoundDate      string  `protobuf:"bytes,10,opt,name=round_date,json=roundDate,proto3" json:"round_date,omitempty"`
	RoundTime      string  `protobuf:"bytes,11,opt,name=round_time,json=roundTime,proto3" json:"round_time,omitempty"`
	BetAmount      float64 `protobuf:"fixed64,12,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Commissionable float64 `protobuf:"fixed64,13,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
	Currency       string  `protobuf:"bytes,14,opt,name=currency,proto3" json:"currency,omitempty"`
	ExchangeRate   float64 `protobuf:"fixed64,15,opt,name=exchange_rate,json=exchangeRate,proto3" json:"exchange_rate,omitempty"`
	Result         int32   `protobuf:"zigzag32,16,opt,name=result,proto3" json:"result,omitempty"`
	Payoff         float64 `protobuf:"fixed64,17,opt,name=payoff,proto3" json:"payoff,omitempty"`
	HallId         uint32  `protobuf:"varint,18,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	RoundSerial    string  `protobuf:"bytes,19,opt,name=round_serial,json=roundSerial,proto3" json:"round_serial,omitempty"`
	ReferenceId    string  `protobuf:"bytes,20,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	EventTime      string  `protobuf:"bytes,21,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"`
	SettledTime    string  `protobuf:"bytes,22,opt,name=settled_time,json=settledTime,proto3" json:"settled_time,omitempty"`
	OddType        string  `protobuf:"bytes,23,opt,name=odd_type,json=oddType,proto3" json:"odd_type,omitempty"`
	Market         string  `protobuf:"bytes,24,opt,name=market,proto3" json:"market,omitempty"`
	Rule           string  `protobuf:"bytes,25,opt,name=rule,proto3" json:"rule,omitempty"`
	ModifiedDate   string  `protobuf:"bytes,26,opt,name=modified_date,json=modifiedDate,proto3" json:"modified_date,omitempty"`
}

func (x *FetchWagersByDB) Reset() {
	*x = FetchWagersByDB{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchWagersByDB) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchWagersByDB) ProtoMessage() {}

func (x *FetchWagersByDB) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchWagersByDB.ProtoReflect.Descriptor instead.
func (*FetchWagersByDB) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{39}
}

func (x *FetchWagersByDB) GetWagersId() uint64 {
	if x != nil {
		return x.WagersId
	}
	return 0
}

func (x *FetchWagersByDB) GetWagersTime() string {
	if x != nil {
		return x.WagersTime
	}
	return ""
}

func (x *FetchWagersByDB) GetHierarchy() uint32 {
	if x != nil {
		return x.Hierarchy
	}
	return 0
}

func (x *FetchWagersByDB) GetPortal() uint32 {
	if x != nil {
		return x.Portal
	}
	return 0
}

func (x *FetchWagersByDB) GetWagersType() int32 {
	if x != nil {
		return x.WagersType
	}
	return 0
}

func (x *FetchWagersByDB) GetPlatform() uint32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *FetchWagersByDB) GetClient() uint32 {
	if x != nil {
		return x.Client
	}
	return 0
}

func (x *FetchWagersByDB) GetGameId() uint32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *FetchWagersByDB) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FetchWagersByDB) GetRoundDate() string {
	if x != nil {
		return x.RoundDate
	}
	return ""
}

func (x *FetchWagersByDB) GetRoundTime() string {
	if x != nil {
		return x.RoundTime
	}
	return ""
}

func (x *FetchWagersByDB) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *FetchWagersByDB) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

func (x *FetchWagersByDB) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *FetchWagersByDB) GetExchangeRate() float64 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *FetchWagersByDB) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *FetchWagersByDB) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

func (x *FetchWagersByDB) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *FetchWagersByDB) GetRoundSerial() string {
	if x != nil {
		return x.RoundSerial
	}
	return ""
}

func (x *FetchWagersByDB) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *FetchWagersByDB) GetEventTime() string {
	if x != nil {
		return x.EventTime
	}
	return ""
}

func (x *FetchWagersByDB) GetSettledTime() string {
	if x != nil {
		return x.SettledTime
	}
	return ""
}

func (x *FetchWagersByDB) GetOddType() string {
	if x != nil {
		return x.OddType
	}
	return ""
}

func (x *FetchWagersByDB) GetMarket() string {
	if x != nil {
		return x.Market
	}
	return ""
}

func (x *FetchWagersByDB) GetRule() string {
	if x != nil {
		return x.Rule
	}
	return ""
}

func (x *FetchWagersByDB) GetModifiedDate() string {
	if x != nil {
		return x.ModifiedDate
	}
	return ""
}

type Total struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number         uint32  `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
	BetAmount      float64 `protobuf:"fixed64,2,opt,name=bet_amount,json=betAmount,proto3" json:"bet_amount,omitempty"`
	Commissionable float64 `protobuf:"fixed64,3,opt,name=commissionable,proto3" json:"commissionable,omitempty"`
	Payoff         float64 `protobuf:"fixed64,4,opt,name=payoff,proto3" json:"payoff,omitempty"`
}

func (x *Total) Reset() {
	*x = Total{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sportgame_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Total) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Total) ProtoMessage() {}

func (x *Total) ProtoReflect() protoreflect.Message {
	mi := &file_sportgame_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Total.ProtoReflect.Descriptor instead.
func (*Total) Descriptor() ([]byte, []int) {
	return file_sportgame_proto_rawDescGZIP(), []int{40}
}

func (x *Total) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *Total) GetBetAmount() float64 {
	if x != nil {
		return x.BetAmount
	}
	return 0
}

func (x *Total) GetCommissionable() float64 {
	if x != nil {
		return x.Commissionable
	}
	return 0
}

func (x *Total) GetPayoff() float64 {
	if x != nil {
		return x.Payoff
	}
	return 0
}

var File_sportgame_proto protoreflect.FileDescriptor

var file_sportgame_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x25, 0x0a, 0x0f,
	0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c,
	0x61, 0x6e, 0x67, 0x22, 0x44, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2e, 0x0a, 0x08, 0x47, 0x61, 0x6d,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x23, 0x0a, 0x0b, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23,
	0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0xad, 0x02, 0x0a, 0x0f, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6e, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x2e, 0x0a, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x03, 0x6f,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x03, 0x6f, 0x74, 0x73, 0x12, 0x35, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x0b,
	0x65, 0x78, 0x69, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x65, 0x78, 0x69, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0e, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x75, 0x72,
	0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x22, 0x24, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xef, 0x01, 0x0a, 0x1b, 0x53, 0x70,
	0x6f, 0x72, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x42, 0x65, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x22, 0x8c, 0x02, 0x0a, 0x20,
	0x53, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x07, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x22, 0xd6, 0x03, 0x0a, 0x06, 0x57,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61,
	0x79, 0x6f, 0x66, 0x66, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f,
	0x66, 0x66, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x22, 0x72, 0x0a, 0x0e, 0x57, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x77,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x06,
	0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x35, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5f, 0x0a,
	0x13, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x28,
	0x0a, 0x14, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x41, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x2e, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x67, 0x0a,
	0x17, 0x47, 0x61, 0x6d, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x37, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x4d, 0x0a, 0x0f, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x65, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x62, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x59,
	0x0a, 0x13, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x69, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x18, 0x47, 0x61,
	0x6d, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x38, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x65, 0x74,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x69, 0x72,
	0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x75, 0x0a, 0x15, 0x55, 0x6e, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x2f, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64,
	0x22, 0xc5, 0x01, 0x0a, 0x16, 0x55, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x57,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x77, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x06, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x22, 0xab, 0x01, 0x0a, 0x10, 0x47, 0x61, 0x6d,
	0x65, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x26,
	0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xfa, 0x01, 0x0a, 0x0a, 0x57, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x64, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x64, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x65, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x26, 0x0a, 0x0e, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x46, 0x0a, 0x13, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x77,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xc3, 0x03, 0x0a,
	0x0c, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63,
	0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x2f, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0x43, 0x0a, 0x14, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x7c, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x7a, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a,
	0x06, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x57, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x06, 0x77, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x22, 0xaf, 0x01, 0x0a, 0x10, 0x44, 0x61, 0x74, 0x65, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65,
	0x65, 0x6b, 0x64, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x65,
	0x6b, 0x64, 0x61, 0x79, 0x12, 0x38, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33,
	0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0x6d, 0x0a, 0x0c, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x57, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x14, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x77, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0xec, 0x03, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x77,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x69, 0x6e, 0x5f, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x42, 0x65, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x65,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x6d, 0x61, 0x78, 0x42, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x6d, 0x69, 0x6e, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x61, 0x78, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x6d, 0x61, 0x78, 0x50, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x22, 0xd5, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x77, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x42, 0x79, 0x44, 0x42, 0x52, 0x06, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x35, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x26, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x8f, 0x06, 0x0a, 0x0f, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x44, 0x42, 0x12, 0x1b,
	0x0a, 0x09, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0a, 0x77, 0x61, 0x67, 0x65, 0x72, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12,
	0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x62, 0x65, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x11, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66,
	0x66, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x12,
	0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x64, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x64, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x7e, 0x0a, 0x05,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x62, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x61, 0x79, 0x6f, 0x66, 0x66, 0x32, 0xa9, 0x08, 0x0a,
	0x09, 0x53, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x47, 0x61,
	0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x43, 0x0a, 0x08, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1a, 0x2e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6e, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0f, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79,
	0x42, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x53, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42,
	0x79, 0x42, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x14, 0x57, 0x61,
	0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2b, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53,
	0x70, 0x6f, 0x72, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0c, 0x53, 0x75,
	0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x55, 0x52, 0x4c, 0x12, 0x1e, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x55, 0x52, 0x4c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x44, 0x12, 0x21,
	0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x18, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0c, 0x57, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x2e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x10, 0x47, 0x61,
	0x6d, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x22,
	0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x70,
	0x6c, 0x61, 0x79, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x42, 0x65, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x0e, 0x55, 0x6e, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x12, 0x20, 0x2e, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f,
	0x0a, 0x0c, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x12, 0x1e,
	0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4f, 0x0a, 0x0c, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12,
	0x1e, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1f, 0x2e, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x46, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x67,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x73, 0x70, 0x6f,
	0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x11, 0x5a, 0x0f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x67, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_sportgame_proto_rawDescOnce sync.Once
	file_sportgame_proto_rawDescData = file_sportgame_proto_rawDesc
)

func file_sportgame_proto_rawDescGZIP() []byte {
	file_sportgame_proto_rawDescOnce.Do(func() {
		file_sportgame_proto_rawDescData = protoimpl.X.CompressGZIP(file_sportgame_proto_rawDescData)
	})
	return file_sportgame_proto_rawDescData
}

var file_sportgame_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_sportgame_proto_goTypes = []interface{}{
	(*GameListRequest)(nil),                  // 0: sportgame.GameListRequest
	(*GameListResponse)(nil),                 // 1: sportgame.GameListResponse
	(*GameInfo)(nil),                         // 2: sportgame.GameInfo
	(*Uint32Value)(nil),                      // 3: sportgame.Uint32Value
	(*StringValue)(nil),                      // 4: sportgame.StringValue
	(*GameLinkRequest)(nil),                  // 5: sportgame.GameLinkRequest
	(*GameLinkResponse)(nil),                 // 6: sportgame.GameLinkResponse
	(*SportWagersByBetTimeRequest)(nil),      // 7: sportgame.SportWagersByBetTimeRequest
	(*SportWagersByModifiedTimeRequest)(nil), // 8: sportgame.SportWagersByModifiedTimeRequest
	(*Wagers)(nil),                           // 9: sportgame.Wagers
	(*Pagination)(nil),                       // 10: sportgame.Pagination
	(*WagersResponse)(nil),                   // 11: sportgame.WagersResponse
	(*SubWagersURLRequest)(nil),              // 12: sportgame.SubWagersURLRequest
	(*SubWagersURLResponse)(nil),             // 13: sportgame.SubWagersURLResponse
	(*CheckWagersByIDRequest)(nil),           // 14: sportgame.CheckWagersByIDRequest
	(*GetCategoryRequest)(nil),               // 15: sportgame.GetCategoryRequest
	(*Category)(nil),                         // 16: sportgame.Category
	(*GetCategoryResponse)(nil),              // 17: sportgame.GetCategoryResponse
	(*GameplayBetLimitRequest)(nil),          // 18: sportgame.GameplayBetLimitRequest
	(*GroupNamePair)(nil),                    // 19: sportgame.GroupNamePair
	(*BetLimitSetting)(nil),                  // 20: sportgame.BetLimitSetting
	(*BetLimitSettingPair)(nil),              // 21: sportgame.BetLimitSettingPair
	(*GameplayBetLimitResponse)(nil),         // 22: sportgame.GameplayBetLimitResponse
	(*UnfinishStatisRequest)(nil),            // 23: sportgame.UnfinishStatisRequest
	(*UnfinishStatisResponse)(nil),           // 24: sportgame.UnfinishStatisResponse
	(*GameWagersStatis)(nil),                 // 25: sportgame.GameWagersStatis
	(*WagersInfo)(nil),                       // 26: sportgame.WagersInfo
	(*EmptyResponse)(nil),                    // 27: sportgame.EmptyResponse
	(*WagersDetailRequest)(nil),              // 28: sportgame.WagersDetailRequest
	(*WagersDetail)(nil),                     // 29: sportgame.WagersDetail
	(*WagersDetailResponse)(nil),             // 30: sportgame.WagersDetailResponse
	(*FinishStatisRequest)(nil),              // 31: sportgame.FinishStatisRequest
	(*FinishStatisResponse)(nil),             // 32: sportgame.FinishStatisResponse
	(*DateWagersStatis)(nil),                 // 33: sportgame.DateWagersStatis
	(*WagersStatis)(nil),                     // 34: sportgame.WagersStatis
	(*FinishWagersRequest)(nil),              // 35: sportgame.FinishWagersRequest
	(*FinishWagersResponse)(nil),             // 36: sportgame.FinishWagersResponse
	(*GetWagersRequest)(nil),                 // 37: sportgame.GetWagersRequest
	(*GetWagersResponse)(nil),                // 38: sportgame.GetWagersResponse
	(*FetchWagersByDB)(nil),                  // 39: sportgame.FetchWagersByDB
	(*Total)(nil),                            // 40: sportgame.Total
}
var file_sportgame_proto_depIdxs = []int32{
	2,  // 0: sportgame.GameListResponse.game_info:type_name -> sportgame.GameInfo
	3,  // 1: sportgame.GameLinkRequest.device:type_name -> sportgame.Uint32Value
	4,  // 2: sportgame.GameLinkRequest.ots:type_name -> sportgame.StringValue
	4,  // 3: sportgame.GameLinkRequest.enter_page:type_name -> sportgame.StringValue
	3,  // 4: sportgame.GameLinkRequest.exit_option:type_name -> sportgame.Uint32Value
	4,  // 5: sportgame.GameLinkRequest.exit_url_param:type_name -> sportgame.StringValue
	3,  // 6: sportgame.SportWagersByBetTimeRequest.game_id:type_name -> sportgame.Uint32Value
	3,  // 7: sportgame.SportWagersByModifiedTimeRequest.agent_id:type_name -> sportgame.Uint32Value
	3,  // 8: sportgame.SportWagersByModifiedTimeRequest.game_id:type_name -> sportgame.Uint32Value
	4,  // 9: sportgame.Wagers.account_date:type_name -> sportgame.StringValue
	9,  // 10: sportgame.WagersResponse.wagers:type_name -> sportgame.Wagers
	10, // 11: sportgame.WagersResponse.pagination:type_name -> sportgame.Pagination
	16, // 12: sportgame.GetCategoryResponse.category:type_name -> sportgame.Category
	20, // 13: sportgame.BetLimitSettingPair.value:type_name -> sportgame.BetLimitSetting
	19, // 14: sportgame.GameplayBetLimitResponse.group_name:type_name -> sportgame.GroupNamePair
	21, // 15: sportgame.GameplayBetLimitResponse.setting:type_name -> sportgame.BetLimitSettingPair
	3,  // 16: sportgame.UnfinishStatisRequest.game_id:type_name -> sportgame.Uint32Value
	25, // 17: sportgame.UnfinishStatisResponse.game_list:type_name -> sportgame.GameWagersStatis
	26, // 18: sportgame.UnfinishStatisResponse.wagers:type_name -> sportgame.WagersInfo
	29, // 19: sportgame.WagersDetailResponse.data:type_name -> sportgame.WagersDetail
	33, // 20: sportgame.FinishStatisResponse.wagers:type_name -> sportgame.DateWagersStatis
	34, // 21: sportgame.FinishStatisResponse.total:type_name -> sportgame.WagersStatis
	25, // 22: sportgame.DateWagersStatis.game_list:type_name -> sportgame.GameWagersStatis
	34, // 23: sportgame.DateWagersStatis.subtotal:type_name -> sportgame.WagersStatis
	2,  // 24: sportgame.FinishWagersResponse.game_list:type_name -> sportgame.GameInfo
	26, // 25: sportgame.FinishWagersResponse.wagers:type_name -> sportgame.WagersInfo
	34, // 26: sportgame.FinishWagersResponse.total:type_name -> sportgame.WagersStatis
	3,  // 27: sportgame.GetWagersRequest.result:type_name -> sportgame.Uint32Value
	39, // 28: sportgame.GetWagersResponse.wagers:type_name -> sportgame.FetchWagersByDB
	10, // 29: sportgame.GetWagersResponse.pagination:type_name -> sportgame.Pagination
	40, // 30: sportgame.GetWagersResponse.sub_total:type_name -> sportgame.Total
	40, // 31: sportgame.GetWagersResponse.total:type_name -> sportgame.Total
	0,  // 32: sportgame.SportGame.GameList:input_type -> sportgame.GameListRequest
	5,  // 33: sportgame.SportGame.GameLink:input_type -> sportgame.GameLinkRequest
	7,  // 34: sportgame.SportGame.WagersByBetTime:input_type -> sportgame.SportWagersByBetTimeRequest
	8,  // 35: sportgame.SportGame.WagersByModifiedTime:input_type -> sportgame.SportWagersByModifiedTimeRequest
	12, // 36: sportgame.SportGame.SubWagersURL:input_type -> sportgame.SubWagersURLRequest
	14, // 37: sportgame.SportGame.CheckWagersByID:input_type -> sportgame.CheckWagersByIDRequest
	15, // 38: sportgame.SportGame.GetCategory:input_type -> sportgame.GetCategoryRequest
	28, // 39: sportgame.SportGame.WagersDetail:input_type -> sportgame.WagersDetailRequest
	18, // 40: sportgame.SportGame.GameplayBetLimit:input_type -> sportgame.GameplayBetLimitRequest
	23, // 41: sportgame.SportGame.UnfinishStatis:input_type -> sportgame.UnfinishStatisRequest
	31, // 42: sportgame.SportGame.FinishStatis:input_type -> sportgame.FinishStatisRequest
	35, // 43: sportgame.SportGame.FinishWagers:input_type -> sportgame.FinishWagersRequest
	37, // 44: sportgame.SportGame.GetWagers:input_type -> sportgame.GetWagersRequest
	1,  // 45: sportgame.SportGame.GameList:output_type -> sportgame.GameListResponse
	6,  // 46: sportgame.SportGame.GameLink:output_type -> sportgame.GameLinkResponse
	11, // 47: sportgame.SportGame.WagersByBetTime:output_type -> sportgame.WagersResponse
	11, // 48: sportgame.SportGame.WagersByModifiedTime:output_type -> sportgame.WagersResponse
	13, // 49: sportgame.SportGame.SubWagersURL:output_type -> sportgame.SubWagersURLResponse
	27, // 50: sportgame.SportGame.CheckWagersByID:output_type -> sportgame.EmptyResponse
	17, // 51: sportgame.SportGame.GetCategory:output_type -> sportgame.GetCategoryResponse
	30, // 52: sportgame.SportGame.WagersDetail:output_type -> sportgame.WagersDetailResponse
	22, // 53: sportgame.SportGame.GameplayBetLimit:output_type -> sportgame.GameplayBetLimitResponse
	24, // 54: sportgame.SportGame.UnfinishStatis:output_type -> sportgame.UnfinishStatisResponse
	32, // 55: sportgame.SportGame.FinishStatis:output_type -> sportgame.FinishStatisResponse
	36, // 56: sportgame.SportGame.FinishWagers:output_type -> sportgame.FinishWagersResponse
	38, // 57: sportgame.SportGame.GetWagers:output_type -> sportgame.GetWagersResponse
	45, // [45:58] is the sub-list for method output_type
	32, // [32:45] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_sportgame_proto_init() }
func file_sportgame_proto_init() {
	if File_sportgame_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sportgame_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SportWagersByBetTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SportWagersByModifiedTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Wagers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pagination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WagersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubWagersURLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubWagersURLResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckWagersByIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Category); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameplayBetLimitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupNamePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BetLimitSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BetLimitSettingPair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameplayBetLimitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnfinishStatisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnfinishStatisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameWagersStatis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WagersInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WagersDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WagersDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WagersDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishStatisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishStatisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateWagersStatis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WagersStatis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishWagersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishWagersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWagersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWagersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchWagersByDB); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sportgame_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Total); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sportgame_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   41,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sportgame_proto_goTypes,
		DependencyIndexes: file_sportgame_proto_depIdxs,
		MessageInfos:      file_sportgame_proto_msgTypes,
	}.Build()
	File_sportgame_proto = out.File
	file_sportgame_proto_rawDesc = nil
	file_sportgame_proto_goTypes = nil
	file_sportgame_proto_depIdxs = nil
}
