// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: sportgame.proto

package sportgame

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SportGame_GameList_FullMethodName             = "/sportgame.SportGame/GameList"
	SportGame_GameLink_FullMethodName             = "/sportgame.SportGame/GameLink"
	SportGame_WagersByBetTime_FullMethodName      = "/sportgame.SportGame/WagersByBetTime"
	SportGame_WagersByModifiedTime_FullMethodName = "/sportgame.SportGame/WagersByModifiedTime"
	SportGame_SubWagersURL_FullMethodName         = "/sportgame.SportGame/SubWagersURL"
	SportGame_CheckWagersByID_FullMethodName      = "/sportgame.SportGame/CheckWagersByID"
	SportGame_GetCategory_FullMethodName          = "/sportgame.SportGame/GetCategory"
	SportGame_WagersDetail_FullMethodName         = "/sportgame.SportGame/WagersDetail"
	SportGame_GameplayBetLimit_FullMethodName     = "/sportgame.SportGame/GameplayBetLimit"
	SportGame_UnfinishStatis_FullMethodName       = "/sportgame.SportGame/UnfinishStatis"
	SportGame_FinishStatis_FullMethodName         = "/sportgame.SportGame/FinishStatis"
	SportGame_FinishWagers_FullMethodName         = "/sportgame.SportGame/FinishWagers"
	SportGame_GetWagers_FullMethodName            = "/sportgame.SportGame/GetWagers"
)

// SportGameClient is the client API for SportGame service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SportGameClient interface {
	GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error)
	GameLink(ctx context.Context, in *GameLinkRequest, opts ...grpc.CallOption) (*GameLinkResponse, error)
	WagersByBetTime(ctx context.Context, in *SportWagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
	WagersByModifiedTime(ctx context.Context, in *SportWagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
	SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error)
	CheckWagersByID(ctx context.Context, in *CheckWagersByIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error)
	WagersDetail(ctx context.Context, in *WagersDetailRequest, opts ...grpc.CallOption) (*WagersDetailResponse, error)
	GameplayBetLimit(ctx context.Context, in *GameplayBetLimitRequest, opts ...grpc.CallOption) (*GameplayBetLimitResponse, error)
	UnfinishStatis(ctx context.Context, in *UnfinishStatisRequest, opts ...grpc.CallOption) (*UnfinishStatisResponse, error)
	FinishStatis(ctx context.Context, in *FinishStatisRequest, opts ...grpc.CallOption) (*FinishStatisResponse, error)
	FinishWagers(ctx context.Context, in *FinishWagersRequest, opts ...grpc.CallOption) (*FinishWagersResponse, error)
	GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error)
}

type sportGameClient struct {
	cc grpc.ClientConnInterface
}

func NewSportGameClient(cc grpc.ClientConnInterface) SportGameClient {
	return &sportGameClient{cc}
}

func (c *sportGameClient) GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error) {
	out := new(GameListResponse)
	err := c.cc.Invoke(ctx, SportGame_GameList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) GameLink(ctx context.Context, in *GameLinkRequest, opts ...grpc.CallOption) (*GameLinkResponse, error) {
	out := new(GameLinkResponse)
	err := c.cc.Invoke(ctx, SportGame_GameLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) WagersByBetTime(ctx context.Context, in *SportWagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	out := new(WagersResponse)
	err := c.cc.Invoke(ctx, SportGame_WagersByBetTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) WagersByModifiedTime(ctx context.Context, in *SportWagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	out := new(WagersResponse)
	err := c.cc.Invoke(ctx, SportGame_WagersByModifiedTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error) {
	out := new(SubWagersURLResponse)
	err := c.cc.Invoke(ctx, SportGame_SubWagersURL_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) CheckWagersByID(ctx context.Context, in *CheckWagersByIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, SportGame_CheckWagersByID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error) {
	out := new(GetCategoryResponse)
	err := c.cc.Invoke(ctx, SportGame_GetCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) WagersDetail(ctx context.Context, in *WagersDetailRequest, opts ...grpc.CallOption) (*WagersDetailResponse, error) {
	out := new(WagersDetailResponse)
	err := c.cc.Invoke(ctx, SportGame_WagersDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) GameplayBetLimit(ctx context.Context, in *GameplayBetLimitRequest, opts ...grpc.CallOption) (*GameplayBetLimitResponse, error) {
	out := new(GameplayBetLimitResponse)
	err := c.cc.Invoke(ctx, SportGame_GameplayBetLimit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) UnfinishStatis(ctx context.Context, in *UnfinishStatisRequest, opts ...grpc.CallOption) (*UnfinishStatisResponse, error) {
	out := new(UnfinishStatisResponse)
	err := c.cc.Invoke(ctx, SportGame_UnfinishStatis_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) FinishStatis(ctx context.Context, in *FinishStatisRequest, opts ...grpc.CallOption) (*FinishStatisResponse, error) {
	out := new(FinishStatisResponse)
	err := c.cc.Invoke(ctx, SportGame_FinishStatis_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) FinishWagers(ctx context.Context, in *FinishWagersRequest, opts ...grpc.CallOption) (*FinishWagersResponse, error) {
	out := new(FinishWagersResponse)
	err := c.cc.Invoke(ctx, SportGame_FinishWagers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportGameClient) GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error) {
	out := new(GetWagersResponse)
	err := c.cc.Invoke(ctx, SportGame_GetWagers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SportGameServer is the server API for SportGame service.
// All implementations must embed UnimplementedSportGameServer
// for forward compatibility
type SportGameServer interface {
	GameList(context.Context, *GameListRequest) (*GameListResponse, error)
	GameLink(context.Context, *GameLinkRequest) (*GameLinkResponse, error)
	WagersByBetTime(context.Context, *SportWagersByBetTimeRequest) (*WagersResponse, error)
	WagersByModifiedTime(context.Context, *SportWagersByModifiedTimeRequest) (*WagersResponse, error)
	SubWagersURL(context.Context, *SubWagersURLRequest) (*SubWagersURLResponse, error)
	CheckWagersByID(context.Context, *CheckWagersByIDRequest) (*EmptyResponse, error)
	GetCategory(context.Context, *GetCategoryRequest) (*GetCategoryResponse, error)
	WagersDetail(context.Context, *WagersDetailRequest) (*WagersDetailResponse, error)
	GameplayBetLimit(context.Context, *GameplayBetLimitRequest) (*GameplayBetLimitResponse, error)
	UnfinishStatis(context.Context, *UnfinishStatisRequest) (*UnfinishStatisResponse, error)
	FinishStatis(context.Context, *FinishStatisRequest) (*FinishStatisResponse, error)
	FinishWagers(context.Context, *FinishWagersRequest) (*FinishWagersResponse, error)
	GetWagers(context.Context, *GetWagersRequest) (*GetWagersResponse, error)
	mustEmbedUnimplementedSportGameServer()
}

// UnimplementedSportGameServer must be embedded to have forward compatible implementations.
type UnimplementedSportGameServer struct {
}

func (UnimplementedSportGameServer) GameList(context.Context, *GameListRequest) (*GameListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameList not implemented")
}
func (UnimplementedSportGameServer) GameLink(context.Context, *GameLinkRequest) (*GameLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameLink not implemented")
}
func (UnimplementedSportGameServer) WagersByBetTime(context.Context, *SportWagersByBetTimeRequest) (*WagersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WagersByBetTime not implemented")
}
func (UnimplementedSportGameServer) WagersByModifiedTime(context.Context, *SportWagersByModifiedTimeRequest) (*WagersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WagersByModifiedTime not implemented")
}
func (UnimplementedSportGameServer) SubWagersURL(context.Context, *SubWagersURLRequest) (*SubWagersURLResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubWagersURL not implemented")
}
func (UnimplementedSportGameServer) CheckWagersByID(context.Context, *CheckWagersByIDRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckWagersByID not implemented")
}
func (UnimplementedSportGameServer) GetCategory(context.Context, *GetCategoryRequest) (*GetCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCategory not implemented")
}
func (UnimplementedSportGameServer) WagersDetail(context.Context, *WagersDetailRequest) (*WagersDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WagersDetail not implemented")
}
func (UnimplementedSportGameServer) GameplayBetLimit(context.Context, *GameplayBetLimitRequest) (*GameplayBetLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameplayBetLimit not implemented")
}
func (UnimplementedSportGameServer) UnfinishStatis(context.Context, *UnfinishStatisRequest) (*UnfinishStatisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfinishStatis not implemented")
}
func (UnimplementedSportGameServer) FinishStatis(context.Context, *FinishStatisRequest) (*FinishStatisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinishStatis not implemented")
}
func (UnimplementedSportGameServer) FinishWagers(context.Context, *FinishWagersRequest) (*FinishWagersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinishWagers not implemented")
}
func (UnimplementedSportGameServer) GetWagers(context.Context, *GetWagersRequest) (*GetWagersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWagers not implemented")
}
func (UnimplementedSportGameServer) mustEmbedUnimplementedSportGameServer() {}

// UnsafeSportGameServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SportGameServer will
// result in compilation errors.
type UnsafeSportGameServer interface {
	mustEmbedUnimplementedSportGameServer()
}

func RegisterSportGameServer(s grpc.ServiceRegistrar, srv SportGameServer) {
	s.RegisterService(&SportGame_ServiceDesc, srv)
}

func _SportGame_GameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).GameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_GameList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).GameList(ctx, req.(*GameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_GameLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).GameLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_GameLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).GameLink(ctx, req.(*GameLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_WagersByBetTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SportWagersByBetTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).WagersByBetTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_WagersByBetTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).WagersByBetTime(ctx, req.(*SportWagersByBetTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_WagersByModifiedTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SportWagersByModifiedTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).WagersByModifiedTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_WagersByModifiedTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).WagersByModifiedTime(ctx, req.(*SportWagersByModifiedTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_SubWagersURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubWagersURLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).SubWagersURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_SubWagersURL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).SubWagersURL(ctx, req.(*SubWagersURLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_CheckWagersByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWagersByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).CheckWagersByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_CheckWagersByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).CheckWagersByID(ctx, req.(*CheckWagersByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_GetCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).GetCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_GetCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).GetCategory(ctx, req.(*GetCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_WagersDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WagersDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).WagersDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_WagersDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).WagersDetail(ctx, req.(*WagersDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_GameplayBetLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameplayBetLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).GameplayBetLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_GameplayBetLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).GameplayBetLimit(ctx, req.(*GameplayBetLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_UnfinishStatis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfinishStatisRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).UnfinishStatis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_UnfinishStatis_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).UnfinishStatis(ctx, req.(*UnfinishStatisRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_FinishStatis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishStatisRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).FinishStatis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_FinishStatis_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).FinishStatis(ctx, req.(*FinishStatisRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_FinishWagers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishWagersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).FinishWagers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_FinishWagers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).FinishWagers(ctx, req.(*FinishWagersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportGame_GetWagers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWagersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportGameServer).GetWagers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportGame_GetWagers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportGameServer).GetWagers(ctx, req.(*GetWagersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SportGame_ServiceDesc is the grpc.ServiceDesc for SportGame service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SportGame_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportgame.SportGame",
	HandlerType: (*SportGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GameList",
			Handler:    _SportGame_GameList_Handler,
		},
		{
			MethodName: "GameLink",
			Handler:    _SportGame_GameLink_Handler,
		},
		{
			MethodName: "WagersByBetTime",
			Handler:    _SportGame_WagersByBetTime_Handler,
		},
		{
			MethodName: "WagersByModifiedTime",
			Handler:    _SportGame_WagersByModifiedTime_Handler,
		},
		{
			MethodName: "SubWagersURL",
			Handler:    _SportGame_SubWagersURL_Handler,
		},
		{
			MethodName: "CheckWagersByID",
			Handler:    _SportGame_CheckWagersByID_Handler,
		},
		{
			MethodName: "GetCategory",
			Handler:    _SportGame_GetCategory_Handler,
		},
		{
			MethodName: "WagersDetail",
			Handler:    _SportGame_WagersDetail_Handler,
		},
		{
			MethodName: "GameplayBetLimit",
			Handler:    _SportGame_GameplayBetLimit_Handler,
		},
		{
			MethodName: "UnfinishStatis",
			Handler:    _SportGame_UnfinishStatis_Handler,
		},
		{
			MethodName: "FinishStatis",
			Handler:    _SportGame_FinishStatis_Handler,
		},
		{
			MethodName: "FinishWagers",
			Handler:    _SportGame_FinishWagers_Handler,
		},
		{
			MethodName: "GetWagers",
			Handler:    _SportGame_GetWagers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sportgame.proto",
}
