// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v3.19.4
// source: operationrecord.proto

package operationrecord

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Uint32Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Uint32Value) Reset() {
	*x = Uint32Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Uint32Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Uint32Value) ProtoMessage() {}

func (x *Uint32Value) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Uint32Value.ProtoReflect.Descriptor instead.
func (*Uint32Value) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{0}
}

func (x *Uint32Value) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type CreateUserRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientIp   string `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	OperatorId uint32 `protobuf:"varint,2,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	UserId     uint32 `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SetType    uint32 `protobuf:"varint,4,opt,name=set_type,json=setType,proto3" json:"set_type,omitempty"`
}

func (x *CreateUserRecordRequest) Reset() {
	*x = CreateUserRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRecordRequest) ProtoMessage() {}

func (x *CreateUserRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUserRecordRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *CreateUserRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateUserRecordRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateUserRecordRequest) GetSetType() uint32 {
	if x != nil {
		return x.SetType
	}
	return 0
}

type CreateTransferRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientIp   string  `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	OperatorId uint32  `protobuf:"varint,2,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	UserId     uint32  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SetType    uint32  `protobuf:"varint,4,opt,name=set_type,json=setType,proto3" json:"set_type,omitempty"`
	SubBalance float64 `protobuf:"fixed64,5,opt,name=sub_balance,json=subBalance,proto3" json:"sub_balance,omitempty"`
	OldBalance float64 `protobuf:"fixed64,6,opt,name=old_balance,json=oldBalance,proto3" json:"old_balance,omitempty"`
	Balance    float64 `protobuf:"fixed64,7,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *CreateTransferRecordRequest) Reset() {
	*x = CreateTransferRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTransferRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTransferRecordRequest) ProtoMessage() {}

func (x *CreateTransferRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTransferRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateTransferRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{2}
}

func (x *CreateTransferRecordRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *CreateTransferRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateTransferRecordRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateTransferRecordRequest) GetSetType() uint32 {
	if x != nil {
		return x.SetType
	}
	return 0
}

func (x *CreateTransferRecordRequest) GetSubBalance() float64 {
	if x != nil {
		return x.SubBalance
	}
	return 0
}

func (x *CreateTransferRecordRequest) GetOldBalance() float64 {
	if x != nil {
		return x.OldBalance
	}
	return 0
}

func (x *CreateTransferRecordRequest) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type CreateAGRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId       uint32      `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	OperatorId   uint32      `protobuf:"varint,2,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	LevelId      uint32      `protobuf:"varint,3,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	ActionId     uint32      `protobuf:"varint,4,opt,name=action_id,json=actionId,proto3" json:"action_id,omitempty"`
	SubActionId  uint32      `protobuf:"varint,5,opt,name=sub_action_id,json=subActionId,proto3" json:"sub_action_id,omitempty"`
	TargetId     uint32      `protobuf:"varint,6,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	TargetRoleId uint32      `protobuf:"varint,7,opt,name=target_role_id,json=targetRoleId,proto3" json:"target_role_id,omitempty"`
	BossKey      string      `protobuf:"bytes,8,opt,name=boss_key,json=bossKey,proto3" json:"boss_key,omitempty"`
	UserKey      string      `protobuf:"bytes,9,opt,name=user_key,json=userKey,proto3" json:"user_key,omitempty"`
	XmlDataMsg   []*KeyValue `protobuf:"bytes,10,rep,name=xml_data_msg,json=xmlDataMsg,proto3" json:"xml_data_msg,omitempty"`
	XmlCancelTag bool        `protobuf:"varint,11,opt,name=xml_cancel_tag,json=xmlCancelTag,proto3" json:"xml_cancel_tag,omitempty"`
	Uri          string      `protobuf:"bytes,12,opt,name=uri,proto3" json:"uri,omitempty"`
	Ip           string      `protobuf:"bytes,13,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateAGRecordRequest) Reset() {
	*x = CreateAGRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAGRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAGRecordRequest) ProtoMessage() {}

func (x *CreateAGRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAGRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateAGRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAGRecordRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *CreateAGRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateAGRecordRequest) GetLevelId() uint32 {
	if x != nil {
		return x.LevelId
	}
	return 0
}

func (x *CreateAGRecordRequest) GetActionId() uint32 {
	if x != nil {
		return x.ActionId
	}
	return 0
}

func (x *CreateAGRecordRequest) GetSubActionId() uint32 {
	if x != nil {
		return x.SubActionId
	}
	return 0
}

func (x *CreateAGRecordRequest) GetTargetId() uint32 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *CreateAGRecordRequest) GetTargetRoleId() uint32 {
	if x != nil {
		return x.TargetRoleId
	}
	return 0
}

func (x *CreateAGRecordRequest) GetBossKey() string {
	if x != nil {
		return x.BossKey
	}
	return ""
}

func (x *CreateAGRecordRequest) GetUserKey() string {
	if x != nil {
		return x.UserKey
	}
	return ""
}

func (x *CreateAGRecordRequest) GetXmlDataMsg() []*KeyValue {
	if x != nil {
		return x.XmlDataMsg
	}
	return nil
}

func (x *CreateAGRecordRequest) GetXmlCancelTag() bool {
	if x != nil {
		return x.XmlCancelTag
	}
	return false
}

func (x *CreateAGRecordRequest) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *CreateAGRecordRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type KeyValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *KeyValue) Reset() {
	*x = KeyValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeyValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyValue) ProtoMessage() {}

func (x *KeyValue) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyValue.ProtoReflect.Descriptor instead.
func (*KeyValue) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{4}
}

func (x *KeyValue) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *KeyValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{5}
}

type AddLogChangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance   float64 `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
	Area      string  `protobuf:"bytes,2,opt,name=area,proto3" json:"area,omitempty"`
	HallId    uint32  `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Applet    string  `protobuf:"bytes,4,opt,name=applet,proto3" json:"applet,omitempty"`
	Act       string  `protobuf:"bytes,5,opt,name=act,proto3" json:"act,omitempty"`
	ActionSql string  `protobuf:"bytes,6,opt,name=action_sql,json=actionSql,proto3" json:"action_sql,omitempty"`
	Content   string  `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
	Bywho     string  `protobuf:"bytes,8,opt,name=bywho,proto3" json:"bywho,omitempty"`
	Byfile    string  `protobuf:"bytes,9,opt,name=byfile,proto3" json:"byfile,omitempty"`
	Ip        string  `protobuf:"bytes,10,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *AddLogChangeRequest) Reset() {
	*x = AddLogChangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLogChangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLogChangeRequest) ProtoMessage() {}

func (x *AddLogChangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLogChangeRequest.ProtoReflect.Descriptor instead.
func (*AddLogChangeRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{6}
}

func (x *AddLogChangeRequest) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *AddLogChangeRequest) GetArea() string {
	if x != nil {
		return x.Area
	}
	return ""
}

func (x *AddLogChangeRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *AddLogChangeRequest) GetApplet() string {
	if x != nil {
		return x.Applet
	}
	return ""
}

func (x *AddLogChangeRequest) GetAct() string {
	if x != nil {
		return x.Act
	}
	return ""
}

func (x *AddLogChangeRequest) GetActionSql() string {
	if x != nil {
		return x.ActionSql
	}
	return ""
}

func (x *AddLogChangeRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddLogChangeRequest) GetBywho() string {
	if x != nil {
		return x.Bywho
	}
	return ""
}

func (x *AddLogChangeRequest) GetByfile() string {
	if x != nil {
		return x.Byfile
	}
	return ""
}

func (x *AddLogChangeRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type AddDownloadRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site           uint32 `protobuf:"varint,1,opt,name=site,proto3" json:"site,omitempty"`
	Action         uint32 `protobuf:"varint,2,opt,name=action,proto3" json:"action,omitempty"`
	HallId         uint32 `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Content        string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	OperatorId     uint32 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	OperatorSite   uint32 `protobuf:"varint,6,opt,name=operator_site,json=operatorSite,proto3" json:"operator_site,omitempty"`
	OperatorIp     string `protobuf:"bytes,7,opt,name=operator_ip,json=operatorIp,proto3" json:"operator_ip,omitempty"`
	OperatorTarget uint32 `protobuf:"varint,8,opt,name=operator_target,json=operatorTarget,proto3" json:"operator_target,omitempty"`
}

func (x *AddDownloadRecordRequest) Reset() {
	*x = AddDownloadRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddDownloadRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddDownloadRecordRequest) ProtoMessage() {}

func (x *AddDownloadRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddDownloadRecordRequest.ProtoReflect.Descriptor instead.
func (*AddDownloadRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{7}
}

func (x *AddDownloadRecordRequest) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *AddDownloadRecordRequest) GetAction() uint32 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *AddDownloadRecordRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *AddDownloadRecordRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddDownloadRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *AddDownloadRecordRequest) GetOperatorSite() uint32 {
	if x != nil {
		return x.OperatorSite
	}
	return 0
}

func (x *AddDownloadRecordRequest) GetOperatorIp() string {
	if x != nil {
		return x.OperatorIp
	}
	return ""
}

func (x *AddDownloadRecordRequest) GetOperatorTarget() uint32 {
	if x != nil {
		return x.OperatorTarget
	}
	return 0
}

type GetWhitelistRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Type      uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Lang      string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
	StartTime string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Operator  string `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	Message   string `protobuf:"bytes,7,opt,name=message,proto3" json:"message,omitempty"`
	Page      uint32 `protobuf:"varint,8,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit uint32 `protobuf:"varint,9,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetWhitelistRecordRequest) Reset() {
	*x = GetWhitelistRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWhitelistRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistRecordRequest) ProtoMessage() {}

func (x *GetWhitelistRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistRecordRequest.ProtoReflect.Descriptor instead.
func (*GetWhitelistRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{8}
}

func (x *GetWhitelistRecordRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetWhitelistRecordRequest) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GetWhitelistRecordRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *GetWhitelistRecordRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetWhitelistRecordRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetWhitelistRecordRequest) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *GetWhitelistRecordRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetWhitelistRecordRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetWhitelistRecordRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type GetWhitelistRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*WhitelistRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetWhitelistRecordResponse) Reset() {
	*x = GetWhitelistRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWhitelistRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWhitelistRecordResponse) ProtoMessage() {}

func (x *GetWhitelistRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWhitelistRecordResponse.ProtoReflect.Descriptor instead.
func (*GetWhitelistRecordResponse) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{9}
}

func (x *GetWhitelistRecordResponse) GetList() []*WhitelistRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetWhitelistRecordResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type WhitelistRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	HallId     uint32 `protobuf:"varint,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Site       uint32 `protobuf:"varint,3,opt,name=site,proto3" json:"site,omitempty"`
	Website    uint32 `protobuf:"varint,4,opt,name=website,proto3" json:"website,omitempty"`
	Type       uint32 `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	Method     string `protobuf:"bytes,6,opt,name=method,proto3" json:"method,omitempty"`
	Operator   string `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	OperatorId uint32 `protobuf:"varint,8,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	ClientIp   string `protobuf:"bytes,9,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	CreatedAt  string `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Message    string `protobuf:"bytes,11,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *WhitelistRecord) Reset() {
	*x = WhitelistRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WhitelistRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhitelistRecord) ProtoMessage() {}

func (x *WhitelistRecord) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhitelistRecord.ProtoReflect.Descriptor instead.
func (*WhitelistRecord) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{10}
}

func (x *WhitelistRecord) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WhitelistRecord) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *WhitelistRecord) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *WhitelistRecord) GetWebsite() uint32 {
	if x != nil {
		return x.Website
	}
	return 0
}

func (x *WhitelistRecord) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *WhitelistRecord) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *WhitelistRecord) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *WhitelistRecord) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *WhitelistRecord) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *WhitelistRecord) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *WhitelistRecord) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageLimit   uint32 `protobuf:"varint,2,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Total       uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPage   uint32 `protobuf:"varint,4,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{11}
}

func (x *Pagination) GetCurrentPage() uint32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *Pagination) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *Pagination) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Pagination) GetTotalPage() uint32 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetMonthlyReconciliationRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId     uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Tab        uint32 `protobuf:"varint,2,opt,name=tab,proto3" json:"tab,omitempty"`
	Action     uint32 `protobuf:"varint,3,opt,name=action,proto3" json:"action,omitempty"`
	OperatorId uint32 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	StartTime  string `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime    string `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Sort       string `protobuf:"bytes,7,opt,name=sort,proto3" json:"sort,omitempty"`
	Order      string `protobuf:"bytes,8,opt,name=order,proto3" json:"order,omitempty"`
	Page       uint32 `protobuf:"varint,9,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit  uint32 `protobuf:"varint,10,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetMonthlyReconciliationRecordRequest) Reset() {
	*x = GetMonthlyReconciliationRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonthlyReconciliationRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonthlyReconciliationRecordRequest) ProtoMessage() {}

func (x *GetMonthlyReconciliationRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonthlyReconciliationRecordRequest.ProtoReflect.Descriptor instead.
func (*GetMonthlyReconciliationRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{12}
}

func (x *GetMonthlyReconciliationRecordRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetMonthlyReconciliationRecordRequest) GetTab() uint32 {
	if x != nil {
		return x.Tab
	}
	return 0
}

func (x *GetMonthlyReconciliationRecordRequest) GetAction() uint32 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *GetMonthlyReconciliationRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetMonthlyReconciliationRecordRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetMonthlyReconciliationRecordRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetMonthlyReconciliationRecordRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *GetMonthlyReconciliationRecordRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

func (x *GetMonthlyReconciliationRecordRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetMonthlyReconciliationRecordRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type GetMonthlyReconciliationRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*MonthlyReconciliationRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination                    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetMonthlyReconciliationRecordResponse) Reset() {
	*x = GetMonthlyReconciliationRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonthlyReconciliationRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonthlyReconciliationRecordResponse) ProtoMessage() {}

func (x *GetMonthlyReconciliationRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonthlyReconciliationRecordResponse.ProtoReflect.Descriptor instead.
func (*GetMonthlyReconciliationRecordResponse) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{13}
}

func (x *GetMonthlyReconciliationRecordResponse) GetList() []*MonthlyReconciliationRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetMonthlyReconciliationRecordResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MonthlyReconciliationRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tab        uint32   `protobuf:"varint,2,opt,name=tab,proto3" json:"tab,omitempty"`
	Action     uint32   `protobuf:"varint,3,opt,name=action,proto3" json:"action,omitempty"`
	HallId     []uint32 `protobuf:"varint,4,rep,packed,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Content    string   `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	OperatorId uint32   `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	Site       uint32   `protobuf:"varint,7,opt,name=site,proto3" json:"site,omitempty"`
	CreatedAt  string   `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *MonthlyReconciliationRecord) Reset() {
	*x = MonthlyReconciliationRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyReconciliationRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyReconciliationRecord) ProtoMessage() {}

func (x *MonthlyReconciliationRecord) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyReconciliationRecord.ProtoReflect.Descriptor instead.
func (*MonthlyReconciliationRecord) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{14}
}

func (x *MonthlyReconciliationRecord) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MonthlyReconciliationRecord) GetTab() uint32 {
	if x != nil {
		return x.Tab
	}
	return 0
}

func (x *MonthlyReconciliationRecord) GetAction() uint32 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *MonthlyReconciliationRecord) GetHallId() []uint32 {
	if x != nil {
		return x.HallId
	}
	return nil
}

func (x *MonthlyReconciliationRecord) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MonthlyReconciliationRecord) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *MonthlyReconciliationRecord) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *MonthlyReconciliationRecord) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type GetDownloadRecordListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime      string `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime        string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Site           uint32 `protobuf:"varint,3,opt,name=site,proto3" json:"site,omitempty"`
	HallId         uint32 `protobuf:"varint,4,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	OperatorId     uint32 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	OperatorSite   uint32 `protobuf:"varint,6,opt,name=operator_site,json=operatorSite,proto3" json:"operator_site,omitempty"`
	OperatorIp     string `protobuf:"bytes,7,opt,name=operator_ip,json=operatorIp,proto3" json:"operator_ip,omitempty"`
	OperatorTarget uint32 `protobuf:"varint,8,opt,name=operator_target,json=operatorTarget,proto3" json:"operator_target,omitempty"`
	Sort           bool   `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty"`
	Order          string `protobuf:"bytes,10,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *GetDownloadRecordListRequest) Reset() {
	*x = GetDownloadRecordListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDownloadRecordListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDownloadRecordListRequest) ProtoMessage() {}

func (x *GetDownloadRecordListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDownloadRecordListRequest.ProtoReflect.Descriptor instead.
func (*GetDownloadRecordListRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{15}
}

func (x *GetDownloadRecordListRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetDownloadRecordListRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetDownloadRecordListRequest) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *GetDownloadRecordListRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetDownloadRecordListRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetDownloadRecordListRequest) GetOperatorSite() uint32 {
	if x != nil {
		return x.OperatorSite
	}
	return 0
}

func (x *GetDownloadRecordListRequest) GetOperatorIp() string {
	if x != nil {
		return x.OperatorIp
	}
	return ""
}

func (x *GetDownloadRecordListRequest) GetOperatorTarget() uint32 {
	if x != nil {
		return x.OperatorTarget
	}
	return 0
}

func (x *GetDownloadRecordListRequest) GetSort() bool {
	if x != nil {
		return x.Sort
	}
	return false
}

func (x *GetDownloadRecordListRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

type GetDownloadRecordListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*DownloadRecord `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetDownloadRecordListResponse) Reset() {
	*x = GetDownloadRecordListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDownloadRecordListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDownloadRecordListResponse) ProtoMessage() {}

func (x *GetDownloadRecordListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDownloadRecordListResponse.ProtoReflect.Descriptor instead.
func (*GetDownloadRecordListResponse) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{16}
}

func (x *GetDownloadRecordListResponse) GetData() []*DownloadRecord {
	if x != nil {
		return x.Data
	}
	return nil
}

type DownloadRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Site           uint32 `protobuf:"varint,2,opt,name=site,proto3" json:"site,omitempty"`
	Action         uint32 `protobuf:"varint,3,opt,name=action,proto3" json:"action,omitempty"`
	Domain         uint32 `protobuf:"varint,4,opt,name=domain,proto3" json:"domain,omitempty"`
	Content        string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	OperatorId     uint32 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	OperatorSite   uint32 `protobuf:"varint,7,opt,name=operator_site,json=operatorSite,proto3" json:"operator_site,omitempty"`
	OperatorIp     string `protobuf:"bytes,8,opt,name=operator_ip,json=operatorIp,proto3" json:"operator_ip,omitempty"`
	OperatorTarget uint32 `protobuf:"varint,9,opt,name=operator_target,json=operatorTarget,proto3" json:"operator_target,omitempty"`
	CreateAt       string `protobuf:"bytes,10,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
}

func (x *DownloadRecord) Reset() {
	*x = DownloadRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadRecord) ProtoMessage() {}

func (x *DownloadRecord) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadRecord.ProtoReflect.Descriptor instead.
func (*DownloadRecord) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{17}
}

func (x *DownloadRecord) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DownloadRecord) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *DownloadRecord) GetAction() uint32 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *DownloadRecord) GetDomain() uint32 {
	if x != nil {
		return x.Domain
	}
	return 0
}

func (x *DownloadRecord) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *DownloadRecord) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *DownloadRecord) GetOperatorSite() uint32 {
	if x != nil {
		return x.OperatorSite
	}
	return 0
}

func (x *DownloadRecord) GetOperatorIp() string {
	if x != nil {
		return x.OperatorIp
	}
	return ""
}

func (x *DownloadRecord) GetOperatorTarget() uint32 {
	if x != nil {
		return x.OperatorTarget
	}
	return 0
}

func (x *DownloadRecord) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

type CreateGameTypeSwitchRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActionId   uint32 `protobuf:"varint,1,opt,name=action_id,json=actionId,proto3" json:"action_id,omitempty"`
	GameKind   uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId     string `protobuf:"bytes,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Content    string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	OperatorId uint32 `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	CreateAt   string `protobuf:"bytes,6,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
}

func (x *CreateGameTypeSwitchRecordRequest) Reset() {
	*x = CreateGameTypeSwitchRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameTypeSwitchRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameTypeSwitchRecordRequest) ProtoMessage() {}

func (x *CreateGameTypeSwitchRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameTypeSwitchRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateGameTypeSwitchRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{18}
}

func (x *CreateGameTypeSwitchRecordRequest) GetActionId() uint32 {
	if x != nil {
		return x.ActionId
	}
	return 0
}

func (x *CreateGameTypeSwitchRecordRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *CreateGameTypeSwitchRecordRequest) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *CreateGameTypeSwitchRecordRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateGameTypeSwitchRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateGameTypeSwitchRecordRequest) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

type GameTypeSwitchRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime  string   `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime    string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	GameKind   uint32   `protobuf:"varint,3,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId     []string `protobuf:"bytes,4,rep,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	HallId     uint32   `protobuf:"varint,5,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	ActionId   uint32   `protobuf:"varint,6,opt,name=action_id,json=actionId,proto3" json:"action_id,omitempty"`
	OperatorId uint32   `protobuf:"varint,7,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	Sort       string   `protobuf:"bytes,8,opt,name=sort,proto3" json:"sort,omitempty"`
	Order      string   `protobuf:"bytes,9,opt,name=order,proto3" json:"order,omitempty"`
	Page       uint32   `protobuf:"varint,10,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit  uint32   `protobuf:"varint,11,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GameTypeSwitchRecordRequest) Reset() {
	*x = GameTypeSwitchRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameTypeSwitchRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameTypeSwitchRecordRequest) ProtoMessage() {}

func (x *GameTypeSwitchRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameTypeSwitchRecordRequest.ProtoReflect.Descriptor instead.
func (*GameTypeSwitchRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{19}
}

func (x *GameTypeSwitchRecordRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GameTypeSwitchRecordRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GameTypeSwitchRecordRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameTypeSwitchRecordRequest) GetGameId() []string {
	if x != nil {
		return x.GameId
	}
	return nil
}

func (x *GameTypeSwitchRecordRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GameTypeSwitchRecordRequest) GetActionId() uint32 {
	if x != nil {
		return x.ActionId
	}
	return 0
}

func (x *GameTypeSwitchRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GameTypeSwitchRecordRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *GameTypeSwitchRecordRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

func (x *GameTypeSwitchRecordRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GameTypeSwitchRecordRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type GameTypeSwitchRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*GameTypeSwitchRecordInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination                 `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GameTypeSwitchRecordResponse) Reset() {
	*x = GameTypeSwitchRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameTypeSwitchRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameTypeSwitchRecordResponse) ProtoMessage() {}

func (x *GameTypeSwitchRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameTypeSwitchRecordResponse.ProtoReflect.Descriptor instead.
func (*GameTypeSwitchRecordResponse) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{20}
}

func (x *GameTypeSwitchRecordResponse) GetList() []*GameTypeSwitchRecordInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GameTypeSwitchRecordResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GameTypeSwitchRecordInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ActionId   uint32 `protobuf:"varint,2,opt,name=action_id,json=actionId,proto3" json:"action_id,omitempty"`
	GameKind   uint32 `protobuf:"varint,3,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	GameId     string `protobuf:"bytes,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Content    string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	OperatorId uint32 `protobuf:"varint,6,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	CreatedAt  string `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *GameTypeSwitchRecordInfo) Reset() {
	*x = GameTypeSwitchRecordInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameTypeSwitchRecordInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameTypeSwitchRecordInfo) ProtoMessage() {}

func (x *GameTypeSwitchRecordInfo) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameTypeSwitchRecordInfo.ProtoReflect.Descriptor instead.
func (*GameTypeSwitchRecordInfo) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{21}
}

func (x *GameTypeSwitchRecordInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GameTypeSwitchRecordInfo) GetActionId() uint32 {
	if x != nil {
		return x.ActionId
	}
	return 0
}

func (x *GameTypeSwitchRecordInfo) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *GameTypeSwitchRecordInfo) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *GameTypeSwitchRecordInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *GameTypeSwitchRecordInfo) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GameTypeSwitchRecordInfo) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type SystemMonitorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime string `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	GroupId   uint32 `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId    uint32 `protobuf:"varint,4,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Act       string `protobuf:"bytes,5,opt,name=act,proto3" json:"act,omitempty"`
	Category  string `protobuf:"bytes,6,opt,name=category,proto3" json:"category,omitempty"`
	Member    uint32 `protobuf:"varint,7,opt,name=member,proto3" json:"member,omitempty"`
	Page      uint32 `protobuf:"varint,8,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit uint32 `protobuf:"varint,9,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Order     string `protobuf:"bytes,10,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *SystemMonitorRequest) Reset() {
	*x = SystemMonitorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemMonitorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemMonitorRequest) ProtoMessage() {}

func (x *SystemMonitorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemMonitorRequest.ProtoReflect.Descriptor instead.
func (*SystemMonitorRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{22}
}

func (x *SystemMonitorRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SystemMonitorRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SystemMonitorRequest) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *SystemMonitorRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SystemMonitorRequest) GetAct() string {
	if x != nil {
		return x.Act
	}
	return ""
}

func (x *SystemMonitorRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *SystemMonitorRequest) GetMember() uint32 {
	if x != nil {
		return x.Member
	}
	return 0
}

func (x *SystemMonitorRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SystemMonitorRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

func (x *SystemMonitorRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

type SystemMonitorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*SystemMonitor `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination      `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *SystemMonitorResponse) Reset() {
	*x = SystemMonitorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemMonitorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemMonitorResponse) ProtoMessage() {}

func (x *SystemMonitorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemMonitorResponse.ProtoReflect.Descriptor instead.
func (*SystemMonitorResponse) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{23}
}

func (x *SystemMonitorResponse) GetList() []*SystemMonitor {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SystemMonitorResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type SystemMonitor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId    uint32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId     uint32 `protobuf:"varint,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Act        string `protobuf:"bytes,3,opt,name=act,proto3" json:"act,omitempty"`
	Category   string `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	Content    string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Operator   string `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	OperatorIp string `protobuf:"bytes,7,opt,name=operator_ip,json=operatorIp,proto3" json:"operator_ip,omitempty"`
	CreatedAt  string `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *SystemMonitor) Reset() {
	*x = SystemMonitor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemMonitor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemMonitor) ProtoMessage() {}

func (x *SystemMonitor) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemMonitor.ProtoReflect.Descriptor instead.
func (*SystemMonitor) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{24}
}

func (x *SystemMonitor) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *SystemMonitor) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SystemMonitor) GetAct() string {
	if x != nil {
		return x.Act
	}
	return ""
}

func (x *SystemMonitor) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *SystemMonitor) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SystemMonitor) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *SystemMonitor) GetOperatorIp() string {
	if x != nil {
		return x.OperatorIp
	}
	return ""
}

func (x *SystemMonitor) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type CreateSystemMonitorRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId         uint32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HallId          uint32 `protobuf:"varint,2,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Category        string `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`
	Act             string `protobuf:"bytes,4,opt,name=act,proto3" json:"act,omitempty"`
	Content         string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	IsAdminOperator bool   `protobuf:"varint,6,opt,name=is_admin_operator,json=isAdminOperator,proto3" json:"is_admin_operator,omitempty"`
	Operator        string `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	OperatorId      uint32 `protobuf:"varint,8,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	OperatorIp      string `protobuf:"bytes,9,opt,name=operator_ip,json=operatorIp,proto3" json:"operator_ip,omitempty"`
}

func (x *CreateSystemMonitorRecordRequest) Reset() {
	*x = CreateSystemMonitorRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSystemMonitorRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSystemMonitorRecordRequest) ProtoMessage() {}

func (x *CreateSystemMonitorRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSystemMonitorRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateSystemMonitorRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{25}
}

func (x *CreateSystemMonitorRecordRequest) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *CreateSystemMonitorRecordRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *CreateSystemMonitorRecordRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CreateSystemMonitorRecordRequest) GetAct() string {
	if x != nil {
		return x.Act
	}
	return ""
}

func (x *CreateSystemMonitorRecordRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateSystemMonitorRecordRequest) GetIsAdminOperator() bool {
	if x != nil {
		return x.IsAdminOperator
	}
	return false
}

func (x *CreateSystemMonitorRecordRequest) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *CreateSystemMonitorRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateSystemMonitorRecordRequest) GetOperatorIp() string {
	if x != nil {
		return x.OperatorIp
	}
	return ""
}

type GetSystemMaintenanceRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime     string       `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string       `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Website       string       `protobuf:"bytes,3,opt,name=website,proto3" json:"website,omitempty"`
	HallIds       []uint32     `protobuf:"varint,4,rep,packed,name=hall_ids,json=hallIds,proto3" json:"hall_ids,omitempty"`
	OperatorId    uint32       `protobuf:"varint,5,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	AdminReadOnly *Uint32Value `protobuf:"bytes,6,opt,name=admin_read_only,json=adminReadOnly,proto3" json:"admin_read_only,omitempty"`
	Page          uint32       `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageLimit     uint32       `protobuf:"varint,8,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
}

func (x *GetSystemMaintenanceRecordRequest) Reset() {
	*x = GetSystemMaintenanceRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSystemMaintenanceRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSystemMaintenanceRecordRequest) ProtoMessage() {}

func (x *GetSystemMaintenanceRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSystemMaintenanceRecordRequest.ProtoReflect.Descriptor instead.
func (*GetSystemMaintenanceRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{26}
}

func (x *GetSystemMaintenanceRecordRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetSystemMaintenanceRecordRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetSystemMaintenanceRecordRequest) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *GetSystemMaintenanceRecordRequest) GetHallIds() []uint32 {
	if x != nil {
		return x.HallIds
	}
	return nil
}

func (x *GetSystemMaintenanceRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *GetSystemMaintenanceRecordRequest) GetAdminReadOnly() *Uint32Value {
	if x != nil {
		return x.AdminReadOnly
	}
	return nil
}

func (x *GetSystemMaintenanceRecordRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetSystemMaintenanceRecordRequest) GetPageLimit() uint32 {
	if x != nil {
		return x.PageLimit
	}
	return 0
}

type SystemMaintenanceRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Site       uint32 `protobuf:"varint,2,opt,name=site,proto3" json:"site,omitempty"`
	Website    string `protobuf:"bytes,3,opt,name=website,proto3" json:"website,omitempty"`
	Category   string `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	Content    string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Operator   string `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	OperatorId uint32 `protobuf:"varint,7,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	OperatorIp string `protobuf:"bytes,8,opt,name=operator_ip,json=operatorIp,proto3" json:"operator_ip,omitempty"`
	CreatedAt  string `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *SystemMaintenanceRecord) Reset() {
	*x = SystemMaintenanceRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemMaintenanceRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemMaintenanceRecord) ProtoMessage() {}

func (x *SystemMaintenanceRecord) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemMaintenanceRecord.ProtoReflect.Descriptor instead.
func (*SystemMaintenanceRecord) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{27}
}

func (x *SystemMaintenanceRecord) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SystemMaintenanceRecord) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *SystemMaintenanceRecord) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *SystemMaintenanceRecord) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *SystemMaintenanceRecord) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SystemMaintenanceRecord) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *SystemMaintenanceRecord) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *SystemMaintenanceRecord) GetOperatorIp() string {
	if x != nil {
		return x.OperatorIp
	}
	return ""
}

func (x *SystemMaintenanceRecord) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type GetSystemMaintenanceRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*SystemMaintenanceRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Pagination *Pagination                `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetSystemMaintenanceRecordResponse) Reset() {
	*x = GetSystemMaintenanceRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSystemMaintenanceRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSystemMaintenanceRecordResponse) ProtoMessage() {}

func (x *GetSystemMaintenanceRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSystemMaintenanceRecordResponse.ProtoReflect.Descriptor instead.
func (*GetSystemMaintenanceRecordResponse) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{28}
}

func (x *GetSystemMaintenanceRecordResponse) GetList() []*SystemMaintenanceRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetSystemMaintenanceRecordResponse) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CreateSystemMaintenanceRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site          uint32 `protobuf:"varint,1,opt,name=site,proto3" json:"site,omitempty"`
	Website       string `protobuf:"bytes,2,opt,name=website,proto3" json:"website,omitempty"`
	HallId        uint32 `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Category      string `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	Content       string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	AdminReadOnly bool   `protobuf:"varint,6,opt,name=admin_read_only,json=adminReadOnly,proto3" json:"admin_read_only,omitempty"`
	Operator      string `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	OperatorId    uint32 `protobuf:"varint,8,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	OperatorIp    string `protobuf:"bytes,9,opt,name=operator_ip,json=operatorIp,proto3" json:"operator_ip,omitempty"`
}

func (x *CreateSystemMaintenanceRecordRequest) Reset() {
	*x = CreateSystemMaintenanceRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_operationrecord_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSystemMaintenanceRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSystemMaintenanceRecordRequest) ProtoMessage() {}

func (x *CreateSystemMaintenanceRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_operationrecord_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSystemMaintenanceRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateSystemMaintenanceRecordRequest) Descriptor() ([]byte, []int) {
	return file_operationrecord_proto_rawDescGZIP(), []int{29}
}

func (x *CreateSystemMaintenanceRecordRequest) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *CreateSystemMaintenanceRecordRequest) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *CreateSystemMaintenanceRecordRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *CreateSystemMaintenanceRecordRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CreateSystemMaintenanceRecordRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateSystemMaintenanceRecordRequest) GetAdminReadOnly() bool {
	if x != nil {
		return x.AdminReadOnly
	}
	return false
}

func (x *CreateSystemMaintenanceRecordRequest) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *CreateSystemMaintenanceRecordRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CreateSystemMaintenanceRecordRequest) GetOperatorIp() string {
	if x != nil {
		return x.OperatorIp
	}
	return ""
}

var File_operationrecord_proto protoreflect.FileDescriptor

var file_operationrecord_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x23, 0x0a, 0x0b, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8b, 0x01,
	0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xeb, 0x01, 0x0a, 0x1b,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x75, 0x62, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x6f, 0x6c, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xab, 0x03, 0x0a, 0x15, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x41, 0x47, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x75,
	0x62, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x62, 0x6f, 0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x6f, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x4b,
	0x65, 0x79, 0x12, 0x3b, 0x0a, 0x0c, 0x78, 0x6d, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x73, 0x67, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0a, 0x78, 0x6d, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x73, 0x67, 0x12,
	0x24, 0x0a, 0x0e, 0x78, 0x6d, 0x6c, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x78, 0x6d, 0x6c, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x54, 0x61, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x32, 0x0a, 0x08, 0x4b, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xfd, 0x01, 0x0a,
	0x13, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72,
	0x65, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x6c, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70,
	0x6c, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x61, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x71, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x71, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x62, 0x79, 0x77, 0x68, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62,
	0x79, 0x77, 0x68, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x79, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x79, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x89, 0x02, 0x0a,
	0x18, 0x41, 0x64, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x74, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x70, 0x12,
	0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0xff, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x8f, 0x01, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x3b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa7, 0x02, 0x0a,
	0x0f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x22, 0xa2, 0x02, 0x0a,
	0x25, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x6e,
	0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x74, 0x61, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x61,
	0x62, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x22, 0xa7, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x3b,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xde, 0x01, 0x0a, 0x1b,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74,
	0x61, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x61, 0x62, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xbf, 0x02, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61,
	0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x54,
	0x0a, 0x1d, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0xab, 0x02, 0x0a, 0x0e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x70, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x74, 0x22, 0xce, 0x01, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69,
	0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x74, 0x22, 0xc1, 0x02, 0x0a, 0x1b, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x1c, 0x47, 0x61, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd7, 0x01, 0x0a, 0x18, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61,
	0x6d, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x93,
	0x02, 0x0a, 0x14, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x61, 0x63, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x22, 0x88, 0x01, 0x0a, 0x15, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xe7, 0x01, 0x0a, 0x0d, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x61, 0x63, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xa8, 0x02, 0x0a, 0x20, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x61, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x63, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73,
	0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x70, 0x22, 0xac, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x22, 0x8a, 0x02, 0x0a, 0x17, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73,
	0x69, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0x9f, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xa9, 0x02, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79,
	0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x70, 0x32, 0x9a,
	0x0c, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x5c, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x28, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x64, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2c, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x47, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x26, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x47, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x54, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x24, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x29, 0x2e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x41, 0x64,
	0x64, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2a, 0x2e, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47,
	0x65, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x68,
	0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x36, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x37, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2d, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2e, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x70, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x32, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x14, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2c, 0x2e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47, 0x61, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x25, 0x2e, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x19, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x31, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x32, 0x2e, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x35, 0x2e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x17, 0x5a, 0x15, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_operationrecord_proto_rawDescOnce sync.Once
	file_operationrecord_proto_rawDescData = file_operationrecord_proto_rawDesc
)

func file_operationrecord_proto_rawDescGZIP() []byte {
	file_operationrecord_proto_rawDescOnce.Do(func() {
		file_operationrecord_proto_rawDescData = protoimpl.X.CompressGZIP(file_operationrecord_proto_rawDescData)
	})
	return file_operationrecord_proto_rawDescData
}

var file_operationrecord_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_operationrecord_proto_goTypes = []any{
	(*Uint32Value)(nil),                            // 0: operationrecord.Uint32Value
	(*CreateUserRecordRequest)(nil),                // 1: operationrecord.CreateUserRecordRequest
	(*CreateTransferRecordRequest)(nil),            // 2: operationrecord.CreateTransferRecordRequest
	(*CreateAGRecordRequest)(nil),                  // 3: operationrecord.CreateAGRecordRequest
	(*KeyValue)(nil),                               // 4: operationrecord.KeyValue
	(*EmptyResponse)(nil),                          // 5: operationrecord.EmptyResponse
	(*AddLogChangeRequest)(nil),                    // 6: operationrecord.AddLogChangeRequest
	(*AddDownloadRecordRequest)(nil),               // 7: operationrecord.AddDownloadRecordRequest
	(*GetWhitelistRecordRequest)(nil),              // 8: operationrecord.GetWhitelistRecordRequest
	(*GetWhitelistRecordResponse)(nil),             // 9: operationrecord.GetWhitelistRecordResponse
	(*WhitelistRecord)(nil),                        // 10: operationrecord.WhitelistRecord
	(*Pagination)(nil),                             // 11: operationrecord.Pagination
	(*GetMonthlyReconciliationRecordRequest)(nil),  // 12: operationrecord.GetMonthlyReconciliationRecordRequest
	(*GetMonthlyReconciliationRecordResponse)(nil), // 13: operationrecord.GetMonthlyReconciliationRecordResponse
	(*MonthlyReconciliationRecord)(nil),            // 14: operationrecord.MonthlyReconciliationRecord
	(*GetDownloadRecordListRequest)(nil),           // 15: operationrecord.GetDownloadRecordListRequest
	(*GetDownloadRecordListResponse)(nil),          // 16: operationrecord.GetDownloadRecordListResponse
	(*DownloadRecord)(nil),                         // 17: operationrecord.DownloadRecord
	(*CreateGameTypeSwitchRecordRequest)(nil),      // 18: operationrecord.CreateGameTypeSwitchRecordRequest
	(*GameTypeSwitchRecordRequest)(nil),            // 19: operationrecord.GameTypeSwitchRecordRequest
	(*GameTypeSwitchRecordResponse)(nil),           // 20: operationrecord.GameTypeSwitchRecordResponse
	(*GameTypeSwitchRecordInfo)(nil),               // 21: operationrecord.GameTypeSwitchRecordInfo
	(*SystemMonitorRequest)(nil),                   // 22: operationrecord.SystemMonitorRequest
	(*SystemMonitorResponse)(nil),                  // 23: operationrecord.SystemMonitorResponse
	(*SystemMonitor)(nil),                          // 24: operationrecord.SystemMonitor
	(*CreateSystemMonitorRecordRequest)(nil),       // 25: operationrecord.CreateSystemMonitorRecordRequest
	(*GetSystemMaintenanceRecordRequest)(nil),      // 26: operationrecord.GetSystemMaintenanceRecordRequest
	(*SystemMaintenanceRecord)(nil),                // 27: operationrecord.SystemMaintenanceRecord
	(*GetSystemMaintenanceRecordResponse)(nil),     // 28: operationrecord.GetSystemMaintenanceRecordResponse
	(*CreateSystemMaintenanceRecordRequest)(nil),   // 29: operationrecord.CreateSystemMaintenanceRecordRequest
}
var file_operationrecord_proto_depIdxs = []int32{
	4,  // 0: operationrecord.CreateAGRecordRequest.xml_data_msg:type_name -> operationrecord.KeyValue
	10, // 1: operationrecord.GetWhitelistRecordResponse.list:type_name -> operationrecord.WhitelistRecord
	11, // 2: operationrecord.GetWhitelistRecordResponse.pagination:type_name -> operationrecord.Pagination
	14, // 3: operationrecord.GetMonthlyReconciliationRecordResponse.list:type_name -> operationrecord.MonthlyReconciliationRecord
	11, // 4: operationrecord.GetMonthlyReconciliationRecordResponse.pagination:type_name -> operationrecord.Pagination
	17, // 5: operationrecord.GetDownloadRecordListResponse.data:type_name -> operationrecord.DownloadRecord
	21, // 6: operationrecord.GameTypeSwitchRecordResponse.list:type_name -> operationrecord.GameTypeSwitchRecordInfo
	11, // 7: operationrecord.GameTypeSwitchRecordResponse.pagination:type_name -> operationrecord.Pagination
	24, // 8: operationrecord.SystemMonitorResponse.list:type_name -> operationrecord.SystemMonitor
	11, // 9: operationrecord.SystemMonitorResponse.pagination:type_name -> operationrecord.Pagination
	0,  // 10: operationrecord.GetSystemMaintenanceRecordRequest.admin_read_only:type_name -> operationrecord.Uint32Value
	27, // 11: operationrecord.GetSystemMaintenanceRecordResponse.list:type_name -> operationrecord.SystemMaintenanceRecord
	11, // 12: operationrecord.GetSystemMaintenanceRecordResponse.pagination:type_name -> operationrecord.Pagination
	1,  // 13: operationrecord.OperationRecord.CreateUserRecord:input_type -> operationrecord.CreateUserRecordRequest
	2,  // 14: operationrecord.OperationRecord.CreateTransferRecord:input_type -> operationrecord.CreateTransferRecordRequest
	3,  // 15: operationrecord.OperationRecord.CreateAGRecord:input_type -> operationrecord.CreateAGRecordRequest
	6,  // 16: operationrecord.OperationRecord.AddLogChange:input_type -> operationrecord.AddLogChangeRequest
	7,  // 17: operationrecord.OperationRecord.AddDownloadRecord:input_type -> operationrecord.AddDownloadRecordRequest
	8,  // 18: operationrecord.OperationRecord.GetWhitelistRecord:input_type -> operationrecord.GetWhitelistRecordRequest
	12, // 19: operationrecord.OperationRecord.GetMonthlyReconciliationRecord:input_type -> operationrecord.GetMonthlyReconciliationRecordRequest
	15, // 20: operationrecord.OperationRecord.GetDownloadRecordList:input_type -> operationrecord.GetDownloadRecordListRequest
	18, // 21: operationrecord.OperationRecord.CreateGameTypeSwitchRecord:input_type -> operationrecord.CreateGameTypeSwitchRecordRequest
	19, // 22: operationrecord.OperationRecord.GameTypeSwitchRecord:input_type -> operationrecord.GameTypeSwitchRecordRequest
	22, // 23: operationrecord.OperationRecord.GetSystemMonitor:input_type -> operationrecord.SystemMonitorRequest
	25, // 24: operationrecord.OperationRecord.CreateSystemMonitorRecord:input_type -> operationrecord.CreateSystemMonitorRecordRequest
	26, // 25: operationrecord.OperationRecord.GetSystemMaintenanceRecord:input_type -> operationrecord.GetSystemMaintenanceRecordRequest
	29, // 26: operationrecord.OperationRecord.CreateSystemMaintenanceRecord:input_type -> operationrecord.CreateSystemMaintenanceRecordRequest
	5,  // 27: operationrecord.OperationRecord.CreateUserRecord:output_type -> operationrecord.EmptyResponse
	5,  // 28: operationrecord.OperationRecord.CreateTransferRecord:output_type -> operationrecord.EmptyResponse
	5,  // 29: operationrecord.OperationRecord.CreateAGRecord:output_type -> operationrecord.EmptyResponse
	5,  // 30: operationrecord.OperationRecord.AddLogChange:output_type -> operationrecord.EmptyResponse
	5,  // 31: operationrecord.OperationRecord.AddDownloadRecord:output_type -> operationrecord.EmptyResponse
	9,  // 32: operationrecord.OperationRecord.GetWhitelistRecord:output_type -> operationrecord.GetWhitelistRecordResponse
	13, // 33: operationrecord.OperationRecord.GetMonthlyReconciliationRecord:output_type -> operationrecord.GetMonthlyReconciliationRecordResponse
	16, // 34: operationrecord.OperationRecord.GetDownloadRecordList:output_type -> operationrecord.GetDownloadRecordListResponse
	5,  // 35: operationrecord.OperationRecord.CreateGameTypeSwitchRecord:output_type -> operationrecord.EmptyResponse
	20, // 36: operationrecord.OperationRecord.GameTypeSwitchRecord:output_type -> operationrecord.GameTypeSwitchRecordResponse
	23, // 37: operationrecord.OperationRecord.GetSystemMonitor:output_type -> operationrecord.SystemMonitorResponse
	5,  // 38: operationrecord.OperationRecord.CreateSystemMonitorRecord:output_type -> operationrecord.EmptyResponse
	28, // 39: operationrecord.OperationRecord.GetSystemMaintenanceRecord:output_type -> operationrecord.GetSystemMaintenanceRecordResponse
	5,  // 40: operationrecord.OperationRecord.CreateSystemMaintenanceRecord:output_type -> operationrecord.EmptyResponse
	27, // [27:41] is the sub-list for method output_type
	13, // [13:27] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_operationrecord_proto_init() }
func file_operationrecord_proto_init() {
	if File_operationrecord_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_operationrecord_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Uint32Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CreateUserRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*CreateTransferRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*CreateAGRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*KeyValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*AddLogChangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*AddDownloadRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*GetWhitelistRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetWhitelistRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*WhitelistRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*Pagination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*GetMonthlyReconciliationRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetMonthlyReconciliationRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*MonthlyReconciliationRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetDownloadRecordListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*GetDownloadRecordListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*DownloadRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*CreateGameTypeSwitchRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GameTypeSwitchRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*GameTypeSwitchRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*GameTypeSwitchRecordInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*SystemMonitorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*SystemMonitorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*SystemMonitor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*CreateSystemMonitorRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*GetSystemMaintenanceRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*SystemMaintenanceRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*GetSystemMaintenanceRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_operationrecord_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*CreateSystemMaintenanceRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_operationrecord_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_operationrecord_proto_goTypes,
		DependencyIndexes: file_operationrecord_proto_depIdxs,
		MessageInfos:      file_operationrecord_proto_msgTypes,
	}.Build()
	File_operationrecord_proto = out.File
	file_operationrecord_proto_rawDesc = nil
	file_operationrecord_proto_goTypes = nil
	file_operationrecord_proto_depIdxs = nil
}
