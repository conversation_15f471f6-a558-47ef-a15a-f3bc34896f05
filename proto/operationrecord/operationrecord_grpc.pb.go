// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: operationrecord.proto

package operationrecord

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OperationRecord_CreateUserRecord_FullMethodName               = "/operationrecord.OperationRecord/CreateUserRecord"
	OperationRecord_CreateTransferRecord_FullMethodName           = "/operationrecord.OperationRecord/CreateTransferRecord"
	OperationRecord_CreateAGRecord_FullMethodName                 = "/operationrecord.OperationRecord/CreateAGRecord"
	OperationRecord_AddLogChange_FullMethodName                   = "/operationrecord.OperationRecord/AddLogChange"
	OperationRecord_AddDownloadRecord_FullMethodName              = "/operationrecord.OperationRecord/AddDownloadRecord"
	OperationRecord_GetWhitelistRecord_FullMethodName             = "/operationrecord.OperationRecord/GetWhitelistRecord"
	OperationRecord_GetMonthlyReconciliationRecord_FullMethodName = "/operationrecord.OperationRecord/GetMonthlyReconciliationRecord"
	OperationRecord_GetDownloadRecordList_FullMethodName          = "/operationrecord.OperationRecord/GetDownloadRecordList"
	OperationRecord_CreateGameTypeSwitchRecord_FullMethodName     = "/operationrecord.OperationRecord/CreateGameTypeSwitchRecord"
	OperationRecord_GameTypeSwitchRecord_FullMethodName           = "/operationrecord.OperationRecord/GameTypeSwitchRecord"
	OperationRecord_GetSystemMonitor_FullMethodName               = "/operationrecord.OperationRecord/GetSystemMonitor"
	OperationRecord_CreateSystemMonitorRecord_FullMethodName      = "/operationrecord.OperationRecord/CreateSystemMonitorRecord"
	OperationRecord_GetSystemMaintenanceRecord_FullMethodName     = "/operationrecord.OperationRecord/GetSystemMaintenanceRecord"
	OperationRecord_CreateSystemMaintenanceRecord_FullMethodName  = "/operationrecord.OperationRecord/CreateSystemMaintenanceRecord"
)

// OperationRecordClient is the client API for OperationRecord service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OperationRecordClient interface {
	CreateUserRecord(ctx context.Context, in *CreateUserRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	CreateTransferRecord(ctx context.Context, in *CreateTransferRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	CreateAGRecord(ctx context.Context, in *CreateAGRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	AddLogChange(ctx context.Context, in *AddLogChangeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	AddDownloadRecord(ctx context.Context, in *AddDownloadRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetWhitelistRecord(ctx context.Context, in *GetWhitelistRecordRequest, opts ...grpc.CallOption) (*GetWhitelistRecordResponse, error)
	GetMonthlyReconciliationRecord(ctx context.Context, in *GetMonthlyReconciliationRecordRequest, opts ...grpc.CallOption) (*GetMonthlyReconciliationRecordResponse, error)
	GetDownloadRecordList(ctx context.Context, in *GetDownloadRecordListRequest, opts ...grpc.CallOption) (*GetDownloadRecordListResponse, error)
	CreateGameTypeSwitchRecord(ctx context.Context, in *CreateGameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GameTypeSwitchRecord(ctx context.Context, in *GameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*GameTypeSwitchRecordResponse, error)
	GetSystemMonitor(ctx context.Context, in *SystemMonitorRequest, opts ...grpc.CallOption) (*SystemMonitorResponse, error)
	CreateSystemMonitorRecord(ctx context.Context, in *CreateSystemMonitorRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	GetSystemMaintenanceRecord(ctx context.Context, in *GetSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*GetSystemMaintenanceRecordResponse, error)
	CreateSystemMaintenanceRecord(ctx context.Context, in *CreateSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
}

type operationRecordClient struct {
	cc grpc.ClientConnInterface
}

func NewOperationRecordClient(cc grpc.ClientConnInterface) OperationRecordClient {
	return &operationRecordClient{cc}
}

func (c *operationRecordClient) CreateUserRecord(ctx context.Context, in *CreateUserRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_CreateUserRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) CreateTransferRecord(ctx context.Context, in *CreateTransferRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_CreateTransferRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) CreateAGRecord(ctx context.Context, in *CreateAGRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_CreateAGRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) AddLogChange(ctx context.Context, in *AddLogChangeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_AddLogChange_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) AddDownloadRecord(ctx context.Context, in *AddDownloadRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_AddDownloadRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) GetWhitelistRecord(ctx context.Context, in *GetWhitelistRecordRequest, opts ...grpc.CallOption) (*GetWhitelistRecordResponse, error) {
	out := new(GetWhitelistRecordResponse)
	err := c.cc.Invoke(ctx, OperationRecord_GetWhitelistRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) GetMonthlyReconciliationRecord(ctx context.Context, in *GetMonthlyReconciliationRecordRequest, opts ...grpc.CallOption) (*GetMonthlyReconciliationRecordResponse, error) {
	out := new(GetMonthlyReconciliationRecordResponse)
	err := c.cc.Invoke(ctx, OperationRecord_GetMonthlyReconciliationRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) GetDownloadRecordList(ctx context.Context, in *GetDownloadRecordListRequest, opts ...grpc.CallOption) (*GetDownloadRecordListResponse, error) {
	out := new(GetDownloadRecordListResponse)
	err := c.cc.Invoke(ctx, OperationRecord_GetDownloadRecordList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) CreateGameTypeSwitchRecord(ctx context.Context, in *CreateGameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_CreateGameTypeSwitchRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) GameTypeSwitchRecord(ctx context.Context, in *GameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*GameTypeSwitchRecordResponse, error) {
	out := new(GameTypeSwitchRecordResponse)
	err := c.cc.Invoke(ctx, OperationRecord_GameTypeSwitchRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) GetSystemMonitor(ctx context.Context, in *SystemMonitorRequest, opts ...grpc.CallOption) (*SystemMonitorResponse, error) {
	out := new(SystemMonitorResponse)
	err := c.cc.Invoke(ctx, OperationRecord_GetSystemMonitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) CreateSystemMonitorRecord(ctx context.Context, in *CreateSystemMonitorRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_CreateSystemMonitorRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) GetSystemMaintenanceRecord(ctx context.Context, in *GetSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*GetSystemMaintenanceRecordResponse, error) {
	out := new(GetSystemMaintenanceRecordResponse)
	err := c.cc.Invoke(ctx, OperationRecord_GetSystemMaintenanceRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *operationRecordClient) CreateSystemMaintenanceRecord(ctx context.Context, in *CreateSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, OperationRecord_CreateSystemMaintenanceRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OperationRecordServer is the server API for OperationRecord service.
// All implementations must embed UnimplementedOperationRecordServer
// for forward compatibility
type OperationRecordServer interface {
	CreateUserRecord(context.Context, *CreateUserRecordRequest) (*EmptyResponse, error)
	CreateTransferRecord(context.Context, *CreateTransferRecordRequest) (*EmptyResponse, error)
	CreateAGRecord(context.Context, *CreateAGRecordRequest) (*EmptyResponse, error)
	AddLogChange(context.Context, *AddLogChangeRequest) (*EmptyResponse, error)
	AddDownloadRecord(context.Context, *AddDownloadRecordRequest) (*EmptyResponse, error)
	GetWhitelistRecord(context.Context, *GetWhitelistRecordRequest) (*GetWhitelistRecordResponse, error)
	GetMonthlyReconciliationRecord(context.Context, *GetMonthlyReconciliationRecordRequest) (*GetMonthlyReconciliationRecordResponse, error)
	GetDownloadRecordList(context.Context, *GetDownloadRecordListRequest) (*GetDownloadRecordListResponse, error)
	CreateGameTypeSwitchRecord(context.Context, *CreateGameTypeSwitchRecordRequest) (*EmptyResponse, error)
	GameTypeSwitchRecord(context.Context, *GameTypeSwitchRecordRequest) (*GameTypeSwitchRecordResponse, error)
	GetSystemMonitor(context.Context, *SystemMonitorRequest) (*SystemMonitorResponse, error)
	CreateSystemMonitorRecord(context.Context, *CreateSystemMonitorRecordRequest) (*EmptyResponse, error)
	GetSystemMaintenanceRecord(context.Context, *GetSystemMaintenanceRecordRequest) (*GetSystemMaintenanceRecordResponse, error)
	CreateSystemMaintenanceRecord(context.Context, *CreateSystemMaintenanceRecordRequest) (*EmptyResponse, error)
	mustEmbedUnimplementedOperationRecordServer()
}

// UnimplementedOperationRecordServer must be embedded to have forward compatible implementations.
type UnimplementedOperationRecordServer struct {
}

func (UnimplementedOperationRecordServer) CreateUserRecord(context.Context, *CreateUserRecordRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserRecord not implemented")
}
func (UnimplementedOperationRecordServer) CreateTransferRecord(context.Context, *CreateTransferRecordRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTransferRecord not implemented")
}
func (UnimplementedOperationRecordServer) CreateAGRecord(context.Context, *CreateAGRecordRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAGRecord not implemented")
}
func (UnimplementedOperationRecordServer) AddLogChange(context.Context, *AddLogChangeRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLogChange not implemented")
}
func (UnimplementedOperationRecordServer) AddDownloadRecord(context.Context, *AddDownloadRecordRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDownloadRecord not implemented")
}
func (UnimplementedOperationRecordServer) GetWhitelistRecord(context.Context, *GetWhitelistRecordRequest) (*GetWhitelistRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWhitelistRecord not implemented")
}
func (UnimplementedOperationRecordServer) GetMonthlyReconciliationRecord(context.Context, *GetMonthlyReconciliationRecordRequest) (*GetMonthlyReconciliationRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonthlyReconciliationRecord not implemented")
}
func (UnimplementedOperationRecordServer) GetDownloadRecordList(context.Context, *GetDownloadRecordListRequest) (*GetDownloadRecordListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDownloadRecordList not implemented")
}
func (UnimplementedOperationRecordServer) CreateGameTypeSwitchRecord(context.Context, *CreateGameTypeSwitchRecordRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGameTypeSwitchRecord not implemented")
}
func (UnimplementedOperationRecordServer) GameTypeSwitchRecord(context.Context, *GameTypeSwitchRecordRequest) (*GameTypeSwitchRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameTypeSwitchRecord not implemented")
}
func (UnimplementedOperationRecordServer) GetSystemMonitor(context.Context, *SystemMonitorRequest) (*SystemMonitorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSystemMonitor not implemented")
}
func (UnimplementedOperationRecordServer) CreateSystemMonitorRecord(context.Context, *CreateSystemMonitorRecordRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSystemMonitorRecord not implemented")
}
func (UnimplementedOperationRecordServer) GetSystemMaintenanceRecord(context.Context, *GetSystemMaintenanceRecordRequest) (*GetSystemMaintenanceRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSystemMaintenanceRecord not implemented")
}
func (UnimplementedOperationRecordServer) CreateSystemMaintenanceRecord(context.Context, *CreateSystemMaintenanceRecordRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSystemMaintenanceRecord not implemented")
}
func (UnimplementedOperationRecordServer) mustEmbedUnimplementedOperationRecordServer() {}

// UnsafeOperationRecordServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OperationRecordServer will
// result in compilation errors.
type UnsafeOperationRecordServer interface {
	mustEmbedUnimplementedOperationRecordServer()
}

func RegisterOperationRecordServer(s grpc.ServiceRegistrar, srv OperationRecordServer) {
	s.RegisterService(&OperationRecord_ServiceDesc, srv)
}

func _OperationRecord_CreateUserRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).CreateUserRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_CreateUserRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).CreateUserRecord(ctx, req.(*CreateUserRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_CreateTransferRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTransferRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).CreateTransferRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_CreateTransferRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).CreateTransferRecord(ctx, req.(*CreateTransferRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_CreateAGRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAGRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).CreateAGRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_CreateAGRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).CreateAGRecord(ctx, req.(*CreateAGRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_AddLogChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLogChangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).AddLogChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_AddLogChange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).AddLogChange(ctx, req.(*AddLogChangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_AddDownloadRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDownloadRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).AddDownloadRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_AddDownloadRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).AddDownloadRecord(ctx, req.(*AddDownloadRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_GetWhitelistRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhitelistRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).GetWhitelistRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_GetWhitelistRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).GetWhitelistRecord(ctx, req.(*GetWhitelistRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_GetMonthlyReconciliationRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonthlyReconciliationRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).GetMonthlyReconciliationRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_GetMonthlyReconciliationRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).GetMonthlyReconciliationRecord(ctx, req.(*GetMonthlyReconciliationRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_GetDownloadRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDownloadRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).GetDownloadRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_GetDownloadRecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).GetDownloadRecordList(ctx, req.(*GetDownloadRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_CreateGameTypeSwitchRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGameTypeSwitchRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).CreateGameTypeSwitchRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_CreateGameTypeSwitchRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).CreateGameTypeSwitchRecord(ctx, req.(*CreateGameTypeSwitchRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_GameTypeSwitchRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameTypeSwitchRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).GameTypeSwitchRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_GameTypeSwitchRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).GameTypeSwitchRecord(ctx, req.(*GameTypeSwitchRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_GetSystemMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SystemMonitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).GetSystemMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_GetSystemMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).GetSystemMonitor(ctx, req.(*SystemMonitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_CreateSystemMonitorRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSystemMonitorRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).CreateSystemMonitorRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_CreateSystemMonitorRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).CreateSystemMonitorRecord(ctx, req.(*CreateSystemMonitorRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_GetSystemMaintenanceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSystemMaintenanceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).GetSystemMaintenanceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_GetSystemMaintenanceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).GetSystemMaintenanceRecord(ctx, req.(*GetSystemMaintenanceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OperationRecord_CreateSystemMaintenanceRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSystemMaintenanceRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OperationRecordServer).CreateSystemMaintenanceRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OperationRecord_CreateSystemMaintenanceRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OperationRecordServer).CreateSystemMaintenanceRecord(ctx, req.(*CreateSystemMaintenanceRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OperationRecord_ServiceDesc is the grpc.ServiceDesc for OperationRecord service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OperationRecord_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "operationrecord.OperationRecord",
	HandlerType: (*OperationRecordServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUserRecord",
			Handler:    _OperationRecord_CreateUserRecord_Handler,
		},
		{
			MethodName: "CreateTransferRecord",
			Handler:    _OperationRecord_CreateTransferRecord_Handler,
		},
		{
			MethodName: "CreateAGRecord",
			Handler:    _OperationRecord_CreateAGRecord_Handler,
		},
		{
			MethodName: "AddLogChange",
			Handler:    _OperationRecord_AddLogChange_Handler,
		},
		{
			MethodName: "AddDownloadRecord",
			Handler:    _OperationRecord_AddDownloadRecord_Handler,
		},
		{
			MethodName: "GetWhitelistRecord",
			Handler:    _OperationRecord_GetWhitelistRecord_Handler,
		},
		{
			MethodName: "GetMonthlyReconciliationRecord",
			Handler:    _OperationRecord_GetMonthlyReconciliationRecord_Handler,
		},
		{
			MethodName: "GetDownloadRecordList",
			Handler:    _OperationRecord_GetDownloadRecordList_Handler,
		},
		{
			MethodName: "CreateGameTypeSwitchRecord",
			Handler:    _OperationRecord_CreateGameTypeSwitchRecord_Handler,
		},
		{
			MethodName: "GameTypeSwitchRecord",
			Handler:    _OperationRecord_GameTypeSwitchRecord_Handler,
		},
		{
			MethodName: "GetSystemMonitor",
			Handler:    _OperationRecord_GetSystemMonitor_Handler,
		},
		{
			MethodName: "CreateSystemMonitorRecord",
			Handler:    _OperationRecord_CreateSystemMonitorRecord_Handler,
		},
		{
			MethodName: "GetSystemMaintenanceRecord",
			Handler:    _OperationRecord_GetSystemMaintenanceRecord_Handler,
		},
		{
			MethodName: "CreateSystemMaintenanceRecord",
			Handler:    _OperationRecord_CreateSystemMaintenanceRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "operationrecord.proto",
}
