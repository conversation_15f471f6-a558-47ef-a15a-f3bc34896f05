// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.19.4
// source: hall.proto

package hall

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BoolValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value bool `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{0}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{1}
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{2}
}

type GetHallIdByWebsiteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Website string `protobuf:"bytes,1,opt,name=website,proto3" json:"website,omitempty"`
}

func (x *GetHallIdByWebsiteRequest) Reset() {
	*x = GetHallIdByWebsiteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallIdByWebsiteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallIdByWebsiteRequest) ProtoMessage() {}

func (x *GetHallIdByWebsiteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallIdByWebsiteRequest.ProtoReflect.Descriptor instead.
func (*GetHallIdByWebsiteRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{3}
}

func (x *GetHallIdByWebsiteRequest) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

type GetHallIdByWebsiteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetHallIdByWebsiteResponse) Reset() {
	*x = GetHallIdByWebsiteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallIdByWebsiteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallIdByWebsiteResponse) ProtoMessage() {}

func (x *GetHallIdByWebsiteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallIdByWebsiteResponse.ProtoReflect.Descriptor instead.
func (*GetHallIdByWebsiteResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{4}
}

func (x *GetHallIdByWebsiteResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetHallByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetHallByIdRequest) Reset() {
	*x = GetHallByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallByIdRequest) ProtoMessage() {}

func (x *GetHallByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallByIdRequest.ProtoReflect.Descriptor instead.
func (*GetHallByIdRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{5}
}

func (x *GetHallByIdRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetHallByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LoginCode string `protobuf:"bytes,3,opt,name=login_code,json=loginCode,proto3" json:"login_code,omitempty"`
	Enable    bool   `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *GetHallByIdResponse) Reset() {
	*x = GetHallByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallByIdResponse) ProtoMessage() {}

func (x *GetHallByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallByIdResponse.ProtoReflect.Descriptor instead.
func (*GetHallByIdResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{6}
}

func (x *GetHallByIdResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetHallByIdResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetHallByIdResponse) GetLoginCode() string {
	if x != nil {
		return x.LoginCode
	}
	return ""
}

func (x *GetHallByIdResponse) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type HallListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable *BoolValue `protobuf:"bytes,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *HallListRequest) Reset() {
	*x = HallListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HallListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HallListRequest) ProtoMessage() {}

func (x *HallListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HallListRequest.ProtoReflect.Descriptor instead.
func (*HallListRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{7}
}

func (x *HallListRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

type HallListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GetHallByIdResponse `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *HallListResponse) Reset() {
	*x = HallListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HallListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HallListResponse) ProtoMessage() {}

func (x *HallListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HallListResponse.ProtoReflect.Descriptor instead.
func (*HallListResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{8}
}

func (x *HallListResponse) GetData() []*GetHallByIdResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetHallSiteListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SiteList []*SiteList `protobuf:"bytes,1,rep,name=site_list,json=siteList,proto3" json:"site_list,omitempty"`
}

func (x *GetHallSiteListResponse) Reset() {
	*x = GetHallSiteListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallSiteListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallSiteListResponse) ProtoMessage() {}

func (x *GetHallSiteListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallSiteListResponse.ProtoReflect.Descriptor instead.
func (*GetHallSiteListResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{9}
}

func (x *GetHallSiteListResponse) GetSiteList() []*SiteList {
	if x != nil {
		return x.SiteList
	}
	return nil
}

type SiteList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BigGroup  string `protobuf:"bytes,1,opt,name=big_group,json=bigGroup,proto3" json:"big_group,omitempty"`
	SiteGroup string `protobuf:"bytes,2,opt,name=site_group,json=siteGroup,proto3" json:"site_group,omitempty"`
	SiteName  string `protobuf:"bytes,3,opt,name=site_name,json=siteName,proto3" json:"site_name,omitempty"`
	LoginCode string `protobuf:"bytes,4,opt,name=login_code,json=loginCode,proto3" json:"login_code,omitempty"`
	HallId    uint32 `protobuf:"varint,5,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *SiteList) Reset() {
	*x = SiteList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SiteList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SiteList) ProtoMessage() {}

func (x *SiteList) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SiteList.ProtoReflect.Descriptor instead.
func (*SiteList) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{10}
}

func (x *SiteList) GetBigGroup() string {
	if x != nil {
		return x.BigGroup
	}
	return ""
}

func (x *SiteList) GetSiteGroup() string {
	if x != nil {
		return x.SiteGroup
	}
	return ""
}

func (x *SiteList) GetSiteName() string {
	if x != nil {
		return x.SiteName
	}
	return ""
}

func (x *SiteList) GetLoginCode() string {
	if x != nil {
		return x.LoginCode
	}
	return ""
}

func (x *SiteList) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetCurrencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetCurrencyRequest) Reset() {
	*x = GetCurrencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyRequest) ProtoMessage() {}

func (x *GetCurrencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyRequest.ProtoReflect.Descriptor instead.
func (*GetCurrencyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{11}
}

func (x *GetCurrencyRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetCurrencyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrencyInfo []*CurrencyInfo `protobuf:"bytes,1,rep,name=currency_info,json=currencyInfo,proto3" json:"currency_info,omitempty"`
}

func (x *GetCurrencyResponse) Reset() {
	*x = GetCurrencyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyResponse) ProtoMessage() {}

func (x *GetCurrencyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyResponse.ProtoReflect.Descriptor instead.
func (*GetCurrencyResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{12}
}

func (x *GetCurrencyResponse) GetCurrencyInfo() []*CurrencyInfo {
	if x != nil {
		return x.CurrencyInfo
	}
	return nil
}

type CurrencyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Preset   bool   `protobuf:"varint,1,opt,name=preset,proto3" json:"preset,omitempty"`
	Currency string `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *CurrencyInfo) Reset() {
	*x = CurrencyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrencyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrencyInfo) ProtoMessage() {}

func (x *CurrencyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrencyInfo.ProtoReflect.Descriptor instead.
func (*CurrencyInfo) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{13}
}

func (x *CurrencyInfo) GetPreset() bool {
	if x != nil {
		return x.Preset
	}
	return false
}

func (x *CurrencyInfo) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type GetPopUpBulletinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *GetPopUpBulletinRequest) Reset() {
	*x = GetPopUpBulletinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPopUpBulletinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPopUpBulletinRequest) ProtoMessage() {}

func (x *GetPopUpBulletinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPopUpBulletinRequest.ProtoReflect.Descriptor instead.
func (*GetPopUpBulletinRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{14}
}

func (x *GetPopUpBulletinRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type GetPopUpBulletinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Enable    bool   `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	Role      uint32 `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	ZhTw      string `protobuf:"bytes,4,opt,name=zh_tw,json=zhTw,proto3" json:"zh_tw,omitempty"`
	ZhCn      string `protobuf:"bytes,5,opt,name=zh_cn,json=zhCn,proto3" json:"zh_cn,omitempty"`
	En        string `protobuf:"bytes,6,opt,name=en,proto3" json:"en,omitempty"`
	Th        string `protobuf:"bytes,7,opt,name=th,proto3" json:"th,omitempty"`
	Ja        string `protobuf:"bytes,8,opt,name=ja,proto3" json:"ja,omitempty"`
	Ko        string `protobuf:"bytes,9,opt,name=ko,proto3" json:"ko,omitempty"`
	Vi        string `protobuf:"bytes,10,opt,name=vi,proto3" json:"vi,omitempty"`
	CreatedAt string `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *GetPopUpBulletinResponse) Reset() {
	*x = GetPopUpBulletinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPopUpBulletinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPopUpBulletinResponse) ProtoMessage() {}

func (x *GetPopUpBulletinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPopUpBulletinResponse.ProtoReflect.Descriptor instead.
func (*GetPopUpBulletinResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{15}
}

func (x *GetPopUpBulletinResponse) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetPopUpBulletinResponse) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *GetPopUpBulletinResponse) GetRole() uint32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *GetPopUpBulletinResponse) GetZhTw() string {
	if x != nil {
		return x.ZhTw
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetZhCn() string {
	if x != nil {
		return x.ZhCn
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetEn() string {
	if x != nil {
		return x.En
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetTh() string {
	if x != nil {
		return x.Th
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetJa() string {
	if x != nil {
		return x.Ja
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetKo() string {
	if x != nil {
		return x.Ko
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetVi() string {
	if x != nil {
		return x.Vi
	}
	return ""
}

func (x *GetPopUpBulletinResponse) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type UpdatePopUpBulletinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32     `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Enable *BoolValue `protobuf:"bytes,2,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *UpdatePopUpBulletinRequest) Reset() {
	*x = UpdatePopUpBulletinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePopUpBulletinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePopUpBulletinRequest) ProtoMessage() {}

func (x *UpdatePopUpBulletinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePopUpBulletinRequest.ProtoReflect.Descriptor instead.
func (*UpdatePopUpBulletinRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{16}
}

func (x *UpdatePopUpBulletinRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *UpdatePopUpBulletinRequest) GetEnable() *BoolValue {
	if x != nil {
		return x.Enable
	}
	return nil
}

type DeletePopUpBulletinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
}

func (x *DeletePopUpBulletinRequest) Reset() {
	*x = DeletePopUpBulletinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePopUpBulletinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePopUpBulletinRequest) ProtoMessage() {}

func (x *DeletePopUpBulletinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePopUpBulletinRequest.ProtoReflect.Descriptor instead.
func (*DeletePopUpBulletinRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{17}
}

func (x *DeletePopUpBulletinRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

type SetHallConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId    uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	LoginCode string `protobuf:"bytes,2,opt,name=login_code,json=loginCode,proto3" json:"login_code,omitempty"`
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetHallConfigRequest) Reset() {
	*x = SetHallConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetHallConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetHallConfigRequest) ProtoMessage() {}

func (x *SetHallConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetHallConfigRequest.ProtoReflect.Descriptor instead.
func (*SetHallConfigRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{18}
}

func (x *SetHallConfigRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetHallConfigRequest) GetLoginCode() string {
	if x != nil {
		return x.LoginCode
	}
	return ""
}

func (x *SetHallConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SetCurrencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId       uint32   `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	CurrencyList []string `protobuf:"bytes,2,rep,name=currency_list,json=currencyList,proto3" json:"currency_list,omitempty"`
}

func (x *SetCurrencyRequest) Reset() {
	*x = SetCurrencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCurrencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCurrencyRequest) ProtoMessage() {}

func (x *SetCurrencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCurrencyRequest.ProtoReflect.Descriptor instead.
func (*SetCurrencyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{19}
}

func (x *SetCurrencyRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetCurrencyRequest) GetCurrencyList() []string {
	if x != nil {
		return x.CurrencyList
	}
	return nil
}

type SetPresetCurrencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Currency string `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *SetPresetCurrencyRequest) Reset() {
	*x = SetPresetCurrencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPresetCurrencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPresetCurrencyRequest) ProtoMessage() {}

func (x *SetPresetCurrencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPresetCurrencyRequest.ProtoReflect.Descriptor instead.
func (*SetPresetCurrencyRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{20}
}

func (x *SetPresetCurrencyRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetPresetCurrencyRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type GetCurrencyByDefaultIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultId uint32 `protobuf:"varint,1,opt,name=default_id,json=defaultId,proto3" json:"default_id,omitempty"`
}

func (x *GetCurrencyByDefaultIdRequest) Reset() {
	*x = GetCurrencyByDefaultIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyByDefaultIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyByDefaultIdRequest) ProtoMessage() {}

func (x *GetCurrencyByDefaultIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyByDefaultIdRequest.ProtoReflect.Descriptor instead.
func (*GetCurrencyByDefaultIdRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{21}
}

func (x *GetCurrencyByDefaultIdRequest) GetDefaultId() uint32 {
	if x != nil {
		return x.DefaultId
	}
	return 0
}

type GetDomainDefaultCurrency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency string `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"`
	Preset   bool   `protobuf:"varint,2,opt,name=preset,proto3" json:"preset,omitempty"`
}

func (x *GetDomainDefaultCurrency) Reset() {
	*x = GetDomainDefaultCurrency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDomainDefaultCurrency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDomainDefaultCurrency) ProtoMessage() {}

func (x *GetDomainDefaultCurrency) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDomainDefaultCurrency.ProtoReflect.Descriptor instead.
func (*GetDomainDefaultCurrency) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{22}
}

func (x *GetDomainDefaultCurrency) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetDomainDefaultCurrency) GetPreset() bool {
	if x != nil {
		return x.Preset
	}
	return false
}

type GetCurrencyByDefaultIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DomainDefaultCurrency []*GetDomainDefaultCurrency `protobuf:"bytes,1,rep,name=domain_default_currency,json=domainDefaultCurrency,proto3" json:"domain_default_currency,omitempty"`
}

func (x *GetCurrencyByDefaultIdResponse) Reset() {
	*x = GetCurrencyByDefaultIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrencyByDefaultIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrencyByDefaultIdResponse) ProtoMessage() {}

func (x *GetCurrencyByDefaultIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrencyByDefaultIdResponse.ProtoReflect.Descriptor instead.
func (*GetCurrencyByDefaultIdResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{23}
}

func (x *GetCurrencyByDefaultIdResponse) GetDomainDefaultCurrency() []*GetDomainDefaultCurrency {
	if x != nil {
		return x.DomainDefaultCurrency
	}
	return nil
}

type GetPermissionByDefaultIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultId uint32 `protobuf:"varint,1,opt,name=default_id,json=defaultId,proto3" json:"default_id,omitempty"`
}

func (x *GetPermissionByDefaultIdRequest) Reset() {
	*x = GetPermissionByDefaultIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPermissionByDefaultIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPermissionByDefaultIdRequest) ProtoMessage() {}

func (x *GetPermissionByDefaultIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPermissionByDefaultIdRequest.ProtoReflect.Descriptor instead.
func (*GetPermissionByDefaultIdRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{24}
}

func (x *GetPermissionByDefaultIdRequest) GetDefaultId() uint32 {
	if x != nil {
		return x.DefaultId
	}
	return 0
}

type GetDomainDefaultPerm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermId uint32 `protobuf:"varint,1,opt,name=perm_id,json=permId,proto3" json:"perm_id,omitempty"`
	RoleId uint32 `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Modify bool   `protobuf:"varint,3,opt,name=modify,proto3" json:"modify,omitempty"`
}

func (x *GetDomainDefaultPerm) Reset() {
	*x = GetDomainDefaultPerm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDomainDefaultPerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDomainDefaultPerm) ProtoMessage() {}

func (x *GetDomainDefaultPerm) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDomainDefaultPerm.ProtoReflect.Descriptor instead.
func (*GetDomainDefaultPerm) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{25}
}

func (x *GetDomainDefaultPerm) GetPermId() uint32 {
	if x != nil {
		return x.PermId
	}
	return 0
}

func (x *GetDomainDefaultPerm) GetRoleId() uint32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *GetDomainDefaultPerm) GetModify() bool {
	if x != nil {
		return x.Modify
	}
	return false
}

type GetPermissionByDefaultIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DomainDefaultPerm []*GetDomainDefaultPerm `protobuf:"bytes,1,rep,name=domain_default_perm,json=domainDefaultPerm,proto3" json:"domain_default_perm,omitempty"`
}

func (x *GetPermissionByDefaultIdResponse) Reset() {
	*x = GetPermissionByDefaultIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPermissionByDefaultIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPermissionByDefaultIdResponse) ProtoMessage() {}

func (x *GetPermissionByDefaultIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPermissionByDefaultIdResponse.ProtoReflect.Descriptor instead.
func (*GetPermissionByDefaultIdResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{26}
}

func (x *GetPermissionByDefaultIdResponse) GetDomainDefaultPerm() []*GetDomainDefaultPerm {
	if x != nil {
		return x.DomainDefaultPerm
	}
	return nil
}

type GetGameInfoSettingByDefaultIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultId uint32 `protobuf:"varint,1,opt,name=default_id,json=defaultId,proto3" json:"default_id,omitempty"`
}

func (x *GetGameInfoSettingByDefaultIdRequest) Reset() {
	*x = GetGameInfoSettingByDefaultIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameInfoSettingByDefaultIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameInfoSettingByDefaultIdRequest) ProtoMessage() {}

func (x *GetGameInfoSettingByDefaultIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameInfoSettingByDefaultIdRequest.ProtoReflect.Descriptor instead.
func (*GetGameInfoSettingByDefaultIdRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{27}
}

func (x *GetGameInfoSettingByDefaultIdRequest) GetDefaultId() uint32 {
	if x != nil {
		return x.DefaultId
	}
	return 0
}

type GetDomainDefaultGameInfoSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lobby uint32 `protobuf:"varint,1,opt,name=lobby,proto3" json:"lobby,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetDomainDefaultGameInfoSetting) Reset() {
	*x = GetDomainDefaultGameInfoSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDomainDefaultGameInfoSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDomainDefaultGameInfoSetting) ProtoMessage() {}

func (x *GetDomainDefaultGameInfoSetting) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDomainDefaultGameInfoSetting.ProtoReflect.Descriptor instead.
func (*GetDomainDefaultGameInfoSetting) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{28}
}

func (x *GetDomainDefaultGameInfoSetting) GetLobby() uint32 {
	if x != nil {
		return x.Lobby
	}
	return 0
}

func (x *GetDomainDefaultGameInfoSetting) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetGameInfoSettingByDefaultIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameInfoSetting []*GetDomainDefaultGameInfoSetting `protobuf:"bytes,1,rep,name=game_info_setting,json=gameInfoSetting,proto3" json:"game_info_setting,omitempty"`
}

func (x *GetGameInfoSettingByDefaultIdResponse) Reset() {
	*x = GetGameInfoSettingByDefaultIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameInfoSettingByDefaultIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameInfoSettingByDefaultIdResponse) ProtoMessage() {}

func (x *GetGameInfoSettingByDefaultIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameInfoSettingByDefaultIdResponse.ProtoReflect.Descriptor instead.
func (*GetGameInfoSettingByDefaultIdResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{29}
}

func (x *GetGameInfoSettingByDefaultIdResponse) GetGameInfoSetting() []*GetDomainDefaultGameInfoSetting {
	if x != nil {
		return x.GameInfoSetting
	}
	return nil
}

type SetGameSwitchInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId   uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	GameKind uint32 `protobuf:"varint,2,opt,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
	Enable   bool   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *SetGameSwitchInfoRequest) Reset() {
	*x = SetGameSwitchInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetGameSwitchInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGameSwitchInfoRequest) ProtoMessage() {}

func (x *SetGameSwitchInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGameSwitchInfoRequest.ProtoReflect.Descriptor instead.
func (*SetGameSwitchInfoRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{30}
}

func (x *SetGameSwitchInfoRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *SetGameSwitchInfoRequest) GetGameKind() uint32 {
	if x != nil {
		return x.GameKind
	}
	return 0
}

func (x *SetGameSwitchInfoRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type GetAPIListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetAPIListRequest) Reset() {
	*x = GetAPIListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAPIListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAPIListRequest) ProtoMessage() {}

func (x *GetAPIListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAPIListRequest.ProtoReflect.Descriptor instead.
func (*GetAPIListRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{31}
}

func (x *GetAPIListRequest) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *GetAPIListRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetAPIListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiList []*APIListData `protobuf:"bytes,1,rep,name=api_list,json=apiList,proto3" json:"api_list,omitempty"`
}

func (x *GetAPIListResponse) Reset() {
	*x = GetAPIListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAPIListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAPIListResponse) ProtoMessage() {}

func (x *GetAPIListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAPIListResponse.ProtoReflect.Descriptor instead.
func (*GetAPIListResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{32}
}

func (x *GetAPIListResponse) GetApiList() []*APIListData {
	if x != nil {
		return x.ApiList
	}
	return nil
}

type APIListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HallId  uint32 `protobuf:"varint,1,opt,name=hall_id,json=hallId,proto3" json:"hall_id,omitempty"`
	Type    uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	KeyA    uint32 `protobuf:"varint,3,opt,name=key_a,json=keyA,proto3" json:"key_a,omitempty"`
	KeyB    string `protobuf:"bytes,4,opt,name=key_b,json=keyB,proto3" json:"key_b,omitempty"`
	KeyC    uint32 `protobuf:"varint,5,opt,name=key_c,json=keyC,proto3" json:"key_c,omitempty"`
	Content string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	Name    string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	OrderBy uint32 `protobuf:"varint,8,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
}

func (x *APIListData) Reset() {
	*x = APIListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *APIListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APIListData) ProtoMessage() {}

func (x *APIListData) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APIListData.ProtoReflect.Descriptor instead.
func (*APIListData) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{33}
}

func (x *APIListData) GetHallId() uint32 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *APIListData) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *APIListData) GetKeyA() uint32 {
	if x != nil {
		return x.KeyA
	}
	return 0
}

func (x *APIListData) GetKeyB() string {
	if x != nil {
		return x.KeyB
	}
	return ""
}

func (x *APIListData) GetKeyC() uint32 {
	if x != nil {
		return x.KeyC
	}
	return 0
}

func (x *APIListData) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *APIListData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *APIListData) GetOrderBy() uint32 {
	if x != nil {
		return x.OrderBy
	}
	return 0
}

type GetLobbySettingByDefaultIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultId uint32 `protobuf:"varint,1,opt,name=default_id,json=defaultId,proto3" json:"default_id,omitempty"`
}

func (x *GetLobbySettingByDefaultIdRequest) Reset() {
	*x = GetLobbySettingByDefaultIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbySettingByDefaultIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbySettingByDefaultIdRequest) ProtoMessage() {}

func (x *GetLobbySettingByDefaultIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbySettingByDefaultIdRequest.ProtoReflect.Descriptor instead.
func (*GetLobbySettingByDefaultIdRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{34}
}

func (x *GetLobbySettingByDefaultIdRequest) GetDefaultId() uint32 {
	if x != nil {
		return x.DefaultId
	}
	return 0
}

type GetLobbySettingByDefaultIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameKind []uint32 `protobuf:"varint,1,rep,packed,name=game_kind,json=gameKind,proto3" json:"game_kind,omitempty"`
}

func (x *GetLobbySettingByDefaultIdResponse) Reset() {
	*x = GetLobbySettingByDefaultIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLobbySettingByDefaultIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLobbySettingByDefaultIdResponse) ProtoMessage() {}

func (x *GetLobbySettingByDefaultIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLobbySettingByDefaultIdResponse.ProtoReflect.Descriptor instead.
func (*GetLobbySettingByDefaultIdResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{35}
}

func (x *GetLobbySettingByDefaultIdResponse) GetGameKind() []uint32 {
	if x != nil {
		return x.GameKind
	}
	return nil
}

var File_hall_proto protoreflect.FileDescriptor

var file_hall_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x68, 0x61,
	0x6c, 0x6c, 0x22, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x35, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x22, 0x35, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62, 0x73,
	0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61,
	0x6c, 0x6c, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x3a,
	0x0a, 0x0f, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x41, 0x0a, 0x10, 0x48, 0x61,
	0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68,
	0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x46, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x73, 0x69, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x68, 0x61,
	0x6c, 0x6c, 0x2e, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x73, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9b, 0x01, 0x0a, 0x08, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x67, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61,
	0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c,
	0x6c, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c,
	0x49, 0x64, 0x22, 0x4e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x42, 0x0a, 0x0c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x32, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x70,
	0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0xf8, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x13, 0x0a, 0x05,
	0x7a, 0x68, 0x5f, 0x74, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x68, 0x54,
	0x77, 0x12, 0x13, 0x0a, 0x05, 0x7a, 0x68, 0x5f, 0x63, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x68, 0x43, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x65, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x6a, 0x61, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6a, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x6b, 0x6f, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6b, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x76, 0x69, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x76, 0x69, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x5e, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x68,
	0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x35, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x14,
	0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x52, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x4f, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x3e, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x22, 0x78, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x17, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x15, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22,
	0x40, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49,
	0x64, 0x22, 0x60, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x65, 0x72,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x65, 0x72, 0x6d,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x22, 0x6e, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x13, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x65, 0x72, 0x6d,
	0x52, 0x11, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50,
	0x65, 0x72, 0x6d, 0x22, 0x45, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x22, 0x4b, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x47, 0x61,
	0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x6f, 0x62, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x6f,
	0x62, 0x62, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x7a, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x47, 0x61,
	0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x51, 0x0a, 0x11, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x68, 0x61,
	0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x0f, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x22, 0x68, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x40, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x41, 0x50, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x42, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x50, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x41,
	0x50, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x61, 0x70, 0x69, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0xc2, 0x01, 0x0a, 0x0b, 0x41, 0x50, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x13, 0x0a, 0x05, 0x6b, 0x65, 0x79, 0x5f, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x6b, 0x65, 0x79, 0x41, 0x12, 0x13, 0x0a, 0x05, 0x6b, 0x65, 0x79, 0x5f, 0x62, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x42, 0x12, 0x13, 0x0a, 0x05, 0x6b, 0x65,
	0x79, 0x5f, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x43, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x22, 0x42, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x62, 0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x22,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42,
	0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x32,
	0xea, 0x0a, 0x0a, 0x04, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x57, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x48,
	0x61, 0x6c, 0x6c, 0x49, 0x64, 0x42, 0x79, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x12, 0x1f,
	0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x42,
	0x79, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x42, 0x79, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x49, 0x64,
	0x12, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x68, 0x61, 0x6c,
	0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x48, 0x61, 0x6c, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x68, 0x61,
	0x6c, 0x6c, 0x2e, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x53, 0x69,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x68, 0x61, 0x6c,
	0x6c, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x19, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69,
	0x6e, 0x12, 0x1d, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x70, 0x55,
	0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x70, 0x55, 0x70,
	0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4c, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42,
	0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x12, 0x20, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c,
	0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c,
	0x6c, 0x65, 0x74, 0x69, 0x6e, 0x12, 0x20, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x6f, 0x70, 0x55, 0x70, 0x42, 0x75, 0x6c, 0x6c, 0x65, 0x74, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0d,
	0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x2e,
	0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c,
	0x0a, 0x0b, 0x53, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x18, 0x2e,
	0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x11,
	0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64,
	0x12, 0x23, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x12, 0x25, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61,
	0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x48, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x65, 0x74,
	0x47, 0x61, 0x6d, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x41, 0x50, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x50, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x50, 0x49, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x12, 0x27, 0x2e, 0x68, 0x61, 0x6c, 0x6c,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x62,
	0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0c, 0x5a, 0x0a,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_hall_proto_rawDescOnce sync.Once
	file_hall_proto_rawDescData = file_hall_proto_rawDesc
)

func file_hall_proto_rawDescGZIP() []byte {
	file_hall_proto_rawDescOnce.Do(func() {
		file_hall_proto_rawDescData = protoimpl.X.CompressGZIP(file_hall_proto_rawDescData)
	})
	return file_hall_proto_rawDescData
}

var file_hall_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_hall_proto_goTypes = []interface{}{
	(*BoolValue)(nil),                             // 0: hall.BoolValue
	(*EmptyRequest)(nil),                          // 1: hall.EmptyRequest
	(*EmptyResponse)(nil),                         // 2: hall.EmptyResponse
	(*GetHallIdByWebsiteRequest)(nil),             // 3: hall.GetHallIdByWebsiteRequest
	(*GetHallIdByWebsiteResponse)(nil),            // 4: hall.GetHallIdByWebsiteResponse
	(*GetHallByIdRequest)(nil),                    // 5: hall.GetHallByIdRequest
	(*GetHallByIdResponse)(nil),                   // 6: hall.GetHallByIdResponse
	(*HallListRequest)(nil),                       // 7: hall.HallListRequest
	(*HallListResponse)(nil),                      // 8: hall.HallListResponse
	(*GetHallSiteListResponse)(nil),               // 9: hall.GetHallSiteListResponse
	(*SiteList)(nil),                              // 10: hall.SiteList
	(*GetCurrencyRequest)(nil),                    // 11: hall.GetCurrencyRequest
	(*GetCurrencyResponse)(nil),                   // 12: hall.GetCurrencyResponse
	(*CurrencyInfo)(nil),                          // 13: hall.CurrencyInfo
	(*GetPopUpBulletinRequest)(nil),               // 14: hall.GetPopUpBulletinRequest
	(*GetPopUpBulletinResponse)(nil),              // 15: hall.GetPopUpBulletinResponse
	(*UpdatePopUpBulletinRequest)(nil),            // 16: hall.UpdatePopUpBulletinRequest
	(*DeletePopUpBulletinRequest)(nil),            // 17: hall.DeletePopUpBulletinRequest
	(*SetHallConfigRequest)(nil),                  // 18: hall.SetHallConfigRequest
	(*SetCurrencyRequest)(nil),                    // 19: hall.SetCurrencyRequest
	(*SetPresetCurrencyRequest)(nil),              // 20: hall.SetPresetCurrencyRequest
	(*GetCurrencyByDefaultIdRequest)(nil),         // 21: hall.GetCurrencyByDefaultIdRequest
	(*GetDomainDefaultCurrency)(nil),              // 22: hall.GetDomainDefaultCurrency
	(*GetCurrencyByDefaultIdResponse)(nil),        // 23: hall.GetCurrencyByDefaultIdResponse
	(*GetPermissionByDefaultIdRequest)(nil),       // 24: hall.GetPermissionByDefaultIdRequest
	(*GetDomainDefaultPerm)(nil),                  // 25: hall.GetDomainDefaultPerm
	(*GetPermissionByDefaultIdResponse)(nil),      // 26: hall.GetPermissionByDefaultIdResponse
	(*GetGameInfoSettingByDefaultIdRequest)(nil),  // 27: hall.GetGameInfoSettingByDefaultIdRequest
	(*GetDomainDefaultGameInfoSetting)(nil),       // 28: hall.GetDomainDefaultGameInfoSetting
	(*GetGameInfoSettingByDefaultIdResponse)(nil), // 29: hall.GetGameInfoSettingByDefaultIdResponse
	(*SetGameSwitchInfoRequest)(nil),              // 30: hall.SetGameSwitchInfoRequest
	(*GetAPIListRequest)(nil),                     // 31: hall.GetAPIListRequest
	(*GetAPIListResponse)(nil),                    // 32: hall.GetAPIListResponse
	(*APIListData)(nil),                           // 33: hall.APIListData
	(*GetLobbySettingByDefaultIdRequest)(nil),     // 34: hall.GetLobbySettingByDefaultIdRequest
	(*GetLobbySettingByDefaultIdResponse)(nil),    // 35: hall.GetLobbySettingByDefaultIdResponse
}
var file_hall_proto_depIdxs = []int32{
	0,  // 0: hall.HallListRequest.enable:type_name -> hall.BoolValue
	6,  // 1: hall.HallListResponse.data:type_name -> hall.GetHallByIdResponse
	10, // 2: hall.GetHallSiteListResponse.site_list:type_name -> hall.SiteList
	13, // 3: hall.GetCurrencyResponse.currency_info:type_name -> hall.CurrencyInfo
	0,  // 4: hall.UpdatePopUpBulletinRequest.enable:type_name -> hall.BoolValue
	22, // 5: hall.GetCurrencyByDefaultIdResponse.domain_default_currency:type_name -> hall.GetDomainDefaultCurrency
	25, // 6: hall.GetPermissionByDefaultIdResponse.domain_default_perm:type_name -> hall.GetDomainDefaultPerm
	28, // 7: hall.GetGameInfoSettingByDefaultIdResponse.game_info_setting:type_name -> hall.GetDomainDefaultGameInfoSetting
	33, // 8: hall.GetAPIListResponse.api_list:type_name -> hall.APIListData
	3,  // 9: hall.Hall.GetHallIdByWebsite:input_type -> hall.GetHallIdByWebsiteRequest
	5,  // 10: hall.Hall.GetHallById:input_type -> hall.GetHallByIdRequest
	7,  // 11: hall.Hall.GetHallList:input_type -> hall.HallListRequest
	1,  // 12: hall.Hall.GetHallSiteList:input_type -> hall.EmptyRequest
	11, // 13: hall.Hall.GetCurrency:input_type -> hall.GetCurrencyRequest
	14, // 14: hall.Hall.GetPopUpBulletin:input_type -> hall.GetPopUpBulletinRequest
	16, // 15: hall.Hall.UpdatePopUpBulletin:input_type -> hall.UpdatePopUpBulletinRequest
	17, // 16: hall.Hall.DeletePopUpBulletin:input_type -> hall.DeletePopUpBulletinRequest
	18, // 17: hall.Hall.SetHallConfig:input_type -> hall.SetHallConfigRequest
	19, // 18: hall.Hall.SetCurrency:input_type -> hall.SetCurrencyRequest
	20, // 19: hall.Hall.SetPresetCurrency:input_type -> hall.SetPresetCurrencyRequest
	21, // 20: hall.Hall.GetCurrencyByDefaultId:input_type -> hall.GetCurrencyByDefaultIdRequest
	24, // 21: hall.Hall.GetPermissionByDefaultId:input_type -> hall.GetPermissionByDefaultIdRequest
	27, // 22: hall.Hall.GetGameInfoSettingByDefaultId:input_type -> hall.GetGameInfoSettingByDefaultIdRequest
	30, // 23: hall.Hall.SetGameSwitchInfo:input_type -> hall.SetGameSwitchInfoRequest
	31, // 24: hall.Hall.GetAPIList:input_type -> hall.GetAPIListRequest
	34, // 25: hall.Hall.GetLobbySettingByDefaultId:input_type -> hall.GetLobbySettingByDefaultIdRequest
	4,  // 26: hall.Hall.GetHallIdByWebsite:output_type -> hall.GetHallIdByWebsiteResponse
	6,  // 27: hall.Hall.GetHallById:output_type -> hall.GetHallByIdResponse
	8,  // 28: hall.Hall.GetHallList:output_type -> hall.HallListResponse
	9,  // 29: hall.Hall.GetHallSiteList:output_type -> hall.GetHallSiteListResponse
	12, // 30: hall.Hall.GetCurrency:output_type -> hall.GetCurrencyResponse
	15, // 31: hall.Hall.GetPopUpBulletin:output_type -> hall.GetPopUpBulletinResponse
	2,  // 32: hall.Hall.UpdatePopUpBulletin:output_type -> hall.EmptyResponse
	2,  // 33: hall.Hall.DeletePopUpBulletin:output_type -> hall.EmptyResponse
	2,  // 34: hall.Hall.SetHallConfig:output_type -> hall.EmptyResponse
	2,  // 35: hall.Hall.SetCurrency:output_type -> hall.EmptyResponse
	2,  // 36: hall.Hall.SetPresetCurrency:output_type -> hall.EmptyResponse
	23, // 37: hall.Hall.GetCurrencyByDefaultId:output_type -> hall.GetCurrencyByDefaultIdResponse
	26, // 38: hall.Hall.GetPermissionByDefaultId:output_type -> hall.GetPermissionByDefaultIdResponse
	29, // 39: hall.Hall.GetGameInfoSettingByDefaultId:output_type -> hall.GetGameInfoSettingByDefaultIdResponse
	2,  // 40: hall.Hall.SetGameSwitchInfo:output_type -> hall.EmptyResponse
	32, // 41: hall.Hall.GetAPIList:output_type -> hall.GetAPIListResponse
	35, // 42: hall.Hall.GetLobbySettingByDefaultId:output_type -> hall.GetLobbySettingByDefaultIdResponse
	26, // [26:43] is the sub-list for method output_type
	9,  // [9:26] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_hall_proto_init() }
func file_hall_proto_init() {
	if File_hall_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hall_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoolValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHallIdByWebsiteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHallIdByWebsiteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHallByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHallByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HallListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HallListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHallSiteListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SiteList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrencyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrencyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPopUpBulletinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPopUpBulletinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePopUpBulletinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePopUpBulletinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetHallConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCurrencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPresetCurrencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrencyByDefaultIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDomainDefaultCurrency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrencyByDefaultIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPermissionByDefaultIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDomainDefaultPerm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPermissionByDefaultIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameInfoSettingByDefaultIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDomainDefaultGameInfoSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameInfoSettingByDefaultIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetGameSwitchInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAPIListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAPIListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*APIListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbySettingByDefaultIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLobbySettingByDefaultIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hall_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hall_proto_goTypes,
		DependencyIndexes: file_hall_proto_depIdxs,
		MessageInfos:      file_hall_proto_msgTypes,
	}.Build()
	File_hall_proto = out.File
	file_hall_proto_rawDesc = nil
	file_hall_proto_goTypes = nil
	file_hall_proto_depIdxs = nil
}
