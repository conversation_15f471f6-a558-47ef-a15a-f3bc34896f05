// Code generated by goctl. DO NOT EDIT.
// Source: sportgame.proto

package sportgameclient

import (
	"context"

	"gbh/proto/sportgame"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	BetLimitSetting                  = sportgame.BetLimitSetting
	BetLimitSettingPair              = sportgame.BetLimitSettingPair
	Category                         = sportgame.Category
	CheckWagersByIDRequest           = sportgame.CheckWagersByIDRequest
	DateWagersStatis                 = sportgame.DateWagersStatis
	EmptyResponse                    = sportgame.EmptyResponse
	FetchWagersByDB                  = sportgame.FetchWagersByDB
	FinishStatisRequest              = sportgame.FinishStatisRequest
	FinishStatisResponse             = sportgame.FinishStatisResponse
	FinishWagersRequest              = sportgame.FinishWagersRequest
	FinishWagersResponse             = sportgame.FinishWagersResponse
	GameInfo                         = sportgame.GameInfo
	GameLinkRequest                  = sportgame.GameLinkRequest
	GameLinkResponse                 = sportgame.GameLinkResponse
	GameListRequest                  = sportgame.GameListRequest
	GameListResponse                 = sportgame.GameListResponse
	GameWagersStatis                 = sportgame.GameWagersStatis
	GameplayBetLimitRequest          = sportgame.GameplayBetLimitRequest
	GameplayBetLimitResponse         = sportgame.GameplayBetLimitResponse
	GetCategoryRequest               = sportgame.GetCategoryRequest
	GetCategoryResponse              = sportgame.GetCategoryResponse
	GetWagersRequest                 = sportgame.GetWagersRequest
	GetWagersResponse                = sportgame.GetWagersResponse
	GroupNamePair                    = sportgame.GroupNamePair
	Pagination                       = sportgame.Pagination
	SportWagersByBetTimeRequest      = sportgame.SportWagersByBetTimeRequest
	SportWagersByModifiedTimeRequest = sportgame.SportWagersByModifiedTimeRequest
	StringValue                      = sportgame.StringValue
	SubWagersURLRequest              = sportgame.SubWagersURLRequest
	SubWagersURLResponse             = sportgame.SubWagersURLResponse
	Total                            = sportgame.Total
	Uint32Value                      = sportgame.Uint32Value
	UnfinishStatisRequest            = sportgame.UnfinishStatisRequest
	UnfinishStatisResponse           = sportgame.UnfinishStatisResponse
	Wagers                           = sportgame.Wagers
	WagersDetail                     = sportgame.WagersDetail
	WagersDetailRequest              = sportgame.WagersDetailRequest
	WagersDetailResponse             = sportgame.WagersDetailResponse
	WagersInfo                       = sportgame.WagersInfo
	WagersResponse                   = sportgame.WagersResponse
	WagersStatis                     = sportgame.WagersStatis

	SportGame interface {
		GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error)
		GameLink(ctx context.Context, in *GameLinkRequest, opts ...grpc.CallOption) (*GameLinkResponse, error)
		WagersByBetTime(ctx context.Context, in *SportWagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
		WagersByModifiedTime(ctx context.Context, in *SportWagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error)
		SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error)
		CheckWagersByID(ctx context.Context, in *CheckWagersByIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error)
		WagersDetail(ctx context.Context, in *WagersDetailRequest, opts ...grpc.CallOption) (*WagersDetailResponse, error)
		GameplayBetLimit(ctx context.Context, in *GameplayBetLimitRequest, opts ...grpc.CallOption) (*GameplayBetLimitResponse, error)
		UnfinishStatis(ctx context.Context, in *UnfinishStatisRequest, opts ...grpc.CallOption) (*UnfinishStatisResponse, error)
		FinishStatis(ctx context.Context, in *FinishStatisRequest, opts ...grpc.CallOption) (*FinishStatisResponse, error)
		FinishWagers(ctx context.Context, in *FinishWagersRequest, opts ...grpc.CallOption) (*FinishWagersResponse, error)
		GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error)
	}

	defaultSportGame struct {
		cli zrpc.Client
	}
)

func NewSportGame(cli zrpc.Client) SportGame {
	return &defaultSportGame{
		cli: cli,
	}
}

func (m *defaultSportGame) GameList(ctx context.Context, in *GameListRequest, opts ...grpc.CallOption) (*GameListResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.GameList(ctx, in, opts...)
}

func (m *defaultSportGame) GameLink(ctx context.Context, in *GameLinkRequest, opts ...grpc.CallOption) (*GameLinkResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.GameLink(ctx, in, opts...)
}

func (m *defaultSportGame) WagersByBetTime(ctx context.Context, in *SportWagersByBetTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.WagersByBetTime(ctx, in, opts...)
}

func (m *defaultSportGame) WagersByModifiedTime(ctx context.Context, in *SportWagersByModifiedTimeRequest, opts ...grpc.CallOption) (*WagersResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.WagersByModifiedTime(ctx, in, opts...)
}

func (m *defaultSportGame) SubWagersURL(ctx context.Context, in *SubWagersURLRequest, opts ...grpc.CallOption) (*SubWagersURLResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.SubWagersURL(ctx, in, opts...)
}

func (m *defaultSportGame) CheckWagersByID(ctx context.Context, in *CheckWagersByIDRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.CheckWagersByID(ctx, in, opts...)
}

func (m *defaultSportGame) GetCategory(ctx context.Context, in *GetCategoryRequest, opts ...grpc.CallOption) (*GetCategoryResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.GetCategory(ctx, in, opts...)
}

func (m *defaultSportGame) WagersDetail(ctx context.Context, in *WagersDetailRequest, opts ...grpc.CallOption) (*WagersDetailResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.WagersDetail(ctx, in, opts...)
}

func (m *defaultSportGame) GameplayBetLimit(ctx context.Context, in *GameplayBetLimitRequest, opts ...grpc.CallOption) (*GameplayBetLimitResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.GameplayBetLimit(ctx, in, opts...)
}

func (m *defaultSportGame) UnfinishStatis(ctx context.Context, in *UnfinishStatisRequest, opts ...grpc.CallOption) (*UnfinishStatisResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.UnfinishStatis(ctx, in, opts...)
}

func (m *defaultSportGame) FinishStatis(ctx context.Context, in *FinishStatisRequest, opts ...grpc.CallOption) (*FinishStatisResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.FinishStatis(ctx, in, opts...)
}

func (m *defaultSportGame) FinishWagers(ctx context.Context, in *FinishWagersRequest, opts ...grpc.CallOption) (*FinishWagersResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.FinishWagers(ctx, in, opts...)
}

func (m *defaultSportGame) GetWagers(ctx context.Context, in *GetWagersRequest, opts ...grpc.CallOption) (*GetWagersResponse, error) {
	client := sportgame.NewSportGameClient(m.cli.Conn())
	return client.GetWagers(ctx, in, opts...)
}
