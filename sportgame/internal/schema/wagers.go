package schema

type Wagers struct {
	WagersID       uint64  `gorm:"column:wagers_id"`
	WagersTime     string  `gorm:"column:wagers_time"`
	Hierarchy      uint32  `gorm:"column:hierarchy"`
	Portal         uint32  `gorm:"column:portal"`
	WagersType     int32   `gorm:"column:wagers_type"`
	Platform       uint32  `gorm:"column:platform"`
	Client         uint32  `gorm:"column:client"`
	GameType       uint32  `gorm:"column:game_type"`
	UserID         uint32  `gorm:"column:user_id"`
	RoundDate      string  `gorm:"column:round_date"`
	RoundTime      string  `gorm:"column:round_time"`
	BetAmount      float64 `gorm:"column:bet_amount"`
	Commissionable float64 `gorm:"column:commissionable"`
	Currency       string  `gorm:"column:currency"`
	ExchangeRate   float64 `gorm:"column:exchange_rate"`
	Result         int32   `gorm:"column:result"`
	Payoff         float64 `gorm:"column:payoff"`
	HallID         uint32  `gorm:"column:hall_id"`
	RoundSerial    string  `gorm:"column:round_serial"`
	ReferenceID    string  `gorm:"column:reference_id"`
	EventTime      string  `gorm:"column:event_time"`
	SettledTime    string  `gorm:"column:settled_time"`
	OddType        string  `gorm:"column:odd_type"`
	Market         string  `gorm:"column:market"`
	Period         string  `gorm:"column:period"`
	RealType       string  `gorm:"column:real_type"`
	Rule           string  `gorm:"column:rule"`
	ModifiedDate   string  `gorm:"column:modified_date"`
}
