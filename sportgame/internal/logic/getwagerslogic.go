package logic

import (
	"context"

	"gbh/errorx"
	"gbh/proto/sportgame"
	"gbh/sportgame/internal/constants"
	"gbh/sportgame/internal/schema"
	"gbh/sportgame/internal/svc"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type GetWagersLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

type TotalSums struct {
	TotalCount          uint32
	TotalBetAmount      float64
	TotalCommissionable float64
	TotalPayoff         float64
}

func NewGetWagersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWagersLogic {
	return &GetWagersLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetWagersLogic) GetWagers(in *sportgame.GetWagersRequest) (*sportgame.GetWagersResponse, error) {
	sumSelect := []string{
		"COUNT(*) AS total_count",
		"SUM(bet_amount) AS total_bet_amount",
		"SUM(commissionable) AS total_commissionable",
		"SUM(payoff) AS total_payoff",
	}
	sumQuery := l.svcCtx.WagersDB.Table("wagers").Select(sumSelect)
	sumQuery = l.applyFilters(sumQuery, in)

	selectFields := []string{
		"wagers_id",
		"wagers_time",
		"hierarchy",
		"portal",
		"wagers_type",
		"platform",
		"client",
		"game_type",
		"user_id",
		"round_date",
		"round_time",
		"bet_amount",
		"commissionable",
		"currency",
		"exchange_rate",
		"result",
		"payoff",
		"hall_id",
		"round_serial",
		"reference_id",
		"event_time",
		"settled_time",
		"odd_type",
		"market",
		"period",
		"real_type",
		"rule",
		"modified_date",
	}
	query := l.svcCtx.WagersDB.Table("wagers").Select(selectFields)
	query = l.applyFilters(query, in)

	// 排序
	order := constants.OrderDesc
	if in.GetOrder() != "" {
		order = in.GetOrder()
	}
	query = query.Order("`wagers_time` " + order)

	// 設定分頁
	limit := in.GetPageLimit()
	if limit <= 0 {
		limit = 50
	}
	page := in.GetPage()
	if page <= 0 {
		page = 1
	}

	// 計算總數
	var totalSums TotalSums
	if err := sumQuery.Scan(&totalSums).Error; err != nil {
		return nil, errorx.DatabaseError
	}
	totalPage := (totalSums.TotalCount + limit - 1) / limit

	// 設定分頁查詢
	query = query.Limit(int(limit)).Offset(int(limit * (page - 1)))

	// 執行查詢
	dbResult := []schema.Wagers{}
	if err := query.Find(&dbResult).Error; err != nil {
		return nil, errorx.DatabaseError
	}

	// 處理回應與計算總額
	var subNumber uint32
	var subBetAmount, subCommissionable, subPayoff float64
	respList := make([]*sportgame.FetchWagersByDB, 0, len(dbResult))

	for _, v := range dbResult {
		subNumber++
		subBetAmount += v.BetAmount
		subCommissionable += v.Commissionable
		subPayoff += v.Payoff

		wagersTime := carbon.Parse(v.WagersTime, constants.TimezoneGMT4)
		roundTime := carbon.Parse(v.RoundTime, constants.TimezoneGMT4)
		eventTime := carbon.Parse(v.EventTime, constants.TimezoneGMT4)
		settledTime := carbon.Parse(v.SettledTime, constants.TimezoneGMT4)
		modifiedDate := carbon.Parse(v.ModifiedDate, carbon.Taipei)

		respList = append(respList, &sportgame.FetchWagersByDB{
			WagersId:       v.WagersID,
			WagersTime:     wagersTime.ToRfc3339String(),
			Hierarchy:      v.Hierarchy,
			Portal:         v.Portal,
			WagersType:     v.WagersType,
			Platform:       v.Platform,
			Client:         v.Client,
			GameId:         v.GameType,
			UserId:         v.UserID,
			RoundDate:      v.RoundDate,
			RoundTime:      roundTime.ToRfc3339String(),
			BetAmount:      v.BetAmount,
			Commissionable: v.Commissionable,
			Currency:       v.Currency,
			ExchangeRate:   v.ExchangeRate,
			Result:         v.Result,
			Payoff:         v.Payoff,
			HallId:         v.HallID,
			RoundSerial:    v.RoundSerial,
			ReferenceId:    v.ReferenceID,
			EventTime:      eventTime.ToRfc3339String(),
			SettledTime:    settledTime.ToRfc3339String(),
			OddType:        v.OddType,
			Market:         v.Market,
			Rule:           v.Rule,
			ModifiedDate:   modifiedDate.ToRfc3339String(constants.TimezoneGMT4),
		})
	}

	return &sportgame.GetWagersResponse{
		Wagers: respList,
		Total: &sportgame.Total{
			Number:         totalSums.TotalCount,
			BetAmount:      totalSums.TotalBetAmount,
			Commissionable: totalSums.TotalCommissionable,
			Payoff:         totalSums.TotalPayoff,
		},
		SubTotal: &sportgame.Total{
			Number:         subNumber,
			BetAmount:      subBetAmount,
			Commissionable: subCommissionable,
			Payoff:         subPayoff,
		},
		Pagination: &sportgame.Pagination{
			Total:       totalSums.TotalCount,
			TotalPage:   totalPage,
			CurrentPage: page,
			PageLimit:   limit,
		},
	}, nil
}

func (l *GetWagersLogic) applyFilters(db *gorm.DB, in *sportgame.GetWagersRequest) *gorm.DB {
	if in.GetHallId() > 0 {
		db = db.Where("hall_id = ?", in.GetHallId())
	}

	if len(in.GetUserId()) > 0 {
		db = db.Where("user_id IN (?)", in.GetUserId())
	}

	if in.GetWagersId() > 0 {
		db = db.Where("wagers_id = ?", in.GetWagersId())
	}

	if len(in.GetGameId()) > 0 {
		db = db.Where("game_type IN (?)", in.GetGameId())
	}

	if in.GetCloseDate() != "" {
		db = db.Where("round_date >= ?", in.GetCloseDate())
	}

	if in.GetStartRoundDate() != "" {
		db = db.Where("round_date >= ?", in.GetStartRoundDate())
	}

	if in.GetEndRoundDate() != "" {
		db = db.Where("round_date <= ?", in.GetEndRoundDate())
	}

	if in.GetResult() != nil {
		db = db.Where("result = ? ", in.GetResult().GetValue())
	}

	if in.GetMinBetAmount() > 0 {
		db = db.Where("bet_amount >= ?", in.GetMinBetAmount())
	}

	if in.GetMaxBetAmount() > 0 {
		db = db.Where("bet_amount <= ?", in.GetMaxBetAmount())
	}

	if in.GetMinPayoff() > 0 {
		db = db.Where("payoff >= ?", in.GetMinPayoff())
	}

	if in.GetMaxPayoff() > 0 {
		db = db.Where("payoff <= ?", in.GetMaxPayoff())
	}

	return db
}
