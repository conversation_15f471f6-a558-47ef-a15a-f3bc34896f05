package logic

import (
	"gbh/errorx"
	"gbh/proto/sportgame"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func Test_GetWagersLogic_Get(t *testing.T) {
	request := &sportgame.GetWagersRequest{
		StartRoundDate: `2025-05-06`,
		EndRoundDate:   `2025-05-06`,
		HallId:         3820474,
		UserId:         []uint32{456121189},
		WagersId:       9215063190,
		GameId:         []string{"176"},
		Result:         &sportgame.Uint32Value{Value: 0},
		MinBetAmount:   1,
		MaxBetAmount:   10,
		MinPayoff:      1,
		MaxPayoff:      100,
		CloseDate:      `2025-05-06`,
		Order:          `desc`,
	}

	rowsCount := sqlMock.NewRows([]string{"total_count", "total_bet_amount", "total_commissionable", "total_payoff"}).
		AddRow(1, 100, 100, 97)
	sqlMock.ExpectQuery("SELECT COUNT(*) AS total_count,SUM(bet_amount) AS total_bet_amount,SUM(commissionable) AS total_commissionable,SUM(payoff) AS total_payoff FROM `wagers` WHERE hall_id = ? AND user_id IN (?) AND wagers_id = ? AND game_type IN (?) AND round_date >= ? AND round_date >= ? AND round_date <= ? AND result = ? AND bet_amount >= ? AND bet_amount <= ? AND payoff >= ? AND payoff <= ?").
		WithArgs(request.GetHallId(),
			request.GetUserId()[0],
			request.GetWagersId(),
			request.GetGameId()[0],
			request.GetCloseDate(),
			request.GetStartRoundDate(),
			request.GetEndRoundDate(),
			request.GetResult().GetValue(),
			request.GetMinBetAmount(),
			request.GetMaxBetAmount(),
			request.GetMinPayoff(),
			request.GetMaxPayoff(),
		).WillReturnRows(rowsCount)

	rows := sqlMock.NewRows([]string{"wagers_id", "wagers_time", "hierarchy", "portal", "wagers_type", "platform", "client", "game_type", "user_id", "round_date", "round_time", "bet_amount", "commissionable", "currency", "exchange_rate", "result", "payoff", "hall_id", "round_serial", "reference_id", "event_time", "settled_time", "odd_type", "market", "period", "real_type", "rule", "modified_date"}).
		AddRow(9215063190, "2025-02-19 03:06:11", 0, 0, 0, 6, 3, 176, 456121189, "2025-02-19", "2025-02-19 03:06:11", 100, 100, "CNY", 1, 1, 97, 3820474, ``, ``, "2025-02-19 03:06:11", "2025-02-19 03:06:11", "EURO", ``, ``, ``, 9198, "2025-02-19 15:11:10")
	sqlMock.ExpectQuery("SELECT `wagers_id`,`wagers_time`,`hierarchy`,`portal`,`wagers_type`,`platform`,`client`,`game_type`,`user_id`,`round_date`,`round_time`,`bet_amount`,`commissionable`,`currency`,`exchange_rate`,`result`,`payoff`,`hall_id`,`round_serial`,`reference_id`,`event_time`,`settled_time`,`odd_type`,`market`,`period`,`real_type`,`rule`,`modified_date` FROM `wagers` WHERE hall_id = ? AND user_id IN (?) AND wagers_id = ? AND game_type IN (?) AND round_date >= ? AND round_date >= ? AND round_date <= ? AND result = ? AND bet_amount >= ? AND bet_amount <= ? AND payoff >= ? AND payoff <= ? ORDER BY `wagers_time` desc LIMIT ?").
		WithArgs(request.GetHallId(),
			request.GetUserId()[0],
			request.GetWagersId(),
			request.GetGameId()[0],
			request.GetCloseDate(),
			request.GetStartRoundDate(),
			request.GetEndRoundDate(),
			request.GetResult().GetValue(),
			request.GetMinBetAmount(),
			request.GetMaxBetAmount(),
			request.GetMinPayoff(),
			request.GetMaxPayoff(),
			50,
		).WillReturnRows(rows)

	response := sportgame.GetWagersResponse{
		Wagers: []*sportgame.FetchWagersByDB{
			{
				WagersId:       9215063190,
				WagersTime:     "2025-02-19T03:06:11-04:00",
				Hierarchy:      0,
				Portal:         0,
				Platform:       6,
				Client:         3,
				GameId:         176,
				UserId:         456121189,
				RoundDate:      "2025-02-19",
				RoundTime:      "2025-02-19T03:06:11-04:00",
				BetAmount:      100,
				Commissionable: 100,
				Currency:       "CNY",
				ExchangeRate:   1,
				Result:         1,
				Payoff:         97,
				HallId:         3820474,
				RoundSerial:    "",
				EventTime:      "2025-02-19T03:06:11-04:00",
				SettledTime:    "2025-02-19T03:06:11-04:00",
				OddType:        "EURO",
				Rule:           "9198",
				ModifiedDate:   "2025-02-19T03:11:10-04:00",
			},
		},
		Pagination: &sportgame.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   50,
		},
		SubTotal: &sportgame.Total{
			Number:         1,
			BetAmount:      100,
			Commissionable: 100,
			Payoff:         97,
		},
		Total: &sportgame.Total{
			Number:         1,
			BetAmount:      100,
			Commissionable: 100,
			Payoff:         97,
		},
	}

	l := NewGetWagersLogic(ctx, svcCtx)
	resp, err := l.GetWagers(request)

	assert.NoError(t, err)
	assert.Equal(t, &response, resp)
	assert.NoError(t, sqlMock.ExpectationsWereMet())
}

func Test_GetWagersLogic_CountDatabaseError(t *testing.T) {
	request := &sportgame.GetWagersRequest{
		StartRoundDate: `2025-05-06`,
		EndRoundDate:   `2025-05-06`,
	}

	sqlMock.ExpectQuery("SELECT COUNT(*) AS total_count,SUM(bet_amount) AS total_bet_amount,SUM(commissionable) AS total_commissionable,SUM(payoff) AS total_payoff FROM `wagers` WHERE round_date >= ? AND round_date <= ? ").
		WithArgs(request.GetStartRoundDate(), request.GetEndRoundDate()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetWagersLogic(ctx, svcCtx)
	resp, err := l.GetWagers(request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMock.ExpectationsWereMet())
}

func Test_GetWagersLogic_FindDatabaseError(t *testing.T) {
	request := &sportgame.GetWagersRequest{
		StartRoundDate: `2025-05-06`,
		EndRoundDate:   `2025-05-06`,
	}

	rowsCount := sqlMock.NewRows([]string{"total_count", "total_bet_amount", "total_commissionable", "total_payoff"}).
		AddRow(1, 100, 100, 97)
	sqlMock.ExpectQuery("SELECT COUNT(*) AS total_count,SUM(bet_amount) AS total_bet_amount,SUM(commissionable) AS total_commissionable,SUM(payoff) AS total_payoff FROM `wagers` WHERE round_date >= ? AND round_date <= ? ").
		WithArgs(request.GetStartRoundDate(), request.GetEndRoundDate()).
		WillReturnRows(rowsCount)

	sqlMock.ExpectQuery("SELECT `wagers_id`,`wagers_time`,`hierarchy`,`portal`,`wagers_type`,`platform`,`client`,`game_type`,`user_id`,`round_date`,`round_time`,`bet_amount`,`commissionable`,`currency`,`exchange_rate`,`result`,`payoff`,`hall_id`,`round_serial`,`reference_id`,`event_time`,`settled_time`,`odd_type`,`market`,`period`,`real_type`,`rule`,`modified_date` FROM `wagers` WHERE round_date >= ? AND round_date <= ?  ORDER BY `wagers_time` desc LIMIT ?").
		WithArgs(request.GetStartRoundDate(), request.GetEndRoundDate(), 50).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetWagersLogic(ctx, svcCtx)
	resp, err := l.GetWagers(request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMock.ExpectationsWereMet())
}
