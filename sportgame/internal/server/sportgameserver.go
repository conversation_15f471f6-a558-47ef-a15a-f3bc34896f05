// Code generated by goctl. DO NOT EDIT.
// Source: sportgame.proto

package server

import (
	"context"

	"gbh/proto/sportgame"
	"gbh/sportgame/internal/logic"
	"gbh/sportgame/internal/svc"
)

type SportGameServer struct {
	svcCtx *svc.ServiceContext
	sportgame.UnimplementedSportGameServer
}

func NewSportGameServer(svcCtx *svc.ServiceContext) *SportGameServer {
	return &SportGameServer{
		svcCtx: svcCtx,
	}
}

func (s *SportGameServer) GameList(ctx context.Context, in *sportgame.GameListRequest) (*sportgame.GameListResponse, error) {
	l := logic.NewGameListLogic(ctx, s.svcCtx)
	return l.GameList(in)
}

func (s *SportGameServer) GameLink(ctx context.Context, in *sportgame.GameLinkRequest) (*sportgame.GameLinkResponse, error) {
	l := logic.NewGameLinkLogic(ctx, s.svcCtx)
	return l.GameLink(in)
}

func (s *SportGameServer) WagersByBetTime(ctx context.Context, in *sportgame.SportWagersByBetTimeRequest) (*sportgame.WagersResponse, error) {
	l := logic.NewWagersByBetTimeLogic(ctx, s.svcCtx)
	return l.WagersByBetTime(in)
}

func (s *SportGameServer) WagersByModifiedTime(ctx context.Context, in *sportgame.SportWagersByModifiedTimeRequest) (*sportgame.WagersResponse, error) {
	l := logic.NewWagersByModifiedTimeLogic(ctx, s.svcCtx)
	return l.WagersByModifiedTime(in)
}

func (s *SportGameServer) SubWagersURL(ctx context.Context, in *sportgame.SubWagersURLRequest) (*sportgame.SubWagersURLResponse, error) {
	l := logic.NewSubWagersURLLogic(ctx, s.svcCtx)
	return l.SubWagersURL(in)
}

func (s *SportGameServer) CheckWagersByID(ctx context.Context, in *sportgame.CheckWagersByIDRequest) (*sportgame.EmptyResponse, error) {
	l := logic.NewCheckWagersByIDLogic(ctx, s.svcCtx)
	return l.CheckWagersByID(in)
}

func (s *SportGameServer) GetCategory(ctx context.Context, in *sportgame.GetCategoryRequest) (*sportgame.GetCategoryResponse, error) {
	l := logic.NewGetCategoryLogic(ctx, s.svcCtx)
	return l.GetCategory(in)
}

func (s *SportGameServer) WagersDetail(ctx context.Context, in *sportgame.WagersDetailRequest) (*sportgame.WagersDetailResponse, error) {
	l := logic.NewWagersDetailLogic(ctx, s.svcCtx)
	return l.WagersDetail(in)
}

func (s *SportGameServer) GameplayBetLimit(ctx context.Context, in *sportgame.GameplayBetLimitRequest) (*sportgame.GameplayBetLimitResponse, error) {
	l := logic.NewGameplayBetLimitLogic(ctx, s.svcCtx)
	return l.GameplayBetLimit(in)
}

func (s *SportGameServer) UnfinishStatis(ctx context.Context, in *sportgame.UnfinishStatisRequest) (*sportgame.UnfinishStatisResponse, error) {
	l := logic.NewUnfinishStatisLogic(ctx, s.svcCtx)
	return l.UnfinishStatis(in)
}

func (s *SportGameServer) FinishStatis(ctx context.Context, in *sportgame.FinishStatisRequest) (*sportgame.FinishStatisResponse, error) {
	l := logic.NewFinishStatisLogic(ctx, s.svcCtx)
	return l.FinishStatis(in)
}

func (s *SportGameServer) FinishWagers(ctx context.Context, in *sportgame.FinishWagersRequest) (*sportgame.FinishWagersResponse, error) {
	l := logic.NewFinishWagersLogic(ctx, s.svcCtx)
	return l.FinishWagers(in)
}

func (s *SportGameServer) GetWagers(ctx context.Context, in *sportgame.GetWagersRequest) (*sportgame.GetWagersResponse, error) {
	l := logic.NewGetWagersLogic(ctx, s.svcCtx)
	return l.GetWagers(in)
}
