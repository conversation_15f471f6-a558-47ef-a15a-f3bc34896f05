package constants

import "time"

const APITimeout = 5 * time.Second

const TimezoneGMT4 = "Etc/GMT+4"

const (
	GameListAPI             = "/api/bc/api-partner/report-bet-type"
	GameLinkAPI             = "/api/bc/sportsbook-url"
	WagersByBetTimeAPI      = "/api/bc/api-partner/bet-record-by-create-time"
	WagersByModifiedTimeAPI = "/api/bc/api-partner/bet-record-by-update-time"
	SubWagersURLAPI         = "/api/bc/create-wager-token"
	GetCategoryAPI          = "/api/bc/sport-group-id"
	WagersDetailAPI         = "/api/bc/bet-record-detail/%d"
	GamePlayBetLimitAPI     = "/api/bc/bet-setting/%d/sport/%d"
	UnfinishWagersAPI       = "/api/bc/bet-record-for-running/%d"
	FinishWagersAPI         = "/api/bc/wager-history-list/%d"
	FinishBetRecordAPI      = "/api/bc/bet-record/%d"
)

const (
	NBBGameNotFound  = 112150017
	NBBIsMaintaining = 112015028
)

const (
	OrderDesc = "desc"
)
