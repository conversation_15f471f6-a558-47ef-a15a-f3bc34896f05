Name: report.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: report.rpc

ReportDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: ReportDB_new
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: ReportDB_new
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

CheckAccDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: CheckAccount_DB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: CheckAccount_DB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

AccountDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: AccountDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: AccountDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

AdminDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: AdminDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: AdminDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

BIConf:
  Schema: https
  Host: bbgp-reportquery-go-************.us-central1.run.app
  IP: 127.0.0.1
  Port: 80
  Authorization: token

LiveWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers3
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers3
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

SlotWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers5
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers5
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

LotteryWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers12
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers12
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

SportWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers31
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers31
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

FishWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers38
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers38
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

BattleWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers66
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers66
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

TipWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers99
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers99
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

Middlewares:
  Stat: false
