package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/logger"
	"gbh/proto/report"
	"gbh/report/internal/config"
	"gbh/report/internal/server"
	"gbh/report/internal/svc"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/report.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	ctx := svc.NewServiceContext(c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	reportDB, err := database.New(c.Report<PERSON>, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.ReportDB = reportDB

	checkAccDB, err := database.New(c.CheckAccDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.CheckAccDB = checkAccDB

	accountDB, err := database.New(c.AccountDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.AccountDB = accountDB

	adminDB, err := database.New(c.AdminDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.AdminDB = adminDB

	liveWagersDB, err := database.New(c.LiveWagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.LiveWagersDB = liveWagersDB

	slotWagersDB, err := database.New(c.SlotWagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.SlotWagersDB = slotWagersDB

	lotteryWagersDB, err := database.New(c.LotteryWagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.LotteryWagersDB = lotteryWagersDB

	sportWagersDB, err := database.New(c.SportWagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.SportWagersDB = sportWagersDB

	fishWagersDB, err := database.New(c.FishWagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.FishWagersDB = fishWagersDB

	battleWagersDB, err := database.New(c.BattleWagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.BattleWagersDB = battleWagersDB

	tipWagersDB, err := database.New(c.TipWagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}
	ctx.TipWagersDB = tipWagersDB

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		report.RegisterReportServer(grpcServer, server.NewReportServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "report",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
