package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/logger"
	"gbh/lotterygame/internal/config"
	"gbh/lotterygame/internal/server"
	"gbh/lotterygame/internal/svc"
	"gbh/proto/lotterygame"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/lotterygame.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	wagersDB, err := database.New(c.WagersDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		WagersDB: wagersDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		lotterygame.RegisterLotteryGameServer(grpcServer, server.NewLotteryGameServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "lottergame",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
