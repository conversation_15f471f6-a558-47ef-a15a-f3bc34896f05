package logic

import (
	"context"

	"gbh/errorx"
	"gbh/monitor/internal/schema"
	"gbh/monitor/internal/svc"
	"gbh/proto/monitor"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProfitLossByGameTypeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetProfitLossByGameTypeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProfitLossByGameTypeLogic {
	return &GetProfitLossByGameTypeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetProfitLossByGameTypeLogic) GetProfitLossByGameType(in *monitor.GetProfitLossByGameTypeRequest) (*monitor.GetProfitLossByGameTypeResponse, error) {
	selectFields := []string{"id", "game_type", "payoff", "bet_amount", "growth"}
	query := l.svcCtx.MonitorDB.
		Table("PayoffAlertGameType").
		Select(selectFields).
		Where("id = ?", in.GetId())

	// 取得查詢結果
	var monitorList []schema.MonitorDBPayoffAlertGameTypeTable
	if err := query.Find(&monitorList).Error; err != nil {
		return nil, errorx.DatabaseError
	}

	var subNumber uint32
	var subPayoff, subBetAmount float64
	respList := make([]*monitor.GetProfitLossByGameType, 0, len(monitorList))

	// 整理回傳
	for _, v := range monitorList {
		subNumber++
		subPayoff += v.PayOff
		subBetAmount += v.BetAmount

		respList = append(respList, &monitor.GetProfitLossByGameType{
			Id:        v.ID,
			GameId:    v.GameID,
			PayOff:    v.PayOff,
			BetAmount: v.BetAmount,
			Growth:    v.Growth,
		})
	}

	return &monitor.GetProfitLossByGameTypeResponse{
		List: respList,
		SubTotal: &monitor.Total{
			Number:    subNumber,
			PayOff:    subPayoff,
			BetAmount: subBetAmount,
		},
	}, nil
}
