package logic

import (
	"gbh/errorx"
	"gbh/proto/monitor"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestSetPayOffLobbyLogic_Success(t *testing.T) {
	request := monitor.SetPayOffLobbyRequest{
		MonitorId:          1,
		GameKind:           []uint32{3},
		InfluenceMonitorId: []uint32{2},
	}

	sqlMockMonitor.ExpectBegin()

	sqlMockMonitor.ExpectExec("DELETE FROM `PayoffLobby` WHERE mid IN (?) AND lobby IN (?)").
		WithArgs(request.GetInfluenceMonitorId()[0], request.GetGameKind()[0]).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockMonitor.ExpectExec("DELETE FROM `PayoffLobby` WHERE mid = ?").
		WithArgs(request.GetMonitorId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockMonitor.ExpectExec("INSERT INTO `PayoffLobby` (`mid`,`lobby`) VALUES (?,?)").
		WithArgs(request.GetMonitorId(), request.GetGameKind()[0]).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockMonitor.ExpectCommit()

	l := NewSetPayOffLobbyLogic(ctx, svcCtx)
	resp, err := l.SetPayOffLobby(&request)

	assert.Equal(t, &monitor.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}

func TestSetPayOffLobbyLogic_DeleteInfluence_DatabaseError(t *testing.T) {
	request := monitor.SetPayOffLobbyRequest{
		MonitorId:          1,
		GameKind:           []uint32{3},
		InfluenceMonitorId: []uint32{2},
	}

	sqlMockMonitor.ExpectBegin()

	sqlMockMonitor.ExpectExec("DELETE FROM `PayoffLobby` WHERE mid IN (?) AND lobby IN (?)").
		WithArgs(request.GetInfluenceMonitorId()[0], request.GetGameKind()[0]).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockMonitor.ExpectRollback()

	l := NewSetPayOffLobbyLogic(ctx, svcCtx)
	resp, err := l.SetPayOffLobby(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}

func TestSetPayOffLobbyLogic_OldMonitorId_DatabaseError(t *testing.T) {
	request := monitor.SetPayOffLobbyRequest{
		MonitorId:          1,
		GameKind:           []uint32{3},
		InfluenceMonitorId: []uint32{2},
	}

	sqlMockMonitor.ExpectBegin()

	sqlMockMonitor.ExpectExec("DELETE FROM `PayoffLobby` WHERE mid IN (?) AND lobby IN (?)").
		WithArgs(request.GetInfluenceMonitorId()[0], request.GetGameKind()[0]).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockMonitor.ExpectExec("DELETE FROM `PayoffLobby` WHERE mid = ?").
		WithArgs(request.GetMonitorId()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockMonitor.ExpectRollback()

	l := NewSetPayOffLobbyLogic(ctx, svcCtx)
	resp, err := l.SetPayOffLobby(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}

func TestSetPayOffLobbyLogic_Insert_DatabaseError(t *testing.T) {
	request := monitor.SetPayOffLobbyRequest{
		MonitorId:          1,
		GameKind:           []uint32{3},
		InfluenceMonitorId: []uint32{2},
	}

	sqlMockMonitor.ExpectBegin()

	sqlMockMonitor.ExpectExec("DELETE FROM `PayoffLobby` WHERE mid IN (?) AND lobby IN (?)").
		WithArgs(request.GetInfluenceMonitorId()[0], request.GetGameKind()[0]).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockMonitor.ExpectExec("DELETE FROM `PayoffLobby` WHERE mid = ?").
		WithArgs(request.GetMonitorId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockMonitor.ExpectExec("INSERT INTO `PayoffLobby` (`mid`,`lobby`) VALUES (?,?)").
		WithArgs(request.GetMonitorId(), request.GetGameKind()[0]).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockMonitor.ExpectRollback()

	l := NewSetPayOffLobbyLogic(ctx, svcCtx)
	resp, err := l.SetPayOffLobby(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}
