package logic

import (
	"gbh/errorx"
	"gbh/proto/monitor"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func Test_NewGetProfitLossByGameTypeLogic_Get(t *testing.T) {
	request := &monitor.GetProfitLossByGameTypeRequest{
		Id: 1,
	}

	row := sqlmock.NewRows([]string{"id", "game_type", "payoff", "bet_amount", "growth"}).
		AddRow(1, 5044, 11350, 133600, 0).
		AddRow(1, 5160, -80360, 522720, 0)
	sqlMockMonitor.ExpectQuery("SELECT `id`,`game_type`,`payoff`,`bet_amount`,`growth` FROM `PayoffAlertGameType` WHERE id = ? ").
		WithArgs(request.GetId()).
		WillReturnRows(row)

	l := NewGetProfitLossByGameTypeLogic(ctx, svcCtx)
	resp, err := l.GetProfitLossByGameType(request)

	expect := monitor.GetProfitLossByGameTypeResponse{
		List: []*monitor.GetProfitLossByGameType{
			{
				Id:        1,
				GameId:    "5044",
				PayOff:    11350,
				BetAmount: 133600,
				Growth:    0,
			},
			{
				Id:        1,
				GameId:    "5160",
				PayOff:    -80360,
				BetAmount: 522720,
				Growth:    0,
			},
		},
		SubTotal: &monitor.Total{
			Number:    2,
			PayOff:    11350 + (-80360),
			BetAmount: 133600 + 522720,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, &expect, resp)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}

func Test_NewGetProfitLossByGameTypeLogic_DatabaseError(t *testing.T) {
	request := &monitor.GetProfitLossByGameTypeRequest{
		Id: 1,
	}

	sqlMockMonitor.ExpectQuery("SELECT `id`,`game_type`,`payoff`,`bet_amount`,`growth` FROM `PayoffAlertGameType` WHERE id = ? ").
		WithArgs(request.GetId()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetProfitLossByGameTypeLogic(ctx, svcCtx)
	resp, err := l.GetProfitLossByGameType(request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}
