package logic

import (
	"context"

	"gbh/errorx"
	"gbh/monitor/internal/schema"
	"gbh/monitor/internal/svc"
	"gbh/monitor/monitorclient"
	"gbh/proto/monitor"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CreateConditionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
	tx *gorm.DB
}

func NewCreateConditionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateConditionLogic {
	return &CreateConditionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateConditionLogic) CreateCondition(in *monitor.CreateConditionRequest) (*monitor.EmptyResponse, error) {
	insertCondition := schema.PayoffCondition{
		Site:    in.GetCondition().GetSite(),
		GroupID: in.GetCondition().GetGroupId(),
		Domain:  in.GetCondition().GetHallId(),
	}

	userID := in.GetCondition().GetUserId()
	if userID > 0 {
		insertCondition.Member = &userID
	}

	err := l.svcCtx.MonitorDB.Transaction(func(tx *gorm.DB) error {
		l.tx = tx
		// 產生監控條件
		err := tx.Table("PayoffCondition").Create(&insertCondition).Error
		if err != nil {
			return err
		}

		err = l.setPayoffLobby(insertCondition.ID, in.GetGameKinds())
		if err != nil {
			return err
		}

		err = l.setPayoffSubCondition(insertCondition.ID, in.GetSubCondition())
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &monitor.EmptyResponse{}, nil
}

// 設關聯遊戲
func (l *CreateConditionLogic) setPayoffLobby(mID uint32, gameKinds []uint32) error {
	// 新增該監控條件的遊戲大廳
	insertData := make([]schema.PayoffLobby, 0, len(gameKinds))
	for _, gameKind := range gameKinds {
		insertData = append(insertData, schema.PayoffLobby{
			MID:   mID,
			Lobby: gameKind,
		})
	}

	err := l.tx.Table("PayoffLobby").Create(&insertData).Error
	if err != nil {
		return err
	}

	return nil
}

// 設關聯子條件
func (l *CreateConditionLogic) setPayoffSubCondition(mID uint32, subCondition *monitorclient.CreateSubCondition) error {
	// 總會員損益子條件
	if subCondition.GetWin() > 0 || subCondition.GetLose() > 0 {
		win := subCondition.GetWin()
		lose := subCondition.GetLose()

		totalCondition := schema.PayoffSubCondition{
			MID:           mID,
			ConditionType: "total",
			Win:           &win,
			Lose:          &lose,
		}

		err := l.tx.Table("PayoffSubcondition").Create(&totalCondition).Error
		if err != nil {
			return err
		}
	}

	// 單一會員損益子條件
	if subCondition.GetMemberWin() > 0 || subCondition.GetMemberLose() > 0 {
		memberWin := subCondition.GetMemberWin()
		memberLose := subCondition.GetMemberLose()

		singleCondition := schema.PayoffSubCondition{
			MID:           mID,
			ConditionType: "single",
			Win:           &memberWin,
			Lose:          &memberLose,
		}

		err := l.tx.Table("PayoffSubcondition").Create(&singleCondition).Error
		if err != nil {
			return err
		}
	}

	// 平均%數/天數子條件
	if subCondition.GetDay() > 0 && subCondition.GetPercent() > 0 {
		day := subCondition.GetDay()
		percent := subCondition.GetPercent()
		betAmount := subCondition.GetBetAmount()

		averageCondition := schema.PayoffSubCondition{
			MID:           mID,
			ConditionType: "average",
			Day:           &day,
			Percent:       &percent,
			BetAmount:     &betAmount,
		}

		err := l.tx.Table("PayoffSubcondition").Create(&averageCondition).Error
		if err != nil {
			return err
		}
	}

	return nil
}
