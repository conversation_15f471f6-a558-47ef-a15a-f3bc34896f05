package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"gbh/errorx"
	"gbh/monitor/internal/constants"
	"gbh/monitor/internal/svc"
	"gbh/proto/monitor"
	"gbh/utils/urlutil"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

type SendTelegramLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSendTelegramLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SendTelegramLogic {
	return &SendTelegramLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SendTelegramLogic) SendTelegram(in *monitor.SendTelegramRequest) (*monitor.EmptyResponse, error) {
	tgChatID, ok := l.svcCtx.Config.TelegramConf.ChatID[in.GetServiceName()]

	if !ok {
		return nil, errorx.InvalidParameter
	}

	err := l.sendTelegramMessage(tgChatID, in.GetMsg())

	if err != nil {
		notifyGroupID := l.svcCtx.Config.TelegramConf.ChatID["gti"]

		var sb strings.Builder
		sb.WriteString("--------------------\n")
		sb.WriteString("發送 Telegram 訊息失敗\n")
		sb.WriteString(fmt.Sprintf("服務: %s\n", in.GetServiceName()))
		sb.WriteString(fmt.Sprintf("原始訊息: %s\n", in.GetMsg()))
		sb.WriteString(fmt.Sprintf("錯誤: %v\n", err))
		sb.WriteString("--------------------")

		sendErr := l.sendTelegramMessage(notifyGroupID, sb.String())

		if sendErr != nil {
			return nil, err
		}

		return nil, err
	}

	return &monitor.EmptyResponse{}, nil
}

func (l *SendTelegramLogic) sendTelegramMessage(chatID, message string) error {
	uri := fmt.Sprintf(constants.TelegramAPI, l.svcCtx.Config.TelegramConf.Token)

	params := urlutil.NewBuilder()
	params.AddString("chat_id", chatID)
	params.AddString("text", message)

	resp, err := l.svcCtx.TelegramClient.R().
		SetQueryParamsFromValues(params.Values()).
		Post(uri)

	if err != nil {
		return errorx.ConnectionFailed
	}

	var tgResp struct {
		OK          bool   `json:"ok"`
		Description string `json:"description"`
	}

	jsonParseErr := json.Unmarshal(resp.Body(), &tgResp)

	if jsonParseErr != nil {
		return errorx.JSONParseFailed
	}

	if !tgResp.OK {
		return fmt.Errorf("%w: %s", errorx.InvalidResponse, tgResp.Description)
	}

	return nil
}
