package logic

import (
	"gbh/errorx"
	"gbh/monitor/internal/constants"
	"gbh/proto/monitor"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func Test_GetMonitorDBPayoffAlertTableLogic_Get(t *testing.T) {
	request := &monitor.GetMonitorDBPayoffAlertTableRequest{
		GroupId:             1,
		StartDate:           "2025-05-20",
		EndDate:             "2025-05-20",
		HallId:              3820474,
		UserId:              1234,
		LobbyId:             5,
		Category:            "common",
		ConditionType:       "total",
		IsPayoffGreaterThan: &monitor.BoolValue{Value: true},
		Sort:                constants.SortTime,
		Order:               constants.OrderDesc,
	}

	countRows := sqlmock.NewRows([]string{"count(*)"}).AddRow(1)
	sqlMockMonitor.ExpectQuery("SELECT count(*) FROM `PayoffAlert` WHERE (time BETWEEN ? AND ?) AND site = ? AND group_id = ? AND domain = ? AND member = ? AND lobby = ? AND category = ? AND condition_type = ? AND payoff > ?").
		WithArgs(request.GetStartDate()+" 00:00:00", request.GetEndDate()+" 23:59:59", "admin", request.GetGroupId(), request.GetHallId(), request.GetUserId(), request.GetLobbyId(), request.GetCategory(), request.GetConditionType(), `0`).
		WillReturnRows(countRows)

	row := sqlmock.NewRows([]string{"id", "site", "group_id", "domain", "member", "lobby", "category", "condition_type", "win", "lose", "day", "percent", "bet_amount_setting", "time", "payoff", "bet_amount", "growth"}).
		AddRow(1, "admin", 1, 3820474, 1234, 5, "common", "total", 100000, 100000, 0, 0, 0, "2025-05-20 18:00:06", 0, 690000, 0)
	sqlMockMonitor.ExpectQuery("SELECT `id`,`site`,`group_id`,`domain`,`member`,`lobby`,`category`,`condition_type`,`win`,`lose`,`day`,`percent`,`bet_amount_setting`,`time`,`payoff`,`bet_amount`,`growth` FROM `PayoffAlert` WHERE (time BETWEEN ? AND ?) AND site = ? AND group_id = ? AND domain = ? AND member = ? AND lobby = ? AND category = ? AND condition_type = ? AND payoff > ? ORDER BY time desc LIMIT ?").
		WithArgs(request.GetStartDate()+" 00:00:00", request.GetEndDate()+" 23:59:59", "admin", request.GetGroupId(), request.GetHallId(), request.GetUserId(), request.GetLobbyId(), request.GetCategory(), request.GetConditionType(), `0`, 20).
		WillReturnRows(row)

	l := NewGetMonitorDBPayoffAlertTableLogic(ctx, svcCtx)
	resp, err := l.GetMonitorDBPayoffAlertTable(request)

	expect := monitor.GetMonitorDBPayoffAlertTableResponse{
		List: []*monitor.FetchMonitorDBPayoffAlertTable{
			{
				Id:               1,
				GroupId:          1,
				HallId:           3820474,
				UserId:           1234,
				LobbyId:          5,
				Category:         "common",
				ConditionType:    "total",
				Win:              100000,
				Lost:             100000,
				Day:              0,
				Percent:          0,
				BetAmountSetting: 0,
				Payoff:           0,
				BetAmount:        690000,
				Growth:           0,
				Time:             "2025-05-20T18:00:06-04:00",
			},
		},
		Pagination: &monitor.Pagination{
			CurrentPage: 1,
			PageLimit:   20,
			Total:       1,
			TotalPage:   1,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, &expect, resp)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}

func Test_GetMonitorDBPayoffAlertTableLogic_Get_IsPayoffGreaterThaFalse(t *testing.T) {
	request := &monitor.GetMonitorDBPayoffAlertTableRequest{
		GroupId:             1,
		StartDate:           "2025-05-20",
		EndDate:             "2025-05-20",
		IsPayoffGreaterThan: &monitor.BoolValue{Value: false},
	}

	countRows := sqlmock.NewRows([]string{"count(*)"}).AddRow(1)
	sqlMockMonitor.ExpectQuery("SELECT count(*) FROM `PayoffAlert` WHERE (time BETWEEN ? AND ?) AND site = ? AND group_id = ? AND payoff < ?").
		WithArgs(request.GetStartDate()+" 00:00:00", request.GetEndDate()+" 23:59:59", "admin", request.GetGroupId(), `0`).
		WillReturnRows(countRows)

	row := sqlmock.NewRows([]string{"id", "site", "group_id", "domain", "member", "lobby", "category", "condition_type", "win", "lose", "day", "percent", "bet_amount_setting", "time", "payoff", "bet_amount", "growth"}).
		AddRow(1, "admin", 1, 3820474, 1234, 5, "common", "total", 100000, 100000, 0, 0, 0, "2025-05-20 18:00:06", 0, 690000, 0)
	sqlMockMonitor.ExpectQuery("SELECT `id`,`site`,`group_id`,`domain`,`member`,`lobby`,`category`,`condition_type`,`win`,`lose`,`day`,`percent`,`bet_amount_setting`,`time`,`payoff`,`bet_amount`,`growth` FROM `PayoffAlert` WHERE (time BETWEEN ? AND ?) AND site = ? AND group_id = ? AND payoff < ? ORDER BY time desc LIMIT ?").
		WithArgs(request.GetStartDate()+" 00:00:00", request.GetEndDate()+" 23:59:59", "admin", request.GetGroupId(), `0`, 20).
		WillReturnRows(row)

	l := NewGetMonitorDBPayoffAlertTableLogic(ctx, svcCtx)
	resp, err := l.GetMonitorDBPayoffAlertTable(request)

	expect := monitor.GetMonitorDBPayoffAlertTableResponse{
		List: []*monitor.FetchMonitorDBPayoffAlertTable{
			{
				Id:               1,
				GroupId:          1,
				HallId:           3820474,
				UserId:           1234,
				LobbyId:          5,
				Category:         "common",
				ConditionType:    "total",
				Win:              100000,
				Lost:             100000,
				Day:              0,
				Percent:          0,
				BetAmountSetting: 0,
				Payoff:           0,
				BetAmount:        690000,
				Growth:           0,
				Time:             "2025-05-20T18:00:06-04:00",
			},
		},
		Pagination: &monitor.Pagination{
			CurrentPage: 1,
			PageLimit:   20,
			Total:       1,
			TotalPage:   1,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, &expect, resp)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}

func Test_GetMonitorDBPayoffAlertTableLogic_Count_DatabaseError(t *testing.T) {
	request := &monitor.GetMonitorDBPayoffAlertTableRequest{
		GroupId:   1,
		StartDate: "2025-05-20",
		EndDate:   "2025-05-20",
	}

	sqlMockMonitor.ExpectQuery("SELECT count(*) FROM `PayoffAlert` WHERE (time BETWEEN ? AND ?) AND site = ? AND group_id = ?").
		WithArgs(request.GetStartDate()+" 00:00:00", request.GetEndDate()+" 23:59:59", "admin", request.GetGroupId()).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetMonitorDBPayoffAlertTableLogic(ctx, svcCtx)
	resp, err := l.GetMonitorDBPayoffAlertTable(request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}

func Test_GetMonitorDBPayoffAlertTableLogic_Find_DatabaseError(t *testing.T) {
	request := &monitor.GetMonitorDBPayoffAlertTableRequest{
		GroupId:   1,
		StartDate: "2025-05-20",
		EndDate:   "2025-05-20",
	}

	countRows := sqlmock.NewRows([]string{"count(*)"}).AddRow(1)
	sqlMockMonitor.ExpectQuery("SELECT count(*) FROM `PayoffAlert` WHERE (time BETWEEN ? AND ?) AND site = ? AND group_id = ?").
		WithArgs(request.GetStartDate()+" 00:00:00", request.GetEndDate()+" 23:59:59", "admin", request.GetGroupId()).
		WillReturnRows(countRows)

	sqlMockMonitor.ExpectQuery("SELECT `id`,`site`,`group_id`,`domain`,`member`,`lobby`,`category`,`condition_type`,`win`,`lose`,`day`,`percent`,`bet_amount_setting`,`time`,`payoff`,`bet_amount`,`growth` FROM `PayoffAlert` WHERE (time BETWEEN ? AND ?) AND site = ? AND group_id = ? ORDER BY time desc LIMIT ?").
		WithArgs(request.GetStartDate()+" 00:00:00", request.GetEndDate()+" 23:59:59", "admin", request.GetGroupId(), 20).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetMonitorDBPayoffAlertTableLogic(ctx, svcCtx)
	resp, err := l.GetMonitorDBPayoffAlertTable(request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockMonitor.ExpectationsWereMet())
}
