package logic

import (
	"context"

	"gbh/errorx"
	"gbh/monitor/internal/constants"
	"gbh/monitor/internal/schema"
	"gbh/monitor/internal/svc"
	"gbh/proto/monitor"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type GetMonitorDBPayoffAlertTableLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMonitorDBPayoffAlertTableLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMonitorDBPayoffAlertTableLogic {
	return &GetMonitorDBPayoffAlertTableLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetMonitorDBPayoffAlertTableLogic) GetMonitorDBPayoffAlertTable(in *monitor.GetMonitorDBPayoffAlertTableRequest) (*monitor.GetMonitorDBPayoffAlertTableResponse, error) {
	selectFields := []string{"id", "site", "group_id", "domain", "member", "lobby", "category", "condition_type", "win", "lose", "day", "percent", "bet_amount_setting", "time", "payoff", "bet_amount", "growth"}
	query := l.svcCtx.MonitorDB.
		Table("PayoffAlert").
		Select(selectFields).
		Where("time BETWEEN ? AND ?", in.GetStartDate()+" 00:00:00", in.GetEndDate()+" 23:59:59").
		Where("site = ?", "admin").
		Where("group_id = ?", in.GetGroupId())

	// 處理非必帶參數
	query = l.applyFilters(query, in)

	// 取得總筆數
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, errorx.DatabaseError
	}

	// 處理 query.Order
	query = l.applyOrder(query, in)

	// 處理 query.Limit
	query, limit, page := l.applyLimit(query, in)

	// 計算總頁數, 避免浮點運算
	totalPage := (uint32(totalCount) + limit - 1) / limit

	// 取得查詢結果
	var alert []schema.MonitorDBPayoffAlertTable
	if err := query.Find(&alert).Error; err != nil {
		return nil, errorx.DatabaseError
	}

	// 整理回傳
	respList := make([]*monitor.FetchMonitorDBPayoffAlertTable, 0, len(alert))
	for _, v := range alert {
		respList = append(respList, &monitor.FetchMonitorDBPayoffAlertTable{
			Id:               v.ID,
			GroupId:          v.GroupID,
			HallId:           v.HallID,
			UserId:           v.UserID,
			LobbyId:          v.LobbyID,
			Category:         v.Category,
			ConditionType:    v.ConditionType,
			Win:              v.Win,
			Lost:             v.Lose,
			Day:              v.Day,
			Percent:          v.Percent,
			BetAmountSetting: v.BetAmountSetting,
			Payoff:           v.PayOff,
			BetAmount:        v.BetAmount,
			Growth:           v.Growth,
			Time:             carbon.Parse(v.Time, constants.TimezoneGMT4).ToRfc3339String(),
		})
	}

	return &monitor.GetMonitorDBPayoffAlertTableResponse{
		List: respList,
		Pagination: &monitor.Pagination{
			CurrentPage: page,
			PageLimit:   limit,
			Total:       uint32(totalCount),
			TotalPage:   totalPage,
		},
	}, nil
}

func (l *GetMonitorDBPayoffAlertTableLogic) applyFilters(db *gorm.DB, in *monitor.GetMonitorDBPayoffAlertTableRequest) *gorm.DB {
	// 廳主ID
	if in.GetHallId() > 0 {
		db.Where("domain = ?", in.GetHallId())
	}

	// 監控會員ID
	if in.GetUserId() > 0 {
		db.Where("member = ?", in.GetUserId())
	}

	// 監控的遊戲大廳
	if in.GetLobbyId() > 0 {
		db.Where("lobby = ?", in.GetLobbyId())
	}

	// 監控類別(common：共用條件 / domain：特例廳主 / member：特例會員)
	if in.GetCategory() != "" {
		db.Where("category = ?", in.GetCategory())
	}

	// 監控子條件(total：總會員輸贏 / single：任一會員輸贏 / average：平均%數)
	if in.GetConditionType() != "" {
		db.Where("condition_type = ?", in.GetConditionType())
	}

	// 撈總損益 > 0
	if in.GetIsPayoffGreaterThan() != nil && in.GetIsPayoffGreaterThan().GetValue() {
		db.Where("payoff > ?", "0")
	}

	// 撈總損益 < 0
	if in.GetIsPayoffGreaterThan() != nil && !in.GetIsPayoffGreaterThan().GetValue() {
		db.Where("payoff < ?", "0")
	}

	return db
}

func (l *GetMonitorDBPayoffAlertTableLogic) applyOrder(db *gorm.DB, in *monitor.GetMonitorDBPayoffAlertTableRequest) *gorm.DB {
	// 排序的類別
	sort := constants.SortTime
	if in.GetSort() != "" {
		sort = in.GetSort()
	}

	// 排序的方法
	order := constants.OrderDesc
	if in.GetOrder() != "" {
		order = in.GetOrder()
	}

	// 設定排序方法
	db = db.Order(sort + " " + order)

	return db
}

func (l *GetMonitorDBPayoffAlertTableLogic) applyLimit(db *gorm.DB, in *monitor.GetMonitorDBPayoffAlertTableRequest) (*gorm.DB, uint32, uint32) {
	// 撈取筆數
	limit := in.GetPageLimit()
	if limit <= 0 {
		limit = 20
	}

	// 第幾頁
	page := in.GetPage()
	if page <= 0 {
		page = 1
	}

	// 設定分頁查詢
	db = db.Limit(int(limit)).Offset(int(limit * (page - 1)))

	return db, limit, page
}
