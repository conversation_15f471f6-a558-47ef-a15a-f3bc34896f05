package logic

import (
	"context"

	"gbh/errorx"
	"gbh/monitor/internal/schema"
	"gbh/monitor/internal/svc"
	"gbh/proto/monitor"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetConditionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetConditionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetConditionLogic {
	return &GetConditionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetConditionLogic) GetCondition(in *monitor.GetConditionRequest) (*monitor.GetConditionResponse, error) {
	query := l.svcCtx.MonitorDB.Table("PayoffCondition").
		Where("site = ?", in.GetSite()).
		Where("group_id = ?", in.GetGroupId())

	if in.GetUserId() > 0 {
		query.Where("member = ?", in.GetUserId())
	}

	if in.GetMonitorId() > 0 {
		query.Where("id != ?", in.GetMonitorId())
	}

	if in.GetHallId() > 0 {
		query.Where("domain = ?", in.GetHallId())
	} else {
		query.Where("domain != 0")
	}

	if len(in.GetGameKinds()) > 0 {
		query.Joins("INNER JOIN PayoffLobby ON PayoffCondition.id = PayoffLobby.mid").
			Where("PayoffLobby.lobby IN ?", in.GetGameKinds())
	}

	var conditions []schema.PayoffCondition
	if err := query.Find(&conditions).Error; err != nil {
		return nil, errorx.DatabaseError
	}

	monitors := make([]*monitor.MonitorCondition, 0, len(conditions))
	for _, v := range conditions {
		var lobbies []schema.PayoffLobby
		err := l.svcCtx.MonitorDB.Table("PayoffLobby").
			Where("mid = ?", v.ID).
			Where("lobby IN ?", in.GetGameKinds()).
			Find(&lobbies).Error

		if err != nil {
			return nil, errorx.DatabaseError
		}

		gameKindList := make([]uint32, 0, len(lobbies))
		for _, lobby := range lobbies {
			gameKindList = append(gameKindList, lobby.Lobby)
		}

		var subconditions []schema.PayoffSubCondition
		err = l.svcCtx.MonitorDB.Table("PayoffSubcondition").
			Where("mid = ?", v.ID).
			Find(&subconditions).Error

		if err != nil {
			return nil, errorx.DatabaseError
		}

		filteredConditions := handleSubCondition(subconditions)

		userID := uint32(0)
		if v.Member != nil {
			userID = *v.Member
		}

		monitors = append(monitors, &monitor.MonitorCondition{
			MonitorId: v.ID,
			Site:      v.Site,
			GroupId:   v.GroupID,
			HallId:    v.Domain,
			UserId:    userID,
			GameKind:  gameKindList,
			Condition: filteredConditions,
		})
	}

	return &monitor.GetConditionResponse{
		Monitors: monitors,
	}, nil
}

func handleSubCondition(subConditions []schema.PayoffSubCondition) []*monitor.SubCondition {
	var result []*monitor.SubCondition

	for _, v := range subConditions {
		subCond := &monitor.SubCondition{
			ConditionType: v.ConditionType,
		}

		switch v.ConditionType {
		case "total", "single":
			if v.Win != nil {
				win := v.Win
				subCond.Win = *win
			}

			if v.Lose != nil {
				lose := v.Lose
				subCond.Lose = *lose
			}

			result = append(result, subCond)
		case "average":
			day := v.Day
			percent := v.Percent
			betAmount := v.BetAmount
			subCond.Day = *day
			subCond.Percent = *percent
			subCond.BetAmount = *betAmount

			result = append(result, subCond)
		}
	}

	return result
}
