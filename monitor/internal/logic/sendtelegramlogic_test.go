package logic

import (
	"gbh/errorx"
	"gbh/proto/monitor"
	"net/http"
	"testing"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
)

func TestSendTelegramLogic(t *testing.T) {
	request := monitor.SendTelegramRequest{
		ServiceName: "market",
		Msg:         "test tg msg",
	}

	responder := httpmock.NewStringResponder(200, `{"ok":true,"result":{"message_id":2518,"from":{"id":5308224698,"is_bot":true,"first_name":"simon","username":"wewe0204_bot"},"chat":{"id":963686361,"first_name":"<PERSON>","username":"wewe070707","type":"private"},"date":1747281012,"text":"test tg msg"}}`)

	httpmock.ActivateNonDefault(svcCtx.TelegramClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("POST", telegramURL, responder)

	l := NewSendTelegramLogic(ctx, svcCtx)
	resp, err := l.SendTelegram(&request)

	assert.NoError(t, err)
	assert.Equal(t, &monitor.EmptyResponse{}, resp)
}

func TestSendTelegramLogic_InvalidParameter(t *testing.T) {
	request := monitor.SendTelegramRequest{
		ServiceName: "test",
		Msg:         "test tg msg",
	}

	responder := httpmock.NewStringResponder(200, `{"ok":true,"result":{"message_id":2518,"from":{"id":5308224698,"is_bot":true,"first_name":"simon","username":"wewe0204_bot"},"chat":{"id":963686361,"first_name":"Simon","username":"wewe070707","type":"private"},"date":1747281012,"text":"test tg msg"}}`)

	httpmock.ActivateNonDefault(svcCtx.TelegramClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("POST", telegramURL, responder)

	l := NewSendTelegramLogic(ctx, svcCtx)
	resp, err := l.SendTelegram(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.InvalidParameter, err)
}

func TestSendTelegramLogic_ConnectionFailed(t *testing.T) {
	request := monitor.SendTelegramRequest{
		ServiceName: "market",
		Msg:         "test tg msg",
	}

	responder := httpmock.NewErrorResponder(http.ErrHandlerTimeout)

	httpmock.ActivateNonDefault(svcCtx.TelegramClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("POST", telegramURL, responder)

	l := NewSendTelegramLogic(ctx, svcCtx)
	resp, err := l.SendTelegram(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestSendTelegramLogic_JSONParseFailed(t *testing.T) {
	request := monitor.SendTelegramRequest{
		ServiceName: "market",
		Msg:         "test tg msg",
	}

	responder := httpmock.NewStringResponder(200, `ok`)

	httpmock.ActivateNonDefault(svcCtx.TelegramClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("POST", telegramURL, responder)

	l := NewSendTelegramLogic(ctx, svcCtx)
	resp, err := l.SendTelegram(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.JSONParseFailed, err)
}

func TestSendTelegramLogic_RespNotOK(t *testing.T) {
	request := monitor.SendTelegramRequest{
		ServiceName: "market",
		Msg:         "test tg msg",
	}

	responder := httpmock.NewStringResponder(200, `{"ok":false,"error_code":400,"description":"Bad Request: chat not found"}`)

	httpmock.ActivateNonDefault(svcCtx.TelegramClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("POST", telegramURL, responder)

	l := NewSendTelegramLogic(ctx, svcCtx)
	resp, err := l.SendTelegram(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, err, errorx.InvalidResponse)
}

func TestSendTelegramLogic_SendErr(t *testing.T) {
	request := monitor.SendTelegramRequest{
		ServiceName: "market",
		Msg:         "\u0000",
	}

	resp1 := httpmock.NewStringResponse(200, `{"ok":false,"error_code":400,"description":"Bad Request: text must be non-empty"}`)
	defer resp1.Body.Close()

	resp2 := httpmock.NewStringResponse(200, `{"ok":true}`)
	defer resp2.Body.Close()

	responses := []*http.Response{resp1, resp2}

	httpmock.ActivateNonDefault(svcCtx.TelegramClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("POST", telegramURL, httpmock.ResponderFromMultipleResponses(responses))

	l := NewSendTelegramLogic(ctx, svcCtx)
	resp, err := l.SendTelegram(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, err, errorx.InvalidResponse)
	assert.Contains(t, err.Error(), "Bad Request: text must be non-empty")

	info := httpmock.GetCallCountInfo()
	assert.Equal(t, 2, info["POST "+telegramURL])
}
