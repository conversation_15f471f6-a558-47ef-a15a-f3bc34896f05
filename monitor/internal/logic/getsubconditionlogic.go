package logic

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gbh/errorx"
	"gbh/monitor/internal/schema"
	"gbh/monitor/internal/svc"
	"gbh/proto/monitor"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type GetSubConditionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetSubConditionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSubConditionLogic {
	return &GetSubConditionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetSubConditionLogic) GetSubCondition(in *monitor.GetSubConditionRequest) (*monitor.GetSubConditionResponse, error) {
	query := l.svcCtx.MonitorDB.Table("PayoffCondition").
		Where("site = ?", in.GetSite()).
		Where("group_id = ?", in.GetGroupId())

	if in.GetUserId() > 0 {
		query.Where("member = ?", in.GetUserId())
	}

	if in.GetMonitorId() > 0 {
		query.Where("id != ?", in.GetMonitorId())
	}

	if in.GetHallId() > 0 {
		query.Where("domain = ?", in.GetHallId())
	} else {
		query.Where("domain != 0")
	}

	subConditionSQL, subConditionCount := buildSubConditionQuery(in.GetSubCondition())
	if subConditionSQL != "" {
		query = query.Where(subConditionSQL)
		query = query.Where("(SELECT COUNT(*) FROM PayoffSubcondition WHERE mid = PayoffCondition.id) = ?", subConditionCount)
	}

	var condition schema.PayoffCondition
	if err := query.First(&condition).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &monitor.GetSubConditionResponse{}, nil
		}

		return nil, errorx.DatabaseError
	}

	var lobbies []schema.PayoffLobby
	err := l.svcCtx.MonitorDB.Table("PayoffLobby").
		Where("mid = ?", condition.ID).
		Find(&lobbies).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	gameKindList := make([]uint32, 0, len(lobbies))
	for _, lobby := range lobbies {
		gameKindList = append(gameKindList, lobby.Lobby)
	}

	userID := uint32(0)
	if condition.Member != nil {
		userID = *condition.Member
	}

	return &monitor.GetSubConditionResponse{
		MonitorId: condition.ID,
		Site:      condition.Site,
		GroupId:   condition.GroupID,
		HallId:    condition.Domain,
		UserId:    userID,
		GameKind:  gameKindList,
	}, nil
}

// 撈取子條件，並確認是否與帶入的條件內容及數量相符
func buildSubConditionQuery(sc *monitor.GetSubCondition) (string, uint32) {
	if sc == nil {
		return "", 0
	}

	var conditions []string
	count := uint32(0)

	// total 條件
	if sc.GetWin() != nil || sc.GetLose() != nil {
		var cond strings.Builder
		cond.WriteString("EXISTS (SELECT 1 FROM PayoffSubcondition WHERE mid = PayoffCondition.id AND condition_type = 'total'")

		if sc.GetWin() != nil {
			cond.WriteString(fmt.Sprintf(" AND win = %d", sc.GetWin().GetValue()))
		} else {
			cond.WriteString(" AND win IS NULL")
		}

		if sc.GetLose() != nil {
			cond.WriteString(fmt.Sprintf(" AND lose = %d", sc.GetLose().GetValue()))
		} else {
			cond.WriteString(" AND lose IS NULL")
		}

		cond.WriteString(")")
		conditions = append(conditions, fmt.Sprintf("(%s)", cond.String()))
		count++
	}

	// single 條件
	if sc.GetMemberWin() != nil || sc.GetMemberLose() != nil {
		var cond strings.Builder
		cond.WriteString("EXISTS (SELECT 1 FROM PayoffSubcondition WHERE mid = PayoffCondition.id AND condition_type = 'single'")

		if sc.GetMemberWin() != nil {
			cond.WriteString(fmt.Sprintf(" AND win = %d", sc.GetMemberWin().GetValue()))
		} else {
			cond.WriteString(" AND win IS NULL")
		}

		if sc.GetMemberLose() != nil {
			cond.WriteString(fmt.Sprintf(" AND lose = %d", sc.GetMemberLose().GetValue()))
		} else {
			cond.WriteString(" AND lose IS NULL")
		}

		cond.WriteString(")")
		conditions = append(conditions, fmt.Sprintf("(%s)", cond.String()))
		count++
	}

	// average 條件
	if sc.GetDay() != nil && sc.GetPercent() != nil && sc.GetBetAmount() != nil {
		conditions = append(conditions, fmt.Sprintf(
			`(EXISTS (SELECT 1 FROM PayoffSubcondition WHERE mid = PayoffCondition.id AND condition_type = 'average' AND day = %d AND percent = %d AND bet_amount = %d))`,
			sc.GetDay().GetValue(), sc.GetPercent().GetValue(), sc.GetBetAmount().GetValue()))
		count++
	}

	return strings.Join(conditions, " AND "), count
}
