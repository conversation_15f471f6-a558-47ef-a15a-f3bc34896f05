package logic

import (
	"context"
	"fmt"

	"gbh/monitor/internal/config"
	"gbh/monitor/internal/svc"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	ctx            context.Context
	svcCtx         *svc.ServiceContext
	sqlMockMonitor sqlmock.Sqlmock
	gormMonitorDB  *gorm.DB
	telegramURL    string
	telegramConf   config.TelegramConf
)

func init() {
	ctx = context.Background()

	telegramConf = config.TelegramConf{
		Token: "token",
		ChatID: map[string]string{
			"market": "chatid",
		},
	}
	telegramURL = fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", telegramConf.Token)

	gormMonitorDB, sqlMockMonitor = NewMockDB()

	svcCtx = svc.NewServiceContext(config.Config{
		TelegramConf: telegramConf,
	}, svc.ExternalContext{
		MonitorDB: gormMonitorDB,
	})
}

func NewMockDB() (*gorm.DB, sqlmock.Sqlmock) {
	dbObj, sqlMock, _ := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	dialector := mysql.New(mysql.Config{
		DSN:                       "sqlmock_db_0",
		DriverName:                "mysql",
		Conn:                      dbObj,
		SkipInitializeWithVersion: true,
	})

	gormDB, _ := gorm.Open(dialector, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})

	return gormDB, sqlMock
}
