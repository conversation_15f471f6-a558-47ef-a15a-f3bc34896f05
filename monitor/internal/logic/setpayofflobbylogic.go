package logic

import (
	"context"

	"gbh/errorx"
	"gbh/monitor/internal/schema"
	"gbh/monitor/internal/svc"
	"gbh/proto/monitor"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SetPayOffLobbyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetPayOffLobbyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetPayOffLobbyLogic {
	return &SetPayOffLobbyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SetPayOffLobbyLogic) SetPayOffLobby(in *monitor.SetPayOffLobbyRequest) (*monitor.EmptyResponse, error) {
	saveData := make([]schema.PayoffLobby, 0, len(in.GetGameKind()))
	for _, data := range in.GetGameKind() {
		saveData = append(saveData, schema.PayoffLobby{
			MID:   in.GetMonitorId(),
			Lobby: data,
		})
	}

	err := l.svcCtx.MonitorDB.Transaction(func(tx *gorm.DB) error {
		// 清除影響的遊戲大廳
		deleteInfluenceErr := tx.Table("PayoffLobby").
			Where("mid IN (?)", in.GetInfluenceMonitorId()).
			Where("lobby IN (?)", in.GetGameKind()).
			Delete(nil).Error

		if deleteInfluenceErr != nil {
			return deleteInfluenceErr
		}

		// 清除該監控條件的全部遊戲大廳
		deleteErr := tx.Table("PayoffLobby").
			Where("mid = ?", in.GetMonitorId()).
			Delete(nil).Error

		if deleteErr != nil {
			return deleteErr
		}

		createErr := tx.Table("PayoffLobby").Create(&saveData).Error

		if createErr != nil {
			return createErr
		}

		return nil
	})

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &monitor.EmptyResponse{}, nil
}
