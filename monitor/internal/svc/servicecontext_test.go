package svc

import (
	"gbh/monitor/internal/config"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewServiceContext(t *testing.T) {
	conf := config.Config{
		TelegramConf: config.TelegramConf{
			Token: "test_token",
			ChatID: map[string]string{
				"market": "123456789",
			},
		},
	}

	svcCtx := NewServiceContext(conf, ExternalContext{})

	assert.ObjectsAreEqual(&ServiceContext{}, svcCtx)
	assert.Equal(t, conf, svcCtx.Config)
	assert.Nil(t, svcCtx.MonitorDB)
}
