package svc

import (
	"gbh/monitor/internal/config"
	"gbh/monitor/internal/constants"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config         config.Config
	MonitorDB      *gorm.DB
	TelegramClient *resty.Client
}

func NewServiceContext(c config.Config, extSvc ExternalContext) *ServiceContext {
	telegramClient := resty.New().
		SetTimeout(constants.APITimeout)

	return &ServiceContext{
		Config:         c,
		MonitorDB:      extSvc.MonitorDB,
		TelegramClient: telegramClient,
	}
}
