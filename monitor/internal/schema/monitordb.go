package schema

type MonitorDBPayoffAlertTable struct {
	ID               int32  `gorm:"column:id"`
	Site             string `gorm:"column:site"`
	GroupID          int32  `gorm:"column:group_id"`
	HallID           int64  `gorm:"column:domain"`
	UserID           int64  `gorm:"column:member"`
	LobbyID          int32  `gorm:"column:lobby"`
	Category         string `gorm:"column:category"`
	ConditionType    string `gorm:"column:condition_type"`
	Win              int32  `gorm:"column:win"`
	Lose             int32  `gorm:"column:lose"`
	Day              int32  `gorm:"column:day"`
	Percent          int32  `gorm:"column:percent"`
	BetAmountSetting int32  `gorm:"column:bet_amount_setting"`
	Time             string `gorm:"column:time"`
	PayOff           int32  `gorm:"column:payoff"`
	BetAmount        int32  `gorm:"column:bet_amount"`
	Growth           int32  `gorm:"column:growth"`
}

type MonitorDBPayoffAlertGameTypeTable struct {
	ID        int32   `gorm:"column:id"`
	GameID    string  `gorm:"column:game_type"`
	PayOff    float64 `gorm:"column:payoff"`
	BetAmount float64 `gorm:"column:bet_amount"`
	Growth    int32   `gorm:"column:growth"`
}
