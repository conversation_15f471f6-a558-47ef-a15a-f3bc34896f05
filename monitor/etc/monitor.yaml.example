Name: monitor.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
    - 127.0.0.1:2379
  Key: monitor.rpc

MonitorDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: MonitorDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: MonitorDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

TelegramConf:
  Token: *********************************************
  ChatID:
    "gti": "-1002535635341"
    "market": "ChatID"

Middlewares:
  Stat: false
