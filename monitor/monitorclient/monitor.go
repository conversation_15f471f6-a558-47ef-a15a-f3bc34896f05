// Code generated by goctl. DO NOT EDIT.
// Source: monitor.proto

package monitorclient

import (
	"context"

	"gbh/proto/monitor"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	BoolValue                            = monitor.BoolValue
	CreateCondition                      = monitor.CreateCondition
	CreateConditionRequest               = monitor.CreateConditionRequest
	CreateSubCondition                   = monitor.CreateSubCondition
	DeleteConditionRequest               = monitor.DeleteConditionRequest
	EmptyResponse                        = monitor.EmptyResponse
	FetchMonitorDBPayoffAlertTable       = monitor.FetchMonitorDBPayoffAlertTable
	GetConditionRequest                  = monitor.GetConditionRequest
	GetConditionResponse                 = monitor.GetConditionResponse
	GetMonitorDBPayoffAlertTableRequest  = monitor.GetMonitorDBPayoffAlertTableRequest
	GetMonitorDBPayoffAlertTableResponse = monitor.GetMonitorDBPayoffAlertTableResponse
	GetProfitLossByGameType              = monitor.GetProfitLossByGameType
	GetProfitLossByGameTypeRequest       = monitor.GetProfitLossByGameTypeRequest
	GetProfitLossByGameTypeResponse      = monitor.GetProfitLossByGameTypeResponse
	GetSubCondition                      = monitor.GetSubCondition
	GetSubConditionRequest               = monitor.GetSubConditionRequest
	GetSubConditionResponse              = monitor.GetSubConditionResponse
	MonitorCondition                     = monitor.MonitorCondition
	Pagination                           = monitor.Pagination
	SendTelegramRequest                  = monitor.SendTelegramRequest
	SetPayOffLobbyRequest                = monitor.SetPayOffLobbyRequest
	SubCondition                         = monitor.SubCondition
	Total                                = monitor.Total
	Uint32Value                          = monitor.Uint32Value
	Uint64Value                          = monitor.Uint64Value
	UpdateConditionRequest               = monitor.UpdateConditionRequest

	Monitor interface {
		CreateCondition(ctx context.Context, in *CreateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetCondition(ctx context.Context, in *GetConditionRequest, opts ...grpc.CallOption) (*GetConditionResponse, error)
		DeleteCondition(ctx context.Context, in *DeleteConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetSubCondition(ctx context.Context, in *GetSubConditionRequest, opts ...grpc.CallOption) (*GetSubConditionResponse, error)
		UpdateCondition(ctx context.Context, in *UpdateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SendTelegram(ctx context.Context, in *SendTelegramRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		SetPayOffLobby(ctx context.Context, in *SetPayOffLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetMonitorDBPayoffAlertTable(ctx context.Context, in *GetMonitorDBPayoffAlertTableRequest, opts ...grpc.CallOption) (*GetMonitorDBPayoffAlertTableResponse, error)
		GetProfitLossByGameType(ctx context.Context, in *GetProfitLossByGameTypeRequest, opts ...grpc.CallOption) (*GetProfitLossByGameTypeResponse, error)
	}

	defaultMonitor struct {
		cli zrpc.Client
	}
)

func NewMonitor(cli zrpc.Client) Monitor {
	return &defaultMonitor{
		cli: cli,
	}
}

func (m *defaultMonitor) CreateCondition(ctx context.Context, in *CreateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.CreateCondition(ctx, in, opts...)
}

func (m *defaultMonitor) GetCondition(ctx context.Context, in *GetConditionRequest, opts ...grpc.CallOption) (*GetConditionResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.GetCondition(ctx, in, opts...)
}

func (m *defaultMonitor) DeleteCondition(ctx context.Context, in *DeleteConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.DeleteCondition(ctx, in, opts...)
}

func (m *defaultMonitor) GetSubCondition(ctx context.Context, in *GetSubConditionRequest, opts ...grpc.CallOption) (*GetSubConditionResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.GetSubCondition(ctx, in, opts...)
}

func (m *defaultMonitor) UpdateCondition(ctx context.Context, in *UpdateConditionRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.UpdateCondition(ctx, in, opts...)
}

func (m *defaultMonitor) SendTelegram(ctx context.Context, in *SendTelegramRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.SendTelegram(ctx, in, opts...)
}

func (m *defaultMonitor) SetPayOffLobby(ctx context.Context, in *SetPayOffLobbyRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.SetPayOffLobby(ctx, in, opts...)
}

func (m *defaultMonitor) GetMonitorDBPayoffAlertTable(ctx context.Context, in *GetMonitorDBPayoffAlertTableRequest, opts ...grpc.CallOption) (*GetMonitorDBPayoffAlertTableResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.GetMonitorDBPayoffAlertTable(ctx, in, opts...)
}

func (m *defaultMonitor) GetProfitLossByGameType(ctx context.Context, in *GetProfitLossByGameTypeRequest, opts ...grpc.CallOption) (*GetProfitLossByGameTypeResponse, error) {
	client := monitor.NewMonitorClient(m.cli.Conn())
	return client.GetProfitLossByGameType(ctx, in, opts...)
}
