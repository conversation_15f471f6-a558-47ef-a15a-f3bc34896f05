syntax = "proto3";

package monitor;
option go_package = "proto/monitor";

message Uint32Value { uint32 value = 1; }
message Uint64Value { uint64 value = 1; }
message BoolValue { bool value = 1; }

message EmptyResponse {}

message GetConditionRequest {
  string site = 1;
  uint32 group_id = 2;
  uint32 hall_id = 3;
  uint32 user_id = 4;
  repeated uint32 game_kinds = 5;
  uint32 monitor_id = 6;
}

message SubCondition {
  string condition_type = 1;
  uint64 win = 2;
  uint64 lose = 3;
  uint32 day = 4;
  uint32 percent = 5;
  uint64 bet_amount = 6;
}

message MonitorCondition {
  uint32 monitor_id = 1;
  string site = 2;
  uint32 group_id = 3;
  uint32 hall_id = 4;
  uint32 user_id = 5;
  repeated uint32 game_kind = 6;
  repeated SubCondition condition = 7;
}

message GetConditionResponse { repeated MonitorCondition monitors = 1; }

message CreateConditionRequest {
  CreateCondition condition = 1;
  CreateSubCondition sub_condition = 2;
  repeated uint32 game_kinds = 3;
}

message CreateCondition {
  string site = 1;
  uint32 group_id = 2;
  uint32 hall_id = 3;
  uint32 user_id = 4;
}

message CreateSubCondition {
  uint64 win = 1;
  uint64 lose = 2;
  uint64 member_win = 3;
  uint64 member_lose = 4;
  uint32 day = 5;
  uint32 percent = 6;
  uint64 bet_amount = 7;
}

message DeleteConditionRequest {
  uint32 monitor_id = 1;
  uint32 game_kind = 2;
}

message GetSubConditionRequest {
  string site = 1;
  uint32 group_id = 2;
  uint32 hall_id = 3;
  uint32 user_id = 4;
  uint32 monitor_id = 5;
  GetSubCondition sub_condition = 6;
}

message GetSubCondition {
  Uint64Value win = 1;
  Uint64Value lose = 2;
  Uint64Value member_win = 3;
  Uint64Value member_lose = 4;
  Uint32Value day = 5;
  Uint32Value percent = 6;
  Uint64Value bet_amount = 7;
}

message GetSubConditionResponse {
  uint32 monitor_id = 1;
  string site = 2;
  uint32 group_id = 3;
  uint32 hall_id = 4;
  uint32 user_id = 5;
  repeated uint32 game_kind = 6;
}

message UpdateConditionRequest {
  uint32 monitor_id = 1;
  repeated uint32 game_kinds = 2;
  repeated uint32 affected_monitor_id = 3;
}

message SendTelegramRequest {
  string service_name = 1;
  string msg = 2;
}

message SetPayOffLobbyRequest {
  uint32 monitor_id = 1;
  repeated uint32 game_kind = 2;
  repeated uint32 influence_monitor_id = 3;
}

message GetMonitorDBPayoffAlertTableRequest {
  string start_date = 1;
  string end_date = 2;
  uint32 user_id = 3;
  uint32 hall_id = 4;
  uint32 lobby_id = 5;
  uint32 group_id = 6;
  string category = 7;
  BoolValue is_payoff_greater_than = 8;
  string condition_type = 9;
  string sort = 10;
  string order = 11;
  uint32 page = 12;
  uint32 page_limit = 13;
}

message GetMonitorDBPayoffAlertTableResponse {
 repeated FetchMonitorDBPayoffAlertTable list = 1;
 Pagination pagination = 2;
}

message FetchMonitorDBPayoffAlertTable {
  sint32 id = 1;
  sint32 group_id = 2;
  sint64 hall_id = 3;
  sint64 user_id = 4;
  sint32 lobby_id = 5;
  string category = 6;
  string condition_type = 7;
  sint32 win = 8;
  sint32 lost = 9;
  sint32 day = 10;
  sint32 percent = 11;
  sint32 bet_amount_setting = 12;
  sint32 payoff = 13;
  sint32 bet_amount = 14;
  sint32 growth = 15;
  string time = 16;
}

message Pagination {
  uint32 current_page = 1;
  uint32 page_limit = 2;
  uint32 total = 3;
  uint32 total_page = 4;
}

message GetProfitLossByGameTypeRequest {
  sint32 id = 1;
}

message GetProfitLossByGameTypeResponse {
 repeated GetProfitLossByGameType list = 1;
 Total subTotal = 2; 
}

message GetProfitLossByGameType {
  sint32 id = 1;
  string game_id = 2;
  double pay_off = 3;
  double bet_amount = 4;
  int32 growth = 5;
}

message Total {
  uint32 number = 1;
  double pay_off = 2;
  double bet_amount = 3;
}

service Monitor {
  rpc CreateCondition(CreateConditionRequest) returns (EmptyResponse);
  rpc GetCondition(GetConditionRequest) returns (GetConditionResponse);
  rpc DeleteCondition(DeleteConditionRequest) returns (EmptyResponse);
  rpc GetSubCondition(GetSubConditionRequest) returns (GetSubConditionResponse);
  rpc UpdateCondition(UpdateConditionRequest) returns (EmptyResponse);
  rpc SendTelegram(SendTelegramRequest) returns (EmptyResponse);
  rpc SetPayOffLobby(SetPayOffLobbyRequest) returns (EmptyResponse);
  rpc GetMonitorDBPayoffAlertTable(GetMonitorDBPayoffAlertTableRequest) returns (GetMonitorDBPayoffAlertTableResponse);
  rpc GetProfitLossByGameType(GetProfitLossByGameTypeRequest) returns (GetProfitLossByGameTypeResponse);
}
