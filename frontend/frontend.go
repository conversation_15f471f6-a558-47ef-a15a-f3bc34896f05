package main

import (
	"flag"
	"gbh/errorx"
	"gbh/frontend/internal/application"
	"gbh/frontend/internal/config"
	"gbh/frontend/internal/handler"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"

	"log"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/collection"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest/httpx"
)

var configFile = flag.String("f", "etc/frontend.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	app := application.New(c.RestConf)

	cache, err := collection.NewCache(time.Minute, collection.WithName("memory"))

	if err != nil {
		log.Fatal(err)
	}

	ctx := svc.NewServiceContext(c, cache)
	handler.RegisterHandlers(app.Server, ctx)
	httpx.SetErrorHandler(func(err error) (int, any) {
		errx := errorx.ErrorToErrorx(err)
		return http.StatusOK, &types.BaseResponse{
			Code:    errx.Code,
			Message: errx.Message,
		}
	})

	app.Run()
}
