package mock

import (
	"gbh/frontend/internal/livegame"
	"gbh/livegame/livegameclient"

	"github.com/stretchr/testify/mock"
)

type LiveGameCtx struct{ mock.Mock }

func (m *LiveGameCtx) GetPlatformMenu(userId uint32, language string) (*livegameclient.PlatformMenuResponse, error) {
	args := m.Called(userId, language)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.PlatformMenuResponse), nil
}

func (m *LiveGameCtx) GetGameRuleList(userId uint32, language string) (*livegameclient.GameRuleListResponse, error) {
	args := m.Called(userId, language)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.GameRuleListResponse), nil
}

func (m *LiveGameCtx) GetGameCodeList(hallID uint32) (*livegameclient.GameCodeListResponse, error) {
	args := m.Called(hallID)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.GameCodeListResponse), nil
}

func (m *LiveGameCtx) GetBulletinList(in livegame.GetBulletinListRequest) (*livegameclient.BulletinResponse, error) {
	args := m.Called(in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.BulletinResponse), nil
}

func (m *LiveGameCtx) GetWagersBySession(in *livegameclient.WagersByUserRequest) (*livegameclient.WagersByUserResponse, error) {
	args := m.Called(in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.WagersByUserResponse), nil
}

func (m *LiveGameCtx) GetWagersByRoundserial(in *livegameclient.WagersByUserRequest) (*livegameclient.WagersByUserResponse, error) {
	args := m.Called(in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.WagersByUserResponse), nil
}

func (m *LiveGameCtx) GameplayBetLimit(userId uint32, language string) (*livegameclient.GameplayBetLimitResponse, error) {
	args := m.Called(userId, language)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.GameplayBetLimitResponse), nil
}

func (m *LiveGameCtx) GameList(language string, hallId uint32) (*livegameclient.GameListResponse, error) {
	args := m.Called(language, hallId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*livegameclient.GameListResponse), nil
}

func NewLiveGameCtx() *LiveGameCtx {
	return &LiveGameCtx{}
}
