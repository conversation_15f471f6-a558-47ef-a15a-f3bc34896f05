package mock

import (
	"gbh/frontend/internal/game"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"

	"github.com/stretchr/testify/mock"
)

type GameCtx struct{ mock.Mock }

func (m *GameCtx) GameDomain(gameKind uint32, hallId uint32) (*gameclient.GameDomainResponse, error) {
	args := m.Called(gameKind, hallId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GameDomainResponse), nil
}

func (m *GameCtx) CacheGameDomain(gameKind uint32, hallId uint32) ([]string, error) {
	args := m.Called(gameKind, hallId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.([]string), nil
}

func (m *GameCtx) GetLobbySwitchByHallID(hallId uint32, filterLobbyEnable bool, lobbySwitchEnable *bool) ([]*gameclient.LobbySwitch, error) {
	args := m.Called(hallId, filterLobbyEnable, lobbySwitchEnable)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.([]*gameclient.LobbySwitch), nil
}

func (m *GameCtx) LobbyGameLink(in game.LobbyGameLinkRequest) (*gameclient.LobbyLinkResponse, error) {
	args := m.Called(in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.LobbyLinkResponse), nil
}

func (m *GameCtx) GetLobbySwitch(in game.GetLobbySwitchRequest) ([]*gameclient.LobbySwitch, error) {
	args := m.Called(in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.([]*gameclient.LobbySwitch), nil
}

func (m *GameCtx) GetSlotBulletinList(in game.SlotBulletinListRequest) (*gameclient.BulletinResponse, error) {
	args := m.Called(in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.BulletinResponse), nil
}

func (m *GameCtx) AddUserFavorite(in game.SetUserFavoriteRequest) error {
	args := m.Called(in)
	return args.Error(0)
}

func (m *GameCtx) DeleteUserFavorite(in game.SetUserFavoriteRequest) error {
	args := m.Called(in)
	return args.Error(0)
}

func (m *GameCtx) CacheMenuInfo(gameKind uint32) (map[uint32]*types.MenuInfoData, error) {
	args := m.Called(gameKind)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(map[uint32]*types.MenuInfoData), nil
}

func (m *GameCtx) CacheGameSettingInfo(gameKind uint32, hallId uint32) (map[uint32]*gameclient.GameInfo, error) {
	args := m.Called(gameKind, hallId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(map[uint32]*gameclient.GameInfo), nil
}

func (m *GameCtx) CacheMenuName(language string) ([]*gameclient.MenuInfo, error) {
	args := m.Called(language)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.([]*gameclient.MenuInfo), nil
}

func (m *GameCtx) CacheGameList(gameKind uint32, language string) ([]*gameclient.GameList, error) {
	args := m.Called(gameKind, language)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.([]*gameclient.GameList), nil
}

func (m *GameCtx) GameIconKind() (map[uint32]string, error) {
	args := m.Called()
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(map[uint32]string), nil
}

func (m *GameCtx) GetCacheGuestCasinoLink(hallId uint32, gameKind uint32, deviceTag string, language string) []types.CasinoLinkData {
	args := m.Called(hallId, gameKind, deviceTag, language)
	resp := args.Get(0)

	return resp.([]types.CasinoLinkData)
}

func (m *GameCtx) SetCacheGuestCasinoLink(hallId uint32, gameKind uint32, deviceTag string, language string, gameLinkList []types.CasinoLinkData) {
}

func (m *GameCtx) GetUserFavorite(userId uint64, gameKind uint32) ([]*gameclient.UserFavorite, error) {
	args := m.Called(userId, gameKind)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.([]*gameclient.UserFavorite), nil
}

func (m *GameCtx) CacheGameKindList() ([]*gameclient.GameKindList, error) {
	args := m.Called()
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.([]*gameclient.GameKindList), nil
}

func NewGameCtx() *GameCtx {
	return &GameCtx{}
}
