package types

type BaseResponse struct {
	Code    int32       `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type MaintainInfo struct {
	GameKind  uint32 `json:"game_kind"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Message   string `json:"message"`
}

type MaintainList struct {
	MaintainInfo []MaintainInfo `json:"maintain_info"`
}

type PaginateResponse struct {
	Page        uint32 `json:"page"`
	PageLimit   uint32 `json:"page_limit"`
	TotalNumber uint32 `json:"total_number"`
	TotalPage   uint32 `json:"total_page"`
}

type Total struct {
	Amount string `json:"amount"`
}

type WagersTotal struct {
	TotalBetAmount string `json:"total_bet_amount,omitempty"`
	TotalPayoff    string `json:"total_payoff,omitempty"`
}

type WagersPagination struct {
	CurrentPage uint32 `json:"current_page"`
	PageLimit   uint32 `json:"page_limit"`
	Total       uint32 `json:"total"`
	TotalPage   uint32 `json:"total_page"`
}

type WagersList struct {
	ID              uint64  `json:"id"`
	WagersDate      string  `json:"wagers_date"`
	GameType        uint32  `json:"game_type"`
	ResultStatus    *string `json:"result_status,omitempty"`
	BetAmount       string  `json:"bet_amount"`
	Payoff          string  `json:"payoff"`
	WagersDetailURL string  `json:"wagers_detail_url"`
}
