package types

type LotteryFinishWagersDetailRequest struct {
	HallID    uint32 `json:"hall_id" form:"hall_id" binding:"required"`
	SessionID string `json:"session_id" form:"session_id" binding:"required"`
	RoundDate string `json:"date" form:"date" binding:"required" validate:"datetime=2006-01-02"`
	GameID    string `json:"game_id" form:"game_id" binding:"required"`
	WinTypeID string `json:"win_type_id" form:"win_type_id,optional"`
	Lang      string `json:"lang" form:"lang,optional"`
}
