package types

type LiveWagersByDate struct {
	HallID    uint32 `json:"hall_id" form:"hall_id" binding:"required"`
	SessionID string `json:"session_id" form:"session_id" binding:"required"`
	StartDate string `json:"start_date" form:"start_date" binding:"required" validate:"datetime=2006-01-02"`
	EndDate   string `json:"end_date" form:"end_date" binding:"required" validate:"datetime=2006-01-02"`
	Scheme    string `json:"scheme" form:"scheme" binding:"required" validate:"oneof=http https"`
	Lang      string `json:"lang" form:"lang,optional"`
	Page      uint32 `json:"page" form:"page,optional"`
	PageLimit uint32 `json:"page_limit" form:"page_limit,optional"`
}

type LiveWagersByRoundSerial struct {
	HallID      uint32 `json:"hall_id" form:"hall_id" binding:"required"`
	SessionID   string `json:"session_id" form:"session_id" binding:"required"`
	RoundSerial uint32 `json:"roundserial" form:"roundserial" binding:"required"`
	Scheme      string `json:"scheme" form:"scheme" binding:"required" validate:"oneof=http https"`
	Lang        string `json:"lang" form:"lang,optional"`
	Page        uint32 `json:"page" form:"page,optional"`
	PageLimit   uint32 `json:"page_limit" form:"page_limit,optional"`
}

type LiveGameList struct {
	HallID uint32 `json:"hall_id" form:"hall_id" binding:"required"`
	Lang   string `json:"lang" form:"lang" binding:"required"`
}
