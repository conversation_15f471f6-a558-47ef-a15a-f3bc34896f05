package validator

import (
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/validator/ip"
	"gbh/frontend/internal/validator/maintain"
	"gbh/frontend/internal/validator/parameter"

	"github.com/gin-gonic/gin"
)

type Context interface {
	ValidAll(hallID uint32, clientIP string, req any) error
}

type validator struct {
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

func New(c *gin.Context, svcCtx *svc.ServiceContext) Context {
	return &validator{
		ctx:    c,
		svcCtx: svcCtx,
	}
}

func (c *validator) ValidAll(hallID uint32, clientIP string, req any) error {
	maintainCtx := maintain.New(c.svcCtx)
	maintainState, maintainErr := maintainCtx.CheckSysMaintain(hallID, clientIP)

	if maintainErr != nil {
		return maintainErr
	}

	if maintainState.GetStatus() {
		return errorx.SystemMaintain
	}

	// 判斷是否cookie含有IBCACHE (呼叫acc判斷ip是否通過的暫存)
	ipStatus := common.CheckIPCookies(c.ctx, clientIP)
	if ipStatus != "" {
		if ipStatus == "N" {
			return errorx.IPIsNotAccepted
		}
	} else {
		ipValid := ip.New(c.ctx, c.svcCtx)
		ipErr := ipValid.CheckIP(clientIP, hallID)

		if ipErr != nil {
			return ipErr
		}
	}

	paramErr := parameter.Valid(req, c.ctx)

	if paramErr != nil {
		return paramErr
	}

	return nil
}
