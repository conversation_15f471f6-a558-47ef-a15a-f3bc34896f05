package ip

import (
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/ip"
	"gbh/frontend/internal/svc"

	"github.com/gin-gonic/gin"
)

type Context interface {
	CheckIP(clientIP string, hallID uint32) error
}

type ipContext struct {
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

func New(c *gin.Context, svcCtx *svc.ServiceContext) Context {
	return &ipContext{
		ctx:    c,
		svcCtx: svcCtx,
	}
}

func (c *ipContext) CheckIP(clientIP string, hallID uint32) error {
	checkIPInfo, err := c.svcCtx.IPCtx.CheckIP(ip.CheckIPRequest{
		IP:       clientIP,
		HallID:   hallID,
		Entrance: constants.MemberEntrance,
	})

	if err != nil {
		return err
	}

	if !checkIPInfo.GetRet() {
		common.SetIPCookies(c.ctx, clientIP, "N")
		return errorx.IPIsNotAccepted
	}

	common.SetIPCookies(c.ctx, clientIP, "Y")

	return nil
}
