package parameter

import (
	"errors"
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/utils/strutil"
	"regexp"
	"slices"
	"strings"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

func Valid(req any, _ *gin.Context) error {
	validate := validator.New()

	effectiveBettingDateErr := validate.RegisterValidation("effectiveBettingDate", validEffectiveBettingDate)
	if effectiveBettingDateErr != nil {
		return errorx.ParameterValidError
	}

	regexpErr := validate.RegisterValidation("regexp", Regexp)

	if regexpErr != nil {
		return errorx.ParameterValidError
	}

	checkLiveEnterPageErr := validate.RegisterValidation("checkLiveEnterPage", validCheckLiveEnterPage)

	if checkLiveEnterPageErr != nil {
		return errorx.ParameterValidError
	}

	checkSessionIDOrHallIDNotEmptyErr := validate.RegisterValidation("checkSessionIDOrHallIDNotEmpty", validCheckSessionIDOrHallIDNotEmpty)

	if checkSessionIDOrHallIDNotEmptyErr != nil {
		return errorx.ParameterValidError
	}

	checkDatelimit3Err := validate.RegisterValidation("checkLimitDate3", validCheckLimitDate3)
	if checkDatelimit3Err != nil {
		return errorx.ParameterValidError
	}

	checkDatelimit7Err := validate.RegisterValidation("checkLimitDate7", validCheckLimitDate7)
	if checkDatelimit7Err != nil {
		return errorx.ParameterValidError
	}

	checkLotteryGameLobbyMenuLinkFilterErr := validate.RegisterValidation("checkLotteryGameLobbyMenuLinkFilter", validCheckLotteryGameLobbyMenuLinkFilter)
	if checkLotteryGameLobbyMenuLinkFilterErr != nil {
		return errorx.ParameterValidError
	}

	validErr := validate.Struct(req)

	var validationErrors validator.ValidationErrors

	if errors.As(validErr, &validationErrors) {
		return getCustomError(validationErrors[0])
	}

	return nil
}

func getCustomError(fieldErr validator.FieldError) error {
	switch normalizeField(fieldErr.Field()) {
	case "StartDate", "EndDate":
		return validDateTimeField(fieldErr)
	case "RoundDate":
		return validDateTimeField(fieldErr)
	case "Date":
		return validDateTimeField(fieldErr)
	case "Category":
		return errorx.CategoryError
	default:
		return errorx.ParameterValidError
	}
}

func normalizeField(field string) string {
	re := regexp.MustCompile(`\[\d+\]`)

	return re.ReplaceAllString(field, "")
}

func validDateTimeField(fieldErr validator.FieldError) error {
	switch fieldErr.Field() {
	case "StartDate":
		return errorx.StartDateError
	case "EndDate":
		return errorx.EndDateError
	}
	return errorx.DateError
}

func validEffectiveBettingDate(fl validator.FieldLevel) bool {
	startDate := carbon.Parse(fl.Top().FieldByName("StartDate").String(), constants.TimezoneGMT4)
	endDate := carbon.Parse(fl.Top().FieldByName("EndDate").String(), constants.TimezoneGMT4)

	if startDate.Gt(endDate) {
		return false
	}

	// 時間不能超過 31 天
	if startDate.DiffAbsInSeconds(endDate) > constants.ThirtyOneDaysInSeconds {
		return false
	}

	return true
}

func Regexp(fl validator.FieldLevel) bool {
	regex := regexp.MustCompile(fl.Param())

	var value = strutil.Uint64ToString(fl.Field().Uint())

	return regex.MatchString(value)
}

func validCheckLiveEnterPage(fl validator.FieldLevel) bool {
	isMobile := fl.Top().FieldByName("IsMobile").Bool()

	if isMobile {
		return true
	}

	enterPage := fl.Top().FieldByName("EnterPage").String()

	return slices.Contains([]string{"livedealer", "ultimate", "multibet", "blockchain"}, enterPage)
}

func validCheckLimitDate7(fl validator.FieldLevel) bool {
	dateTime := carbon.Parse(fl.Top().FieldByName("Date").String(), constants.TimezoneGMT4)
	endTime := carbon.Now(constants.TimezoneGMT4).EndOfDay()

	const lastSixDay = 6
	startTime := carbon.Now(constants.TimezoneGMT4).SubDays(lastSixDay).StartOfDay()

	return (dateTime.Gte(startTime) && endTime.Gte(dateTime))
}

func validCheckSessionIDOrHallIDNotEmpty(fl validator.FieldLevel) bool {
	hallID := fl.Field().Uint()
	sessionID := fl.Top().FieldByName("SessionID").String()

	if hallID == 0 && sessionID == "" {
		return false
	}

	return true
}

func validCheckLimitDate3(fl validator.FieldLevel) bool {
	dateTime := carbon.Parse(fl.Top().FieldByName("Date").String(), constants.TimezoneGMT4)
	endTime := carbon.Now(constants.TimezoneGMT4).EndOfDay()

	const lastTwoDay = 2
	startTime := carbon.Now(constants.TimezoneGMT4).SubDays(lastTwoDay).StartOfDay()

	return (dateTime.Gte(startTime) && endTime.Gte(dateTime))
}

func validCheckLotteryGameLobbyMenuLinkFilter(fl validator.FieldLevel) bool {
	filterString := fl.Top().FieldByName("Filter").String()
	if filterString == "" {
		return true
	}

	filter := strings.Split(filterString, ",")

	// 驗證參數
	filterArray := []string{
		"ad",
		"like_guess",
		"leaderboard",
		"tradition",
		"game_panel",
	}

	for _, item := range filter {
		if !slices.Contains(filterArray, item) {
			return false
		}
	}

	return true
}
