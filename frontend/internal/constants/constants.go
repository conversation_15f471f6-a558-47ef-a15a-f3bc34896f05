package constants

const TimezoneGMT4 = "Etc/GMT+4"
const MemberEntrance = 3

const LivePortalPath = "ipl/portal.php/game/httpredirect"
const SlotPortalPath = "ipl/portal.php/game/httpredirect"

const (
	HallRole               uint32 = 7
	SupremeShareholderRole uint32 = 5
	ShareholderRole        uint32 = 4
	SuperAgentRole         uint32 = 3
	AgentRole              uint32 = 2
	MemberRole             uint32 = 1
)

const (
	BBSport    uint32 = 1
	BBLive     uint32 = 3
	BBSlot     uint32 = 5
	BBLottery  uint32 = 12
	NewBBSport uint32 = 31
	BBFish     uint32 = 38
	BBBattle   uint32 = 66
	BBLiveTip  uint32 = 99
)

const (
	GameCasino     = "casino"
	GameLive       = "live"
	GameBattle     = "card"
	GameNewBBSport = "nbbsport"
	GameLottery    = "lottery"
	GameFish       = "fisharea"

	EntranceMoblieMainPage = "/m/main"
)

const (
	ThirtyOneDaysInSeconds = 2678400
)

const (
	Cn    = "cn"
	Tw    = "tw"
	ZhTw  = "zh-tw"
	ZhCn  = "zh-cn"
	EucJp = "euc-jp"
	Ko    = "ko"
	Vi    = "vi"
	Th    = "th"
	EnUs  = "en-us"
	Us    = "us"
	Vn    = "vn"
	Jp    = "jp"
	Ja    = "ja"
	Gb    = "gb"
	En    = "en"
	Es    = "es"
	Lo    = "lo"
	Km    = "km"
	Ug    = "ug"
	Id    = "id"
	In    = "in"
	Big5  = "big5"
	Bg    = "bg"
)

const (
	IPCookieMinLength    = 2
	RandomKeyLength      = 8
	BaseEncodedMinLength = 2
)

const (
	ExitOptionWithURL uint32 = 2
)

const (
	HTTPSURLScheme  = "https://"
	HTTPURLScheme   = "http://"
	SchemeSeparator = "://"
)

// 遊戲分類
const (
	NewGameMenuID    uint32 = 1  // 最新遊戲
	HotGameMenuID    uint32 = 2  // 熱門遊戲
	CasinoGameMenuID uint32 = 4  // 電子
	TableGameMenuID  uint32 = 5  // 桌上遊戲
	AllGameMenuID    uint32 = 84 // 全部
	FishGameMenuID   uint32 = 91 // 捕魚機
	DemoGameMenuID   uint32 = 94 // 試玩遊戲
	BattleGameMenuID uint32 = 96 // 棋牌遊戲

	NewGameMaxLimit = 20 // 最新遊戲只顯示20筆
	HotGameMaxLimit = 40 // 熱門遊戲只顯示40筆

	SevenDays    = 7
	FourteenDays = 14

	DevicePc     = "pc"
	DeviceMobile = "mobile"
)

const (
	GeneralMessageID uint32 = 2
	SystemMessageID  uint32 = 3
)

const (
	DefaultPageNum = 1

	PageLimit     = 30
	PageLimitBy20 = 20
)

const (
	OneHundred = 100
)

const (
	Payoff = "PAYOFF"
	Cash   = "CASH"
	Out    = "OUT"
)

const (
	LangCodeMaintainTime      = "S_MAINTAIN_TIME"
	LangCodeBEIJINGTime       = "S_TIME_OF_BEIJING_PRC"
	LangCodeUSEastTime        = "S_TIME_OF_EAST_US"
	LangCodeNBBSport          = "S_NBB"
	LangCodeNoPrivilege       = "S_NO_PRIVILEGE"
	LangCodeBBBattle          = "S_BBCard"
	LangCodeBBCasino          = "S_BBCASINO"
	LangCodeBBLive            = "S_BB_LIVE"
	LangCodeBBLottery         = "S_BBLOTTERY"
	LangCodeBBFish            = "S_BBFM"
	LangCustomerServiceOnline = "S_CUSTOMER_SERVICE_ONLINE"
	LangWebUpUp               = "S_WEB_UPUP"
	LangUpUpNotice            = "S_UPUP_NOTICE"
	LangUpUpContent           = "S_UPUP_CONTENT"
	LangGameNotOpen           = "S_GAME_NOT_OPEN"
)

const (
	GameID38001 uint32 = 38001
	GameID38002 uint32 = 38002
	GameID38003 uint32 = 38003
	GameID66033 uint32 = 66033
	GameID66034 uint32 = 66034
	GameID66035 uint32 = 66035
	GameID66036 uint32 = 66036
	GameID66037 uint32 = 66037
	GameID66039 uint32 = 66039
	GameID66040 uint32 = 66040
	GameID66041 uint32 = 66041
	GameID66042 uint32 = 66042
	GameID66064 uint32 = 66064
	GameID66068 uint32 = 66068
	GameID66069 uint32 = 66069
	GameID66087 uint32 = 66087
	GameID66094 uint32 = 66094
)

const (
	APIUpUp          = "/api/entrance/upup"
	APIGameRoutePage = "/api/entrance/gameroutepage"
)

const (
	ResultStatus200 = 200
	ResultStatus1   = 1
)

const (
	ResultStatusLose = "LOSE"
	ResultStatusWin  = "WIN"
)
