package livegame

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/seeder"
	"gbh/livegame/livegameclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()
	liveGameCtx := New(ctx, liveGameRPC)

	expectedResponse := &liveGameContext{
		ctx: ctx,

		LiveGameRPC: liveGameRPC,
	}

	assert.Equal(t, expectedResponse, liveGameCtx)
}

func Test_liveGameContext_GetPlatformMenu_Get(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.PlatformMenuRequest{
		HallId: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	}

	liveGameRPC.On("PlatformMenu", ctx, &mockRequest).Return(&seeder.PlatformMenu, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GetPlatformMenu(seeder.BgpHallID, constants.ZhTw)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.PlatformMenu, resp)
}

func Test_liveGameContext_GetPlatformMenu_Error(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.PlatformMenuRequest{
		HallId: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	liveGameRPC.On("PlatformMenu", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GetPlatformMenu(seeder.BgpHallID, constants.ZhTw)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_liveGameContext_GetGameRuleList_Get(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.GameRuleListRequest{
		HallId: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	}

	liveGameRPC.On("GameRuleList", ctx, &mockRequest).Return(&seeder.GameRuleList, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GetGameRuleList(seeder.BgpHallID, constants.ZhTw)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GameRuleList, resp)
}

func Test_liveGameContext_GetGameRuleList_Error(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.GameRuleListRequest{
		HallId: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	liveGameRPC.On("GameRuleList", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GetGameRuleList(seeder.BgpHallID, constants.ZhTw)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_liveGameContext_GetGameCodeList_Get(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.GameCodeListRequest{
		HallId: seeder.BgpHallID,
	}

	liveGameRPC.On("GameCodeList", ctx, &mockRequest).Return(&seeder.GameCodeListResp, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GetGameCodeList(seeder.BgpHallID)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GameCodeListResp, resp)
}

func Test_liveGameContext_GetGameCodeList_Error(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.GameCodeListRequest{
		HallId: seeder.BgpHallID,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	liveGameRPC.On("GameCodeList", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GetGameCodeList(seeder.BgpHallID)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_liveGameContext_GetBulletinList_Get(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.BulletinRequest{
		HallId: seeder.BgpHallID,
		Lang:   constants.ZhTw,
		Date:   "2024-09-10",
		GameId: &livegameclient.Uint32Value{
			Value: 3001,
		},
		TableCode: &livegameclient.Uint32Value{
			Value: 1,
		},
	}

	liveGameRPC.On("BulletinList", ctx, &mockRequest).Return(&seeder.GameBulletinList, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	request := GetBulletinListRequest{
		HallID:     mockRequest.GetHallId(),
		SelectDate: mockRequest.GetDate(),
		Lang:       mockRequest.GetLang(),
		GameID:     mockRequest.GetGameId().GetValue(),
		TableCode:  mockRequest.GetTableCode().GetValue(),
	}
	resp, err := liveGameCtx.GetBulletinList(request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GameBulletinList, resp)
}

func Test_liveGameContext_GetBulletinList_Error(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := livegameclient.BulletinRequest{
		HallId: seeder.BgpHallID,
		Lang:   constants.ZhTw,
		GameId: &livegameclient.Uint32Value{
			Value: seeder.BaccaratGameId,
		},
		TableCode: &livegameclient.Uint32Value{
			Value: 1,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	liveGameRPC.On("BulletinList", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	request := GetBulletinListRequest{
		HallID:     mockRequest.GetHallId(),
		SelectDate: mockRequest.GetDate(),
		Lang:       mockRequest.GetLang(),
		GameID:     mockRequest.GetGameId().GetValue(),
		TableCode:  mockRequest.GetTableCode().GetValue(),
	}

	resp, err := liveGameCtx.GetBulletinList(request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_liveGameContext_GetWagersBySession_Get(t *testing.T) {
	mockRequest := livegameclient.WagersByUserRequest{
		Session:   seeder.Session,
		StartDate: seeder.RoundDate,
		EndDate:   seeder.RoundDate,
		Lang:      constants.ZhCn,
	}

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("WagersBySession", ctx, &mockRequest).Return(&seeder.WagersBySession, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	request := livegameclient.WagersByUserRequest{
		Session:   mockRequest.GetSession(),
		StartDate: mockRequest.GetStartDate(),
		EndDate:   mockRequest.GetEndDate(),
		Lang:      mockRequest.GetLang(),
	}
	resp, err := liveGameCtx.GetWagersBySession(&request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.WagersBySession, resp)
}

func Test_liveGameContext_GetWagersBySession_Error(t *testing.T) {
	mockRequest := livegameclient.WagersByUserRequest{
		Session:   seeder.Session,
		StartDate: seeder.RoundDate,
		EndDate:   seeder.RoundDate,
		Lang:      constants.ZhCn,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("WagersBySession", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	request := livegameclient.WagersByUserRequest{
		Session:   mockRequest.GetSession(),
		StartDate: mockRequest.GetStartDate(),
		EndDate:   mockRequest.GetEndDate(),
		Lang:      mockRequest.GetLang(),
	}
	resp, err := liveGameCtx.GetWagersBySession(&request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_liveGameContext_GetWagersByRoundserial_Get(t *testing.T) {
	mockRequest := livegameclient.WagersByUserRequest{
		Session:     seeder.Session,
		Roundserial: seeder.LiveRoundSerial,
		Lang:        constants.ZhCn,
	}

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("WagersByRoundserial", ctx, &mockRequest).Return(&seeder.WagersByRoundserial, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	request := livegameclient.WagersByUserRequest{
		Session:     mockRequest.GetSession(),
		Roundserial: mockRequest.GetRoundserial(),
		Lang:        mockRequest.GetLang(),
	}
	resp, err := liveGameCtx.GetWagersByRoundserial(&request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.WagersByRoundserial, resp)
}

func Test_liveGameContext_GetWagersByRoundserial_Error(t *testing.T) {
	mockRequest := livegameclient.WagersByUserRequest{
		Session:     seeder.Session,
		Roundserial: seeder.LiveRoundSerial,
		Lang:        constants.ZhCn,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("WagersByRoundserial", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	request := livegameclient.WagersByUserRequest{
		Session:     mockRequest.GetSession(),
		Roundserial: seeder.LiveRoundSerial,
		Lang:        mockRequest.GetLang(),
	}
	resp, err := liveGameCtx.GetWagersByRoundserial(&request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_liveGameContext_GameplayBetLimit_Get(t *testing.T) {
	mockRequest := livegameclient.GameplayBetLimitRequest{
		UserId: seeder.UserGTPTestID,
		Lang:   constants.ZhCn,
	}

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("GameplayBetLimit", ctx, &mockRequest).Return(&seeder.LiveGameplayBetLimit, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GameplayBetLimit(mockRequest.GetUserId(), mockRequest.GetLang())

	assert.NoError(t, err)
	assert.Equal(t, &seeder.LiveGameplayBetLimit, resp)
}

func Test_liveGameContext_GameplayBetLimit_Error(t *testing.T) {
	mockRequest := livegameclient.GameplayBetLimitRequest{
		UserId: seeder.UserGTPTestID,
		Lang:   constants.ZhCn,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("GameplayBetLimit", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GameplayBetLimit(mockRequest.GetUserId(), mockRequest.GetLang())

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_liveGameContext_GameList_Get(t *testing.T) {
	mockRequest := livegameclient.GameListRequest{
		Lang: constants.ZhCn,
		HallId: &livegameclient.Uint32Value{
			Value: seeder.BgpHallID,
		},
	}

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("GameList", ctx, &mockRequest).Return(&seeder.LiveGameList, nil)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GameList(mockRequest.GetLang(), mockRequest.GetHallId().GetValue())

	assert.NoError(t, err)
	assert.Equal(t, &seeder.LiveGameList, resp)
}

func Test_liveGameContext_GameList_Error(t *testing.T) {
	mockRequest := livegameclient.GameListRequest{
		Lang: constants.ZhCn,
		HallId: &livegameclient.Uint32Value{
			Value: seeder.BgpHallID,
		},
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	liveGameRPC := newMockLiveGameRPC()
	liveGameRPC.On("GameList", ctx, &mockRequest).Return(nil, mockError)
	liveGameCtx := New(ctx, liveGameRPC)

	resp, err := liveGameCtx.GameList(mockRequest.GetLang(), mockRequest.GetHallId().GetValue())

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}
