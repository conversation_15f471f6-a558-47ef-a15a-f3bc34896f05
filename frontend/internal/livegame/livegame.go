package livegame

import (
	"context"
	"gbh/errorx"
	"gbh/livegame/livegameclient"
)

type Context interface {
	GetPlatformMenu(hallID uint32, lang string) (*livegameclient.PlatformMenuResponse, error)
	GetGameRuleList(hallID uint32, lang string) (*livegameclient.GameRuleListResponse, error)
	GetGameCodeList(hallID uint32) (*livegameclient.GameCodeListResponse, error)
	GetBulletinList(req GetBulletinListRequest) (*livegameclient.BulletinResponse, error)
	GetWagersBySession(req *livegameclient.WagersByUserRequest) (*livegameclient.WagersByUserResponse, error)
	GetWagersByRoundserial(req *livegameclient.WagersByUserRequest) (*livegameclient.WagersByUserResponse, error)
	GameplayBetLimit(userId uint32, language string) (*livegameclient.GameplayBetLimitResponse, error)
	GameList(language string, hallId uint32) (*livegameclient.GameListResponse, error)
}

type GetBulletinListRequest struct {
	HallID     uint32
	SelectDate string
	Lang       string
	GameID     uint32
	TableCode  uint32
}

type liveGameContext struct {
	ctx         context.Context
	LiveGameRPC livegameclient.LiveGame
}

func New(ctx context.Context, liveGameRPC livegameclient.LiveGame) Context {
	return &liveGameContext{
		ctx:         ctx,
		LiveGameRPC: liveGameRPC,
	}
}

func (c *liveGameContext) GetPlatformMenu(hallID uint32, lang string) (*livegameclient.PlatformMenuResponse, error) {
	resp, err := c.LiveGameRPC.PlatformMenu(c.ctx, &livegameclient.PlatformMenuRequest{
		HallId: hallID,
		Lang:   lang,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) GetGameRuleList(hallID uint32, lang string) (*livegameclient.GameRuleListResponse, error) {
	resp, err := c.LiveGameRPC.GameRuleList(c.ctx, &livegameclient.GameRuleListRequest{
		HallId: hallID,
		Lang:   lang,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) GetGameCodeList(hallID uint32) (*livegameclient.GameCodeListResponse, error) {
	req := livegameclient.GameCodeListRequest{
		HallId: hallID,
	}

	resp, err := c.LiveGameRPC.GameCodeList(c.ctx, &req)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) GetBulletinList(req GetBulletinListRequest) (*livegameclient.BulletinResponse, error) {
	bulletinReq := livegameclient.BulletinRequest{
		HallId: req.HallID,
		Lang:   req.Lang,
		Date:   req.SelectDate,
	}

	if req.GameID != 0 {
		bulletinReq.GameId = &livegameclient.Uint32Value{Value: req.GameID}
	}

	if req.TableCode != 0 {
		bulletinReq.TableCode = &livegameclient.Uint32Value{Value: req.TableCode}
	}

	resp, err := c.LiveGameRPC.BulletinList(c.ctx, &bulletinReq)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) GetWagersBySession(req *livegameclient.WagersByUserRequest) (*livegameclient.WagersByUserResponse, error) {
	request := livegameclient.WagersByUserRequest{
		Session:     req.GetSession(),
		StartDate:   req.GetStartDate(),
		EndDate:     req.GetEndDate(),
		Lang:        req.GetLang(),
		CurrentPage: req.GetCurrentPage(),
		PageLimit:   req.GetPageLimit(),
	}

	resp, err := c.LiveGameRPC.WagersBySession(c.ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) GetWagersByRoundserial(req *livegameclient.WagersByUserRequest) (*livegameclient.WagersByUserResponse, error) {
	request := livegameclient.WagersByUserRequest{
		Session:     req.GetSession(),
		Roundserial: req.GetRoundserial(),
		Lang:        req.GetLang(),
		CurrentPage: req.GetCurrentPage(),
		PageLimit:   req.GetPageLimit(),
	}

	resp, err := c.LiveGameRPC.WagersByRoundserial(c.ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) GameplayBetLimit(userId uint32, language string) (*livegameclient.GameplayBetLimitResponse, error) {
	resp, err := c.LiveGameRPC.GameplayBetLimit(c.ctx, &livegameclient.GameplayBetLimitRequest{
		UserId: userId,
		Lang:   language,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) GameList(language string, hallId uint32) (*livegameclient.GameListResponse, error) {
	request := &livegameclient.GameListRequest{
		Lang: language,
	}

	if hallId > 0 {
		request.HallId = &livegameclient.Uint32Value{
			Value: hallId,
		}
	}

	resp, err := c.LiveGameRPC.GameList(c.ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
