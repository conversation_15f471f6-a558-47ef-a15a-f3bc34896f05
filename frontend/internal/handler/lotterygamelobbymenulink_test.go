package handler

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/ip"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestLotteryGameLobbyMenuLinkHandler(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLotteryGameLobbyMenuLink, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	query.AddString("lang", constants.ZhTw)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.LotteryGameLobbyMenuLink
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewSysMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBLottery).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBLottery, seeder.User.GetDomain()).Return(&seeder.GameDomain, nil)

	lotteryGameCtx := mock.NewLotteryGameCtx()
	lotteryGameCtx.On("LobbyLogin", seeder.Session, constants.ZhTw, c.ClientIP(), "", "").Return(&seeder.LobbyLogin, nil)

	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	ipCtx := mock.NewIPCtx()

	mockRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockRequest).Return(&seeder.CheckIPIsAvailable, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LotteryGameCtx = lotteryGameCtx
	svcCtx.IPCtx = ipCtx

	testFunc := LotteryGameLobbyMenuLinkHandler(svcCtx)
	testFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.Contains(t, w.Body.String(), `"code":0,"message":""`)
	assert.Contains(t, w.Body.String(), `"game_panel":{"name":"Mark Six","open_timestamp":1728273600,"close_timestamp":1728273600,"link":"/balv/bv/pilot?lang=en-us\u0026referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FHKLT","prev_num":"108","prev_result":{"pre_info":[{"key":"01","value":32},{"key":"02","value":47},{"key":"03","value":21},{"key":"04","value":8},{"key":"05","value":43},{"key":"06","value":15},{"key":"07","value":41}]},"result_group":"LT"},`)
	assert.Contains(t, w.Body.String(), `"like_guess":[{"platform_name":"BB LOTTERY","platform_key":"bb_lottery","name":"BB Happy 10","link":"/balv/bv/pilot?lang=en-us\u0026referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FB1SF"}]`)
	assert.Contains(t, w.Body.String(), `"official":[{"group_name":"Lotto","group_key":"NORMAL","group_link":"/balv/bv/pilot?lang=en-us\u0026referer_url=%2Fpantheon%2Favengers%2Favengers%2Ffashion%3Ffcdn%3DNORMAL","link":"/balv/bv/pilot?lang=en-us\u0026referer_url=%2Fpantheon%2Favengers%2Favengers%2Fofficial%2Fgame%2FBQ3D%3Ftypes%3DBBQL%2CBBLT%2CHKLT%2CBJ3D%2CPL3D%2CB128","games":[{"name":"BB Quick 3D","game_id":"BQ3D","link":"/balv/bv/pilot?lang=en-us\u0026referer_url=%2Fpantheon%2Favengers%2Favengers%2Fofficial%2Fgame%2FBQ3D"}]}]`)
	assert.Contains(t, w.Body.String(), `"official_on":"1"`)
	assert.Contains(t, w.Body.String(), `"tradition":[{"num":"202410061267","game_id":"B1SF","name":"BB Happy 10","link":"/balv/bv/pilot?lang=en-us\u0026referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FB1SF","open_timestamp":1728263101,"close_timestamp":1728263160,"group":"DEFAULT","tag":"new"}]`)
	assert.Contains(t, w.Body.String(), `"tradition_entrance":"/balv/bv/pilot?lang=en-us\u0026referer_url=%2Fpantheon%2Favengers%2Favengers%2Ffashion"`)
	assert.Contains(t, w.Body.String(), `"server_timestamp"`)
	assert.Contains(t, w.Body.String(), `"timezone":"-04"`)
	assert.Contains(t, w.Body.String(), `"lt_cdn":"","domain":["localhost"]`)
}

func TestLotteryGameLobbyMenuLinkHandler_WithoutHallID(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req, err := http.NewRequest(http.MethodGet, apiLotteryGameLobbyMenuLink, http.NoBody)
	assert.NoError(t, err)

	query := urlutil.NewBuilder()
	query.AddString("session_id", seeder.Session)
	query.AddString("lang", constants.ZhTw)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	testFunc := LotteryGameLobbyMenuLinkHandler(svcCtx)
	testFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":561000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestLotteryGameLobbyMenuLinkHandler_WithoutParameterLang(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLotteryGameLobbyMenuLink, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	testFunc := LotteryGameLobbyMenuLinkHandler(svcCtx)
	testFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":561000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestLotteryGameLobbyMenuLinkHandler_IPValidError(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req, err := http.NewRequest(http.MethodGet, apiLotteryGameLobbyMenuLink, http.NoBody)
	assert.NoError(t, err)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	query.AddString("lang", constants.ZhTw)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	assert.NoError(t, err)

	maintainCtx := mock.NewSysMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	ipCtx := mock.NewIPCtx()

	mockRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockRequest).Return(&seeder.CheckIPIsUnavailable, nil)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.IPCtx = ipCtx

	testFunc := LotteryGameLobbyMenuLinkHandler(svcCtx)
	testFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":561000009,"message":"IP is not accepted"}`, w.Body.String())
}

func TestLotteryGameLobbyMenuLinkHandler_SessionRPCError(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLotteryGameLobbyMenuLink, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	query.AddString("lang", constants.ZhTw)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.LotteryGameLobbyMenuLink
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	maintainCtx := mock.NewSysMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	ipCtx := mock.NewIPCtx()

	mockRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockRequest).Return(&seeder.CheckIPIsAvailable, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.IPCtx = ipCtx

	testFunc := LotteryGameLobbyMenuLinkHandler(svcCtx)
	testFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
