package handler

import (
	"gbh/frontend/internal/svc"
	"log"
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func RegisterHandlers(server *http.Server, serverCtx *svc.ServiceContext) {
	router := gin.New()
	err := router.SetTrustedProxies([]string{
		"10.0.0.0/8",
		"172.16.0.0/12",
		"192.168.0.0/16",
	})
	if err != nil {
		log.Fatalln(err)
	}

	router.Use(gin.LoggerWithWriter(gin.DefaultWriter, "/healthz"))
	router.Use(gin.Recovery())
	router.Use(cors.Default())

	router.GET("/healthz", HealthCheckHandler)

	apiRouter := router.Group("/api")
	apiRouter.GET("/account/effective_betting", EffectiveBettingHandler(serverCtx))
	apiRouter.GET("/account/info", AccountInfoHandler(serverCtx))
	apiRouter.GET("/account/favorite_game", AccountGetFavoriteGameHandler(serverCtx))
	apiRouter.POST("/account/favorite_game", AccountAddFavoriteGameHandler(serverCtx))
	apiRouter.DELETE("/account/favorite_game", AccountDeleteFavoriteGameHandler(serverCtx))
	apiRouter.GET("/account/lobby_switch", AccountLobbySwitchHandler(serverCtx))
	apiRouter.GET("/account/transaction_record", TransactionRecordHandler(serverCtx))
	apiRouter.GET("/account/live_game_bet_limit", LiveGameBetLimitHandler(serverCtx))
	apiRouter.GET("/account/lottery_game_bet_limit", LotteryGameBetLimitHandler(serverCtx))
	apiRouter.GET("/account/sport_game_bet_limit", SportGameBetLimitHandler(serverCtx))
	apiRouter.DELETE("/account/logout", AccountLogoutHandler(serverCtx))

	apiRouter.GET("/live/wagers_by_date", LiveWagersByDateHandler(serverCtx))
	apiRouter.GET("/live/wagers_by_round_serial", LiveWagersByRoundSerialHandler(serverCtx))
	apiRouter.GET("/live/main_setting", LiveMainSettingHandler(serverCtx))
	apiRouter.GET("/live/redirect_rule_url", LiveRuleURLHandler(serverCtx))
	apiRouter.GET("/live/game_code_list", LiveGameCodeListHandler(serverCtx))
	apiRouter.GET("/live/bulletin_list", LiveBulletinListHandler(serverCtx))
	apiRouter.GET("/live/lobby_link", LiveLobbyLinkHandler(serverCtx))
	apiRouter.GET("/live/game_list", LiveGameListHandler(serverCtx))

	apiRouter.GET("/sport/lobby_link", SportLobbyLinkHandler(serverCtx))
	apiRouter.GET("/sport/wagers_detail", SportWagersDetailHandler(serverCtx))
	apiRouter.GET("/sport/unfinish_statis", SportUnfinishStatisHandler(serverCtx))
	apiRouter.GET("/sport/unfinish_statis_by_game", SportUnfinishStatisByGameHandler(serverCtx))
	apiRouter.GET("/sport/finish_statis", SportFinishStatisHandler(serverCtx))
	apiRouter.GET("/sport/finish_statis_by_game", SportFinishStatisByGameHandler(serverCtx))

	apiRouter.GET("/lottery/lobby_link", LotteryLobbyLinkHandler(serverCtx))
	apiRouter.GET("/lottery/unfinish_statis", LotteryUnfinishStatisHandler(serverCtx))
	apiRouter.GET("/lottery/unfinish_wagers_detail", LotteryUnfinishWagersDetailHandler(serverCtx))
	apiRouter.GET("/lottery/finish_statis_by_date", LotteryFinishStatisByDateHandler(serverCtx))
	apiRouter.GET("/lottery/finish_statis_by_game", LotteryFinishStatisByGameHandler(serverCtx))
	apiRouter.GET("/lottery/finish_wagers_detail", LotteryFinishWagersDetailHandler(serverCtx))
	apiRouter.GET("/lottery/bulletin_list", LotteryBulletinListHandler(serverCtx))
	apiRouter.GET("/lottery/game_lobby_menu_link", LotteryGameLobbyMenuLinkHandler(serverCtx))
	apiRouter.GET("/lottery/game_list", LotteryGameListHandler(serverCtx))

	apiRouter.GET("/battle/lobby_link", BattleLobbyLinkHandler(serverCtx))
	apiRouter.GET("/battle/game_lobby_menu", BattleGameLobbyMenuHandler(serverCtx))
	apiRouter.GET("/battle/game_lobby_menu_link", BattleGameLobbyMenuLinkHandler(serverCtx))
	apiRouter.GET("/battle/wagers_by_date", BattleWagersByDateHandler(serverCtx))

	apiRouter.GET("/slot/game_lobby_menu", SlotGameLobbyMenuHandler(serverCtx))
	apiRouter.GET("/slot/game_lobby_menu_link", SlotGameLobbyMenuLinkHandler(serverCtx))
	apiRouter.GET("/slot/bulletin_list", SlotBulletinListHandler(serverCtx))
	apiRouter.GET("/slot/wagers_by_date", SlotWagersByDateHandler(serverCtx))
	apiRouter.GET("/slot/crash_combo_wagers_by_date", SlotCrashComboWagersByDateHandler(serverCtx))
	apiRouter.GET("/slot/wager_search_bar", SlotWagerSearchBarHandler(serverCtx))

	apiRouter.GET("/hall/info", HallInfoHandler(serverCtx))

	apiRouter.GET("/fish/game_lobby_menu_link", FishGameLobbyMenuLinkHandler(serverCtx))
	apiRouter.GET("/fish/introduction_images_info", IntroductionImagesInfoHandler(serverCtx))
	apiRouter.GET("/fish/wagers_by_date", FishWagersByDateHandler(serverCtx))

	apiRouter.GET("/lang/list", LangListHandler(serverCtx))

	apiRouter.GET("/server/info", ServerInfoHandler(serverCtx))

	apiRouter.GET("/entrance/apiroutepage", APIRoutePageHandler(serverCtx))
	apiRouter.GET("/entrance/upup", UpUpHandler(serverCtx))
	apiRouter.GET("/entrance/gameroutepage", GameRoutePageHandler(serverCtx))

	server.Handler = router
}
