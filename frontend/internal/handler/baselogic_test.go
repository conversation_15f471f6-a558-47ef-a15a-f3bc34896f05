package handler

import (
	"gbh/errorx"
	"gbh/frontend/internal/config"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/rest/httpx"
)

const (
	apiEffectiveBetting            = "/api/account/effective_betting"
	apiAccountInfo                 = "/api/account/info"
	apiAccountFavoriteGame         = "/api/account/favorite_game"
	apiAccountLobbySwitch          = "/api/account/lobby_switch"
	apiTransactionRecord           = "/api/account/transaction_record"
	apiAccountLogout               = "/api/account/logout"
	apiMainSetting                 = "/api/live/main_setting"
	apiLiveRuleURL                 = "/api/live/redirect_rule_url"
	apiLiveLobbyLink               = "/api/live/lobby_link"
	apiLiveGameCodeList            = "/api/live/game_code_list"
	apiLiveBulletinList            = "/api/live/bulletin_list"
	apiLiveWagersByDate            = "/api/live/wagers_by_date"
	apiLiveWagersByRoundSerial     = "/api/live/wagers_by_round_serial"
	apiSportLobbyLink              = "/api/sport/lobby_link"
	apiSportWagersDetail           = "/api/sport/wagers_detail"
	apiSportUnfinishStatis         = "/api/sport/unfinish_statis"
	apiSportUnfinishStatisByGame   = "/api/sport/unfinish_statis_by_game"
	apiSportFinishStatis           = "/api/sport/finish_statis"
	apiSportFinishStatisByGame     = "/api/sport/finish_statis_by_game"
	apiLotteryLobbyLink            = "/api/lottery/lobby_link"
	apiLotteryUnfinishStatis       = "/api/lottery/unfinish_statis"
	apiLotteryUnfinishWagersDetail = "/api/lottery/unfinish_wagers_detail"
	apiLotteryFinishStatisByDate   = "/api/lottery/finish_statis_by_date"
	apiLotteryFinishStatisByGame   = "/api/lottery/finish_statis_by_game"
	apiLotteryFinishWagersDetail   = "/api/lottery/finish_wagers_detail"
	apiLotteryBulletinList         = "/api/lottery/bulletin_list"
	apiLotteryGameLobbyMenuLink    = "/api/lottery/game_lobby_menu_link"
	apiLotteryGameList             = "/api/lottery/game_list"
	apiBattleLobbyLink             = "/api/battle/lobby_link"
	apiSlotGameLobbyMenu           = "/api/slot/game_lobby_menu"
	apiSlotGameLobbyMenuLink       = "/api/slot/game_lobby_menu_link"
	apiSlotBullinList              = "/api/slot/bulletin_list"
	apiSlotWagersByDate            = "/api/slot/wagers_by_date"
	apiSlotCrashComboWagersByDate  = "/api/slot/crash_combo_wagers_by_date"
	apiHallInfo                    = "/api/hall/info"
	apiFishGameLobbyMenuLink       = "/api/fish/game_lobby_menu_link"
	apiFishIntroductionImagesInfo  = "/api/fish/introduction_images_info"
	apiFishWagersByDate            = "/api/fish/wagers_by_date"
	apiLangList                    = "/api/lang/list"
	apiServerInfo                  = "/api/server/info"
	apiBattleGameLobbyMenu         = "/api/battle/game_lobby_menu"
	apiBattleGameLobbyMenuLink     = "/api/battle/game_lobby_menu_link"
	apiBattleWagersByDate          = "/api/battle/wagers_by_date"
	apiEntranceAPIRoutePage        = "/api/entrance/apiroutepage"
	apiSlotWagerSearchBar          = "/api/slot/wager_search_bar"
	apiLiveGameBetLimit            = "/api/account/live_game_bet_limit"
	apiEntranceGameRoutePage       = "/api/entrance/gameroutepage"
	apiLotteryGameBetLimit         = "/api/account/lottery_game_bet_limit"
	apiSportGameBetLimit           = "/api/account/sport_game_bet_limit"
	apiLiveGameList                = "/api/live/game_list"
)

var (
	svcCtx *svc.ServiceContext
)

func init() {
	gin.SetMode(gin.TestMode)

	conf := config.Config{}
	svcCtx = &svc.ServiceContext{
		Config:         conf,
		MaintainCtx:    mock.NewMaintainCtx(),
		SessionCtx:     mock.NewSessionCtx(),
		ReportCtx:      mock.NewReportCtx(),
		UserCtx:        mock.NewUserCtx(),
		LiveGameCtx:    mock.NewLiveGameCtx(),
		GameCtx:        mock.NewGameCtx(),
		SportGameCtx:   mock.NewSportGameCtx(),
		LotteryGameCtx: mock.NewLotteryGameCtx(),
		WalletCtx:      mock.NewWalletCtx(),
		JackpotCtx:     mock.NewJackpotCtx(),
		HallCtx:        mock.NewHallCtx(),
		FishGameCtx:    mock.NewFishGameCtx(),
		LangCtx:        mock.NewLangCtx(),
		SlotGameCtx:    mock.NewSlotGameCtx(),
	}

	// 統一處理錯誤回傳
	httpx.SetErrorHandler(func(err error) (int, any) {
		errx := errorx.ErrorToErrorx(err)
		return http.StatusOK, &types.BaseResponse{
			Code:    errx.Code,
			Message: errx.Message,
		}
	})
}
