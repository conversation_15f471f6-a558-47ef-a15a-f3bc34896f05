package handler

import (
	"gbh/errorx"
	"gbh/frontend/internal/logic"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/frontend/internal/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func LiveGameListHandler(svcCtx *svc.ServiceContext) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.LiveGameList
		err := c.ShouldBind(&req)
		if err != nil {
			httpx.Error(c.Writer, errorx.InvalidParameters)
			return
		}

		valid := validator.New(c, svcCtx)
		validErr := valid.ValidAll(req.Hall<PERSON>, c.ClientIP(), req)
		if validErr != nil {
			httpx.Error(c.Writer, validErr)
			return
		}

		l := logic.NewLiveGameListLogic(c, svcCtx)
		resp, err := l.LiveGameList(&req)
		if err != nil {
			httpx.Error(c.<PERSON>, err)
			return
		}

		c.<PERSON>(http.StatusOK, resp)
	}
}
