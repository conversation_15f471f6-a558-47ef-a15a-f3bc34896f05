package handler

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/ip"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/maintain/maintainclient"
	"gbh/proto/game"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestAccountLobbySwitchHandler(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiAccountLobbySwitch, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.AccountLobbySwitch
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	ipCtx := mock.NewIPCtx()
	mockCheckIPRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockCheckIPRequest).Return(&seeder.CheckIPIsAvailable, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	for _, switchInfo := range seeder.GetSession.GetRdInfo().GetLobbySwitch() {
		maintainCtx.On("GetMaintainByGameKind", switchInfo.GetGameKind(), c.ClientIP()).
			Return(&maintainclient.GetMaintainByGameKindResponse{
				BeginAt: "2025-04-07T16:50:00+0800",
				EndAt:   "2025-04-07T16:55:00+0800",
				Msg:     "全產品維護測試",
			}, nil)
	}

	svcCtx.SessionCtx = sessionCtx
	svcCtx.IPCtx = ipCtx
	svcCtx.MaintainCtx = maintainCtx

	accountLobbySwitchFunc := AccountLobbySwitchHandler(svcCtx)
	accountLobbySwitchFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"game_kind":3,"switch":true,"is_maintaining":false},{"game_kind":5,"switch":true,"is_maintaining":false},{"game_kind":12,"switch":true,"is_maintaining":false},{"game_kind":31,"switch":true,"is_maintaining":false},{"game_kind":38,"switch":true,"is_maintaining":false},{"game_kind":66,"switch":true,"is_maintaining":false}]}`, w.Body.String())
}

func TestAccountLobbySwitchHandler_WithoutLobbySwitch(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiAccountLobbySwitch, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.AccountLobbySwitch
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return([]*game.LobbySwitch{}, nil)

	ipCtx := mock.NewIPCtx()
	mockCheckIPRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockCheckIPRequest).Return(&seeder.CheckIPIsAvailable, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.IPCtx = ipCtx
	svcCtx.MaintainCtx = maintainCtx

	accountLobbySwitchFunc := AccountLobbySwitchHandler(svcCtx)
	accountLobbySwitchFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":0,"message":"","data":[]}`, w.Body.String())
}

func TestAccountLobbySwitchHandler_WithoutParameterHallID(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiAccountLobbySwitch, http.NoBody)

	query := urlutil.NewBuilder()
	req.URL.RawQuery = query.Encode()
	c.Request = req

	accountLobbySwitchFunc := AccountLobbySwitchHandler(svcCtx)
	accountLobbySwitchFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestAccountLobbySwitchHandler_MainValidError(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiAccountLobbySwitch, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.AccountLobbySwitch
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	seeder.GetMaintainByHallID.Status = true

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	svcCtx.MaintainCtx = maintainCtx

	accountLobbySwitchFunc := AccountLobbySwitchHandler(svcCtx)
	accountLobbySwitchFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":*********,"message":"System is under maintenance"}`, w.Body.String())

	seeder.GetMaintainByHallID.Status = false
}

func TestAccountLobbySwitchHandler_IPValidError(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiAccountLobbySwitch, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.AccountLobbySwitch
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	ipCtx := mock.NewIPCtx()
	mockCheckIPRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockCheckIPRequest).Return(&seeder.CheckIPIsUnavailable, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	svcCtx.IPCtx = ipCtx
	svcCtx.MaintainCtx = maintainCtx

	accountLobbySwitchFunc := AccountLobbySwitchHandler(svcCtx)
	accountLobbySwitchFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":*********,"message":"IP is not accepted"}`, w.Body.String())
}

func TestAccountLobbySwitchHandler_LogicError(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiAccountLobbySwitch, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("session_id", seeder.Session)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.AccountLobbySwitch
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	ipCtx := mock.NewIPCtx()
	mockCheckIPRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockCheckIPRequest).Return(&seeder.CheckIPIsAvailable, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.IPCtx = ipCtx
	svcCtx.MaintainCtx = maintainCtx

	accountLobbySwitchFunc := AccountLobbySwitchHandler(svcCtx)
	accountLobbySwitchFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":*********,"message":"Connection failed"}`, w.Body.String())
}
