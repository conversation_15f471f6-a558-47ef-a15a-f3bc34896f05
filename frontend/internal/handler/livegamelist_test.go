package handler

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/ip"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestLiveGameListHandler(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLiveGameList, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("lang", "zh-tw")
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.LiveGameList
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	maintainCtx := mock.NewSysMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	ipCtx := mock.NewIPCtx()
	mockCheckIPRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockCheckIPRequest).Return(&seeder.CheckIPIsAvailable, nil)

	liveGameCtx := mock.NewLiveGameCtx()
	liveGameCtx.On("GameList", "zh-tw", uint32(seeder.BgpHallID)).Return(&seeder.LiveGameList, nil)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.IPCtx = ipCtx
	svcCtx.LiveGameCtx = liveGameCtx

	liveGameListFunc := LiveGameListHandler(svcCtx)
	liveGameListFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"id":3001,"name":"Baccarat"}]}`, w.Body.String())
}

func TestLiveGameListHandler_WithoutParameterHallID(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLiveGameList, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddString("lang", "zh-tw")
	req.URL.RawQuery = query.Encode()
	c.Request = req

	liveGameListFunc := LiveGameListHandler(svcCtx)
	liveGameListFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":561000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestLiveGameListHandler_WithoutParameterLang(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLiveGameList, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.BgpHallID)
	req.URL.RawQuery = query.Encode()
	c.Request = req

	liveGameListFunc := LiveGameListHandler(svcCtx)
	liveGameListFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":561000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestLiveGameListHandler_IPValidError(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLiveGameList, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("lang", "zh-tw")
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.LiveGameList
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	maintainCtx := mock.NewSysMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	ipCtx := mock.NewIPCtx()
	mockCheckIPRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockCheckIPRequest).Return(&seeder.CheckIPIsUnavailable, nil)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.IPCtx = ipCtx

	liveGameListFunc := LiveGameListHandler(svcCtx)
	liveGameListFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":561000009,"message":"IP is not accepted"}`, w.Body.String())
}

func TestLiveGameListHandler_LogicError(t *testing.T) {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	req := httptest.NewRequest(http.MethodGet, apiLiveGameList, http.NoBody)

	query := urlutil.NewBuilder()
	query.AddInt("hall_id", seeder.BgpHallID)
	query.AddString("lang", "zh-tw")
	req.URL.RawQuery = query.Encode()
	c.Request = req

	var validReq types.LiveGameList
	err := c.ShouldBind(&validReq)

	assert.NoError(t, err)

	maintainCtx := mock.NewSysMaintainCtx()
	maintainCtx.On("GetMaintainByHallID", uint32(seeder.BgpHallID), c.ClientIP()).Return(&seeder.GetMaintainByHallID, nil)

	ipCtx := mock.NewIPCtx()
	mockCheckIPRequest := ip.CheckIPRequest{
		IP:       c.ClientIP(),
		HallID:   seeder.BgpHallID,
		Entrance: constants.MemberEntrance,
	}
	ipCtx.On("CheckIP", mockCheckIPRequest).Return(&seeder.CheckIPIsAvailable, nil)

	liveGameCtx := mock.NewLiveGameCtx()
	liveGameCtx.On("GameList", "zh-tw", uint32(seeder.BgpHallID)).Return(nil, errorx.ConnectionFailed)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.IPCtx = ipCtx
	svcCtx.LiveGameCtx = liveGameCtx

	liveGameListFunc := LiveGameListHandler(svcCtx)
	liveGameListFunc(c)

	assert.Equal(t, http.StatusOK, c.Writer.Status())
	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
