package battlegame

import (
	"context"
	"gbh/battlegame/battlegameclient"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx        context.Context
	redisCache *redis.Client
	redisMock  redismock.ClientMock
)

type mockBattleGameRPC struct{ mock.Mock }

func (m *mockBattleGameRPC) GetUserPermissions(ctx context.Context, in *battlegameclient.GetUserPermissionsRequest, _ ...grpc.CallOption) (*battlegameclient.GetUserPermissionsResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.GetUserPermissionsResponse), nil
}

func (m *mockBattleGameRPC) EnableUserPermissions(ctx context.Context, in *battlegameclient.EnableUserPermissionsRequest, _ ...grpc.CallOption) (*battlegameclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	return nil, args.Error(1)
}

func (m *mockBattleGameRPC) WagersByBetTime(ctx context.Context, in *battlegameclient.WagersByBetTimeRequest, _ ...grpc.CallOption) (*battlegameclient.WagersResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.WagersResponse), nil
}

func (m *mockBattleGameRPC) WagersByModifiedTime(ctx context.Context, in *battlegameclient.WagersByModifiedTimeRequest, _ ...grpc.CallOption) (*battlegameclient.WagersResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.WagersResponse), nil
}

func (m *mockBattleGameRPC) LinkList(ctx context.Context, in *battlegameclient.LinkListRequest, _ ...grpc.CallOption) (*battlegameclient.LinkListResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.LinkListResponse), nil
}

func (m *mockBattleGameRPC) SubWagersURL(ctx context.Context, in *battlegameclient.SubWagersURLRequest, _ ...grpc.CallOption) (*battlegameclient.SubWagersURLResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.SubWagersURLResponse), nil
}

func (m *mockBattleGameRPC) WagersByUserID(ctx context.Context, in *battlegameclient.WagersByUserIDRequest, _ ...grpc.CallOption) (*battlegameclient.WagersByUserIDResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.WagersByUserIDResponse), nil
}

func (m *mockBattleGameRPC) GetWagers(ctx context.Context, in *battlegameclient.GetWagersRequest, _ ...grpc.CallOption) (*battlegameclient.GetWagersResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.GetWagersResponse), nil
}

func (m *mockBattleGameRPC) UpdateGameSwitch(ctx context.Context, in *battlegameclient.UpdateGameSwitchRequest, _ ...grpc.CallOption) (*battlegameclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	return nil, args.Error(1)
}

func (m *mockBattleGameRPC) GetMultiSubWagersURL(ctx context.Context, in *battlegameclient.GetMultiSubWagersURLRequest, _ ...grpc.CallOption) (*battlegameclient.GetMultiSubWagersURLResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*battlegameclient.GetMultiSubWagersURLResponse), nil
}

func (m *mockBattleGameRPC) LogoutByHall(ctx context.Context, in *battlegameclient.LogoutByHallRequest, _ ...grpc.CallOption) (*battlegameclient.EmptyResponse, error) {
	args := m.Called(ctx, in)

	return nil, args.Error(1)
}

func newMockBattleGameRPC() *mockBattleGameRPC {
	return &mockBattleGameRPC{}
}

func init() {
	ctx = context.Background()
	redisCache, redisMock = redismock.NewClientMock()
}
