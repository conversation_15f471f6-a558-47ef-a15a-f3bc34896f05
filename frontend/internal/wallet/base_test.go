package wallet

import (
	"context"
	"gbh/wallet/walletclient"

	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx context.Context
)

type mockWalletRPC struct{ mock.Mock }

func (s *mockWalletRPC) EntryStatus(ctx context.Context, in *walletclient.EntryStatusRequest, _ ...grpc.CallOption) (*walletclient.EntryStatusResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}
	return resp.(*walletclient.EntryStatusResponse), nil
}

func (s *mockWalletRPC) GetUsersBalance(ctx context.Context, in *walletclient.UsersBalanceRequest, _ ...grpc.CallOption) (*walletclient.UsersBalanceResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.UsersBalanceResponse), nil
}

func (s *mockWalletRPC) SlotMachineBalance(ctx context.Context, in *walletclient.SlotMachineBalanceRequest, _ ...grpc.CallOption) (*walletclient.SlotMachineBalanceResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.SlotMachineBalanceResponse), nil
}

func (s *mockWalletRPC) Withdraw(ctx context.Context, in *walletclient.WithdrawRequest, _ ...grpc.CallOption) (*walletclient.WithdrawResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.WithdrawResponse), nil
}

func (s *mockWalletRPC) Deposit(ctx context.Context, in *walletclient.DepositRequest, _ ...grpc.CallOption) (*walletclient.DepositResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.DepositResponse), nil
}

func (s *mockWalletRPC) TransferEntry(ctx context.Context, in *walletclient.TransferEntryRequest, _ ...grpc.CallOption) (*walletclient.TransferEntryResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.TransferEntryResponse), nil
}

func (s *mockWalletRPC) TransferEntryList(ctx context.Context, in *walletclient.TransferEntryListRequest, _ ...grpc.CallOption) (*walletclient.TransferEntryListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*walletclient.TransferEntryListResponse), nil
}

func (s *mockWalletRPC) FishMachineBalance(ctx context.Context, in *walletclient.FishMachineBalanceRequest, _ ...grpc.CallOption) (*walletclient.FishMachineBalanceResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.FishMachineBalanceResponse), nil
}

func (s *mockWalletRPC) GetUsersBalanceByAgent(ctx context.Context, in *walletclient.UsersBalanceByAgentRequest, _ ...grpc.CallOption) (*walletclient.UsersBalanceByAgentResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.UsersBalanceByAgentResponse), nil
}

func (s *mockWalletRPC) GetUserBalance(ctx context.Context, in *walletclient.GetUserBalanceRequest, _ ...grpc.CallOption) (*walletclient.GetUserBalanceResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetUserBalanceResponse), nil
}

func (s *mockWalletRPC) GetEntryList(ctx context.Context, in *walletclient.GetEntryListRequest, _ ...grpc.CallOption) (*walletclient.GetEntryListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetEntryListResponse), nil
}

func (s *mockWalletRPC) GetTotalAmount(ctx context.Context, in *walletclient.GetTotalAmountRequest, _ ...grpc.CallOption) (*walletclient.GetTotalAmountResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetTotalAmountResponse), nil
}

func (s *mockWalletRPC) GetOpCodeList(ctx context.Context, in *walletclient.GetOpCodeListRequest, _ ...grpc.CallOption) (*walletclient.GetOpCodeListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetOpCodeListResponse), nil
}

func (s *mockWalletRPC) GetOpCodeDict(ctx context.Context, in *walletclient.GetOpCodeDictRequest, _ ...grpc.CallOption) (*walletclient.GetOpCodeDictResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetOpCodeDictResponse), nil
}

func (s *mockWalletRPC) GetAccountCloseDate(ctx context.Context, _ *walletclient.AccountCloseDateRequest, _ ...grpc.CallOption) (*walletclient.AccountCloseDateResponse, error) {
	args := s.Called(ctx)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.AccountCloseDateResponse), nil
}

func (s *mockWalletRPC) GetTransferEntryDetail(ctx context.Context, _ *walletclient.GetTransferEntryDetailRequest, _ ...grpc.CallOption) (*walletclient.GetTransferEntryDetailResponse, error) {
	args := s.Called(ctx)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetTransferEntryDetailResponse), nil
}

func (s *mockWalletRPC) GetUserCashFake(ctx context.Context, _ *walletclient.GetUserCashFakeRequest, _ ...grpc.CallOption) (*walletclient.GetUserCashFakeResponse, error) {
	args := s.Called(ctx)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetUserCashFakeResponse), nil
}

func (s *mockWalletRPC) GetOpcodeListByACC(ctx context.Context, _ *walletclient.GetOpcodeListByACCRequest, _ ...grpc.CallOption) (*walletclient.GetOpcodeListByACCResponse, error) {
	args := s.Called(ctx)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetOpcodeListByACCResponse), nil
}

func (s *mockWalletRPC) GetExchangeRate(ctx context.Context, in *walletclient.GetExchangeRateRequest, _ ...grpc.CallOption) (*walletclient.GetExchangeRateResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetExchangeRateResponse), nil
}

func (s *mockWalletRPC) ExchangeRateList(ctx context.Context, in *walletclient.EmptyRequest, _ ...grpc.CallOption) (*walletclient.ExchangeRateListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.ExchangeRateListResponse), nil
}

func (s *mockWalletRPC) GetOpcodeGroupsList(ctx context.Context, in *walletclient.GetOpcodeGroupsListRequest, _ ...grpc.CallOption) (*walletclient.GetOpcodeGroupsListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetOpcodeGroupsListResponse), nil
}

func (s *mockWalletRPC) GetTransferEntryDetailAll(ctx context.Context, in *walletclient.GetTransferEntryDetailAllRequest, _ ...grpc.CallOption) (*walletclient.GetTransferEntryDetailAllResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetTransferEntryDetailAllResponse), nil
}

func (s *mockWalletRPC) GetCurrencyList(ctx context.Context, in *walletclient.GetCurrencyListRequest, _ ...grpc.CallOption) (*walletclient.GetCurrencyListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*walletclient.GetCurrencyListResponse), nil
}

func newMockWalletRPC() *mockWalletRPC {
	return &mockWalletRPC{}
}

func init() {
	ctx = context.Background()
}
