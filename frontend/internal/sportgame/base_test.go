package sportgame

import (
	"context"
	"gbh/proto/sportgame"
	"gbh/sportgame/sportgameclient"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx        context.Context
	redisCache *redis.Client
	redisMock  redismock.ClientMock
)

type mockSportGameRPC struct{ mock.Mock }

func (m *mockSportGameRPC) GameList(ctx context.Context, in *sportgameclient.GameListRequest, _ ...grpc.CallOption) (*sportgameclient.GameListResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.GameListResponse), nil
}

func (m *mockSportGameRPC) GameLink(ctx context.Context, in *sportgameclient.GameLinkRequest, _ ...grpc.CallOption) (*sportgameclient.GameLinkResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.GameLinkResponse), nil
}

func (m *mockSportGameRPC) WagersByBetTime(ctx context.Context, in *sportgameclient.SportWagersByBetTimeRequest, _ ...grpc.CallOption) (*sportgameclient.WagersResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.WagersResponse), nil
}

func (m *mockSportGameRPC) WagersByModifiedTime(ctx context.Context, in *sportgameclient.SportWagersByModifiedTimeRequest, _ ...grpc.CallOption) (*sportgameclient.WagersResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.WagersResponse), nil
}

func (m *mockSportGameRPC) SubWagersURL(ctx context.Context, in *sportgameclient.SubWagersURLRequest, _ ...grpc.CallOption) (*sportgameclient.SubWagersURLResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.SubWagersURLResponse), nil
}

func (m *mockSportGameRPC) CheckWagersByID(ctx context.Context, in *sportgameclient.CheckWagersByIDRequest, _ ...grpc.CallOption) (*sportgameclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	return nil, args.Error(1)
}

func (m *mockSportGameRPC) GetCategory(ctx context.Context, in *sportgameclient.GetCategoryRequest, _ ...grpc.CallOption) (*sportgameclient.GetCategoryResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.GetCategoryResponse), nil
}

func (m *mockSportGameRPC) WagersDetail(ctx context.Context, in *sportgameclient.WagersDetailRequest, _ ...grpc.CallOption) (*sportgameclient.WagersDetailResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.WagersDetailResponse), nil
}

func (m *mockSportGameRPC) GameplayBetLimit(ctx context.Context, in *sportgameclient.GameplayBetLimitRequest, _ ...grpc.CallOption) (*sportgameclient.GameplayBetLimitResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.GameplayBetLimitResponse), nil
}

func (m *mockSportGameRPC) UnfinishStatis(ctx context.Context, in *sportgameclient.UnfinishStatisRequest, _ ...grpc.CallOption) (*sportgameclient.UnfinishStatisResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.UnfinishStatisResponse), nil
}

func (m *mockSportGameRPC) FinishStatis(ctx context.Context, in *sportgameclient.FinishStatisRequest, _ ...grpc.CallOption) (*sportgameclient.FinishStatisResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.FinishStatisResponse), nil
}

func (m *mockSportGameRPC) FinishWagers(ctx context.Context, in *sportgameclient.FinishWagersRequest, _ ...grpc.CallOption) (*sportgameclient.FinishWagersResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.FinishWagersResponse), nil
}

func (m *mockSportGameRPC) GetWagers(ctx context.Context, in *sportgameclient.GetWagersRequest, _ ...grpc.CallOption) (*sportgameclient.GetWagersResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*sportgame.GetWagersResponse), nil
}

func newMockSportGameRPC() *mockSportGameRPC {
	return &mockSportGameRPC{}
}

func init() {
	ctx = context.Background()
	redisCache, redisMock = redismock.NewClientMock()
}
