package seeder

import (
	"gbh/frontend/internal/constants"
	"gbh/livegame/livegameclient"
)

var (
	PlatformMenu         livegameclient.PlatformMenuResponse
	GameRuleList         livegameclient.GameRuleListResponse
	GameCodeListResp     livegameclient.GameCodeListResponse
	GameBulletinList     livegameclient.BulletinResponse
	WagersBySession      livegameclient.WagersByUserResponse
	WagersByRoundserial  livegameclient.WagersByUserResponse
	LiveGameplayBetLimit livegameclient.GameplayBetLimitResponse
	LiveGameList         livegameclient.GameListResponse
)

const (
	platformUltimateID   = 176
	platformLiveDealerID = 38
	platformMultibetID   = 40
	platformBlockchainID = 99
	GameFPCDiceID        = 3039
	GameAceyDeuceyID     = 3048
	GameBaccaratID       = 3050
	BaccaratGameId       = uint32(3001)
	BaccaratGameCode     = uint32(1)
	BaccaratGameCode2    = uint32(2)

	LiveWagersID    = 66666666
	LiveRoundSerial = 77777777

	LiveBetAmount      = 10
	LivePayoff         = 15
	LiveCommissionable = 10
)

func init() {
	PlatformMenu = livegameclient.PlatformMenuResponse{
		PlatformMenu: []*livegameclient.PlatformMenuData{
			{
				Id:   platformUltimateID,
				Name: "旗艦廳",
			},
			{
				Id:   platformLiveDealerID,
				Name: "快速廳",
			},
			{
				Id:   platformMultibetID,
				Name: "多台下注",
			},
			{
				Id:   platformBlockchainID,
				Name: "區塊鏈",
			},
		},
	}

	GameRuleList = livegameclient.GameRuleListResponse{
		GameRule: []*livegameclient.GameRuleData{
			{
				Id:   GameFPCDiceID,
				Name: "越南魚蝦蟹",
			},
			{
				Id:   GameAceyDeuceyID,
				Name: "射龍門",
			},
			{
				Id:   GameBaccaratID,
				Name: "28百家樂",
			},
		},
	}

	GameCodeListResp = livegameclient.GameCodeListResponse{
		Data: []*livegameclient.LiveGameCode{
			{
				GameId:    BaccaratGameId,
				GameName:  "百家樂",
				TableCode: BaccaratGameCode,
				TableName: "AS1",
			},
			{
				GameId:    BaccaratGameId,
				GameName:  "百家樂",
				TableCode: BaccaratGameCode2,
				TableName: "AS2",
			},
		},
	}

	GameBulletinList = livegameclient.BulletinResponse{
		Bulletin: []*livegameclient.BulletinData{
			{
				Date:      "2024-09-10 14:42:18",
				Content:   "敬請客戶留意!!",
				GameId:    BaccaratGameId,
				TableCode: 1,
			},
		},
	}

	WagersBySession = livegameclient.WagersByUserResponse{
		Wagers: []*livegameclient.WagersByUserToFrontend{
			{
				Id:              LiveWagersID,
				WagersDate:      "2024-11-11T00:00:00-04:00",
				RoundSerial:     LiveRoundSerial,
				RoundNo:         "3-22",
				GameId:          BaccaratGameId,
				Codename:        "EU1",
				BetAmount:       LiveBetAmount,
				Commissionable:  LiveCommissionable,
				Payoff:          LivePayoff,
				WagersDetailKey: "a90cde192373f3a5f2cd272cab016a19",
			},
			{
				Id:              LiveWagersID + 1,
				WagersDate:      "2024-11-11T00:01:00-04:00",
				RoundSerial:     LiveRoundSerial + 1,
				RoundNo:         "6-20",
				GameId:          GameAceyDeuceyID,
				Codename:        "BC1",
				BetAmount:       LiveBetAmount,
				Commissionable:  LiveCommissionable,
				Payoff:          LivePayoff,
				WagersDetailKey: "53178215b0b7988249812200326396f4",
			},
		},
		WagersSum: &livegameclient.WagersSumByUser{
			TotalBetAmount:      LiveBetAmount + LiveBetAmount,
			TotalCommissionable: LiveCommissionable + LiveCommissionable,
			TotalPayoff:         LivePayoff + LivePayoff,
		},
		Pagination: &livegameclient.Pagination{
			CurrentPage: constants.DefaultPageNum,
			PageLimit:   constants.PageLimitBy20,
			Total:       1 + 1,
			TotalPage:   constants.DefaultPageNum,
		},
	}

	WagersByRoundserial = livegameclient.WagersByUserResponse{
		Wagers: []*livegameclient.WagersByUserToFrontend{
			{
				Id:              LiveWagersID,
				WagersDate:      "2024-11-11T00:00:00-04:00",
				RoundSerial:     LiveRoundSerial,
				RoundNo:         "3-22",
				GameId:          BaccaratGameId,
				Codename:        "EU1",
				BetAmount:       LiveBetAmount,
				Commissionable:  LiveCommissionable,
				Payoff:          LivePayoff,
				WagersDetailKey: "a90cde192373f3a5f2cd272cab016a19",
			},
		},
		WagersSum: &livegameclient.WagersSumByUser{
			TotalBetAmount:      LiveBetAmount,
			TotalCommissionable: LiveCommissionable,
			TotalPayoff:         LivePayoff,
		},
		Pagination: &livegameclient.Pagination{
			CurrentPage: constants.DefaultPageNum,
			PageLimit:   constants.PageLimitBy20,
			Total:       1,
			TotalPage:   constants.DefaultPageNum,
		},
	}

	LiveGameplayBetLimit = livegameclient.GameplayBetLimitResponse{
		GameLimitList: []*livegameclient.GameLimitList{
			{
				Name: "龍虎鬥",
				GameplayLimitList: []*livegameclient.GameplayLimitList{
					{
						LimitName:  "單注最低限額",
						LimitValue: LiveGameMinAmountPerBet,
					},
					{
						LimitName:  "單注最高限額",
						LimitValue: LiveGameMaxAmountPerBet,
					},
					{
						LimitName:  "和注最高限額",
						LimitValue: LiveGameTieGameMaxBet,
					},
				},
			},
		},
	}

	LiveGameList = livegameclient.GameListResponse{
		GameInfo: []*livegameclient.GameInfo{
			{
				Id:   BaccaratGameType,
				Name: "Baccarat",
			},
		},
	}
}
