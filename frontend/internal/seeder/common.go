package seeder

const (
	BgpHallID  = 3820474
	BgpWebsite = "bbinbgp"

	UserGTPTestID          = 456120653
	AgentID                = 456120651
	SuperAgentID           = 456120649
	ShareholderID          = 456120647
	SupremeShareholderID   = 456120645
	BgpHallSize            = 644
	SupremeShareholderSize = 1
	ShareholderSize        = 1
	SuperAgentSize         = 1
	AgentSize              = 3
	UserSize               = 3
	ShareholderName        = "bgtptest"
	SuperAgentName         = "cgtptest"
	AgentName              = "dgtptest"
	UserGTPtestName        = "gtptest"

	RoundDate            = "2024-04-11"
	ReportCommissionable = 100
	ReportPayoff         = 20

	Session  = "bbe61cbb5f2e97c7622291e8b53047eaa520a633c2"
	OTS      = "a676222bbe613047eacbb533c2f291e8b5a520e97c"
	RandomID = "random1234"

	Host               = "localhost"
	DomainURLWithHTTP  = "http://localhost"
	DomainURLWithHTTPS = "https://localhost"
	HTTPSScheme        = "https"

	ClientIP   = "*************"
	GinLocalIP = "*************:1234"

	Balance                   = 1002613.5
	DepositOpCode             = 1001
	APITransferOutOpCode      = 1043
	NewBBPayoffOpCode         = 120002
	LiveFailOpCode            = 400032
	APITransferOutUserBalance = 2010260008.2895
	NewBBUserBalance          = 2010259808.2895
	UserAmount                = 0.0000

	TotalPage  = 2
	TotalCount = 53

	JungleGameType uint32 = 5014

	SlotMenuID   = 1
	SlotTopID    = 0
	SlotDepth    = 1
	SlotMenuSort = 4

	LaBarGameSort = 228
	LaBarGameType = 5001
	LaBarGameName = "水果拉霸"

	B128GameType   = 12001
	B128GameName   = "BB 幸運28"
	B128ExternalID = "B128"

	NewBonusIconKind  = 2
	RecommendIconKind = 3
	EventIconKind     = 4

	FishingMasterGameType = 38001
	FishingMasterGameName = "捕魚大師"
	FishingMasterGameSort = 2

	BattleDepth    = 1
	BattleMenuSort = 1

	GoldenFlowerGameType = 66001
	GoldenFlowerGameName = "炸金花"
	GoldenFlowerGameSort = 28

	FishingExpertGameType = 66068

	BaccaratGameType = 3001
)

const (
	SportVolleyballID          uint32  = 15
	SportFootballID            uint32  = 11
	SportUnfinishCountNum2     uint32  = 2
	SportUnfinishCountNum1     uint32  = 1
	SportWagersId              uint64  = 4535228001
	SportUnfinishCountTotal    uint32  = 3
	SportUnfinishBetTotal      float64 = 50
	SportUnfinishVolleyballBet float64 = 35
	SportUnfinishFootballBet   float64 = 15
	SportBetAmount             float64 = 10
	SportPayoff                float64 = -10
)

const (
	MfId = "k4nxPhhf3CSwIv7RWyERD93F8FRldtH-ffvOLO1KmnppBnwVJqu1R0qSONKkEy9FVENTTEIzSVZKSHNaWUhzbWc4cEc5cy02VWV4Tk1TOGJtaXdCdWZJTDZGWQ"

	B1SFOpenTimestamp     = 1728263101
	B1SFCloseTimestamp    = 1728263160
	MarkSixOpenTimestamp  = 1728273600
	MarkSixCloseTimestamp = 1728273600

	PrevInfoKey01Value = 32
	PrevInfoKey02Value = 47
	PrevInfoKey03Value = 21
	PrevInfoKey04Value = 8
	PrevInfoKey05Value = 43
	PrevInfoKey06Value = 15
	PrevInfoKey07Value = 41
)

const (
	CandyParty3GameID   = 5143
	CandyParty3GameName = "糖果派對3"
	ALLSTARSGameID      = 5175
	ALLSTARSGameName    = "滿天星"
)

const (
	LiveGameMinAmountPerBet            = 20
	LiveGameMaxAmountPerBet            = 500000
	LiveGameTieGameMaxBet              = 60000
	LotteryGameSingleCredit            = 100000
	LotteryGameSingleOdds              = 50000
	LotteryGameGoalXLadderSingleCredit = 33330
	LotteryGameGoalXLadderSingleOdds   = 16665
	SportGameBetLimit                  = 1000000
	SportGameGameLimit                 = 10000000
	SportGameCategory                  = 2
)
