package common

import (
	"encoding/base64"
	"fmt"
	"gbh/frontend/internal/constants"
	"net"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

func CheckWhiteListIP(clientIP string, whiteListIP []string) bool {
	for _, v := range whiteListIP {
		if matchIP(clientIP, v) {
			return true
		}
	}

	return false
}

func matchIP(clientIP string, cidr string) bool {
	if cidr[0:1] == "[" && cidr[len(cidr)-1:] == "]" {
		cidr = cidr[1 : len(cidr)-1]
	}

	clientIPAddr := net.ParseIP(clientIP)
	if clientIPAddr == nil {
		return false
	}

	if strings.Contains(cidr, "/") {
		_, subnet, err := net.ParseCIDR(cidr)
		if err != nil {
			return false
		}

		return subnet.Contains(clientIPAddr)
	}

	cidrAddr := net.ParseIP(cidr)
	if cidrAddr == nil {
		return false
	}

	return clientIPAddr.Equal(cidrAddr)
}

func CheckIPCookies(ctx *gin.Context, ip string) string {
	cookie, err := ctx.Request.Cookie("IBCACHE")

	if err != nil {
		return ""
	}

	// 當有ip cookie 時，判斷checkIPKey status
	if cookie.Value != "" {
		if checkIPKey(cookie.Value, ip) == "" {
			return ""
		}

		return checkIPKey(cookie.Value, ip)
	}

	return ""
}

func checkIPKey(key string, ip string) string {
	decoded, err := base64.StdEncoding.DecodeString(key)

	if err != nil {
		return ""
	}

	keyArr := strings.Split(string(decoded), "@")

	// 確認格式：ip@status@randomID
	if len(keyArr) < constants.IPCookieMinLength {
		return ""
	}

	// 確認ip
	if keyArr[0] != ip {
		return ""
	}

	// 回傳 status Y or N
	return keyArr[1]
}

func SetIPCookies(ctx *gin.Context, ip string, status string) {
	domain := GetDomainWithoutPort(ctx.Request.Host)

	randomID := GenerateRandomID()

	key := fmt.Sprintf("%s@%s@%s", ip, status, randomID)
	encodeValue := base64.StdEncoding.EncodeToString([]byte(key))

	http.SetCookie(ctx.Writer, &http.Cookie{
		Name:     "IBCACHE",
		Value:    encodeValue,
		Path:     "/",
		Domain:   domain,
		HttpOnly: false,
	})
}
