package logic

import (
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
)

type AccountLobbySwitchLogic struct {
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

type lobbySwitchWithMaintain struct {
	GameKind      uint32              `json:"game_kind"`
	Switch        bool                `json:"switch"`
	IsMaintaining bool                `json:"is_maintaining"`
	MaintainInfo  *types.MaintainInfo `json:"maintain_info,omitempty"`
}

func NewAccountLobbySwitchLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *AccountLobbySwitchLogic {
	return &AccountLobbySwitchLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AccountLobbySwitchLogic) AccountLobbySwitch(req *types.AccountLobbySwitch) (*types.BaseResponse, error) {
	sessionID := req.SessionID
	hallID := req.HallID

	lobbySwitch := make([]types.LobbySwitch, 0)
	// 有登入的情況
	if sessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(sessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		for _, switchInfo := range session.GetRdInfo().GetLobbySwitch() {
			lobbySwitch = append(lobbySwitch, types.LobbySwitch{
				GameKind: switchInfo.GetGameKind(),
				Switch:   switchInfo.GetSwitch(),
			})
		}
	}

	// 未登入的情況
	if sessionID == "" {
		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(hallID, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		for _, switchInfo := range hallLobbySwitch {
			lobbySwitch = append(lobbySwitch, types.LobbySwitch{
				GameKind: switchInfo.GetGameKind(),
				Switch:   switchInfo.GetSwitch(),
			})
		}
	}

	lobbySwitchWithMaintainRespose := make([]lobbySwitchWithMaintain, 0, len(lobbySwitch))

	now := carbon.Now()

	for _, ls := range lobbySwitch {
		maintainInfo, err := l.svcCtx.MaintainCtx.GetMaintainByGameKind(ls.GameKind, l.ctx.ClientIP())

		if err != nil {
			return nil, err
		}

		maintainStart := carbon.Parse(maintainInfo.GetBeginAt())

		maintainEnd := carbon.Parse(maintainInfo.GetEndAt())

		isMaintaining := now.BetweenIncludedBoth(maintainStart, maintainEnd)

		lobbySwitchMaintainTemp := lobbySwitchWithMaintain{
			GameKind:      ls.GameKind,
			Switch:        ls.Switch,
			IsMaintaining: isMaintaining,
		}

		if isMaintaining {
			lobbySwitchMaintainTemp.MaintainInfo = &types.MaintainInfo{
				GameKind:  ls.GameKind,
				StartTime: maintainInfo.GetBeginAt(),
				EndTime:   maintainInfo.GetEndAt(),
				Message:   maintainInfo.GetMsg(),
			}
		}

		lobbySwitchWithMaintainRespose = append(lobbySwitchWithMaintainRespose, lobbySwitchMaintainTemp)
	}
	return &types.BaseResponse{
		Data: lobbySwitchWithMaintainRespose,
	}, nil
}
