package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/maintain/maintainclient"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestAccountLobbySwitchLogic_WithSessionID(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()

	lobbySwitchWithMaintainResponse := make([]lobbySwitchWithMaintain, 0, len(seeder.GetSession.GetRdInfo().GetLobbySwitch()))

	for _, switchInfo := range seeder.GetSession.GetRdInfo().GetLobbySwitch() {
		maintainCtx.On("GetMaintainByGameKind", switchInfo.GetGameKind(), ginCtx.ClientIP()).
			Return(&maintainclient.GetMaintainByGameKindResponse{
				BeginAt: "2025-04-07T16:50:00+0800",
				EndAt:   "2025-04-07T16:55:00+0800",
				Msg:     "全產品維護測試",
			}, nil)

		lobbySwitchWithMaintainResponse = append(lobbySwitchWithMaintainResponse, lobbySwitchWithMaintain{
			GameKind:      switchInfo.GetGameKind(),
			Switch:        switchInfo.GetSwitch(),
			IsMaintaining: false,
		})
	}

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewAccountLobbySwitchLogic(ginCtx, svcCtx)
	resp, err := l.AccountLobbySwitch(&types.AccountLobbySwitch{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
	})

	expectedResponse := &types.BaseResponse{
		Data: lobbySwitchWithMaintainResponse,
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestAccountLobbySwitchLogic_WithoutLobbySwitch(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo()
	seeder.GetSession.RdInfo = nil

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	svcCtx.SessionCtx = sessionCtx

	l := NewAccountLobbySwitchLogic(ginCtx, svcCtx)
	resp, err := l.AccountLobbySwitch(&types.AccountLobbySwitch{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
	})

	expectedResponse := &types.BaseResponse{
		Data: []lobbySwitchWithMaintain{},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)

	seeder.GetSession.RdInfo = originLobbySwitch
}

func TestAccountLobbySwitchLogic_WithHallID(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()

	lobbySwitchWithMaintainResponse := make([]lobbySwitchWithMaintain, 0, len(seeder.HallIDLobbySwitch.GetLobbySwitch()))

	for _, switchInfo := range seeder.HallIDLobbySwitch.GetLobbySwitch() {
		maintainCtx.On("GetMaintainByGameKind", switchInfo.GetGameKind(), ginCtx.ClientIP()).
			Return(&maintainclient.GetMaintainByGameKindResponse{
				BeginAt: "2025-04-07T16:50:00+0800",
				EndAt:   "2025-04-07T16:55:00+0800",
				Msg:     "全產品維護測試",
			}, nil)

		lobbySwitchWithMaintainResponse = append(lobbySwitchWithMaintainResponse, lobbySwitchWithMaintain{
			GameKind:      switchInfo.GetGameKind(),
			Switch:        switchInfo.GetSwitch(),
			IsMaintaining: false,
		})
	}

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewAccountLobbySwitchLogic(ginCtx, svcCtx)
	resp, err := l.AccountLobbySwitch(&types.AccountLobbySwitch{
		HallID: seeder.BgpHallID,
	})

	expectedResponse := &types.BaseResponse{
		Data: lobbySwitchWithMaintainResponse,
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestAccountLobbySwitchLogic_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewAccountLobbySwitchLogic(ginCtx, svcCtx)
	resp, err := l.AccountLobbySwitch(&types.AccountLobbySwitch{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAccountLobbySwitchLogic_GetLobbySwitchByHallIDError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewAccountLobbySwitchLogic(ginCtx, svcCtx)
	resp, err := l.AccountLobbySwitch(&types.AccountLobbySwitch{
		HallID: seeder.BgpHallID,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAccountLobbySwitchLogic_IsMaintainingWithSessionID(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()

	lobbySwitchWithMaintainResponse := make([]lobbySwitchWithMaintain, 0, len(seeder.GetSession.GetRdInfo().GetLobbySwitch()))

	maintainEnd := carbon.Now().AddDay().ToDateTimeString()

	for _, switchInfo := range seeder.GetSession.GetRdInfo().GetLobbySwitch() {
		maintainCtx.On("GetMaintainByGameKind", switchInfo.GetGameKind(), ginCtx.ClientIP()).
			Return(&maintainclient.GetMaintainByGameKindResponse{
				BeginAt: "2025-04-07T16:50:00+0800",
				EndAt:   maintainEnd,
				Msg:     "全產品維護測試",
			}, nil)

		lobbySwitchWithMaintainResponse = append(lobbySwitchWithMaintainResponse, lobbySwitchWithMaintain{
			GameKind:      switchInfo.GetGameKind(),
			Switch:        switchInfo.GetSwitch(),
			IsMaintaining: true,
			MaintainInfo: &types.MaintainInfo{
				GameKind:  switchInfo.GetGameKind(),
				StartTime: "2025-04-07T16:50:00+0800",
				EndTime:   maintainEnd,
				Message:   "全產品維護測試",
			},
		})
	}

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewAccountLobbySwitchLogic(ginCtx, svcCtx)
	resp, err := l.AccountLobbySwitch(&types.AccountLobbySwitch{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
	})

	expectedResponse := &types.BaseResponse{
		Data: lobbySwitchWithMaintainResponse,
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestAccountLobbySwitchLogic_GetMaintainByGameKindError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBLive, ginCtx.ClientIP()).
		Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewAccountLobbySwitchLogic(ginCtx, svcCtx)
	resp, err := l.AccountLobbySwitch(&types.AccountLobbySwitch{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
	})

	assert.ErrorIs(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
