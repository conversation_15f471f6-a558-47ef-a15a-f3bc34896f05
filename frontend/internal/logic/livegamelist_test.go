package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/livegame/livegameclient"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLiveGameListLogic(t *testing.T) {
	ginCtx.Request = &http.Request{}

	liveGameCtx := mock.NewLiveGameCtx()
	liveGameCtx.On("GameList", "zh-tw", uint32(seeder.BgpHallID)).Return(&seeder.LiveGameList, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewLiveGameListLogic(ginCtx, svcCtx)
	resp, err := l.LiveGameList(&types.LiveGameList{
		HallID: seeder.BgpHallID,
		Lang:   "zh-tw",
	})

	expectedResponse := &types.BaseResponse{
		Data: []*livegameclient.GameInfo{
			{
				Id:   seeder.BaccaratGameType,
				Name: "Baccarat",
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestLiveGameListLogic_GetSessionError(t *testing.T) {
	ginCtx.Request = &http.Request{}

	liveGameCtx := mock.NewLiveGameCtx()
	liveGameCtx.On("GameList", "zh-tw", uint32(seeder.BgpHallID)).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewLiveGameListLogic(ginCtx, svcCtx)
	resp, err := l.LiveGameList(&types.LiveGameList{
		HallID: seeder.BgpHallID,
		Lang:   "zh-tw",
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}
