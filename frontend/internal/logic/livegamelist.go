package logic

import (
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"
)

type LiveGameListLogic struct {
	logx.Logger
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

func NewLiveGameListLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *LiveGameListLogic {
	return &LiveGameListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LiveGameListLogic) LiveGameList(req *types.LiveGameList) (*types.BaseResponse, error) {
	lang := common.ConvertLang(req.Lang)

	gameList, err := l.svcCtx.LiveGameCtx.GameList(lang, req.<PERSON>)
	if err != nil {
		return nil, err
	}

	resp := &types.BaseResponse{
		Data: gameList.GetGameInfo(),
	}

	return resp, nil
}
