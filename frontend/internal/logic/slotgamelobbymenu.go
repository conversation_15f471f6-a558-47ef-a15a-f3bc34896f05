package logic

import (
	"context"
	"errors"
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"slices"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type SlotGameLobbyMenuLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type SlotGameLobbyMenuResponse struct {
	GameMenu []GameMenuData `json:"game_menu"`
}

type GameMenuData struct {
	ID        uint32      `json:"id"`
	TopID     uint32      `json:"top_id,omitempty"`
	Sort      uint32      `json:"sort"`
	Name      string      `json:"name"`
	Games     []uint32    `json:"games"`
	GameCount int         `json:"game_count"`
	Lower     interface{} `json:"lower,omitempty"`
}

func NewSlotGameLobbyMenuLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SlotGameLobbyMenuLogic {
	return &SlotGameLobbyMenuLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SlotGameLobbyMenuLogic) SlotGameLobbyMenu(req *types.SlotGameLobbyMenu) (*types.BaseResponse, error) {
	// 玩家是否登入
	isLogin := false
	hallId := uint32(0)

	// 有登入
	if req.SessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		user, userErr := l.svcCtx.UserCtx.GetUserByUserId(session.GetUser().GetId())
		if userErr != nil {
			return nil, userErr
		}

		// 會員停權
		if user.GetBankrupt() {
			return nil, errorx.BankruptError
		}

		// 遊戲是否開放
		if !isUserGameKindOpen(session.GetRdInfo().GetLobbySwitch(), constants.BBSlot) {
			return nil, errorx.GameIsNotOpen
		}

		isLogin = true
		hallId = user.GetDomain()
	}

	// 未登入
	if req.SessionID == "" {
		hallId = req.HallID

		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(hallId, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		// 遊戲是否開放
		if !isHallGameKindOpen(hallLobbySwitch) {
			return nil, errorx.GameIsNotOpen
		}
	}

	// 遊戲是否維護中
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKindFromRedis(constants.BBSlot)
	if maintainErr != nil {
		if !errors.Is(maintainErr, errorx.MaintainNotFound) {
			return nil, maintainErr
		}
	}

	if maintain != nil {
		return &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  constants.BBSlot,
						Message:   maintain.GetMessage(),
						StartTime: carbon.Parse(maintain.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}, nil
	}

	// 取得選單資料
	menuInfo, err := l.svcCtx.GameCtx.CacheMenuInfo(constants.BBSlot)
	if err != nil {
		return nil, err
	}

	// 取得遊戲產品相關設定
	gameSettingInfo, err := l.svcCtx.GameCtx.CacheGameSettingInfo(constants.BBSlot, hallId)
	if err != nil {
		return nil, err
	}

	// 取得開放的遊戲產品列表(過濾排除廳、開關...等)
	openGames := getOpenGames(hallId, gameSettingInfo, req.IsMobile, false)

	// 未登入且為PC版才取試玩遊戲
	isDemo := false
	demoGames := map[uint32]*gameclient.GameInfo{}
	if !isLogin && !req.IsMobile {
		for menuId := range menuInfo {
			if menuId == constants.DemoGameMenuID {
				isDemo = true
				// 取得試玩的遊戲產品列表
				demoGames = getOpenGames(hallId, gameSettingInfo, req.IsMobile, isDemo)
				break
			}
		}
	}

	lang := common.ConvertLang(req.Lang)

	// 取得遊戲分類名稱
	menuNameList, err := l.svcCtx.GameCtx.CacheMenuName(lang)
	if err != nil {
		return nil, err
	}

	// 組合選單資料
	gameMenuDataTmp := []GameMenuData{}
	topIdList := make([]uint32, 0, len(menuInfo))
	sortList := make([]uint32, 0, len(menuInfo))

	for menuId, data := range menuInfo {
		checkGameTypeList := openGames

		// 遊戲分類為「試玩遊戲」，則使用試玩遊戲的產品列表做驗證
		if menuId == constants.DemoGameMenuID {
			if !isDemo {
				continue
			}

			checkGameTypeList = demoGames
		}

		// 排除 menuInfo 未開放的遊戲產品
		games := getMenuInfoOpenGame(data.Games, checkGameTypeList)

		// 如遊戲產品數量為0，則不顯示該分類
		if len(games) == 0 {
			continue
		}

		// 最新遊戲只顯示20筆
		if menuId == constants.NewGameMenuID {
			if len(games) >= constants.NewGameMaxLimit {
				games = games[0:constants.NewGameMaxLimit]
			}
		}

		// 熱門遊戲只顯示40筆
		if menuId == constants.HotGameMenuID {
			if len(games) >= constants.HotGameMaxLimit {
				games = games[0:constants.HotGameMaxLimit]
			}
		}

		topIdList = append(topIdList, data.TopID)
		sortList = append(sortList, data.Sort)

		gameMenuDataTmp = append(gameMenuDataTmp, GameMenuData{
			ID:        menuId,
			TopID:     data.TopID,
			Sort:      data.Sort,
			Name:      getMenuName(menuId, menuNameList),
			Games:     games,
			GameCount: len(games),
		})
	}

	// 確保大分類會先執行，因此先進行topId的排序
	slices.Sort(topIdList)
	topIdList = slices.Compact(topIdList)

	// 依sort進行排序
	slices.Sort(sortList)
	sortList = slices.Compact(sortList)

	// 依據遊戲分類排序並組合資料
	gameMenuData := getGameMenuData(topIdList, sortList, gameMenuDataTmp)

	return &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: gameMenuData,
		},
	}, nil
}

// 取得有開放的遊戲產品列表
func getOpenGames(hallId uint32, gameSettingInfo map[uint32]*gameclient.GameInfo, isMobile bool, isDemo bool) map[uint32]*gameclient.GameInfo {
	openGames := map[uint32]*gameclient.GameInfo{}
	for gameType, gameSet := range gameSettingInfo {
		// 大開關
		if !gameSet.GetEnable() {
			continue
		}

		// 手機版開關
		if isMobile && !gameSet.GetMobileEnable() {
			continue
		}

		if !isMobile {
			// PC版開關
			if !gameSet.GetPcEnable() {
				continue
			}

			// 是否開放試玩
			if isDemo && !gameSet.GetDemoEnable() {
				continue
			}
		}

		// 排除廳，白名單優先於黑名單
		isAllow := false
		for _, allow := range gameSet.GetAllowList() {
			if hallId == allow {
				isAllow = true
				break
			}
		}

		isBlock := false
		for _, block := range gameSet.GetBlockList() {
			if hallId == block {
				isBlock = true
				break
			}
		}

		if !isAllow && isBlock {
			continue
		}

		openGames[gameType] = gameSet
	}

	return openGames
}

// 取得選單資料有開放的遊戲產品
func getMenuInfoOpenGame(menuInfoGames []types.MenuInfoGameData, openGames map[uint32]*gameclient.GameInfo) []uint32 {
	games := []uint32{}
	for _, game := range menuInfoGames {
		for _, check := range openGames {
			if game.GameType == check.GetGameType() {
				games = append(games, game.GameType)
				break
			}
		}
	}

	return games
}

// 取得遊戲選單名稱
func getMenuName(menuId uint32, menuNameList []*gameclient.MenuInfo) string {
	name := ""
	for _, menuName := range menuNameList {
		if menuName.GetMenuId() == menuId {
			name = menuName.GetMenuName()
			break
		}
	}

	return name
}

// 依據遊戲分類排序並組合資料
func getGameMenuData(topIdList []uint32, sortList []uint32, gameMenuDataTmp []GameMenuData) []GameMenuData {
	// 組 lower 資料到上層選單
	for _, topId := range topIdList {
		if topId == 0 {
			continue
		}

		// 組 lower 資料
		lowerData := map[uint32]GameMenuData{}
		for _, data := range gameMenuDataTmp {
			if data.TopID == topId {
				lowerData[data.ID] = data
			}
		}

		// lower 資料存到上層選單
		for index, data := range gameMenuDataTmp {
			if data.ID == topId {
				gameMenuDataTmp[index].Lower = lowerData
				break
			}
		}
	}

	// 排序選單資料
	sortGameMenuData := []GameMenuData{}
	for _, sort := range sortList {
		for _, data := range gameMenuDataTmp {
			if data.TopID == 0 && data.Sort == sort {
				sortGameMenuData = append(sortGameMenuData, data)
			}
		}
	}

	return sortGameMenuData
}
