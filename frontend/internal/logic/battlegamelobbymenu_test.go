package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/session/sessionclient"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestBattleGameLobbyMenuLogic_WithSessionID(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBBattle).Return(seeder.BattleCacheMenuInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.BattleGameMenuName.GetMenuList(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: BattleGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        constants.AllGameMenuID,
					Sort:      seeder.BattleMenuSort,
					Name:      "全部",
					Games:     []uint32{seeder.GoldenFlowerGameType},
					GameCount: 1,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLogic_WithSessionID_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLogic_WithSessionID_GetUserByUserIdError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLogic_WithSessionID_UserIsBankrupt(t *testing.T) {
	seeder.User.Bankrupt = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.BankruptError)
	assert.Nil(t, resp)

	seeder.User.Bankrupt = false
}

func TestBattleGameLobbyMenuLogic_WithSessionID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo().GetLobbySwitch()
	seeder.GetSession.RdInfo.LobbySwitch = []*sessionclient.LobbySwitch{
		{
			GameKind: constants.BBBattle,
			Switch:   false,
		},
	}

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.GetSession.RdInfo.LobbySwitch = originLobbySwitch
}

func TestBattleGameLobbyMenuLogic_WithSessionID_GetMaintainByGameKindFromRedisError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLogic_WithSessionID_GameIsUnderMaintenance(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(&seeder.GetMaintainByGameKindFromRedis, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBBattle,
					Message:   seeder.GetMaintainByGameKindFromRedis.GetMessage(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLogic_WithSessionID_CacheMenuInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBBattle).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLogic_WithSessionID_CacheMenuNameError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBBattle).Return(seeder.BattleCacheMenuInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLogic_WithHallID(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("CacheMenuInfo", constants.BBBattle).Return(seeder.BattleCacheMenuInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.BattleGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: BattleGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        constants.AllGameMenuID,
					Sort:      seeder.BattleMenuSort,
					Name:      "全部",
					Games:     []uint32{seeder.GoldenFlowerGameType},
					GameCount: 1,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLogic_WithHallID_GetLobbySwitchByHallIDError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLogic_WithHallID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.HallIDLobbySwitch.GetLobbySwitch()
	seeder.HallIDLobbySwitch.LobbySwitch = []*gameclient.LobbySwitch{
		{
			GameKind: constants.BBBattle,
			Switch:   false,
		},
	}

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenu(&types.BattleGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.HallIDLobbySwitch.LobbySwitch = originLobbySwitch
}
