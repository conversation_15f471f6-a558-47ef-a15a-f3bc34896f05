package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/session/sessionclient"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestFishGameLobbyMenuLinkLogic_WithSessionID(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(seeder.FishCacheGameSettingInfo, nil)

	fishGameCtx.On("SetCacheGameSwitchByUser", seeder.User.GetId(), "pc", &seeder.FishGameSwitch)

	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)
	gameCtx.On("CacheGameKindList").Return(seeder.GameKindListResponse.GetGameKindList(), nil)
	gameCtx.On("CacheGameList", constants.BBFish, constants.ZhTw).Return(seeder.FishGameList.GetData(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: FishGameLobbyMenuLinkResponse{
			GameList: []types.CasinoLinkData{
				{
					GameKind: constants.BBFish,
					GameID:   seeder.FishingMasterGameType,
					Name:     seeder.FishingMasterGameName,
					Icon:     "",
					Link:     "http://localhost/api/entrance/gameroutepage?game_id=38001&game_kind=38&hall_id=3820474&is_mobile=false&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
					RuleLink: "",
					GameHall: "BBGame",
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionIDAndIsMobile(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "mobile").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(seeder.FishCacheGameSettingInfo, nil)

	fishGameCtx.On("SetCacheGameSwitchByUser", seeder.User.GetId(), "mobile", &seeder.FishGameSwitch)

	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)
	gameCtx.On("CacheGameKindList").Return(seeder.GameKindListResponse.GetGameKindList(), nil)
	gameCtx.On("CacheGameList", constants.BBFish, constants.ZhTw).Return(seeder.FishGameList.GetData(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
		IsMobile:  true,
	})

	expectedResponse := &types.BaseResponse{
		Data: FishGameLobbyMenuLinkResponse{
			GameList: []types.CasinoLinkData{
				{
					GameKind: constants.BBFish,
					GameID:   seeder.FishingMasterGameType,
					Name:     seeder.FishingMasterGameName,
					Icon:     "",
					Link:     "http://localhost/api/entrance/gameroutepage?game_id=38001&game_kind=38&hall_id=3820474&is_mobile=true&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
					RuleLink: "",
					GameHall: "BBGame",
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo().GetLobbySwitch()
	seeder.GetSession.RdInfo.LobbySwitch = []*sessionclient.LobbySwitch{
		{
			GameKind: constants.BBFish,
			Switch:   false,
		},
	}

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(seeder.FishCacheGameSettingInfo, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.GetSession.RdInfo.LobbySwitch = originLobbySwitch
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GetUserByUserIdError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GetUserByUserId_BankruptError(t *testing.T) {
	seeder.User.Bankrupt = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.BankruptError)
	assert.Nil(t, resp)

	seeder.User.Bankrupt = false
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GetCacheGameSwitchByUser_GameIsNotOpen(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	mockGameSwitch := false
	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return(&mockGameSwitch, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GameIsUnderMaintenance(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(&seeder.GetMaintainByGameKindFromRedis, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBFish,
					Message:   seeder.GetMaintainByGameKindFromRedis.GetMessage(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GetMaintainByGameKindFromRedisError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		Lang:      constants.ZhTw,
	})

	assert.Nil(t, resp)
	assert.Equal(t, err, errorx.ConnectionFailed)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_CacheGameInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_CacheGameSettingInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_getFishSwitchError_GameIsNotOpen(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(map[uint32]*gameclient.GameInfo{}, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithSessionID_GameIconKindError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByUser", seeder.User.GetId(), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(seeder.FishCacheGameSettingInfo, nil)

	fishGameCtx.On("SetCacheGameSwitchByUser", seeder.User.GetId(), "pc", &seeder.FishGameSwitch)

	gameCtx.On("GameIconKind").Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithHallID(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByHall", uint32(seeder.BgpHallID), "pc").Return((*bool)(nil), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBFish).Return(nil, errorx.MaintainNotFound)

	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(seeder.FishCacheGameSettingInfo, nil)

	fishGameCtx.On("SetCacheGameSwitchByHall", uint32(seeder.BgpHallID), "pc", &seeder.FishGameSwitch)

	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBFish, "pc", constants.ZhTw).Return([]types.CasinoLinkData(nil), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)
	gameCtx.On("CacheGameKindList").Return(seeder.GameKindListResponse.GetGameKindList(), nil)
	gameCtx.On("CacheGameList", constants.BBFish, constants.ZhTw).Return(seeder.FishGameList.GetData(), nil)

	mockGameLinkList := []types.CasinoLinkData{
		{
			GameKind: constants.BBFish,
			GameID:   seeder.FishingMasterGameType,
			Name:     seeder.FishingMasterGameName,
			Icon:     "",
			Link:     "",
			RuleLink: "",
			GameHall: "BBGame",
		},
	}
	gameCtx.On("SetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBFish, "pc", constants.ZhTw, mockGameLinkList)

	svcCtx.GameCtx = gameCtx
	svcCtx.FishGameCtx = fishGameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: FishGameLobbyMenuLinkResponse{
			GameList: mockGameLinkList,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithHallID_GetLobbySwitchByHallIDError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithHallID_GetCacheGameSwitchByHall_GameIsNotOpen(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	mockGameSwitch := false
	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByHall", uint32(seeder.BgpHallID), "pc").Return(&mockGameSwitch, nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.FishGameCtx = fishGameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_WithHallID_GetCacheGuestCasinoLink(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("GetCacheGameSwitchByHall", uint32(seeder.BgpHallID), "pc").Return((*bool)(nil), nil)
	fishGameCtx.On("CacheGameInfo").Return(seeder.FishGameDetailInfo.GetGameInfo(), nil)

	gameCtx.On("CacheGameSettingInfo", constants.BBFish, seeder.User.GetDomain()).Return(seeder.FishCacheGameSettingInfo, nil)

	fishGameCtx.On("SetCacheGameSwitchByHall", uint32(seeder.BgpHallID), "pc", &seeder.FishGameSwitch)

	mockGameLinkList := []types.CasinoLinkData{
		{
			GameKind: constants.BBFish,
			GameID:   seeder.FishingMasterGameType,
			Name:     seeder.FishingMasterGameName,
			Icon:     "",
			Link:     "",
			RuleLink: "",
			GameHall: "BBGame",
		},
	}

	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBFish, "pc", constants.ZhTw).Return(mockGameLinkList, nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.FishGameCtx = fishGameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.FishGameLobbyMenuLink(&types.FishGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: FishGameLobbyMenuLinkResponse{
			GameList: mockGameLinkList,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestFishGameLobbyMenuLinkLogic_getGameLinkList_GameIconKindError(t *testing.T) {
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameIconKind").Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.getGameLinkList(&fishGameSwitchInfo{}, constants.ZhTw, true, seeder.Session, seeder.BgpHallID, false)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_getGameLinkList_CacheGameKindListError(t *testing.T) {
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)
	gameCtx.On("CacheGameKindList").Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.getGameLinkList(&fishGameSwitchInfo{}, constants.ZhTw, true, seeder.Session, seeder.BgpHallID, false)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestFishGameLobbyMenuLinkLogic_getGameLinkList_CacheGameListError(t *testing.T) {
	fishGameSwitch := &fishGameSwitchInfo{
		Switch:           true,
		OpenGameList:     map[uint32]*gameclient.GameInfo{},
		OpenGameKindList: []uint32{constants.BBFish},
	}

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)
	gameCtx.On("CacheGameKindList").Return(seeder.GameKindListResponse.GetGameKindList(), nil)
	gameCtx.On("CacheGameList", constants.BBFish, constants.ZhTw).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewFishGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.getGameLinkList(fishGameSwitch, constants.ZhTw, true, seeder.Session, seeder.BgpHallID, false)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}
