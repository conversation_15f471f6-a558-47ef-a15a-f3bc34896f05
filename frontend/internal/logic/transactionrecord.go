package logic

import (
	"fmt"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/frontend/internal/wallet"
	"gbh/session/sessionclient"
	"math"
	"slices"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"
)

type TransactionRecordLogic struct {
	logx.Logger
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

type entryDetail struct {
	Amount     string     `json:"amount"`
	Balance    string     `json:"balance"`
	CreateTime string     `json:"create_time"`
	OpCode     uint32     `json:"op_code"`
	Note       noteDetail `json:"note"`
}

type noteDetail struct {
	RefID  string `json:"ref_id"`
	Result string `json:"result"`
}

type opCodeDetail struct {
	OpCodeCategoryList []string
	OpCodeList         []uint32
}

type transactionRecordResponse struct {
	TransactionRecord []entryDetail          `json:"transaction_record"`
	Pagination        types.PaginateResponse `json:"pagination"`
	Total             types.Total            `json:"total"`
	LimitDate         string                 `json:"limit_date"`
	MaintainInfo      []types.MaintainInfo   `json:"maintain_info"`
}

func NewTransactionRecordLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *TransactionRecordLogic {
	return &TransactionRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TransactionRecordLogic) TransactionRecord(req *types.TransactionRecord) (*types.BaseResponse, error) {
	session, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
	if sessionErr != nil {
		return nil, sessionErr
	}

	isWhiteListIP := common.CheckWhiteListIP(l.ctx.ClientIP(), session.GetWhitelist())
	maintainInfo := make([]types.MaintainInfo, 0, len(session.GetIsMaintaining()))
	if !isWhiteListIP {
		for _, v := range session.GetIsMaintaining() {
			checkLobbySwitch := checkLobbySwitch(session.GetRdInfo().GetLobbySwitch(), v.GetGameKind())
			if checkLobbySwitch {
				maintainInfo = append(maintainInfo, types.MaintainInfo{
					GameKind:  v.GetGameKind(),
					StartTime: carbon.Parse(v.GetBeginAt(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(v.GetEndAt(), constants.TimezoneGMT4).ToRfc3339String(),
					Message:   v.GetMsg(),
				})
			}
		}
	}

	accountCloseDate, accountCloseDateErr := l.svcCtx.WalletCtx.GetAccountCloseDate()
	if accountCloseDateErr != nil {
		return nil, accountCloseDateErr
	}

	startDate := carbon.Parse(req.StartDate, constants.TimezoneGMT4).StartOfDay()
	endDate := carbon.Parse(req.EndDate, constants.TimezoneGMT4).EndOfDay()

	// 原邏輯：startDate & endDate皆在關帳日期之前，endDate才會設為關帳日
	if startDate.ToDateString() < accountCloseDate.GetDate() {
		startDate = carbon.Parse(accountCloseDate.GetDate()).StartOfDay()

		if endDate.ToDateString() < accountCloseDate.GetDate() {
			endDate = carbon.Parse(accountCloseDate.GetDate()).EndOfDay()
		}
	}

	// op code對應
	opCodeList, opCodeListErr := getOpCodeList(l.svcCtx, req.GameKind, req.Type)
	if opCodeListErr != nil {
		return nil, opCodeListErr
	}

	// 查詢全部遊戲 or 查詢NBB體育，才要撈取NBB體育的特定op code
	newBBopCodeList := []uint32{}
	var newBBopCodeListErr error
	if req.GameKind == nil || *req.GameKind == constants.NewBBSport {
		newBBopCodeList, newBBopCodeListErr = getNewBBOpCodeList(l.svcCtx)
		if newBBopCodeListErr != nil {
			return nil, newBBopCodeListErr
		}
	}

	entryListRequest := wallet.GetEntryListRequest{
		UserID:      session.GetUser().GetId(),
		StartTime:   startDate.ToRfc3339String(),
		EndTime:     endDate.ToRfc3339String(),
		Sort:        req.Sort,
		FirstResult: req.Page,
		MaxResults:  constants.PageLimit,
	}

	// 查詢單一遊戲，要帶入op code group給ACC
	uniqueOpCodeGroup := []string{}
	if req.GameKind != nil {
		seen := map[string]bool{}

		// 過濾重複的op code group
		for _, item := range opCodeList.OpCodeCategoryList {
			if !seen[item] {
				uniqueOpCodeGroup = append(uniqueOpCodeGroup, item)
				seen[item] = true
			}
		}
		entryListRequest.OpCodeGroup = uniqueOpCodeGroup
	}

	entryList, entryListErr := l.svcCtx.WalletCtx.GetEntryList(entryListRequest)
	if entryListErr != nil {
		return nil, entryListErr
	}

	// 若往來紀錄資料筆數不為0，再向另外一支API取得同條件下的總計資料
	var TotalSum float64
	if len(entryList.GetEntryInfo()) > 0 {
		totalAmountRequest := wallet.GetTotalAmountRequest{
			UserID:    session.GetUser().GetId(),
			StartTime: startDate.ToRfc3339String(),
			EndTime:   endDate.ToRfc3339String(),
		}

		// 查詢單一遊戲，要帶入op code group給ACC
		if req.GameKind != nil {
			totalAmountRequest.OpCodeGroup = uniqueOpCodeGroup
		}

		totalAmount, totalAmountErr := l.svcCtx.WalletCtx.GetTotalAmount(totalAmountRequest)
		if totalAmountErr != nil {
			return nil, totalAmountErr
		}

		TotalSum = math.Floor(totalAmount.GetTotal()*constants.OneHundred) / constants.OneHundred
	}

	entryInfo := []entryDetail{}
	for _, v := range entryList.GetEntryInfo() {
		// 若有奇怪的opcode不在DB的資料裡面則忽略
		isOpCodeKnown := slices.Contains(opCodeList.OpCodeList, v.GetOpCode())
		if !isOpCodeKnown {
			continue
		}

		// 特定op code須產生備註
		var note string
		isNewBBOpCodeValid := slices.Contains(newBBopCodeList, v.GetOpCode())
		if isNewBBOpCodeValid {
			note = v.GetMemo()
		}

		amount := math.Floor(v.GetAmount()*constants.OneHundred) / constants.OneHundred
		balance := math.Floor(v.GetBalance()*constants.OneHundred) / constants.OneHundred

		entryInfo = append(entryInfo, entryDetail{
			Amount:     fmt.Sprintf("%.2f", amount),
			Balance:    fmt.Sprintf("%.2f", balance),
			CreateTime: carbon.Parse(v.GetCreatedTime(), constants.TimezoneGMT4).ToRfc3339String(),
			OpCode:     v.GetOpCode(),
			Note: noteDetail{
				RefID:  v.GetRefId(),
				Result: note,
			},
		})
	}

	// 頁數計算
	pageNum := math.Ceil(float64(entryList.GetPagination().GetTotal()) / constants.PageLimit)
	if pageNum == 0 {
		pageNum = 1
	}

	transactionRecord := transactionRecordResponse{
		TransactionRecord: entryInfo,
		Pagination: types.PaginateResponse{
			Page:        req.Page,
			PageLimit:   constants.PageLimit,
			TotalNumber: entryList.GetPagination().GetTotal(),
			TotalPage:   uint32(pageNum),
		},
		Total: types.Total{
			Amount: fmt.Sprintf("%.2f", TotalSum),
		},
		LimitDate:    accountCloseDate.GetDate(),
		MaintainInfo: maintainInfo,
	}

	resp := &types.BaseResponse{
		Data: transactionRecord,
	}

	return resp, nil
}

// 取得每種opcode對應
func getOpCodeList(svcCtx *svc.ServiceContext, gameKind *uint32, resultType *string) (*opCodeDetail, error) {
	gameKindAll := uint32(0)
	resultTypeAll := "All"
	request := wallet.GetOpCodeListRequest{}

	if gameKind != nil && gameKind != &gameKindAll {
		request.GameKind = gameKind
	}

	if resultType != nil && resultType != &resultTypeAll {
		request.ResultType = resultType
	}

	opCodeList, opCodeListErr := svcCtx.WalletCtx.GetOpCodeList(request)
	if opCodeListErr != nil {
		return nil, opCodeListErr
	}

	resp := &opCodeDetail{}
	for _, v := range opCodeList.GetOpcodeInfo() {
		resp.OpCodeList = append(resp.OpCodeList, v.GetId())
		resp.OpCodeCategoryList = append(resp.OpCodeCategoryList, v.GetCategoryId())
	}

	return resp, nil
}

func getNewBBOpCodeList(svcCtx *svc.ServiceContext) ([]uint32, error) {
	payoff := constants.Payoff
	gameKind := constants.NewBBSport
	cash := constants.Cash
	out := constants.Out

	request := wallet.GetOpCodeListRequest{
		ResultType: &payoff,
		GameKind:   &gameKind,
	}
	payoffOpCodeList, opCodeListErr := svcCtx.WalletCtx.GetOpCodeList(request)
	if opCodeListErr != nil {
		return nil, opCodeListErr
	}

	request = wallet.GetOpCodeListRequest{
		ResultType: &cash,
		GameKind:   &gameKind,
		DetailType: &out,
	}
	cashOpCodeList, opCodeListErr := svcCtx.WalletCtx.GetOpCodeList(request)
	if opCodeListErr != nil {
		return nil, opCodeListErr
	}

	opCodeList := []uint32{}
	for _, v := range payoffOpCodeList.GetOpcodeInfo() {
		opCodeList = append(opCodeList, v.GetId())
	}

	for _, v := range cashOpCodeList.GetOpcodeInfo() {
		opCodeList = append(opCodeList, v.GetId())
	}

	return opCodeList, nil
}

func checkLobbySwitch(lobbySwitch []*sessionclient.LobbySwitch, gameKind uint32) bool {
	for _, v := range lobbySwitch {
		if v.GetGameKind() == gameKind && !v.GetSwitch() {
			return false
		}
	}

	return true
}
