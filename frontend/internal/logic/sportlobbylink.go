package logic

import (
	"encoding/base64"
	"html"
	"net/url"

	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/sportgame"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/proto/game"
	sessionProto "gbh/proto/session"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
)

type SportLobbyLinkLogic struct {
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

func NewSportLobbyLinkLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *SportLobbyLinkLogic {
	return &SportLobbyLinkLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SportLobbyLinkLogic) SportLobbyLink(req *types.SportLobbyLink) (*types.BaseResponse, error) {
	sessionID := req.SessionID
	hallID := req.HallID

	userID := uint32(0)
	// 有登入的情況
	if sessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(sessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		// 遊戲是否開放
		if !isUserGameKindOpen(session.GetRdInfo().GetLobbySwitch(), constants.NewBBSport) {
			return nil, errorx.GameIsNotOpen
		}

		userID = session.GetUser().GetId()
	}

	// 未登入的情況
	if sessionID == "" {
		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(hallID, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		// 遊戲是否開放
		if !isHallGameKindOpen(hallLobbySwitch) {
			return nil, errorx.GameIsNotOpen
		}
	}

	// 遊戲是否維護中
	// TODO - 原本維護是讀redis，但後續發現來源是acc的資料，故直接使用acc的資料
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKind(constants.NewBBSport, l.ctx.ClientIP())
	if maintainErr != nil {
		return nil, maintainErr
	}

	if maintain.GetIsMaintaining() && !maintain.GetInWhitelist() {
		return &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  constants.NewBBSport,
						Message:   maintain.GetMsg(),
						StartTime: carbon.Parse(maintain.GetBeginAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}, nil
	}

	oneTimeSessionID := ""
	if sessionID != "" {
		// TODO - 原本體育大廳沒有檢查會員停權，統一加上停權判斷
		userInfo, getUserErr := l.svcCtx.UserCtx.GetUserByUserId(userID)
		if getUserErr != nil {
			return nil, getUserErr
		}

		if userInfo.GetBankrupt() {
			return nil, errorx.BankruptError
		}

		// 取得一次性session
		oneTimeSession, oneTimeSessionErr := l.svcCtx.SessionCtx.OneTimeSession(sessionID)
		if oneTimeSessionErr != nil {
			// TODO － 原本體育大廳客端邏輯，當取得一次性session失敗的話，會重撈SessionId，再重新取一次性session
			return nil, oneTimeSessionErr
		}

		oneTimeSessionID = oneTimeSession.GetSession()
	}

	device := uint32(0)
	if req.IsMobile {
		device = 1
	}
	gameLinkRequest := sportgame.GameLinkRequest{
		Lang:   common.ConvertLang(req.Lang),
		Device: &device,
	}

	if oneTimeSessionID != "" {
		gameLinkRequest.OneTimeSession = &oneTimeSessionID
	}

	if req.EnterPage != "" {
		gameLinkRequest.EnterPage = &req.EnterPage
	}

	if device == 1 {
		exitOption := constants.ExitOptionWithURL
		exitURLParam := encryptExitURL(req.DomainURL)
		gameLinkRequest.ExitOption = &exitOption
		gameLinkRequest.ExitURLParam = &exitURLParam
	}

	sportGameLink, sportGameLinkErr := l.svcCtx.SportGameCtx.GameLink(gameLinkRequest)
	if sportGameLinkErr != nil {
		return nil, sportGameLinkErr
	}

	gameLink := getDomainScheme(req.DomainURL) + sportGameLink.GetUrl()

	return &types.BaseResponse{
		Data: map[string]interface{}{
			"link": gameLink,
		},
	}, nil
}

func encryptExitURL(exitURL string) string {
	exitURL = "exit_url=" + exitURL
	exitURL = base64.StdEncoding.EncodeToString([]byte(exitURL))
	exitURL = html.EscapeString(exitURL)

	firstIndex := 0
	lastIndex := len(exitURL) - 1
	runes := []rune(exitURL)

	last := runes[lastIndex]
	first := runes[firstIndex]

	runes[firstIndex] = last
	runes[lastIndex] = first

	return string(runes)
}

func getDomainScheme(domain string) string {
	parsedURL, err := url.Parse(domain)
	if err != nil {
		return constants.HTTPURLScheme
	}

	if parsedURL.Scheme == "https" {
		return constants.HTTPSURLScheme
	}

	return constants.HTTPURLScheme
}

func isHallGameKindOpen(lobbySwitch []*game.LobbySwitch) bool {
	for _, lobbySwitch := range lobbySwitch {
		if lobbySwitch.GetGameKind() == constants.NewBBSport {
			if lobbySwitch.GetSwitch() {
				return true
			}
		}
	}

	return false
}

func isUserGameKindOpen(lobbySwitch []*sessionProto.LobbySwitch, gameKind uint32) bool {
	for _, lobbySwitch := range lobbySwitch {
		if lobbySwitch.GetGameKind() == gameKind {
			if lobbySwitch.GetSwitch() {
				return true
			}
		}
	}

	return false
}
