package logic

import (
	"fmt"
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/lang"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/proto/battlegame"
	"gbh/proto/fishgame"
	"strconv"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestGameRoutePageLogic(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBLive, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBLive, uint32(seeder.BgpHallID)).Return(&seeder.GameDomain, nil)

	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBLive,
		GameID:    3001,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.NoError(t, err)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_SlotGameLink(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBSlot, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBSlot, uint32(seeder.BgpHallID)).Return(&seeder.GameDomain, nil)

	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBSlot,
		GameID:    seeder.LaBarGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	gameLink := l.getSlotGameLink(seeder.BgpHallID, constants.ZhCn, seeder.OneTimeSession.GetSession(), seeder.LaBarGameType, false, seeder.GameDomain.GetDomain()[0])

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_FishGameLink(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBFish, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBFish, uint32(seeder.BgpHallID)).Return(&seeder.GameDomain, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("LinkList", &fishgame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &fishgame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.FishLinkList.GetLinks(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.FishGameCtx = fishGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBFish,
		GameID:    seeder.FishingMasterGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.NoError(t, err)

	gameLink, err := l.getFishGameLink(seeder.Session, constants.ZhCn, seeder.FishingMasterGameType, false, seeder.GameDomain.GetDomain()[0])

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_BattleGameLink(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBBattle, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	battleGameCtx := mock.NewBattleGameCtx()
	battleGameCtx.On("LinkList", &battlegame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &battlegame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.BattleLinkList.GetLinks(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.BattleGameCtx = battleGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBBattle,
		GameID:    seeder.FishingExpertGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.NoError(t, err)

	gameLink, err := l.getBattleGameLink(seeder.Session, constants.ZhCn, seeder.FishingExpertGameType, false)

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_GetSessionInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBSlot,
		GameID:    seeder.LaBarGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_GetMaintainByGameKindError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBSlot, ginCtx.ClientIP()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBSlot,
		GameID:    seeder.LaBarGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_GameDomainError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBSlot, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBSlot, uint32(seeder.BgpHallID)).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBSlot,
		GameID:    seeder.LaBarGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_OneTimeSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBSlot, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBSlot, uint32(seeder.BgpHallID)).Return(&seeder.GameDomain, nil)

	sessionCtx.On("OneTimeSession", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBSlot,
		GameID:    seeder.LaBarGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_FishGame_LinkListError(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBFish, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("GameDomain", constants.BBFish, uint32(seeder.BgpHallID)).Return(&seeder.GameDomain, nil)

	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("LinkList", &fishgame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &fishgame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.FishGameCtx = fishGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBFish,
		GameID:    seeder.FishingMasterGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_BattleGame_LinkListError(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBBattle, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	battlegameGameCtx := mock.NewBattleGameCtx()
	battlegameGameCtx.On("LinkList", &battlegame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &battlegame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.BattleGameCtx = battlegameGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBBattle,
		GameID:    seeder.FishingExpertGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_GameIsNotOpen(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	mockGetLangRequest := lang.GetLangRequest{
		Lang:      constants.ZhCn,
		LangIndex: constants.LangGameNotOpen,
	}
	langCtx := mock.NewLangCtx()
	langCtx.On("GetLang", mockGetLangRequest).Return(seeder.ZhCnSNoPrivilege)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.LangCtx = langCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBLiveTip,
		GameID:    3001,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Equal(t, seeder.ZhCnSNoPrivilege, resp)
}

func TestGameRoutePageLogic_GameIsUnderMaintenance(t *testing.T) {
	seeder.GetMaintainByGameKind.IsMaintaining = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBSlot, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	getLangRequest := lang.GetLangRequest{
		Lang:      constants.ZhCn,
		LangIndex: constants.LangCodeBBCasino,
	}
	langCtx := mock.NewLangCtx()
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnBBCasino)

	getLangRequest.LangIndex = constants.LangCodeMaintainTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSMaintainTime)

	getLangRequest.LangIndex = constants.LangCodeBEIJINGTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSTimeOfBeijing)

	getLangRequest.LangIndex = constants.LangCodeUSEastTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSTimeOfEastUS)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.LangCtx = langCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:    seeder.BgpHallID,
		GameKind:  constants.BBSlot,
		GameID:    seeder.LaBarGameType,
		SessionID: seeder.Session,
		Lang:      constants.ZhCn,
		IsMobile:  false,
	})

	maintainResp := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBSlot,
					Message:   seeder.GetMaintainByGameKind.GetMsg(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKind.GetBeginAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKind.GetEndAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	maintainMsg := l.getMaintainMsg(*maintainResp, constants.LangCodeBBCasino, constants.ZhCn)

	result := fmt.Sprintf(`
<meta charset="utf-8">
<script>
	alert(%s);
	window.location.href = "%s";
</script>
`, strconv.Quote(maintainMsg), "/")

	assert.Equal(t, err, errorx.GameIsUnderMaintenance)
	assert.Equal(t, result, resp)

	seeder.GetMaintainByGameKind.IsMaintaining = false
}

func TestGameRoutePageLogic_checkMaintenance_BBFish(t *testing.T) {
	seeder.GetMaintainByGameKind.IsMaintaining = true

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBFish, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	getLangRequest := lang.GetLangRequest{
		Lang:      constants.ZhCn,
		LangIndex: constants.LangCodeBBFish,
	}
	langCtx := mock.NewLangCtx()
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnBBFish)

	getLangRequest.LangIndex = constants.LangCodeMaintainTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSMaintainTime)

	getLangRequest.LangIndex = constants.LangCodeBEIJINGTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSTimeOfBeijing)

	getLangRequest.LangIndex = constants.LangCodeUSEastTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSTimeOfEastUS)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.LangCtx = langCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.checkMaintenance(constants.BBFish, constants.ZhCn)

	maintainResp := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBFish,
					Message:   seeder.GetMaintainByGameKind.GetMsg(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKind.GetBeginAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKind.GetEndAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	maintainMsg := l.getMaintainMsg(*maintainResp, constants.LangCodeBBFish, constants.ZhCn)

	assert.Equal(t, err, errorx.GameIsUnderMaintenance)
	assert.Equal(t, maintainMsg, resp)

	seeder.GetMaintainByGameKind.IsMaintaining = false
}

func TestGameRoutePageLogic_checkMaintenance_BBBattle(t *testing.T) {
	seeder.GetMaintainByGameKind.IsMaintaining = true

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBBattle, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	getLangRequest := lang.GetLangRequest{
		Lang:      constants.ZhCn,
		LangIndex: constants.LangCodeBBBattle,
	}
	langCtx := mock.NewLangCtx()
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSBBCard)

	getLangRequest.LangIndex = constants.LangCodeMaintainTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSMaintainTime)

	getLangRequest.LangIndex = constants.LangCodeBEIJINGTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSTimeOfBeijing)

	getLangRequest.LangIndex = constants.LangCodeUSEastTime
	langCtx.On("GetLang", getLangRequest).Return(seeder.ZhCnSTimeOfEastUS)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.LangCtx = langCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.checkMaintenance(constants.BBBattle, constants.ZhCn)

	maintainResp := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBBattle,
					Message:   seeder.GetMaintainByGameKind.GetMsg(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKind.GetBeginAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKind.GetEndAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	maintainMsg := l.getMaintainMsg(*maintainResp, constants.LangCodeBBBattle, constants.ZhCn)

	assert.Equal(t, err, errorx.GameIsUnderMaintenance)
	assert.Equal(t, maintainMsg, resp)

	seeder.GetMaintainByGameKind.IsMaintaining = false
}

func TestGameRoutePageLogic_getSlotGameLink_IsMobile(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp := l.getSlotGameLink(seeder.BgpHallID, constants.ZhCn, seeder.OneTimeSession.GetSession(), seeder.LaBarGameType, true, seeder.GameDomain.GetDomain()[0])

	gameLink := "https://localhost/ipl/portal.php/game/httpredirect?client=2&domain=http%3A%2F%2Flocalhost%2F&gametype=5001&hallid=3820474&lang=zh-cn&ots=a676222bbe613047eacbb533c2f291e8b5a520e97c&p_service=gp&recommend=0&type=casinoiframe"

	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_getSlotGameLink_IsNotMobile(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp := l.getSlotGameLink(seeder.BgpHallID, constants.ZhCn, seeder.OneTimeSession.GetSession(), seeder.LaBarGameType, false, seeder.GameDomain.GetDomain()[0])

	gameLink := "https://localhost/ipl/portal.php/game/httpredirect?client=2&domain=http%3A%2F%2Flocalhost%2F&gametype=5001&hallid=3820474&lang=zh-cn&ots=a676222bbe613047eacbb533c2f291e8b5a520e97c&p_service=gp&recommend=1&type=casinoiframe"

	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_getMaintainMsg(t *testing.T) {
	maintainResp := &types.BaseResponse{}

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp := l.getMaintainMsg(*maintainResp, constants.LangCodeBBCasino, constants.ZhCn)

	assert.Empty(t, resp)
}

func TestGameRoutePageLogic_getFishGameLink_IsMobile(t *testing.T) {
	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("LinkList", &fishgame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &fishgame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.FishLinkList.GetLinks(), nil)

	svcCtx.FishGameCtx = fishGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getFishGameLink(seeder.Session, constants.ZhCn, seeder.FishingMasterGameType, true, seeder.GameDomain.GetDomain()[0])

	gameLink := "https://localhost/fish/mobilelink?test=1"

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_getFishGameLink_IsNotMobile(t *testing.T) {
	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("LinkList", &fishgame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &fishgame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.FishLinkList.GetLinks(), nil)

	svcCtx.FishGameCtx = fishGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getFishGameLink(seeder.Session, constants.ZhCn, seeder.FishingMasterGameType, false, seeder.GameDomain.GetDomain()[0])

	gameLink := "https://localhost/fish/pclink?test=1"

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_getFishGameLink_NoMatchGame(t *testing.T) {
	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("LinkList", &fishgame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &fishgame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.FishLinkList.GetLinks(), nil)

	svcCtx.FishGameCtx = fishGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getFishGameLink(seeder.Session, constants.ZhCn, seeder.LaBarGameType, false, seeder.GameDomain.GetDomain()[0])

	assert.NoError(t, err)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_getFishGameLink_LinkListError(t *testing.T) {
	fishGameCtx := mock.NewFishGameCtx()
	fishGameCtx.On("LinkList", &fishgame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &fishgame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.FishGameCtx = fishGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getFishGameLink(seeder.Session, constants.ZhCn, seeder.FishingMasterGameType, false, seeder.GameDomain.GetDomain()[0])

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Empty(t, resp)
}

func TestGameRoutePageLogic_getBattleGameLink_IsMobile(t *testing.T) {
	battleGameCtx := mock.NewBattleGameCtx()
	battleGameCtx.On("LinkList", &battlegame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &battlegame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.BattleLinkList.GetLinks(), nil)

	svcCtx.BattleGameCtx = battleGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getBattleGameLink(seeder.Session, constants.ZhCn, seeder.FishingExpertGameType, true)

	gameLink := "https://localhost/battle/mobilelink?test=1"

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_getBattleGameLink_IsNotMobile(t *testing.T) {
	battleGameCtx := mock.NewBattleGameCtx()
	battleGameCtx.On("LinkList", &battlegame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &battlegame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.BattleLinkList.GetLinks(), nil)

	svcCtx.BattleGameCtx = battleGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getBattleGameLink(seeder.Session, constants.ZhCn, seeder.FishingExpertGameType, false)

	gameLink := "https://localhost/battle/pclink?test=1"

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_getBattleGameLink_NoMatchGame(t *testing.T) {
	battleGameCtx := mock.NewBattleGameCtx()
	battleGameCtx.On("LinkList", &battlegame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &battlegame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(seeder.BattleLinkList.GetLinks(), nil)

	svcCtx.BattleGameCtx = battleGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getBattleGameLink(seeder.Session, constants.ZhCn, seeder.LaBarGameType, false)

	assert.NoError(t, err)
	assert.Equal(t, "/", resp)
}

func TestGameRoutePageLogic_getBattleGameLink_LinkListError(t *testing.T) {
	battleGameCtx := mock.NewBattleGameCtx()
	battleGameCtx.On("LinkList", &battlegame.LinkListRequest{
		Session:  seeder.Session,
		ClientIp: ginCtx.ClientIP(),
		Lang: &battlegame.StringValue{
			Value: constants.ZhCn,
		},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.BattleGameCtx = battleGameCtx

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.getBattleGameLink(seeder.Session, constants.ZhCn, seeder.FishingExpertGameType, false)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Empty(t, resp)
}

func TestGameRoutePageLogic_LotteryGameLink(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBLottery, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	gameDomain := "localhost"
	path := "/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FHKLT"

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:     seeder.BgpHallID,
		GameKind:   constants.BBLottery,
		SessionID:  seeder.Session,
		Lang:       constants.ZhCn,
		IsMobile:   false,
		Path:       path,
		GameDomain: gameDomain,
	})

	gameLink := l.getLotteryGameLink(seeder.OneTimeSession.GetSession(), constants.ZhCn, gameDomain, path, false)

	assert.NoError(t, err)
	assert.Equal(t, gameLink, resp)
}

func TestGameRoutePageLogic_LotteryGame_OneTimeSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.BBLottery, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	sessionCtx.On("OneTimeSession", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	gameDomain := "localhost"
	path := "/balv/bv/pilot?lang=zh-tw&referer_url=%2Fpt%2F..%2Fpantheon%2Favengers%2Favengers%2Forigin%2Fgame%2FHKLT"

	l := NewGameRoutePageLogic(ginCtx, svcCtx)
	resp, err := l.GameRoutePage(&types.GameRoutePageRequest{
		HallID:     seeder.BgpHallID,
		GameKind:   constants.BBLottery,
		SessionID:  seeder.Session,
		Lang:       constants.ZhCn,
		IsMobile:   false,
		Path:       path,
		GameDomain: gameDomain,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, "/", resp)
}
