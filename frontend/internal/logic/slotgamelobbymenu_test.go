package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/session/sessionclient"
	"gbh/utils/strutil"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestSlotGameLobbyMenuLogic_WithSessionID(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        seeder.SlotMenuID,
					TopID:     seeder.SlotTopID,
					Sort:      seeder.SlotMenuSort,
					Name:      "最新",
					Games:     []uint32{seeder.LaBarGameType},
					GameCount: 1,
					Lower:     nil,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_GetUserByUserIdError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_UserIsBankrupt(t *testing.T) {
	seeder.User.Bankrupt = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.BankruptError)
	assert.Nil(t, resp)

	seeder.User.Bankrupt = false
}

func TestSlotGameLobbyMenuLogic_WithSessionID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo().GetLobbySwitch()
	seeder.GetSession.RdInfo.LobbySwitch = []*sessionclient.LobbySwitch{
		{
			GameKind: constants.BBSlot,
			Switch:   false,
		},
	}

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.GetSession.RdInfo.LobbySwitch = originLobbySwitch
}

func TestSlotGameLobbyMenuLogic_WithSessionID_GetMaintainByGameKindFromRedisError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_GameIsUnderMaintenance(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(&seeder.GetMaintainByGameKindFromRedis, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBSlot,
					Message:   seeder.GetMaintainByGameKindFromRedis.GetMessage(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_CacheMenuInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_CacheGameSettingInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_CacheMenuNameError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_NewGameMaxLimit(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()

	mockSlotCacheMenuInfo := map[uint32]*types.MenuInfoData{}
	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{}

	menuSortGames := []types.MenuInfoGameData{}
	games := []uint32{}
	for i := 0; i <= constants.NewGameMaxLimit; i++ {
		// 假 GameType
		gameType := uint32(i)

		if i < constants.NewGameMaxLimit {
			games = append(games, gameType)
		}

		menuSortGames = append(menuSortGames, types.MenuInfoGameData{
			Sort:     uint32(i),
			GameType: gameType,
		})

		mockSlotCacheMenuInfo[constants.NewGameMenuID] = &types.MenuInfoData{
			MenuID: constants.NewGameMenuID,
			TopID:  seeder.SlotTopID,
			Depth:  seeder.SlotDepth,
			Sort:   seeder.SlotMenuSort,
			Games:  menuSortGames,
		}

		mockSlotCacheGameSettingInfo[gameType] = &gameclient.GameInfo{
			Lobby:    constants.BBSlot,
			GameType: gameType,
			Name:     "test_" + strutil.IntToString(i),
			Enable:   true,
			PcEnable: true,
		}
	}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(mockSlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        constants.NewGameMenuID,
					TopID:     seeder.SlotTopID,
					Sort:      seeder.SlotMenuSort,
					Name:      "最新",
					Games:     games,
					GameCount: len(games),
					Lower:     nil,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithSessionID_HotGameMaxLimit(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()

	mockSlotCacheMenuInfo := map[uint32]*types.MenuInfoData{}
	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{}

	menuSortGames := []types.MenuInfoGameData{}
	games := []uint32{}
	for i := 0; i <= constants.HotGameMaxLimit; i++ {
		// 假 GameType
		gameType := uint32(i)

		if i < constants.HotGameMaxLimit {
			games = append(games, gameType)
		}

		menuSortGames = append(menuSortGames, types.MenuInfoGameData{
			Sort:     uint32(i),
			GameType: gameType,
		})

		mockSlotCacheMenuInfo[constants.HotGameMenuID] = &types.MenuInfoData{
			MenuID: constants.HotGameMenuID,
			TopID:  seeder.SlotTopID,
			Depth:  seeder.SlotDepth,
			Sort:   seeder.SlotMenuSort,
			Games:  menuSortGames,
		}

		mockSlotCacheGameSettingInfo[gameType] = &gameclient.GameInfo{
			Lobby:    constants.BBSlot,
			GameType: gameType,
			Name:     "test_" + strutil.IntToString(i),
			Enable:   true,
			PcEnable: true,
		}
	}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(mockSlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        constants.HotGameMenuID,
					TopID:     seeder.SlotTopID,
					Sort:      seeder.SlotMenuSort,
					Name:      "熱門",
					Games:     games,
					GameCount: len(games),
					Lower:     nil,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        seeder.SlotMenuID,
					TopID:     seeder.SlotTopID,
					Sort:      seeder.SlotMenuSort,
					Name:      "最新",
					Games:     []uint32{seeder.LaBarGameType},
					GameCount: 1,
					Lower:     nil,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_GetLobbySwitchByHallIDError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.HallIDLobbySwitch.GetLobbySwitch()
	seeder.HallIDLobbySwitch.LobbySwitch = []*gameclient.LobbySwitch{
		{
			GameKind: constants.BBSlot,
			Switch:   false,
		},
	}

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.HallIDLobbySwitch.LobbySwitch = originLobbySwitch
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	games := []uint32{
		seeder.LaBarGameType,
	}

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        constants.DemoGameMenuID,
					TopID:     seeder.SlotTopID,
					Sort:      seeder.SlotMenuSort,
					Name:      "試玩",
					Games:     games,
					GameCount: len(games),
					Lower:     nil,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_EnableFalse(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:    constants.BBSlot,
			GameType: seeder.LaBarGameType,
			Name:     seeder.LaBarGameName,
			Enable:   false,
		},
	}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_PcEnableFalse(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:    constants.BBSlot,
			GameType: seeder.LaBarGameType,
			Name:     seeder.LaBarGameName,
			Enable:   true,
			PcEnable: false,
		},
	}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_DemoEnableFalse(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:      constants.BBSlot,
			GameType:   seeder.LaBarGameType,
			Name:       seeder.LaBarGameName,
			Enable:     true,
			PcEnable:   true,
			DemoEnable: false,
		},
	}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_IsAllow(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:      constants.BBSlot,
			GameType:   seeder.LaBarGameType,
			Name:       seeder.LaBarGameName,
			Enable:     true,
			PcEnable:   true,
			DemoEnable: true,
			AllowList:  []uint32{seeder.BgpHallID},
		},
	}
	games := []uint32{seeder.LaBarGameType}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        constants.DemoGameMenuID,
					TopID:     seeder.SlotTopID,
					Sort:      seeder.SlotMenuSort,
					Name:      "試玩",
					Games:     games,
					GameCount: len(games),
					Lower:     nil,
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_IsNotAllowAndIsBlock(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:      constants.BBSlot,
			GameType:   seeder.LaBarGameType,
			Name:       seeder.LaBarGameName,
			Enable:     true,
			PcEnable:   true,
			DemoEnable: true,
			AllowList:  []uint32{},
			BlockList:  []uint32{seeder.BgpHallID},
		},
	}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_WithLower(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	mockSlotCacheMenuInfo := map[uint32]*types.MenuInfoData{
		constants.NewGameMenuID: {
			MenuID: constants.NewGameMenuID,
			TopID:  0,
			Depth:  seeder.SlotDepth,
			Sort:   seeder.SlotMenuSort,
			Games: []types.MenuInfoGameData{
				{
					Sort:     seeder.LaBarGameSort,
					GameType: seeder.LaBarGameType,
				},
			},
		},
		constants.DemoGameMenuID: {
			MenuID: constants.DemoGameMenuID,
			TopID:  constants.NewGameMenuID,
			Depth:  seeder.SlotDepth,
			Sort:   seeder.SlotMenuSort,
			Games: []types.MenuInfoGameData{
				{
					Sort:     seeder.LaBarGameSort,
					GameType: seeder.LaBarGameType,
				},
			},
		},
	}
	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:      constants.BBSlot,
			GameType:   seeder.LaBarGameType,
			Name:       seeder.LaBarGameName,
			Enable:     true,
			PcEnable:   true,
			DemoEnable: true,
		},
	}
	games := []uint32{seeder.LaBarGameType}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(mockSlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{
				{
					ID:        constants.NewGameMenuID,
					Sort:      seeder.SlotMenuSort,
					Name:      "最新",
					Games:     games,
					GameCount: len(games),
					Lower: map[uint32]GameMenuData{
						constants.DemoGameMenuID: {
							ID:        constants.DemoGameMenuID,
							TopID:     constants.NewGameMenuID,
							Sort:      seeder.SlotMenuSort,
							Name:      "試玩",
							Games:     games,
							GameCount: len(games),
							Lower:     nil,
						},
					},
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_IsMobile(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID:   seeder.BgpHallID,
		Lang:     constants.ZhTw,
		IsMobile: true,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLogic_WithHallID_WithDemoGame_IsMobile_MobileEnableFalse(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:        constants.BBSlot,
			GameType:     seeder.LaBarGameType,
			Name:         seeder.LaBarGameName,
			Enable:       true,
			MobileEnable: false,
		},
	}

	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheMenuName", constants.ZhTw).Return(seeder.SlotGameMenuName.GetMenuList(), nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenu(&types.SlotGameLobbyMenu{
		HallID:   seeder.BgpHallID,
		Lang:     constants.ZhTw,
		IsMobile: true,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuResponse{
			GameMenu: []GameMenuData{},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
