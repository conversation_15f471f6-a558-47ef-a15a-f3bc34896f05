package logic

import (
	"errors"
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/utils/urlutil"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"
)

type FishGameLobbyMenuLinkLogic struct {
	logx.Logger
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

type FishGameLobbyMenuLinkResponse struct {
	GameList []types.CasinoLinkData `json:"game_list"`
}

type fishGameSwitchInfo struct {
	Switch           bool
	OpenGameList     map[uint32]*gameclient.GameInfo
	OpenGameKindList []uint32
}

func NewFishGameLobbyMenuLinkLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *FishGameLobbyMenuLinkLogic {
	return &FishGameLobbyMenuLinkLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FishGameLobbyMenuLinkLogic) FishGameLobbyMenuLink(req *types.FishGameLobbyMenuLink) (*types.BaseResponse, error) {
	isLogin := false
	hallId := uint32(0)
	userId := uint32(0)

	// 大廳遊戲開關
	lobbyGameSwitch := map[uint32]bool{}

	deviceTag := constants.DevicePc
	if req.IsMobile {
		deviceTag = constants.DeviceMobile
	}

	// 有登入
	if req.SessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		user, userErr := l.svcCtx.UserCtx.GetUserByUserId(session.GetUser().GetId())
		if userErr != nil {
			return nil, userErr
		}

		// 會員停權
		if user.GetBankrupt() {
			return nil, errorx.BankruptError
		}

		// 遊戲開關快取
		gameSwitch := l.svcCtx.FishGameCtx.GetCacheGameSwitchByUser(user.GetId(), deviceTag)
		if gameSwitch != nil && !*gameSwitch {
			return nil, errorx.GameIsNotOpen
		}

		for _, lobbySwitch := range session.GetRdInfo().GetLobbySwitch() {
			lobbyGameSwitch[lobbySwitch.GetGameKind()] = lobbySwitch.GetSwitch()
		}

		isLogin = true
		hallId = user.GetDomain()
		userId = user.GetId()
	}

	// 未登入
	if req.SessionID == "" {
		hallId = req.HallID

		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(hallId, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		// 遊戲開關快取
		gameSwitch := l.svcCtx.FishGameCtx.GetCacheGameSwitchByHall(hallId, deviceTag)
		if gameSwitch != nil && !*gameSwitch {
			return nil, errorx.GameIsNotOpen
		}

		for _, lobbySwitch := range hallLobbySwitch {
			lobbyGameSwitch[lobbySwitch.GetGameKind()] = lobbySwitch.GetSwitch()
		}
	}

	// 遊戲是否維護中
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKindFromRedis(constants.BBFish)
	if maintainErr != nil {
		if !errors.Is(maintainErr, errorx.MaintainNotFound) {
			return nil, maintainErr
		}
	}

	if maintain != nil {
		return &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  constants.BBFish,
						Message:   maintain.GetMessage(),
						StartTime: carbon.Parse(maintain.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}, nil
	}

	// 取得遊戲產品資訊
	gameInfo, err := l.svcCtx.FishGameCtx.CacheGameInfo()
	if err != nil {
		return nil, err
	}

	lobbyGameList := map[uint32][]uint32{}
	for _, game := range gameInfo {
		lobbyGameList[game.GetGameKind()] = append(lobbyGameList[game.GetGameKind()], game.GetId())
	}

	// 取捕魚遊戲開關及有開放的遊戲
	fishSwitch, err := l.getFishSwitchInfo(lobbyGameList, lobbyGameSwitch, hallId, req.IsMobile)
	if err != nil {
		return nil, err
	}

	if isLogin {
		l.svcCtx.FishGameCtx.SetCacheGameSwitchByUser(userId, deviceTag, &fishSwitch.Switch)
	} else {
		l.svcCtx.FishGameCtx.SetCacheGameSwitchByHall(hallId, deviceTag, &fishSwitch.Switch)
	}

	// 遊戲未開放
	if !fishSwitch.Switch {
		return nil, errorx.GameIsNotOpen
	}

	lang := common.ConvertLang(req.Lang)

	// 如未登入，則嘗試從Redis撈取資料
	if !isLogin {
		gameLinkList := l.svcCtx.GameCtx.GetCacheGuestCasinoLink(hallId, constants.BBFish, deviceTag, lang)

		if gameLinkList != nil {
			return &types.BaseResponse{
				Data: FishGameLobbyMenuLinkResponse{
					GameList: gameLinkList,
				},
			}, nil
		}
	}

	// 組遊戲資料
	gameLinkList, err := l.getGameLinkList(fishSwitch, lang, isLogin, req.SessionID, hallId, req.IsMobile)
	if err != nil {
		return nil, err
	}

	// 未登入，則把遊戲連結資料寫進Redis中(暫存10分鐘)
	if !isLogin {
		l.svcCtx.GameCtx.SetCacheGuestCasinoLink(hallId, constants.BBFish, deviceTag, lang, gameLinkList)
	}

	// TODO: 原程式 取得搶先看，但資料寫死設定檔且時間過舊，暫不實作

	return &types.BaseResponse{
		Data: FishGameLobbyMenuLinkResponse{
			GameList: gameLinkList,
		},
	}, nil
}

// 取捕魚遊戲開關及有開放的遊戲
func (l *FishGameLobbyMenuLinkLogic) getFishSwitchInfo(lobbyGameList map[uint32][]uint32, lobbyGameSwitch map[uint32]bool, hallId uint32, isMobile bool) (*fishGameSwitchInfo, error) {
	fishSwitch := false
	openGameList := map[uint32]*gameclient.GameInfo{}
	openGameKindList := []uint32{}

	// 判斷遊戲開放(任一款遊戲開放，捕魚專區即開放)
	for gameKind, gameTypeList := range lobbyGameList {
		isOpenGameKind := false

		if !lobbyGameSwitch[gameKind] {
			continue
		}

		gameSettingInfo, err := l.svcCtx.GameCtx.CacheGameSettingInfo(gameKind, hallId)
		if err != nil {
			return nil, err
		}

		// 取得開放的遊戲
		openGames := getOpenGames(hallId, gameSettingInfo, isMobile, false)

		for _, gameType := range gameTypeList {
			if openGames[gameType] != nil {
				fishSwitch = true
				isOpenGameKind = true
				openGameList[gameType] = openGames[gameType]
			}
		}

		if isOpenGameKind {
			openGameKindList = append(openGameKindList, gameKind)
		}
	}

	return &fishGameSwitchInfo{
		Switch:           fishSwitch,
		OpenGameList:     openGameList,
		OpenGameKindList: openGameKindList,
	}, nil
}

func (l *FishGameLobbyMenuLinkLogic) getGameLinkList(fishSwitch *fishGameSwitchInfo, lang string, isLogin bool, session string, hallId uint32, isMobile bool) ([]types.CasinoLinkData, error) {
	// 取得客端遊戲標籤
	gameIconKind, err := l.svcCtx.GameCtx.GameIconKind()
	if err != nil {
		return nil, err
	}

	// 取得遊戲大廳代碼
	gameKindList, err := l.svcCtx.GameCtx.CacheGameKindList()
	if err != nil {
		return nil, err
	}

	gameKindMap := map[uint32]string{}
	for _, game := range gameKindList {
		gameKindMap[game.GetGameKind()] = game.GetGameCode()
	}

	gameList := []*gameclient.GameList{}
	for _, gameKind := range fishSwitch.OpenGameKindList {
		// 取得遊戲產品列表
		gameListTmp, err := l.svcCtx.GameCtx.CacheGameList(gameKind, lang)
		if err != nil {
			return nil, err
		}

		gameList = append(gameList, gameListTmp...)
	}

	// 依序組合所需資料
	gameLinkList := make([]types.CasinoLinkData, 0, len(fishSwitch.OpenGameList))
	for gameType, game := range fishSwitch.OpenGameList {
		gameName := getGameName(game.GetGameType(), gameList)

		// 遊戲的Icon標籤
		icon := getGameIcon(game.GetOpenDate(), game.GetIconKind(), gameIconKind)

		gameLink := ""
		// 有登入才組遊戲連結
		if isLogin {
			gameLink = l.getFishGameLink(session, hallId, game.GetLobby(), gameType, lang, isMobile)
		}

		gameLinkList = append(gameLinkList, types.CasinoLinkData{
			GameKind: game.GetLobby(),              // 遊戲大項ID
			GameID:   gameType,                     // 遊戲產品ID
			Name:     gameName,                     // 遊戲產品名稱
			Icon:     icon,                         // 遊戲的Icon標籤
			Link:     gameLink,                     // 遊戲連結
			RuleLink: "",                           // 遊戲規則連結(38,66 固定回傳空)
			GameHall: gameKindMap[game.GetLobby()], // 遊戲大廳代碼
		})
	}

	return gameLinkList, nil
}

func (l *FishGameLobbyMenuLinkLogic) getFishGameLink(sessionId string, hallId uint32, gameKind uint32, gameId uint32, lang string, isMobile bool) string {
	linkParams := urlutil.NewBuilder()
	linkParams.AddUint32("hall_id", hallId)
	linkParams.AddUint32("game_kind", gameKind)
	linkParams.AddUint32("game_id", gameId)
	linkParams.AddString("lang", lang)
	linkParams.AddString("session_id", sessionId)
	linkParams.AddBool("is_mobile", isMobile)

	scheme := urlutil.GetRequestScheme(l.ctx)

	return scheme + constants.SchemeSeparator + l.ctx.Request.Host + constants.APIGameRoutePage + "?" + linkParams.Encode()
}
