package logic

import (
	"encoding/base64"
	"errors"
	"fmt"
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/lang"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/proto/battlegame"
	"gbh/proto/fishgame"
	"gbh/utils/urlutil"
	"net/http"
	"slices"
	"strconv"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"
)

type GameRoutePageLogic struct {
	logx.Logger
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

func NewGameRoutePageLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *GameRoutePageLogic {
	return &GameRoutePageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GameRoutePageLogic) GameRoutePage(req *types.GameRoutePageRequest) (string, error) {
	url := "/"
	domain := common.GetDomainWithoutPort(l.ctx.Request.Host)
	langConvert := common.ConvertLang(req.Lang)

	http.SetCookie(l.ctx.Writer, &http.Cookie{
		Name:     "lang",
		Value:    langConvert,
		Path:     "/",
		Domain:   domain,
		HttpOnly: false,
	})

	http.SetCookie(l.ctx.Writer, &http.Cookie{
		Name:     "SESSION_ID",
		Value:    req.SessionID,
		Path:     "/",
		Domain:   domain,
		HttpOnly: false,
	})

	http.SetCookie(l.ctx.Writer, &http.Cookie{
		Name:     "LOGINCHK",
		Value:    "Y",
		Path:     "/",
		Domain:   domain,
		HttpOnly: false,
	})

	sessionInfo, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
	if sessionErr != nil {
		return url, sessionErr
	}

	lobbySwitch := map[uint32]bool{}
	if sessionInfo.GetRdInfo().GetLobbySwitch() != nil {
		for _, v := range sessionInfo.GetRdInfo().GetLobbySwitch() {
			lobbySwitch[v.GetGameKind()] = v.GetSwitch()
		}
	}

	// 遊戲是否開放
	lobbySwitchMsg, lobbySwitchErr := l.checkLobbySwitch(lobbySwitch, req.GameKind, langConvert)
	if lobbySwitchErr != nil {
		return lobbySwitchMsg, lobbySwitchErr
	}

	// 遊戲分項維護
	maintainMsg, maintainErr := l.checkMaintenance(req.GameKind, langConvert)
	if maintainErr != nil {
		if errors.Is(maintainErr, errorx.GameIsUnderMaintenance) {
			result := fmt.Sprintf(`
<meta charset="utf-8">
<script>
	alert(%s);
	window.location.href = "%s";
</script>
`, strconv.Quote(maintainMsg), "/")

			return result, maintainErr
		}

		return maintainMsg, maintainErr
	}

	gameDomain := ""
	if !slices.Contains([]uint32{constants.BBBattle, constants.BBLottery}, req.GameKind) {
		// 取遊戲獨立網址
		gameDomainData, gameDomainErr := l.svcCtx.GameCtx.GameDomain(req.GameKind, req.HallID)
		if gameDomainErr != nil {
			return url, gameDomainErr
		}

		if len(gameDomainData.GetDomain()) > 0 {
			gameDomain = gameDomainData.GetDomain()[0]
		}
	}

	// 組遊戲連結
	switch req.GameKind {
	case constants.BBSlot:
		// 電子遊戲才取ots
		oneTimeSession, oneTimeSessionErr := l.svcCtx.SessionCtx.OneTimeSession(req.SessionID)
		if oneTimeSessionErr != nil {
			return url, oneTimeSessionErr
		}

		gameLink := l.getSlotGameLink(req.HallID, langConvert, oneTimeSession.GetSession(), req.GameID, req.IsMobile, gameDomain)

		return gameLink, nil
	case constants.BBFish:
		gameLink, err := l.getFishGameLink(req.SessionID, langConvert, req.GameID, req.IsMobile, gameDomain)
		if err != nil {
			return url, err
		}

		return gameLink, nil
	case constants.BBBattle:
		gameLink, err := l.getBattleGameLink(req.SessionID, langConvert, req.GameID, req.IsMobile)
		if err != nil {
			return url, err
		}

		return gameLink, nil
	case constants.BBLottery:
		oneTimeSession, oneTimeSessionErr := l.svcCtx.SessionCtx.OneTimeSession(req.SessionID)
		if oneTimeSessionErr != nil {
			return url, oneTimeSessionErr
		}

		gameLink := l.getLotteryGameLink(oneTimeSession.GetSession(), langConvert, req.GameDomain, req.Path, req.IsMobile)

		return gameLink, nil
	}

	return url, nil
}

func (l *GameRoutePageLogic) checkLobbySwitch(lobbySwitch map[uint32]bool, gameKind uint32, langConvert string) (string, error) {
	lobbyStatus := lobbySwitch[gameKind]

	if !lobbyStatus {
		// 遊戲未開放
		msg := l.svcCtx.LangCtx.GetLang(lang.GetLangRequest{
			Lang:      langConvert,
			LangIndex: constants.LangGameNotOpen,
		})

		return msg, errorx.GameIsNotOpen
	}

	return "", nil
}

func (l *GameRoutePageLogic) checkMaintenance(gameKind uint32, langConvert string) (string, error) {
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKind(gameKind, l.ctx.ClientIP())
	if maintainErr != nil {
		return "/", maintainErr
	}

	if maintain.GetIsMaintaining() && !maintain.GetInWhitelist() {
		maintainResp := &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  gameKind,
						Message:   maintain.GetMsg(),
						StartTime: carbon.Parse(maintain.GetBeginAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndAt()).SetTimezone(constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}

		langCode := ""
		switch gameKind {
		case constants.BBSlot:
			langCode = constants.LangCodeBBCasino
		case constants.BBFish:
			langCode = constants.LangCodeBBFish
		case constants.BBBattle:
			langCode = constants.LangCodeBBBattle
		}

		// 遊戲分項維護需回傳維護字串組合
		matainMsg := l.getMaintainMsg(*maintainResp, langCode, langConvert)

		return matainMsg, errorx.GameIsUnderMaintenance
	}

	return "", nil
}

func (l *GameRoutePageLogic) getMaintainMsg(data types.BaseResponse, langCode string, langConvert string) string {
	langRequest := lang.GetLangRequest{
		Lang:      langConvert,
		LangIndex: constants.LangCodeMaintainTime,
	}

	maintainInfo, ok := data.Data.(types.MaintainList)

	if !ok {
		return ""
	}

	maintainMsg := ""
	startTime := ""
	endTime := ""
	for _, info := range maintainInfo.MaintainInfo {
		maintainMsg = info.Message
		startTime = info.StartTime
		endTime = info.EndTime
	}

	maintainString := l.svcCtx.LangCtx.GetLang(langRequest)

	langRequest.LangIndex = langCode
	nbbString := l.svcCtx.LangCtx.GetLang(langRequest)

	langRequest.LangIndex = constants.LangCodeBEIJINGTime
	beijinTimeString := l.svcCtx.LangCtx.GetLang(langRequest)

	langRequest.LangIndex = constants.LangCodeUSEastTime
	usEastTimeStromg := l.svcCtx.LangCtx.GetLang(langRequest)

	beijinStartTime := carbon.Parse(startTime).SetTimezone(constants.TimezoneGMT4).SetTimezone(carbon.Taipei).ToDateTimeString()
	beijinEndTime := carbon.Parse(endTime).SetTimezone(constants.TimezoneGMT4).SetTimezone(carbon.Taipei).ToDateTimeString()

	startTime = carbon.Parse(startTime).SetTimezone(constants.TimezoneGMT4).ToDateTimeString()
	endTime = carbon.Parse(endTime).SetTimezone(constants.TimezoneGMT4).ToDateTimeString()

	result := fmt.Sprintf(`%s
%s(%s): %s ~ %s
%s(%s): %s ~ %s`,
		nbbString+" - "+maintainMsg,
		maintainString, beijinTimeString, beijinStartTime, beijinEndTime,
		maintainString, usEastTimeStromg, startTime, endTime,
	)

	return result
}

func (l *GameRoutePageLogic) getSlotGameLink(hallId uint32, langConvert string, ots string, gameId uint32, isMobile bool, gameDomain string) string {
	// 顯示推薦遊戲, 僅支援PC版
	recommend := "0"
	if !isMobile {
		recommend = "1"
	}

	scheme := urlutil.GetRequestScheme(l.ctx)

	params := urlutil.NewBuilder()
	params.AddString("type", "casinoiframe")
	params.AddUint32("hallid", hallId)
	params.AddString("domain", scheme+constants.SchemeSeparator+l.ctx.Request.Host+"/")
	params.AddString("lang", langConvert)
	params.AddString("ots", ots)
	params.AddUint32("gametype", gameId)
	params.AddString("recommend", recommend)
	params.AddString("client", "2")
	params.AddString("p_service", "gp")

	gameLink := fmt.Sprintf("https://%s/ipl/portal.php/game/httpredirect?%s",
		gameDomain,
		params.Encode(),
	)

	return gameLink
}

func (l *GameRoutePageLogic) getFishGameLink(session string, langConvert string, gameId uint32, isMobile bool, gameDomain string) (string, error) {
	linkList, err := l.svcCtx.FishGameCtx.LinkList(&fishgame.LinkListRequest{
		Session:  session,
		ClientIp: l.ctx.ClientIP(),
		Lang: &fishgame.StringValue{
			Value: langConvert,
		},
	})

	if err != nil {
		return "", err
	}

	paramLink := ""
	for _, v := range linkList {
		if v.GetGameId() == gameId {
			if isMobile {
				paramLink = v.GetMobile()
			} else {
				paramLink = v.GetPc()
			}
			break
		}
	}

	// 沒有找到對應的遊戲連結
	if paramLink == "" {
		return "/", nil
	}

	return fmt.Sprintf("https://%s%s", gameDomain, paramLink), nil
}

func (l *GameRoutePageLogic) getBattleGameLink(session string, langConvert string, gameId uint32, isMobile bool) (string, error) {
	linkList, err := l.svcCtx.BattleGameCtx.LinkList(&battlegame.LinkListRequest{
		Session:  session,
		ClientIp: l.ctx.ClientIP(),
		Lang: &battlegame.StringValue{
			Value: langConvert,
		},
	})

	if err != nil {
		return "", err
	}

	paramLink := "/"
	for _, v := range linkList {
		if v.GetGameId() == gameId {
			if isMobile {
				paramLink = v.GetMobile()
			} else {
				paramLink = v.GetPc()
			}
			break
		}
	}

	return paramLink, nil
}
func (l *GameRoutePageLogic) getLotteryGameLink(ots string, langConvert string, gameDomain string, path string, isMobile bool) string {
	scheme := urlutil.GetRequestScheme(l.ctx)
	domainURL := scheme + constants.SchemeSeparator + l.ctx.Request.Host

	// TODO - 原本彩票大廳客端邏輯還有帶入casino_logo，跟前端確認之後，我們不會存放各api業主的logo，故不帶入
	params := urlutil.NewBuilder()
	params.AddString("lang", langConvert)
	params.AddString("casino_url", base64.StdEncoding.EncodeToString([]byte(domainURL)))
	params.AddBoolToInt("mobile", isMobile)
	params.AddString("bbots", ots)
	params.AddString("param", lotteryDomainEncryption(domainURL))
	params.AddString("p_service", "gp")

	gameLink := fmt.Sprintf("%s://%s%s&%s",
		scheme,
		gameDomain,
		path,
		params.Encode(),
	)

	return gameLink
}

// 客端網址特殊加密
func lotteryDomainEncryption(domainURL string) string {
	// 將內容 經過base64加密
	encryptText := "exit_url=" + domainURL
	baseEncoded := base64.StdEncoding.EncodeToString([]byte(encryptText))

	// 前後兩個字母對調
	if len(baseEncoded) >= constants.BaseEncodedMinLength {
		head := baseEncoded[0]
		tail := baseEncoded[len(baseEncoded)-1]

		// 將 baseEncoded 轉為 byte array 修改內容
		b := []byte(baseEncoded)
		b[0] = tail
		b[len(b)-1] = head
		baseEncoded = string(b)
	}

	return baseEncoded
}
