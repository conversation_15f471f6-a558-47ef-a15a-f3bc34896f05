package logic

import (
	"context"
	"errors"
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"slices"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type BattleGameLobbyMenuLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type BattleGameLobbyMenuResponse struct {
	GameMenu []GameMenuData `json:"game_menu"`
}

func NewBattleGameLobbyMenuLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BattleGameLobbyMenuLogic {
	return &BattleGameLobbyMenuLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BattleGameLobbyMenuLogic) BattleGameLobbyMenu(req *types.BattleGameLobbyMenu) (*types.BaseResponse, error) {
	// 有登入
	if req.SessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		user, userErr := l.svcCtx.UserCtx.GetUserByUserId(session.GetUser().GetId())
		if userErr != nil {
			return nil, userErr
		}

		// 會員停權
		if user.GetBankrupt() {
			return nil, errorx.BankruptError
		}

		// 遊戲是否開放
		if !isUserGameKindOpen(session.GetRdInfo().GetLobbySwitch(), constants.BBBattle) {
			return nil, errorx.GameIsNotOpen
		}
	}

	// 未登入
	if req.SessionID == "" {
		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(req.HallID, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		// 遊戲是否開放
		if !isHallGameKindOpen(hallLobbySwitch) {
			return nil, errorx.GameIsNotOpen
		}
	}

	// 遊戲是否維護中
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKindFromRedis(constants.BBBattle)
	if maintainErr != nil {
		if !errors.Is(maintainErr, errorx.MaintainNotFound) {
			return nil, maintainErr
		}
	}

	if maintain != nil {
		return &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  constants.BBBattle,
						Message:   maintain.GetMessage(),
						StartTime: carbon.Parse(maintain.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}, nil
	}

	lang := common.ConvertLang(req.Lang)

	// 取得選單資料
	menuInfo, err := l.svcCtx.GameCtx.CacheMenuInfo(constants.BBBattle)
	if err != nil {
		return nil, err
	}

	// 取得遊戲分類名稱
	menuNameList, err := l.svcCtx.GameCtx.CacheMenuName(lang)
	if err != nil {
		return nil, err
	}

	// 組合選單資料
	gameMenuDataTmp := []GameMenuData{}
	sortList := make([]uint32, 0, len(menuInfo))

	for menuId, data := range menuInfo {
		sortList = append(sortList, data.Sort)

		games := []uint32{}
		for _, game := range data.Games {
			games = append(games, game.GameType)
		}

		gameMenuDataTmp = append(gameMenuDataTmp, GameMenuData{
			ID:        menuId,
			TopID:     data.TopID,
			Sort:      data.Sort,
			Name:      getMenuName(menuId, menuNameList),
			Games:     games,
			GameCount: len(data.Games),
		})
	}

	// 依sort進行排序
	slices.Sort(sortList)
	sortList = slices.Compact(sortList)

	menuData := getGameMenuData([]uint32{}, sortList, gameMenuDataTmp)

	return &types.BaseResponse{
		Data: BattleGameLobbyMenuResponse{
			GameMenu: menuData,
		},
	}, nil
}
