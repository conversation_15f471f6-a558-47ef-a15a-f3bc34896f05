package logic

import (
	"errors"
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/utils/strutil"
	"gbh/utils/urlutil"
	"slices"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"
)

type SlotGameLobbyMenuLinkLogic struct {
	logx.Logger
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

type SlotGameLobbyMenuLinkResponse struct {
	GameList      []types.CasinoLinkData `json:"game_list"`
	JackpotSwitch bool                   `json:"jackpot_switch"`
}

func NewSlotGameLobbyMenuLinkLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *SlotGameLobbyMenuLinkLogic {
	return &SlotGameLobbyMenuLinkLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SlotGameLobbyMenuLinkLogic) SlotGameLobbyMenuLink(req *types.SlotGameLobbyMenuLink) (*types.BaseResponse, error) {
	// 玩家是否登入
	isLogin := false
	hallId := uint32(0)

	// 有登入
	if req.SessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		user, userErr := l.svcCtx.UserCtx.GetUserByUserId(session.GetUser().GetId())
		if userErr != nil {
			return nil, userErr
		}

		// 會員停權
		if user.GetBankrupt() {
			return nil, errorx.BankruptError
		}

		// 遊戲是否開放
		if !isUserGameKindOpen(session.GetRdInfo().GetLobbySwitch(), constants.BBSlot) {
			return nil, errorx.GameIsNotOpen
		}

		isLogin = true
		hallId = user.GetDomain()
	}

	// 未登入
	if req.SessionID == "" {
		hallId = req.HallID

		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(hallId, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		// 遊戲是否開放
		if !isHallGameKindOpen(hallLobbySwitch) {
			return nil, errorx.GameIsNotOpen
		}
	}

	// 遊戲是否維護中
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKindFromRedis(constants.BBSlot)
	if maintainErr != nil {
		if !errors.Is(maintainErr, errorx.MaintainNotFound) {
			return nil, maintainErr
		}
	}

	if maintain != nil {
		return &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  constants.BBSlot,
						Message:   maintain.GetMessage(),
						StartTime: carbon.Parse(maintain.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}, nil
	}

	lang := common.ConvertLang(req.Lang)

	// 彩金開關
	jackpotSwitch, jackpotErr := l.svcCtx.JackpotCtx.CacheSwitch(hallId)
	if jackpotErr != nil {
		return nil, jackpotErr
	}

	deviceTag := constants.DevicePc
	if req.IsMobile {
		deviceTag = constants.DeviceMobile
	}

	// 如未登入，則嘗試從Redis撈取資料
	gameLinkList := make([]types.CasinoLinkData, 0)
	if !isLogin {
		gameLinkList = l.svcCtx.GameCtx.GetCacheGuestCasinoLink(hallId, constants.BBSlot, deviceTag, lang)

		if gameLinkList != nil {
			return &types.BaseResponse{
				Data: SlotGameLobbyMenuLinkResponse{
					GameList:      gameLinkList,
					JackpotSwitch: *jackpotSwitch,
				},
			}, nil
		}
	}

	// 取得遊戲產品相關設定
	gameSettingInfo, err := l.svcCtx.GameCtx.CacheGameSettingInfo(constants.BBSlot, hallId)
	if err != nil {
		return nil, err
	}

	// 取得開放的遊戲產品列表(過濾排除廳、開關...等)
	openGames := getOpenGames(hallId, gameSettingInfo, req.IsMobile, false)

	demoGames := make(map[uint32]*gameclient.GameInfo, 0)
	// 未登入且為PC版才取試玩遊戲
	if !req.IsMobile && !isLogin {
		demoGames, err = l.getDemoGames(req.IsMobile, hallId, gameSettingInfo)
		if err != nil {
			return nil, err
		}
	}

	// 取得遊戲產品名稱
	gameList, err := l.svcCtx.GameCtx.CacheGameList(constants.BBSlot, lang)
	if err != nil {
		return nil, err
	}

	// 取得客端遊戲標籤
	gameIconKind, err := l.svcCtx.GameCtx.GameIconKind()
	if err != nil {
		return nil, err
	}

	// 處理 openGames map 排序問題
	keys := make([]uint32, 0, len(openGames))
	for gameType := range openGames {
		keys = append(keys, gameType)
	}
	slices.Sort(keys)

	for _, gameType := range keys {
		gameData := openGames[gameType]

		// 遊戲名稱
		gameName := getGameName(gameData.GetGameType(), gameList)

		// 遊戲的Icon標籤
		icon := getGameIcon(gameData.GetOpenDate(), gameData.GetIconKind(), gameIconKind)

		// 彩金種類判斷
		jackpotCode := ""
		if gameData.GetIsJackpot() {
			jackpotCode = gameData.GetExternalId()
		}

		gameLink := ""
		// 有登入才組遊戲連結
		if isLogin {
			gameLink = l.getSlotGameLink(req.SessionID, hallId, gameType, lang, req.IsMobile)
		}

		demoLink := l.getSlotDemoLink(lang, gameType, demoGames)

		// 組遊戲連結
		gameLinkList = append(gameLinkList, types.CasinoLinkData{
			GameKind:    constants.BBSlot, // 遊戲大項ID
			GameID:      gameType,         // 遊戲產品ID
			Name:        gameName,         // 遊戲產品名稱
			Icon:        icon,             // 遊戲的Icon標籤
			Link:        gameLink,         // 遊戲連結
			DemoLink:    demoLink,         // 試玩遊戲連結
			JackpotCode: jackpotCode,      // 彩金
		})
	}

	// TODO: 原程式 取得搶先看(只有PC版才有)，但資料寫死設定檔且時間過舊，暫不實作

	// 未登入，則把遊戲連結資料寫進Redis中(暫存10分鐘)
	if !isLogin {
		l.svcCtx.GameCtx.SetCacheGuestCasinoLink(hallId, constants.BBSlot, deviceTag, lang, gameLinkList)
	}

	return &types.BaseResponse{
		Data: SlotGameLobbyMenuLinkResponse{
			GameList:      gameLinkList,
			JackpotSwitch: *jackpotSwitch,
		},
	}, nil
}

// 取得遊戲名稱
func getGameName(gameType uint32, gameList []*gameclient.GameList) string {
	gameName := ""
	for _, game := range gameList {
		if gameType == strutil.StringToUint32(game.GetId()) {
			gameName = game.GetName()
			break
		}
	}

	return gameName
}

// 取得遊戲的Icon標籤
func getGameIcon(openDate string, iconKind string, gameIconKind map[uint32]string) string {
	// 當前時間(美東)
	now := carbon.Now(constants.TimezoneGMT4)

	icon := ""
	openDateCarbon := carbon.Parse(openDate, constants.TimezoneGMT4)

	// 遊戲開放日期在近14天內
	if openDateCarbon.Gt(now.SubDays(constants.FourteenDays)) {
		icon = "New"
	} else if gameIconKind[strutil.StringToUint32(iconKind)] != "" {
		icon = gameIconKind[strutil.StringToUint32(iconKind)]
	}

	return icon
}

// 取得試玩遊戲
func (l *SlotGameLobbyMenuLinkLogic) getDemoGames(isMobile bool, hallId uint32, gameSettingInfo map[uint32]*gameclient.GameInfo) (map[uint32]*gameclient.GameInfo, error) {
	// 取得選單資料
	menuInfo, err := l.svcCtx.GameCtx.CacheMenuInfo(constants.BBSlot)
	if err != nil {
		return nil, err
	}

	demoGames := make(map[uint32]*gameclient.GameInfo, 0)

	// 取得試玩的遊戲產品列表
	if _, ok := menuInfo[constants.DemoGameMenuID]; ok {
		demoGames = getOpenGames(hallId, gameSettingInfo, isMobile, true)
	}

	return demoGames, nil
}

// 遊戲連結
func (l *SlotGameLobbyMenuLinkLogic) getSlotGameLink(sessionId string, hallId uint32, gameId uint32, lang string, isMobile bool) string {
	linkParams := urlutil.NewBuilder()
	linkParams.AddUint32("hall_id", hallId)
	linkParams.AddUint32("game_kind", constants.BBSlot)
	linkParams.AddUint32("game_id", gameId)
	linkParams.AddString("lang", lang)
	linkParams.AddString("session_id", sessionId)
	linkParams.AddBool("is_mobile", isMobile)

	scheme := urlutil.GetRequestScheme(l.ctx)

	return scheme + constants.SchemeSeparator + l.ctx.Request.Host + constants.APIGameRoutePage + "?" + linkParams.Encode()
}

// 試玩遊戲連結(僅Slot遊戲支援試玩)
func (l *SlotGameLobbyMenuLinkLogic) getSlotDemoLink(lang string, gameId uint32, demoGames map[uint32]*gameclient.GameInfo) string {
	demoLink := ""
	isDemoGame := false

	if _, ok := demoGames[gameId]; ok {
		isDemoGame = true
	}

	if isDemoGame {
		demoLinkParams := urlutil.NewBuilder()
		demoLinkParams.AddUint32("GameType", gameId)
		demoLinkParams.AddString("lang", lang)

		demoLink = l.svcCtx.Config.SlotDemoGameLink + "?" + demoLinkParams.Encode()
	}

	return demoLink
}
