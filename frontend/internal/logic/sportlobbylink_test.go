package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/sportgame"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/session/sessionclient"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestSportLobbyLinkLogic_PCDevice_WithSessionID(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(0)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhCn,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPSURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSportLobbyLinkLogic_MobileDevice_WithSessionID(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(1)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	exitOption := constants.ExitOptionWithURL
	exitURLParam := encryptExitURL(seeder.DomainURLWithHTTPS)
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhCn,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
		ExitOption:     &exitOption,
		ExitURLParam:   &exitURLParam,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
		IsMobile:  true,
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPSURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSportLobbyLinkLogic_WithHTTPDomain(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(0)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhCn,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTP,
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSportLobbyLinkLogic_EnterPageIsESports(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(0)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	enterPage := "esports"
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhCn,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
		EnterPage:      &enterPage,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
		EnterPage: enterPage,
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPSURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSportLobbyLinkLogic_WithLang(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(0)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhTw,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPSURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSportLobbyLinkLogic_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSportLobbyLinkLogic_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo().GetLobbySwitch()
	seeder.GetSession.RdInfo.LobbySwitch = []*sessionclient.LobbySwitch{
		{
			GameKind: constants.NewBBSport,
			Switch:   false,
		},
	}
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	svcCtx.SessionCtx = sessionCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.GetSession.RdInfo.LobbySwitch = originLobbySwitch
}

func TestSportLobbyLinkLogic_GetMaintainError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSportLobbyLinkLogic_GameIsUnderMaintenance(t *testing.T) {
	seeder.GetMaintainByGameKind.IsMaintaining = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	expectedResponse := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.NewBBSport,
					Message:   seeder.GetMaintainByGameKind.GetMsg(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKind.GetBeginAt(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKind.GetEndAt(), constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)

	seeder.GetMaintainByGameKind.IsMaintaining = false
}

func TestSportLobbyLinkLogic_GameIsUnderMaintenance_InWhiteList(t *testing.T) {
	seeder.GetMaintainByGameKind.IsMaintaining = true
	seeder.GetMaintainByGameKind.InWhitelist = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(0)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhCn,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPSURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)

	seeder.GetMaintainByGameKind.IsMaintaining = false
	seeder.GetMaintainByGameKind.InWhitelist = false
}

func TestSportLobbyLinkLogic_GetOneTimeSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(nil, errorx.ConnectionFailed)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSportLobbyLinkLogic_GetGameLinkError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(0)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhCn,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSportLobbyLinkLogic_DomainURLParseError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)
	sessionCtx.On("OneTimeSession", seeder.Session).Return(&seeder.OneTimeSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	device := uint32(0)
	oneTimeSession := seeder.OneTimeSession.GetSession()
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:           constants.ZhCn,
		Device:         &device,
		OneTimeSession: &oneTimeSession,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: "https://localhost net",
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSportLobbyLinkLogic_GetUserByUserIdError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSportLobbyLinkLogic_UserIsBankrupt(t *testing.T) {
	seeder.User.Bankrupt = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.UserCtx = userCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		SessionID: seeder.Session,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.BankruptError)
	assert.Nil(t, resp)

	seeder.User.Bankrupt = false
}

func TestSportLobbyLinkLogic_WithHallID(t *testing.T) {
	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	device := uint32(0)
	mockGameLinkrequest := sportgame.GameLinkRequest{
		Lang:   constants.ZhCn,
		Device: &device,
	}

	sportGameCtx := mock.NewSportGameCtx()
	sportGameCtx.On("GameLink", mockGameLinkrequest).Return(&seeder.SportGameLink, nil)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.SportGameCtx = sportGameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	expectedResponse := &types.BaseResponse{
		Data: map[string]interface{}{
			"link": constants.HTTPSURLScheme + seeder.SportGameLink.GetUrl(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSportLobbyLinkLogic_WithHallID_GetLobbySwitchByHallIDError(t *testing.T) {
	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSportLobbyLinkLogic_WithHallID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.HallIDLobbySwitch.GetLobbySwitch()
	seeder.HallIDLobbySwitch.LobbySwitch = []*gameclient.LobbySwitch{
		{
			GameKind: constants.NewBBSport,
			Switch:   false,
		},
	}

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKind", constants.NewBBSport, ginCtx.ClientIP()).Return(&seeder.GetMaintainByGameKind, nil)

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewSportLobbyLinkLogic(ginCtx, svcCtx)
	resp, err := l.SportLobbyLink(&types.SportLobbyLink{
		HallID:    seeder.BgpHallID,
		DomainURL: seeder.DomainURLWithHTTPS,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.HallIDLobbySwitch.LobbySwitch = originLobbySwitch
}
