package logic

import (
	"errors"
	"gbh/errorx"
	"gbh/frontend/internal/common"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/svc"
	"gbh/frontend/internal/types"
	"gbh/utils/urlutil"
	"slices"

	"github.com/dromara/carbon/v2"
	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"
)

type BattleGameLobbyMenuLinkLogic struct {
	logx.Logger
	ctx    *gin.Context
	svcCtx *svc.ServiceContext
}

type BattleGameLobbyMenuLinkResponse struct {
	GameList   []types.CasinoLinkData `json:"game_list"`
	Lang       string                 `json:"lang"`
	LobbyEntry string                 `json:"lobby_entry"`
}

func NewBattleGameLobbyMenuLinkLogic(ctx *gin.Context, svcCtx *svc.ServiceContext) *BattleGameLobbyMenuLinkLogic {
	return &BattleGameLobbyMenuLinkLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BattleGameLobbyMenuLinkLogic) BattleGameLobbyMenuLink(req *types.BattleGameLobbyMenuLink) (*types.BaseResponse, error) {
	// 玩家是否登入
	isLogin := false
	hallId := uint32(0)

	// 有登入
	if req.SessionID != "" {
		session, sessionErr := l.svcCtx.SessionCtx.Get(req.SessionID)
		if sessionErr != nil {
			return nil, sessionErr
		}

		user, userErr := l.svcCtx.UserCtx.GetUserByUserId(session.GetUser().GetId())
		if userErr != nil {
			return nil, userErr
		}

		// 會員停權
		if user.GetBankrupt() {
			return nil, errorx.BankruptError
		}

		// 遊戲是否開放
		if !isUserGameKindOpen(session.GetRdInfo().GetLobbySwitch(), constants.BBBattle) {
			return nil, errorx.GameIsNotOpen
		}

		isLogin = true
		hallId = user.GetDomain()
	}

	// 未登入
	if req.SessionID == "" {
		hallId = req.HallID

		hallLobbySwitch, hallLobbySwitchErr := l.svcCtx.GameCtx.GetLobbySwitchByHallID(hallId, true, nil)
		if hallLobbySwitchErr != nil {
			return nil, hallLobbySwitchErr
		}

		// 遊戲是否開放
		if !isHallGameKindOpen(hallLobbySwitch) {
			return nil, errorx.GameIsNotOpen
		}
	}

	// 遊戲是否維護中
	maintain, maintainErr := l.svcCtx.MaintainCtx.GetMaintainByGameKindFromRedis(constants.BBBattle)
	if maintainErr != nil {
		if !errors.Is(maintainErr, errorx.MaintainNotFound) {
			return nil, maintainErr
		}
	}

	if maintain != nil {
		return &types.BaseResponse{
			Code:    errorx.GameIsUnderMaintenance.Code,
			Message: errorx.GameIsUnderMaintenance.Message,
			Data: types.MaintainList{
				MaintainInfo: []types.MaintainInfo{
					{
						GameKind:  constants.BBBattle,
						Message:   maintain.GetMessage(),
						StartTime: carbon.Parse(maintain.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
						EndTime:   carbon.Parse(maintain.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
					},
				},
			},
		}, nil
	}

	lang := common.ConvertLang(req.Lang)

	deviceTag := constants.DevicePc
	if req.IsMobile {
		deviceTag = constants.DeviceMobile
	}

	gameLinkList := []types.CasinoLinkData{}
	lobbyEntry := ""

	// 如未登入，則嘗試從Redis撈取資料
	if !isLogin {
		gameLinkList = l.svcCtx.GameCtx.GetCacheGuestCasinoLink(hallId, constants.BBBattle, deviceTag, lang)

		if gameLinkList != nil {
			return &types.BaseResponse{
				Data: BattleGameLobbyMenuLinkResponse{
					GameList:   gameLinkList,
					Lang:       lang,
					LobbyEntry: lobbyEntry,
				},
			}, nil
		}
	}

	// 取得遊戲產品相關設定
	gameSettingInfo, err := l.svcCtx.GameCtx.CacheGameSettingInfo(constants.BBBattle, hallId)
	if err != nil {
		return nil, err
	}

	// 取得開放的遊戲產品列表(過濾排除廳、開關...等)
	openGames := getOpenGames(hallId, gameSettingInfo, req.IsMobile, false)

	// 取得遊戲產品名稱
	gameList, err := l.svcCtx.GameCtx.CacheGameList(constants.BBBattle, lang)
	if err != nil {
		return nil, err
	}

	// 取得客端遊戲標籤
	gameIconKind, err := l.svcCtx.GameCtx.GameIconKind()
	if err != nil {
		return nil, err
	}

	// 處理 openGames map 排序問題
	keys := make([]uint32, 0, len(openGames))
	for gameType := range openGames {
		keys = append(keys, gameType)
	}
	slices.Sort(keys)

	for _, gameType := range keys {
		gameData := openGames[gameType]

		// 遊戲名稱
		gameName := getGameName(gameData.GetGameType(), gameList)

		// 遊戲的Icon標籤
		icon := getGameIcon(gameData.GetOpenDate(), gameData.GetIconKind(), gameIconKind)

		gameLink := ""
		// 有登入才組遊戲連結
		if isLogin {
			gameLink = l.getBattleGameLink(req.SessionID, gameType, lang, req.IsMobile)
		}

		// 組遊戲連結
		gameLinkList = append(gameLinkList, types.CasinoLinkData{
			GameKind:   gameData.GetLobby(),      // 遊戲大項ID
			GameID:     gameType,                 // 遊戲產品ID
			Name:       gameName,                 // 遊戲產品名稱
			Icon:       icon,                     // 遊戲的Icon標籤
			Link:       gameLink,                 // 遊戲連結
			ExternalID: gameData.GetExternalId(), // 外接遊戲ID
		})
	}

	// 未登入，則把遊戲連結資料寫進Redis中(暫存10分鐘)
	if !isLogin {
		l.svcCtx.GameCtx.SetCacheGuestCasinoLink(hallId, constants.BBBattle, deviceTag, lang, gameLinkList)
	} else {
		// 取得大廳連結
		lobbyEntry = l.getBattleGameLink(req.SessionID, 0, lang, req.IsMobile)
	}

	return &types.BaseResponse{
		Data: BattleGameLobbyMenuLinkResponse{
			GameList:   gameLinkList,
			Lang:       lang,
			LobbyEntry: lobbyEntry,
		},
	}, nil
}

func (l *BattleGameLobbyMenuLinkLogic) getBattleGameLink(sessionId string, gameId uint32, lang string, isMobile bool) string {
	linkParams := urlutil.NewBuilder()
	linkParams.AddUint32("game_kind", constants.BBBattle)
	linkParams.AddUint32("game_id", gameId)
	linkParams.AddString("lang", lang)
	linkParams.AddString("session_id", sessionId)
	linkParams.AddBool("is_mobile", isMobile)

	scheme := urlutil.GetRequestScheme(l.ctx)

	return scheme + constants.SchemeSeparator + l.ctx.Request.Host + constants.APIGameRoutePage + "?" + linkParams.Encode()
}
