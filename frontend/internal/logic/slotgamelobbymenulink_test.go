package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/session/sessionclient"
	"gbh/utils/strutil"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestSlotGameLobbyMenuLinkLogic_WithSessionID(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBSlot, constants.ZhTw).Return(seeder.SlotGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuLinkResponse{
			GameList: []types.CasinoLinkData{
				{
					GameKind:    constants.BBSlot,
					GameID:      seeder.LaBarGameType,
					Name:        seeder.LaBarGameName,
					Icon:        "",
					Link:        "http://localhost/api/entrance/gameroutepage?game_id=5001&game_kind=5&hall_id=3820474&is_mobile=false&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
					DemoLink:    "",
					JackpotCode: "",
				},
			},
			JackpotSwitch: true,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_GetUserByUserIdError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_UserIsBankrupt(t *testing.T) {
	seeder.User.Bankrupt = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.BankruptError)
	assert.Nil(t, resp)

	seeder.User.Bankrupt = false
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo().GetLobbySwitch()
	seeder.GetSession.RdInfo.LobbySwitch = []*sessionclient.LobbySwitch{
		{
			GameKind: constants.BBSlot,
			Switch:   false,
		},
	}

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.GetSession.RdInfo.LobbySwitch = originLobbySwitch
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_GetMaintainByGameKindFromRedisError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_GameIsUnderMaintenance(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(&seeder.GetMaintainByGameKindFromRedis, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBSlot,
					Message:   seeder.GetMaintainByGameKindFromRedis.GetMessage(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_CacheSwitchError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_CacheGameSettingInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_CacheGameListError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBSlot, constants.ZhTw).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_GameIconKindError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBSlot, constants.ZhTw).Return(seeder.SlotGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithSessionID_NewIconAndRecommendIcon(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	mockSlotCacheGameSettingInfo := map[uint32]*gameclient.GameInfo{
		1: {
			Lobby:    constants.BBSlot,
			GameType: 1,
			Name:     "test_1",
			Enable:   true,
			PcEnable: true,
			OpenDate: carbon.Now().Yesterday().ToDateTimeString(),
		},
		2: {
			Lobby:    constants.BBSlot,
			GameType: 2,
			Name:     "test_2",
			Enable:   true,
			PcEnable: true,
			IconKind: strutil.Uint32ToString(seeder.RecommendIconKind),
		},
	}

	mockSlotGameLobbyMenuLink := []*gameclient.GameList{
		{
			Id:   "1",
			Name: "test_1",
		},
		{
			Id:   "2",
			Name: "test_2",
		},
	}

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(mockSlotCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBSlot, constants.ZhTw).Return(mockSlotGameLobbyMenuLink, nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx
	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuLinkResponse{
			GameList: []types.CasinoLinkData{
				{
					GameKind:    constants.BBSlot,
					GameID:      1,
					Name:        "test_1",
					Icon:        "New",
					Link:        "http://localhost/api/entrance/gameroutepage?game_id=1&game_kind=5&hall_id=3820474&is_mobile=false&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
					DemoLink:    "",
					JackpotCode: "",
				},
				{
					GameKind:    constants.BBSlot,
					GameID:      2,
					Name:        "test_2",
					Icon:        "Recommend",
					Link:        "http://localhost/api/entrance/gameroutepage?game_id=2&game_kind=5&hall_id=3820474&is_mobile=false&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
					DemoLink:    "",
					JackpotCode: "",
				},
			},
			JackpotSwitch: true,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithHallID(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBSlot, "pc", constants.ZhTw).Return([]types.CasinoLinkData(nil), nil)
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotCacheMenuInfo, nil)
	gameCtx.On("CacheGameList", constants.BBSlot, constants.ZhTw).Return(seeder.SlotGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	gameLinkList := []types.CasinoLinkData{
		{
			GameKind:    constants.BBSlot,
			GameID:      seeder.LaBarGameType,
			Name:        seeder.LaBarGameName,
			Icon:        "",
			Link:        "",
			DemoLink:    "",
			JackpotCode: "",
		},
	}

	gameCtx.On("SetCacheGuestCasinoLink", seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw, gameLinkList).Return()

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuLinkResponse{
			GameList:      gameLinkList,
			JackpotSwitch: true,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithHallID_GetLobbySwitchByHallIDError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithHallID_CacheMenuInfoError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBSlot, "pc", constants.ZhTw).Return([]types.CasinoLinkData(nil), nil)
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithHallID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.HallIDLobbySwitch.GetLobbySwitch()
	seeder.HallIDLobbySwitch.LobbySwitch = []*gameclient.LobbySwitch{
		{
			GameKind: constants.BBSlot,
			Switch:   false,
		},
	}

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.HallIDLobbySwitch.LobbySwitch = originLobbySwitch
}

func TestSlotGameLobbyMenuLinkLogic_WithHallID_WithDemoGame(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBSlot, "pc", constants.ZhTw).Return([]types.CasinoLinkData(nil), nil)
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameList", constants.BBSlot, constants.ZhTw).Return(seeder.SlotGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	gameLinkList := []types.CasinoLinkData{
		{
			GameKind:    constants.BBSlot,
			GameID:      seeder.LaBarGameType,
			Name:        seeder.LaBarGameName,
			Icon:        "",
			Link:        "",
			DemoLink:    "http://demo.casinovir999.net/game/game.php?GameType=5001&lang=zh-tw",
			JackpotCode: "",
		},
	}
	gameCtx.On("SetCacheGuestCasinoLink", seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw, gameLinkList).Return()

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx
	svcCtx.Config.SlotDemoGameLink = "http://demo.casinovir999.net/game/game.php"

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuLinkResponse{
			GameList:      gameLinkList,
			JackpotSwitch: true,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithHallID_WithDemoGame_IsMobile(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameCtx.On("CacheGameSettingInfo", constants.BBSlot, seeder.User.GetDomain()).Return(seeder.SlotCacheGameSettingInfo, nil)
	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBSlot, "mobile", constants.ZhTw).Return([]types.CasinoLinkData(nil), nil)
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotDemoCacheMenuInfo, nil)
	gameCtx.On("CacheGameList", constants.BBSlot, constants.ZhTw).Return(seeder.SlotGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	gameLinkList := []types.CasinoLinkData{
		{
			GameKind:    constants.BBSlot,
			GameID:      seeder.LaBarGameType,
			Name:        seeder.LaBarGameName,
			Icon:        "",
			Link:        "",
			DemoLink:    "",
			JackpotCode: "",
		},
	}
	gameCtx.On("SetCacheGuestCasinoLink", seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw, gameLinkList).Return()

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		HallID:   seeder.BgpHallID,
		Lang:     constants.ZhTw,
		IsMobile: true,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuLinkResponse{
			GameList:      gameLinkList,
			JackpotSwitch: true,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLinkLogic_WithHallID_GetCacheGuestCasinoLinkNotNil(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBSlot).Return(nil, errorx.MaintainNotFound)

	jackpotCtx := mock.NewJackpotCtx()
	jackpotSwitch := seeder.JackpotSwitch.GetJpSwitch()
	jackpotCtx.On("CacheSwitch", seeder.User.GetDomain()).Return(&jackpotSwitch, nil)

	gameLinkList := []types.CasinoLinkData{
		{
			GameKind:    constants.BBSlot,
			GameID:      seeder.LaBarGameType,
			Name:        seeder.LaBarGameName,
			Icon:        "",
			Link:        "",
			DemoLink:    "http://demo.casinovir999.net/game/game.php?GameType=5001&lang=zh-tw",
			JackpotCode: "",
		},
	}
	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBSlot, "pc", constants.ZhTw).Return(gameLinkList, nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.SlotGameLobbyMenuLink(&types.SlotGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: SlotGameLobbyMenuLinkResponse{
			GameList:      gameLinkList,
			JackpotSwitch: true,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestSlotGameLobbyMenuLinkLogic_getDemoGames(t *testing.T) {
	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(map[uint32]*types.MenuInfoData{
		constants.DemoGameMenuID: {
			MenuID: constants.DemoGameMenuID,
			TopID:  seeder.SlotTopID,
			Depth:  seeder.SlotTopID,
			Sort:   seeder.SlotDepth,
			Games: []types.MenuInfoGameData{
				{
					Sort:     seeder.LaBarGameSort,
					GameType: seeder.LaBarGameType,
				},
			},
		},
	}, nil)

	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.getDemoGames(false, seeder.BgpHallID, seeder.SlotCacheGameSettingInfo)

	demoGames := seeder.SlotCacheGameSettingInfo

	assert.NoError(t, err)
	assert.Equal(t, demoGames, resp)
}

func TestSlotGameLobbyMenuLinkLogic_getDemoGames_empty(t *testing.T) {
	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheMenuInfo", constants.BBSlot).Return(seeder.SlotCacheMenuInfo, nil)

	svcCtx.GameCtx = gameCtx

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.getDemoGames(false, seeder.BgpHallID, seeder.SlotCacheGameSettingInfo)

	assert.NoError(t, err)
	assert.Empty(t, resp)
}

func TestSlotGameLobbyMenuLinkLogic_getSlotGameLinkWithPC(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp := l.getSlotGameLink(seeder.Session, seeder.BgpHallID, seeder.LaBarGameType, constants.ZhTw, false)

	gameLink := "http://localhost/api/entrance/gameroutepage?game_id=5001&game_kind=5&hall_id=3820474&is_mobile=false&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2"

	assert.Equal(t, gameLink, resp)
}

func TestSlotGameLobbyMenuLinkLogic_getSlotGameLinkWithMobile(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp := l.getSlotGameLink(seeder.Session, seeder.BgpHallID, seeder.LaBarGameType, constants.ZhTw, true)

	gameLink := "http://localhost/api/entrance/gameroutepage?game_id=5001&game_kind=5&hall_id=3820474&is_mobile=true&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2"

	assert.Equal(t, gameLink, resp)
}

func TestSlotGameLobbyMenuLinkLogic_getSlotDemoLink(t *testing.T) {
	svcCtx.Config.SlotDemoGameLink = "http://demo.casinovir999.net/game/game.php"

	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp := l.getSlotDemoLink(constants.ZhTw, seeder.LaBarGameType, map[uint32]*gameclient.GameInfo{
		seeder.LaBarGameType: {
			Lobby:    constants.BBSlot,
			GameType: seeder.LaBarGameType,
			Name:     seeder.LaBarGameName,
		},
	})

	demoLink := "http://demo.casinovir999.net/game/game.php?GameType=5001&lang=zh-tw"

	assert.Equal(t, demoLink, resp)
}

func TestSlotGameLobbyMenuLinkLogic_getSlotDemoLink_empty(t *testing.T) {
	l := NewSlotGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp := l.getSlotDemoLink(constants.ZhTw, seeder.LaBarGameType, map[uint32]*gameclient.GameInfo{})

	assert.Empty(t, resp)
}
