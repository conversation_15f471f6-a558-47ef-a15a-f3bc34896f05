package logic

import (
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/mock"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/session/sessionclient"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestBattleGameLobbyMenuLinkLogic_WithSessionID(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBBattle, seeder.User.GetDomain()).Return(seeder.BattleCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBBattle, constants.ZhTw).Return(seeder.BattleGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: BattleGameLobbyMenuLinkResponse{
			GameList: []types.CasinoLinkData{
				{
					GameKind:   constants.BBBattle,
					GameID:     seeder.GoldenFlowerGameType,
					Name:       seeder.GoldenFlowerGameName,
					Icon:       "Event",
					Link:       "http://localhost/api/entrance/gameroutepage?game_id=66001&game_kind=66&is_mobile=false&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
					ExternalID: "10001",
				},
			},
			Lang:       constants.ZhTw,
			LobbyEntry: "http://localhost/api/entrance/gameroutepage?game_id=0&game_kind=66&is_mobile=false&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_GetSessionError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_GetUserByUserIdError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_UserIsBankrupt(t *testing.T) {
	seeder.User.Bankrupt = true

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.BankruptError)
	assert.Nil(t, resp)

	seeder.User.Bankrupt = false
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.GetSession.GetRdInfo().GetLobbySwitch()
	seeder.GetSession.RdInfo.LobbySwitch = []*sessionclient.LobbySwitch{
		{
			GameKind: constants.BBBattle,
			Switch:   false,
		},
	}

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.GetSession.RdInfo.LobbySwitch = originLobbySwitch
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_GetMaintainByGameKindFromRedisError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_GameIsUnderMaintenance(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(&seeder.GetMaintainByGameKindFromRedis, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Code:    errorx.GameIsUnderMaintenance.Code,
		Message: errorx.GameIsUnderMaintenance.Message,
		Data: types.MaintainList{
			MaintainInfo: []types.MaintainInfo{
				{
					GameKind:  constants.BBBattle,
					Message:   seeder.GetMaintainByGameKindFromRedis.GetMessage(),
					StartTime: carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetStartTime(), constants.TimezoneGMT4).ToRfc3339String(),
					EndTime:   carbon.Parse(seeder.GetMaintainByGameKindFromRedis.GetEndTime(), constants.TimezoneGMT4).ToRfc3339String(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_CacheGameSettingInfoError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBBattle, seeder.User.GetDomain()).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_CacheGameListError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBBattle, seeder.User.GetDomain()).Return(seeder.BattleCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBBattle, constants.ZhTw).Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_GameIconKindError(t *testing.T) {
	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBBattle, seeder.User.GetDomain()).Return(seeder.BattleCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBBattle, constants.ZhTw).Return(seeder.BattleGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(nil, errorx.ConnectionFailed)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithSessionID_IsMobile(t *testing.T) {
	ginCtx.Request.Host = seeder.Host

	sessionCtx := mock.NewSessionCtx()
	sessionCtx.On("Get", seeder.Session).Return(&seeder.GetSession, nil)

	userCtx := mock.NewUserCtx()
	userCtx.On("GetUserByUserId", seeder.GetSession.GetUser().GetId()).Return(&seeder.User, nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx := mock.NewGameCtx()
	gameCtx.On("CacheGameSettingInfo", constants.BBBattle, seeder.User.GetDomain()).Return(seeder.BattleCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBBattle, constants.ZhTw).Return(seeder.BattleGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	svcCtx.SessionCtx = sessionCtx
	svcCtx.UserCtx = userCtx
	svcCtx.MaintainCtx = maintainCtx
	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		SessionID: seeder.Session,
		HallID:    seeder.BgpHallID,
		Lang:      constants.ZhTw,
		IsMobile:  true,
	})

	expectedResponse := &types.BaseResponse{
		Data: BattleGameLobbyMenuLinkResponse{
			GameList: []types.CasinoLinkData{
				{
					GameKind:   constants.BBBattle,
					GameID:     seeder.GoldenFlowerGameType,
					Name:       seeder.GoldenFlowerGameName,
					Icon:       "Event",
					Link:       "http://localhost/api/entrance/gameroutepage?game_id=66001&game_kind=66&is_mobile=true&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
					ExternalID: "10001",
				},
			},
			Lang:       constants.ZhTw,
			LobbyEntry: "http://localhost/api/entrance/gameroutepage?game_id=0&game_kind=66&is_mobile=true&lang=zh-tw&session_id=bbe61cbb5f2e97c7622291e8b53047eaa520a633c2",
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithHallID(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBBattle, "pc", constants.ZhTw).Return([]types.CasinoLinkData(nil), nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBBattle, seeder.User.GetDomain()).Return(seeder.BattleCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBBattle, constants.ZhTw).Return(seeder.BattleGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	gameLinkList := []types.CasinoLinkData{
		{
			GameKind:   constants.BBBattle,
			GameID:     seeder.GoldenFlowerGameType,
			Name:       seeder.GoldenFlowerGameName,
			Icon:       "Event",
			Link:       "",
			ExternalID: "10001",
		},
	}
	gameCtx.On("SetCacheGuestCasinoLink", seeder.BgpHallID, constants.BBBattle, "pc", constants.ZhTw, gameLinkList).Return()

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: BattleGameLobbyMenuLinkResponse{
			GameList:   gameLinkList,
			Lang:       constants.ZhTw,
			LobbyEntry: "",
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithHallID_GetLobbySwitchByHallIDError(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithHallID_GameIsNotOpen(t *testing.T) {
	originLobbySwitch := seeder.HallIDLobbySwitch.GetLobbySwitch()
	seeder.HallIDLobbySwitch.LobbySwitch = []*gameclient.LobbySwitch{
		{
			GameKind: constants.BBBattle,
			Switch:   false,
		},
	}

	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	svcCtx.GameCtx = gameCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	assert.Equal(t, err, errorx.GameIsNotOpen)
	assert.Nil(t, resp)

	seeder.HallIDLobbySwitch.LobbySwitch = originLobbySwitch
}

func TestBattleGameLobbyMenuLinkLogic_WithHallID_GetCacheGuestCasinoLinkNotNil(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameLinkList := []types.CasinoLinkData{
		{
			GameKind:   constants.BBBattle,
			GameID:     seeder.GoldenFlowerGameType,
			Name:       seeder.GoldenFlowerGameName,
			Icon:       "Event",
			Link:       "",
			ExternalID: "10001",
		},
	}
	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBBattle, "pc", constants.ZhTw).Return(gameLinkList, nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		HallID: seeder.BgpHallID,
		Lang:   constants.ZhTw,
	})

	expectedResponse := &types.BaseResponse{
		Data: BattleGameLobbyMenuLinkResponse{
			GameList:   gameLinkList,
			Lang:       constants.ZhTw,
			LobbyEntry: "",
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestBattleGameLobbyMenuLinkLogic_WithHallID_IsMobile(t *testing.T) {
	var lobbySwitchEnable *bool
	gameCtx := mock.NewGameCtx()
	gameCtx.On("GetLobbySwitchByHallID", uint32(seeder.BgpHallID), true, lobbySwitchEnable).Return(seeder.HallIDLobbySwitch.GetLobbySwitch(), nil)

	maintainCtx := mock.NewMaintainCtx()
	maintainCtx.On("GetMaintainByGameKindFromRedis", constants.BBBattle).Return(nil, errorx.MaintainNotFound)

	gameCtx.On("GetCacheGuestCasinoLink", uint32(seeder.BgpHallID), constants.BBBattle, "mobile", constants.ZhTw).Return([]types.CasinoLinkData(nil), nil)
	gameCtx.On("CacheGameSettingInfo", constants.BBBattle, seeder.User.GetDomain()).Return(seeder.BattleCacheGameSettingInfo, nil)
	gameCtx.On("CacheGameList", constants.BBBattle, constants.ZhTw).Return(seeder.BattleGameList.GetData(), nil)
	gameCtx.On("GameIconKind").Return(seeder.SlotGameIconKindMap, nil)

	gameLinkList := []types.CasinoLinkData{
		{
			GameKind:   constants.BBBattle,
			GameID:     seeder.GoldenFlowerGameType,
			Name:       seeder.GoldenFlowerGameName,
			Icon:       "Event",
			Link:       "",
			ExternalID: "10001",
		},
	}
	gameCtx.On("SetCacheGuestCasinoLink", seeder.BgpHallID, constants.BBBattle, "mobile", constants.ZhTw, gameLinkList).Return()

	svcCtx.GameCtx = gameCtx
	svcCtx.MaintainCtx = maintainCtx

	l := NewBattleGameLobbyMenuLinkLogic(ginCtx, svcCtx)
	resp, err := l.BattleGameLobbyMenuLink(&types.BattleGameLobbyMenuLink{
		HallID:   seeder.BgpHallID,
		Lang:     constants.ZhTw,
		IsMobile: true,
	})

	expectedResponse := &types.BaseResponse{
		Data: BattleGameLobbyMenuLinkResponse{
			GameList:   gameLinkList,
			Lang:       constants.ZhTw,
			LobbyEntry: "",
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
