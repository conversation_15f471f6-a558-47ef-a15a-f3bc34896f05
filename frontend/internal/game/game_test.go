package game

import (
	"encoding/json"
	"fmt"
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/seeder"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/utils/strutil"
	"testing"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	gameRPC := newMockGameRPC()
	gameCtx := New(ctx, redisCache, gameRPC)

	expectedResponse := &gameContext{
		ctx:     ctx,
		cache:   redisCache,
		GameRPC: gameRPC,
	}

	assert.Equal(t, expectedResponse, gameCtx)
}

func Test_GameContext_GameDomain(t *testing.T) {
	gameRPC := newMockGameRPC()
	mockRequest := &gameclient.GameDomainRequest{
		GameKind: 3,
		HallId:   seeder.BgpHallID,
	}
	gameRPC.On("GameDomain", ctx, mockRequest).Return(&seeder.GameDomain, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GameDomain(3, seeder.BgpHallID)
	assert.NoError(t, err)
	assert.Equal(t, &seeder.GameDomain, resp)
}

func Test_GameContext_GameDomain_InvalidResponse(t *testing.T) {
	gameRPC := newMockGameRPC()
	mockRequest := &gameclient.GameDomainRequest{
		GameKind: 3,
		HallId:   seeder.BgpHallID,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GameDomain", ctx, mockRequest).Return(nil, mockError)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GameDomain(3, seeder.BgpHallID)
	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheGameDomain_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GameDomainRequest{
		GameKind: constants.BBLive,
		HallId:   seeder.BgpHallID,
	}

	key := fmt.Sprintf(gameDomainKey, constants.BBLive, mockRequest.GetHallId())
	jsonGameDomain, err := json.Marshal(seeder.GameDomain.GetDomain())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameDomain), cacheGameDomainExpiry).SetVal("ok")

	gameRPC.On("GameDomain", ctx, mockRequest).Return(&seeder.GameDomain, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameDomain(mockRequest.GetGameKind(), mockRequest.GetHallId())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameDomain.GetDomain(), resp)
}

func Test_GameContext_CacheGameDomain_FromRPC_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GameDomainRequest{
		GameKind: constants.BBLive,
		HallId:   seeder.BgpHallID,
	}

	key := fmt.Sprintf(gameDomainKey, constants.BBLive, mockRequest.GetHallId())

	redisMock.ExpectGet(key).RedisNil()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GameDomain", ctx, mockRequest).Return(nil, mockError)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameDomain(mockRequest.GetGameKind(), mockRequest.GetHallId())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheGameDomain_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GameDomainRequest{
		GameKind: constants.BBLive,
		HallId:   seeder.BgpHallID,
	}

	key := fmt.Sprintf(gameDomainKey, constants.BBLive, mockRequest.GetHallId())
	jsonGameDomain, err := json.Marshal(seeder.GameDomain.GetDomain())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameDomain), cacheGameDomainExpiry).SetErr(redis.ErrClosed)

	gameRPC.On("GameDomain", ctx, mockRequest).Return(&seeder.GameDomain, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameDomain(mockRequest.GetGameKind(), mockRequest.GetHallId())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameDomain.GetDomain(), resp)
}

func Test_GameContext_CacheGameDomain_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GameDomainRequest{
		GameKind: constants.BBLive,
		HallId:   seeder.BgpHallID,
	}

	key := fmt.Sprintf(gameDomainKey, constants.BBLive, mockRequest.GetHallId())
	jsonGameDomain, err := json.Marshal(seeder.GameDomain.GetDomain())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonGameDomain))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameDomain(mockRequest.GetGameKind(), mockRequest.GetHallId())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.CacheGameDomain, resp)
}

func Test_GameContext_CacheGameDomain_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GameDomainRequest{
		GameKind: constants.BBLive,
		HallId:   seeder.BgpHallID,
	}

	key := fmt.Sprintf(gameDomainKey, constants.BBLive, mockRequest.GetHallId())
	jsonGameDomain, err := json.Marshal(seeder.GameDomain.GetDomain())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(key, string(jsonGameDomain), cacheGameDomainExpiry).SetVal("ok")

	gameRPC.On("GameDomain", ctx, mockRequest).Return(&seeder.GameDomain, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameDomain(mockRequest.GetGameKind(), mockRequest.GetHallId())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameDomain.GetDomain(), resp)
}

func Test_GameContext_CacheGameDomain_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GameDomainRequest{
		GameKind: constants.BBLive,
		HallId:   seeder.BgpHallID,
	}

	key := fmt.Sprintf(gameDomainKey, constants.BBLive, mockRequest.GetHallId())
	jsonGameDomain, err := json.Marshal(seeder.GameDomain.GetDomain())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSetEx(key, string(jsonGameDomain), cacheGameDomainExpiry).SetVal("ok")

	gameRPC.On("GameDomain", ctx, mockRequest).Return(&seeder.GameDomain, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameDomain(mockRequest.GetGameKind(), mockRequest.GetHallId())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameDomain.GetDomain(), resp)
}

func Test_GameContext_GetLobbySwitchByHallID_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            seeder.GetSession.GetUser().GetHallId(),
		FilterLobbyEnable: true,
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, mockRequest.GetHallId(), mockRequest.GetFilterLobbyEnable(), "nil")
	jsonHallLobbySwitchList, err := json.Marshal(seeder.HallIDLobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonHallLobbySwitchList), cacheHallLobbySwitchListExpiry).SetVal("ok")

	gameRPC.On("GetLobbySwitchByHallID", ctx, mockRequest).Return(&seeder.HallIDLobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GetLobbySwitchByHallID(mockRequest.GetHallId(), true, nil)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.HallIDLobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitchByHallID_WithLobbySwitchEnable_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            seeder.GetSession.GetUser().GetHallId(),
		FilterLobbyEnable: false,
		LobbySwitchEnable: &gameclient.BoolValue{
			Value: true,
		},
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, mockRequest.GetHallId(), mockRequest.GetFilterLobbyEnable(), mockRequest.GetLobbySwitchEnable().GetValue())
	jsonHallLobbySwitchList, err := json.Marshal(seeder.HallIDLobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonHallLobbySwitchList), cacheHallLobbySwitchListExpiry).SetVal("ok")

	lobbySwitchEnable := true
	gameRPC.On("GetLobbySwitchByHallID", ctx, mockRequest).Return(&seeder.HallIDLobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GetLobbySwitchByHallID(mockRequest.GetHallId(), false, &lobbySwitchEnable)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.HallIDLobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitchByHallID_FromRPC_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            seeder.GetSession.GetUser().GetHallId(),
		FilterLobbyEnable: true,
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, mockRequest.GetHallId(), mockRequest.GetFilterLobbyEnable(), "nil")

	redisMock.ExpectGet(key).RedisNil()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetLobbySwitchByHallID", ctx, mockRequest).Return(nil, mockError)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GetLobbySwitchByHallID(mockRequest.GetHallId(), true, nil)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_GetLobbySwitchByHallID_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            seeder.GetSession.GetUser().GetHallId(),
		FilterLobbyEnable: true,
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, mockRequest.GetHallId(), mockRequest.GetFilterLobbyEnable(), "nil")
	jsonHallLobbySwitchList, err := json.Marshal(seeder.HallIDLobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonHallLobbySwitchList), cacheHallLobbySwitchListExpiry).SetErr(redis.ErrClosed)

	gameRPC.On("GetLobbySwitchByHallID", ctx, mockRequest).Return(&seeder.HallIDLobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GetLobbySwitchByHallID(mockRequest.GetHallId(), true, nil)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.HallIDLobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitchByHallID_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            seeder.GetSession.GetUser().GetHallId(),
		FilterLobbyEnable: true,
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, mockRequest.GetHallId(), mockRequest.GetFilterLobbyEnable(), "nil")
	jsonHallLobbySwitchList, err := json.Marshal(seeder.HallIDLobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonHallLobbySwitchList))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GetLobbySwitchByHallID(mockRequest.GetHallId(), true, nil)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.HallIDLobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitchByHallID_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            seeder.GetSession.GetUser().GetHallId(),
		FilterLobbyEnable: true,
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, mockRequest.GetHallId(), mockRequest.GetFilterLobbyEnable(), "nil")
	jsonHallLobbySwitchList, err := json.Marshal(seeder.HallIDLobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(key, string(jsonHallLobbySwitchList), cacheHallLobbySwitchListExpiry).SetVal("ok")

	gameRPC.On("GetLobbySwitchByHallID", ctx, mockRequest).Return(&seeder.HallIDLobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GetLobbySwitchByHallID(mockRequest.GetHallId(), true, nil)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.HallIDLobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitchByHallID_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            seeder.GetSession.GetUser().GetHallId(),
		FilterLobbyEnable: true,
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, mockRequest.GetHallId(), mockRequest.GetFilterLobbyEnable(), "nil")
	jsonHallLobbySwitchList, err := json.Marshal(seeder.HallIDLobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSetEx(key, string(jsonHallLobbySwitchList), cacheHallLobbySwitchListExpiry).SetVal("ok")

	gameRPC.On("GetLobbySwitchByHallID", ctx, mockRequest).Return(&seeder.HallIDLobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GetLobbySwitchByHallID(mockRequest.GetHallId(), true, nil)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.HallIDLobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_LobbyGameLink(t *testing.T) {
	gameRPC := newMockGameRPC()

	request := LobbyGameLinkRequest{
		GameKind: constants.BBBattle,
		Session:  seeder.Session,
	}

	mockRequest := &gameclient.LobbyLinkRequest{
		GameKind: request.GameKind,
		Session:  request.Session,
	}

	gameRPC.On("LobbyLink", ctx, mockRequest).Return(&seeder.LobbyGameLink, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.LobbyGameLink(request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.LobbyGameLink, resp)
}

func Test_GameContext_LobbyGameLink_WithOptionalParam(t *testing.T) {
	gameRPC := newMockGameRPC()

	lang := constants.ZhTw
	ip := seeder.ClientIP
	exitOption := uint32(2)
	ExitURLParam := "test"
	request := LobbyGameLinkRequest{
		GameKind:     constants.BBBattle,
		Session:      seeder.Session,
		Lang:         &lang,
		IP:           &ip,
		ExitOption:   &exitOption,
		ExitURLParam: &ExitURLParam,
	}

	mockRequest := &gameclient.LobbyLinkRequest{
		GameKind: request.GameKind,
		Session:  request.Session,
		Lang: &gameclient.StringValue{
			Value: lang,
		},
		Ip: &gameclient.StringValue{
			Value: ip,
		},
		ExitOption: &gameclient.Uint32Value{
			Value: exitOption,
		},
		ExitUrl: &gameclient.StringValue{
			Value: ExitURLParam,
		},
	}

	gameRPC.On("LobbyLink", ctx, mockRequest).Return(&seeder.LobbyGameLink, nil)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.LobbyGameLink(request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.LobbyGameLink, resp)
}

func Test_GameContext_LobbyGameLink_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	request := LobbyGameLinkRequest{
		GameKind: constants.BBBattle,
		Session:  seeder.Session,
	}

	mockRequest := &gameclient.LobbyLinkRequest{
		GameKind: request.GameKind,
		Session:  request.Session,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("LobbyLink", ctx, mockRequest).Return(nil, mockError)
	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.LobbyGameLink(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_GetLobbySwitch_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchRequest{
		AgentId:    seeder.AgentID,
		AllParents: seeder.GetSession.GetUser().GetAllParents(),
		UserId: &gameclient.Uint32Value{
			Value: seeder.GetSession.GetUser().GetId(),
		},
	}

	key := fmt.Sprintf(agentLobbySwitchKey, mockRequest.GetAgentId())
	jsonLobbySwitch, err := json.Marshal(seeder.LobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonLobbySwitch), cacheLobbySwitchExpiry).SetVal("ok")

	gameRPC.On("GetLobbySwitch", ctx, mockRequest).Return(&seeder.LobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	userId := mockRequest.GetUserId().GetValue()
	request := GetLobbySwitchRequest{
		AgentId:    mockRequest.GetAgentId(),
		AllParents: mockRequest.GetAllParents(),
		UserId:     &userId,
	}
	resp, err := gameCtx.GetLobbySwitch(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.LobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitch_FromRPC_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchRequest{
		AgentId:    seeder.AgentID,
		AllParents: seeder.GetSession.GetUser().GetAllParents(),
		UserId: &gameclient.Uint32Value{
			Value: seeder.GetSession.GetUser().GetId(),
		},
	}

	key := fmt.Sprintf(agentLobbySwitchKey, mockRequest.GetAgentId())

	redisMock.ExpectGet(key).RedisNil()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetLobbySwitch", ctx, mockRequest).Return(nil, mockError)
	gameCtx := New(ctx, redisCache, gameRPC)

	userId := mockRequest.GetUserId().GetValue()
	request := GetLobbySwitchRequest{
		AgentId:    mockRequest.GetAgentId(),
		AllParents: mockRequest.GetAllParents(),
		UserId:     &userId,
	}
	resp, err := gameCtx.GetLobbySwitch(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_GetLobbySwitch_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchRequest{
		AgentId:    seeder.AgentID,
		AllParents: seeder.GetSession.GetUser().GetAllParents(),
		UserId: &gameclient.Uint32Value{
			Value: seeder.GetSession.GetUser().GetId(),
		},
	}

	key := fmt.Sprintf(agentLobbySwitchKey, mockRequest.GetAgentId())
	jsonLobbySwitch, err := json.Marshal(seeder.LobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonLobbySwitch), cacheLobbySwitchExpiry).SetErr(redis.ErrClosed)

	gameRPC.On("GetLobbySwitch", ctx, mockRequest).Return(&seeder.LobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	userId := mockRequest.GetUserId().GetValue()
	request := GetLobbySwitchRequest{
		AgentId:    mockRequest.GetAgentId(),
		AllParents: mockRequest.GetAllParents(),
		UserId:     &userId,
	}
	resp, err := gameCtx.GetLobbySwitch(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.LobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitch_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchRequest{
		AgentId:    seeder.AgentID,
		AllParents: seeder.GetSession.GetUser().GetAllParents(),
		UserId: &gameclient.Uint32Value{
			Value: seeder.GetSession.GetUser().GetId(),
		},
	}

	key := fmt.Sprintf(agentLobbySwitchKey, mockRequest.GetAgentId())
	jsonLobbySwitch, err := json.Marshal(seeder.LobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonLobbySwitch))

	gameCtx := New(ctx, redisCache, gameRPC)

	userId := mockRequest.GetUserId().GetValue()
	request := GetLobbySwitchRequest{
		AgentId:    mockRequest.GetAgentId(),
		AllParents: mockRequest.GetAllParents(),
		UserId:     &userId,
	}
	resp, err := gameCtx.GetLobbySwitch(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.LobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitch_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchRequest{
		AgentId:    seeder.AgentID,
		AllParents: seeder.GetSession.GetUser().GetAllParents(),
		UserId: &gameclient.Uint32Value{
			Value: seeder.GetSession.GetUser().GetId(),
		},
	}

	key := fmt.Sprintf(agentLobbySwitchKey, mockRequest.GetAgentId())
	jsonLobbySwitch, err := json.Marshal(seeder.LobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(key, string(jsonLobbySwitch), cacheLobbySwitchExpiry).SetVal("ok")

	gameRPC.On("GetLobbySwitch", ctx, mockRequest).Return(&seeder.LobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	userId := mockRequest.GetUserId().GetValue()
	request := GetLobbySwitchRequest{
		AgentId:    mockRequest.GetAgentId(),
		AllParents: mockRequest.GetAllParents(),
		UserId:     &userId,
	}
	resp, err := gameCtx.GetLobbySwitch(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.LobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_GetLobbySwitch_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetLobbySwitchRequest{
		AgentId:    seeder.AgentID,
		AllParents: seeder.GetSession.GetUser().GetAllParents(),
		UserId: &gameclient.Uint32Value{
			Value: seeder.GetSession.GetUser().GetId(),
		},
	}

	key := fmt.Sprintf(agentLobbySwitchKey, mockRequest.GetAgentId())
	jsonLobbySwitch, err := json.Marshal(seeder.LobbySwitch.GetLobbySwitch())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSetEx(key, string(jsonLobbySwitch), cacheLobbySwitchExpiry).SetVal("ok")

	gameRPC.On("GetLobbySwitch", ctx, mockRequest).Return(&seeder.LobbySwitch, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	userId := mockRequest.GetUserId().GetValue()
	request := GetLobbySwitchRequest{
		AgentId:    mockRequest.GetAgentId(),
		AllParents: mockRequest.GetAllParents(),
		UserId:     &userId,
	}
	resp, err := gameCtx.GetLobbySwitch(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.LobbySwitch.GetLobbySwitch(), resp)
}

func Test_GameContext_AddUserFavorite(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.AddUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())

	redisMock.ExpectDel(key).SetVal(0)

	gameRPC.On("AddUserFavorite", ctx, mockRequest).Return(nil, nil)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.AddUserFavorite(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
}

func Test_GameContext_AddUserFavorite_GRPCError_GameDuplicateUserFavorite(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.AddUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())

	redisMock.ExpectDel(key).SetVal(0)

	mockError := status.Error(codes.Code(errorx.GameDuplicateUserFavorite.Code), errorx.GameDuplicateUserFavorite.Message)
	gameRPC.On("AddUserFavorite", ctx, mockRequest).Return(nil, mockError)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.AddUserFavorite(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
}

func Test_GameContext_AddUserFavorite_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.AddUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("AddUserFavorite", ctx, mockRequest).Return(nil, mockError)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.AddUserFavorite(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_GameContext_AddUserFavorite_DelRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.AddUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())

	redisMock.ExpectDel(key).SetErr(redis.ErrClosed)

	gameRPC.On("AddUserFavorite", ctx, mockRequest).Return(nil, nil)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.AddUserFavorite(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.RedisError, err)
}

func Test_GameContext_DeleteUserFavorite(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.DeleteUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())

	redisMock.ExpectDel(key).SetVal(0)

	gameRPC.On("DeleteUserFavorite", ctx, mockRequest).Return(nil, nil)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.DeleteUserFavorite(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
}

func Test_GameContext_DeleteUserFavorite_GRPCError_GameUserFavoriteNoFound(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.DeleteUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())

	redisMock.ExpectDel(key).SetVal(0)

	mockError := status.Error(codes.Code(errorx.GameUserFavoriteNoFound.Code), errorx.GameUserFavoriteNoFound.Message)
	gameRPC.On("DeleteUserFavorite", ctx, mockRequest).Return(nil, mockError)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.DeleteUserFavorite(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
}

func Test_GameContext_DeleteUserFavorite_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.DeleteUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("DeleteUserFavorite", ctx, mockRequest).Return(nil, mockError)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.DeleteUserFavorite(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_GameContext_DeleteUserFavorite_DelRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.DeleteUserFavoriteRequest{
		UserId:   seeder.UserGTPTestID,
		GameKind: constants.BBSlot,
		GameId:   seeder.JungleGameType,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())

	redisMock.ExpectDel(key).SetErr(redis.ErrClosed)

	gameRPC.On("DeleteUserFavorite", ctx, mockRequest).Return(nil, nil)

	request := SetUserFavoriteRequest{
		UserId:   mockRequest.GetUserId(),
		GameKind: mockRequest.GetGameKind(),
		GameId:   mockRequest.GetGameId(),
	}
	gameCtx := New(ctx, redisCache, gameRPC)
	err := gameCtx.DeleteUserFavorite(request)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.RedisError, err)
}

func Test_GameContext_CacheMenuInfo_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuInfoKey, constants.BBSlot)
	jsonMenuSort, err := json.Marshal(seeder.SlotCacheMenuInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonMenuSort), cacheMenuInfoExpiry).SetVal("ok")

	mockGetCategoryRequest := &gameclient.GetCategoryRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetCategory", ctx, mockGetCategoryRequest).Return(&seeder.SlotGameMenuCategory, nil)

	mockGetMenuSortRequest := &gameclient.GetMenuSortRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetMenuSort", ctx, mockGetMenuSortRequest).Return(&seeder.SlotGameMenuSort, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuInfo(constants.BBSlot)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheMenuInfo, resp)
}

func Test_GameContext_CacheMenuInfo_FromRPC_GRPCError_GetCategory(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuInfoKey, constants.BBSlot)

	redisMock.ExpectGet(key).RedisNil()

	mockGetCategoryRequest := &gameclient.GetCategoryRequest{
		GameKind: constants.BBSlot,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetCategory", ctx, mockGetCategoryRequest).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuInfo(constants.BBSlot)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheMenuInfo_FromRPC_GRPCError_GetMenuSort(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuInfoKey, constants.BBSlot)

	redisMock.ExpectGet(key).RedisNil()

	mockGetCategoryRequest := &gameclient.GetCategoryRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetCategory", ctx, mockGetCategoryRequest).Return(&seeder.SlotGameMenuCategory, nil)

	mockGetMenuSortRequest := &gameclient.GetMenuSortRequest{
		GameKind: constants.BBSlot,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetMenuSort", ctx, mockGetMenuSortRequest).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuInfo(constants.BBSlot)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheMenuInfo_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuInfoKey, constants.BBSlot)
	jsonMenuSort, err := json.Marshal(seeder.SlotCacheMenuInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonMenuSort), cacheMenuInfoExpiry).SetErr(redis.ErrClosed)

	mockGetCategoryRequest := &gameclient.GetCategoryRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetCategory", ctx, mockGetCategoryRequest).Return(&seeder.SlotGameMenuCategory, nil)

	mockGetMenuSortRequest := &gameclient.GetMenuSortRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetMenuSort", ctx, mockGetMenuSortRequest).Return(&seeder.SlotGameMenuSort, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuInfo(constants.BBSlot)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheMenuInfo, resp)
}

func Test_GameContext_CacheMenuInfo_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuInfoKey, constants.BBSlot)
	jsonMenuSort, err := json.Marshal(seeder.SlotCacheMenuInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonMenuSort))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuInfo(constants.BBSlot)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheMenuInfo, resp)
}

func Test_GameContext_CacheMenuInfo_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuInfoKey, constants.BBSlot)
	jsonMenuSort, err := json.Marshal(seeder.SlotCacheMenuInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(key, string(jsonMenuSort), cacheMenuInfoExpiry).SetVal("ok")

	mockGetCategoryRequest := &gameclient.GetCategoryRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetCategory", ctx, mockGetCategoryRequest).Return(&seeder.SlotGameMenuCategory, nil)

	mockGetMenuSortRequest := &gameclient.GetMenuSortRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetMenuSort", ctx, mockGetMenuSortRequest).Return(&seeder.SlotGameMenuSort, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuInfo(constants.BBSlot)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheMenuInfo, resp)
}

func Test_GameContext_CacheMenuInfo_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuInfoKey, constants.BBSlot)
	jsonMenuSort, err := json.Marshal(seeder.SlotCacheMenuInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSetEx(key, string(jsonMenuSort), cacheMenuInfoExpiry).SetVal("ok")

	mockGetCategoryRequest := &gameclient.GetCategoryRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetCategory", ctx, mockGetCategoryRequest).Return(&seeder.SlotGameMenuCategory, nil)

	mockGetMenuSortRequest := &gameclient.GetMenuSortRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetMenuSort", ctx, mockGetMenuSortRequest).Return(&seeder.SlotGameMenuSort, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuInfo(constants.BBSlot)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheMenuInfo, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBSlot, seeder.BgpHallID)
	jsonGameInfo, err := json.Marshal(seeder.SlotCacheGameSettingInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameInfo), cacheGameSettingInfoExpiry).SetVal("ok")

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(&seeder.SlotGameInfo, nil)

	mockGetLobbyGameEntranceSwitchRequest := &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: constants.BBSlot,
		HallId:   seeder.BgpHallID,
	}
	gameRPC.On("GetLobbyGameEntranceSwitch", ctx, mockGetLobbyGameEntranceSwitchRequest).Return(&seeder.SlotLobbyGameEntranceSwitch, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBSlot, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheGameSettingInfo, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromRPC_LotteryGame(t *testing.T) {
	gameRPC := newMockGameRPC()

	var cacheLotteryGameInfo = map[uint32]*gameclient.GameInfo{
		seeder.B128GameType: seeder.LotteryGameInfo.GetData()[0],
	}

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBLottery, seeder.BgpHallID)
	jsonGameInfo, err := json.Marshal(cacheLotteryGameInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameInfo), cacheGameSettingInfoExpiry).SetVal("ok")

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBLottery,
	}
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(&seeder.LotteryGameInfo, nil)

	mockGetLobbyGameEntranceSwitchRequest := &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: constants.BBLottery,
		HallId:   seeder.BgpHallID,
	}
	gameRPC.On("GetLobbyGameEntranceSwitch", ctx, mockGetLobbyGameEntranceSwitchRequest).Return(&seeder.LotteryLobbyGameEntranceSwitch, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBLottery, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, cacheLotteryGameInfo, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromRPC_LotteryGame_LobbyGameSwitchDataMoreThanGameInfoData(t *testing.T) {
	gameRPC := newMockGameRPC()

	var cacheLotteryGameInfo = map[uint32]*gameclient.GameInfo{
		seeder.B128GameType: seeder.LotteryGameInfo.GetData()[0],
	}

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBLottery, seeder.BgpHallID)
	jsonGameInfo, err := json.Marshal(cacheLotteryGameInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameInfo), cacheGameSettingInfoExpiry).SetVal("ok")

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBLottery,
	}
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(&seeder.LotteryGameInfo, nil)

	mockGetLobbyGameEntranceSwitchRequest := &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: constants.BBLottery,
		HallId:   seeder.BgpHallID,
	}

	mockLotteryLobbyGameEntranceSwitch := gameclient.GetLobbyGameEntranceSwitchResponse{
		Data: []*gameclient.LobbyGameEntranceSwitch{
			{
				GameType:     strutil.IntToString(seeder.LaBarGameType),
				PcEnable:     true,
				MobileEnable: true,
			},
			{
				GameType:     seeder.B128ExternalID,
				PcEnable:     false,
				MobileEnable: false,
			},
		},
	}

	gameRPC.On("GetLobbyGameEntranceSwitch", ctx, mockGetLobbyGameEntranceSwitchRequest).Return(&mockLotteryLobbyGameEntranceSwitch, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBLottery, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, cacheLotteryGameInfo, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromRPC_GRPCError_GetGameInfo(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBSlot, seeder.BgpHallID)

	redisMock.ExpectGet(key).RedisNil()

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBSlot,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBSlot, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromRPC_GRPCError_GetLobbyGameEntranceSwitch(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBSlot, seeder.BgpHallID)

	redisMock.ExpectGet(key).RedisNil()

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(&seeder.SlotGameInfo, nil)

	mockGetLobbyGameEntranceSwitchRequest := &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: constants.BBSlot,
		HallId:   seeder.BgpHallID,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetLobbyGameEntranceSwitch", ctx, mockGetLobbyGameEntranceSwitchRequest).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBSlot, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBSlot, seeder.BgpHallID)
	jsonGameInfo, err := json.Marshal(seeder.SlotCacheGameSettingInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameInfo), cacheGameSettingInfoExpiry).SetErr(redis.ErrClosed)

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(&seeder.SlotGameInfo, nil)

	mockGetLobbyGameEntranceSwitchRequest := &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: constants.BBSlot,
		HallId:   seeder.BgpHallID,
	}
	gameRPC.On("GetLobbyGameEntranceSwitch", ctx, mockGetLobbyGameEntranceSwitchRequest).Return(&seeder.SlotLobbyGameEntranceSwitch, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBSlot, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheGameSettingInfo, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBSlot, seeder.BgpHallID)
	seeder.SlotCacheGameSettingInfo[seeder.LaBarGameType].AllowList = []uint32{1}
	seeder.SlotCacheGameSettingInfo[seeder.LaBarGameType].BlockList = []uint32{2}

	jsonGameInfo, err := json.Marshal(seeder.SlotCacheGameSettingInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonGameInfo))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBSlot, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheGameSettingInfo, resp)

	seeder.SlotCacheGameSettingInfo[seeder.LaBarGameType].AllowList = []uint32{}
	seeder.SlotCacheGameSettingInfo[seeder.LaBarGameType].BlockList = []uint32{}
}

func Test_GameContext_CacheGameSettingInfo_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBSlot, seeder.BgpHallID)
	jsonGameInfo, err := json.Marshal(seeder.SlotCacheGameSettingInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(key, string(jsonGameInfo), cacheGameSettingInfoExpiry).SetVal("ok")

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(&seeder.SlotGameInfo, nil)

	mockGetLobbyGameEntranceSwitchRequest := &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: constants.BBSlot,
		HallId:   seeder.BgpHallID,
	}
	gameRPC.On("GetLobbyGameEntranceSwitch", ctx, mockGetLobbyGameEntranceSwitchRequest).Return(&seeder.SlotLobbyGameEntranceSwitch, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBSlot, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheGameSettingInfo, resp)
}

func Test_GameContext_CacheGameSettingInfo_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameSettingInfoKey, constants.BBSlot, seeder.BgpHallID)
	jsonGameInfo, err := json.Marshal(seeder.SlotCacheGameSettingInfo)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSetEx(key, string(jsonGameInfo), cacheGameSettingInfoExpiry).SetVal("ok")

	mockGetGameInfoRequest := &gameclient.GetGameInfoRequest{
		GameKind: constants.BBSlot,
	}
	gameRPC.On("GetGameInfo", ctx, mockGetGameInfoRequest).Return(&seeder.SlotGameInfo, nil)

	mockGetLobbyGameEntranceSwitchRequest := &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: constants.BBSlot,
		HallId:   seeder.BgpHallID,
	}
	gameRPC.On("GetLobbyGameEntranceSwitch", ctx, mockGetLobbyGameEntranceSwitchRequest).Return(&seeder.SlotLobbyGameEntranceSwitch, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameSettingInfo(constants.BBSlot, seeder.BgpHallID)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotCacheGameSettingInfo, resp)
}

func Test_GameContext_CacheMenuName_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuNameKey, constants.ZhTw)
	jsonMenuName, err := json.Marshal(seeder.SlotGameMenuName.GetMenuList())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonMenuName), cacheMenuNameExpiry).SetVal("ok")

	mockGetMenuNameRequest := &gameclient.GetMenuNameRequest{
		Lang: constants.ZhTw,
	}
	gameRPC.On("GetMenuName", ctx, mockGetMenuNameRequest).Return(&seeder.SlotGameMenuName, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuName(constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameMenuName.GetMenuList(), resp)
}

func Test_GameContext_CacheMenuName_FromRPC_GRPCError_GetMenuName(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuNameKey, constants.ZhTw)

	redisMock.ExpectGet(key).RedisNil()

	mockGetMenuNameRequest := &gameclient.GetMenuNameRequest{
		Lang: constants.ZhTw,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetMenuName", ctx, mockGetMenuNameRequest).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuName(constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheMenuName_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuNameKey, constants.ZhTw)
	jsonMenuName, err := json.Marshal(seeder.SlotGameMenuName.GetMenuList())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonMenuName), cacheMenuNameExpiry).SetErr(redis.ErrClosed)

	mockGetMenuNameRequest := &gameclient.GetMenuNameRequest{
		Lang: constants.ZhTw,
	}
	gameRPC.On("GetMenuName", ctx, mockGetMenuNameRequest).Return(&seeder.SlotGameMenuName, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuName(constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameMenuName.GetMenuList(), resp)
}

func Test_GameContext_CacheMenuName_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuNameKey, constants.ZhTw)
	jsonMenuName, err := json.Marshal(seeder.SlotGameMenuName.GetMenuList())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonMenuName))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuName(constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameMenuName.GetMenuList(), resp)
}

func Test_GameContext_CacheMenuName_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuNameKey, constants.ZhTw)
	jsonMenuName, err := json.Marshal(seeder.SlotGameMenuName.GetMenuList())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(key, string(jsonMenuName), cacheMenuNameExpiry).SetVal("ok")

	mockGetMenuNameRequest := &gameclient.GetMenuNameRequest{
		Lang: constants.ZhTw,
	}
	gameRPC.On("GetMenuName", ctx, mockGetMenuNameRequest).Return(&seeder.SlotGameMenuName, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuName(constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameMenuName.GetMenuList(), resp)
}

func Test_GameContext_CacheMenuName_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(menuNameKey, constants.ZhTw)
	jsonMenuName, err := json.Marshal(seeder.SlotGameMenuName.GetMenuList())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSetEx(key, string(jsonMenuName), cacheMenuNameExpiry).SetVal("ok")

	mockGetMenuNameRequest := &gameclient.GetMenuNameRequest{
		Lang: constants.ZhTw,
	}
	gameRPC.On("GetMenuName", ctx, mockGetMenuNameRequest).Return(&seeder.SlotGameMenuName, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheMenuName(constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameMenuName.GetMenuList(), resp)
}

func Test_GameContext_CacheGameList_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameListKey, constants.BBSlot, constants.ZhTw)
	jsonGameList, err := json.Marshal(seeder.SlotGameList.GetData())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameList), cacheGameListExpiry).SetVal("ok")

	mockGameListRequest := &gameclient.GameListRequest{
		GameKind: constants.BBSlot,
		Lang:     constants.ZhTw,
	}
	gameRPC.On("GameList", ctx, mockGameListRequest).Return(&seeder.SlotGameList, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameList(constants.BBSlot, constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameList.GetData(), resp)
}

func Test_GameContext_CacheGameList_FromRPC_GRPCError_GameList(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameListKey, constants.BBSlot, constants.ZhTw)

	redisMock.ExpectGet(key).RedisNil()

	mockGameListRequest := &gameclient.GameListRequest{
		GameKind: constants.BBSlot,
		Lang:     constants.ZhTw,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GameList", ctx, mockGameListRequest).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameList(constants.BBSlot, constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheGameList_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameListKey, constants.BBSlot, constants.ZhTw)
	jsonGameList, err := json.Marshal(seeder.SlotGameList.GetData())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSetEx(key, string(jsonGameList), cacheGameListExpiry).SetErr(redis.ErrClosed)

	mockGameListRequest := &gameclient.GameListRequest{
		GameKind: constants.BBSlot,
		Lang:     constants.ZhTw,
	}
	gameRPC.On("GameList", ctx, mockGameListRequest).Return(&seeder.SlotGameList, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameList(constants.BBSlot, constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameList.GetData(), resp)
}

func Test_GameContext_CacheGameList_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameListKey, constants.BBSlot, constants.ZhTw)
	jsonGameList, err := json.Marshal(seeder.SlotGameList.GetData())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonGameList))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameList(constants.BBSlot, constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameList.GetData(), resp)
}

func Test_GameContext_CacheGameList_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameListKey, constants.BBSlot, constants.ZhTw)
	jsonGameList, err := json.Marshal(seeder.SlotGameList.GetData())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(key, string(jsonGameList), cacheGameListExpiry).SetVal("ok")

	mockGameListRequest := &gameclient.GameListRequest{
		GameKind: constants.BBSlot,
		Lang:     constants.ZhTw,
	}
	gameRPC.On("GameList", ctx, mockGameListRequest).Return(&seeder.SlotGameList, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameList(constants.BBSlot, constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameList.GetData(), resp)
}

func Test_GameContext_CacheGameList_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(gameListKey, constants.BBSlot, constants.ZhTw)
	jsonGameList, err := json.Marshal(seeder.SlotGameList.GetData())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSetEx(key, string(jsonGameList), cacheGameListExpiry).SetVal("ok")

	mockGameListRequest := &gameclient.GameListRequest{
		GameKind: constants.BBSlot,
		Lang:     constants.ZhTw,
	}
	gameRPC.On("GameList", ctx, mockGameListRequest).Return(&seeder.SlotGameList, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameList(constants.BBSlot, constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameList.GetData(), resp)
}

func Test_GameContext_GameIconKind(t *testing.T) {
	gameRPC := newMockGameRPC()

	gameRPC.On("GameIconKind", ctx, &gameclient.EmptyRequest{}).Return(&seeder.SlotGameIconKind, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GameIconKind()

	assert.NoError(t, err)
	assert.Equal(t, seeder.SlotGameIconKindMap, resp)
}

func Test_GameContext_GameIconKind_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GameIconKind", ctx, &gameclient.EmptyRequest{}).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.GameIconKind()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_GetCacheGuestCasinoLink(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(guestCasinoLinkKey, seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)
	jsonGuestCasinoLink, err := json.Marshal(seeder.SlotCacheGuestCasinoLink)
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonGuestCasinoLink))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp := gameCtx.GetCacheGuestCasinoLink(seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, seeder.SlotCacheGuestCasinoLink, resp)
}

func Test_GameContext_GetCacheGuestCasinoLink_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(guestCasinoLinkKey, seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)

	redisMock.ExpectGet(key).SetVal("ok")

	gameCtx := New(ctx, redisCache, gameRPC)
	resp := gameCtx.GetCacheGuestCasinoLink(seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, []types.CasinoLinkData(nil), resp)
}

func Test_GameContext_GetCacheGuestCasinoLink_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(guestCasinoLinkKey, seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp := gameCtx.GetCacheGuestCasinoLink(seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, []types.CasinoLinkData(nil), resp)
}

func Test_GameContext_SetCacheGuestCasinoLink(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(guestCasinoLinkKey, seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)
	jsonGuestCasinoLink, err := json.Marshal(seeder.SlotCacheGuestCasinoLink)
	assert.NoError(t, err)

	redisMock.ExpectSetEx(key, string(jsonGuestCasinoLink), cacheGuestCasinoLinkExpiry).SetVal("ok")

	gameCtx := New(ctx, redisCache, gameRPC)
	gameCtx.SetCacheGuestCasinoLink(seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw, seeder.SlotCacheGuestCasinoLink)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func Test_GameContext_SetCacheGuestCasinoLink_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	key := fmt.Sprintf(guestCasinoLinkKey, seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw)
	jsonGuestCasinoLink, err := json.Marshal(seeder.SlotCacheGuestCasinoLink)
	assert.NoError(t, err)

	redisMock.ExpectSetEx(key, string(jsonGuestCasinoLink), cacheGuestCasinoLinkExpiry).SetErr(redis.ErrClosed)

	gameCtx := New(ctx, redisCache, gameRPC)
	gameCtx.SetCacheGuestCasinoLink(seeder.BgpHallID, constants.BBSlot, "pc", constants.ZhTw, seeder.SlotCacheGuestCasinoLink)

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func Test_GameContext_GetUserFavorite_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetUserFavoriteRequest{
		GameKind: constants.BBSlot,
		UserId:   seeder.UserGTPTestID,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())
	jsonUserFavorite, err := json.Marshal(seeder.UserFavorite.GetUserFavorite())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSet(key, string(jsonUserFavorite), 0).SetVal("ok")

	gameRPC.On("GetUserFavorite", ctx, mockRequest).Return(&seeder.UserFavorite, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	resp, err := gameCtx.GetUserFavorite(mockRequest.GetUserId(), mockRequest.GetGameKind())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.UserFavorite.GetUserFavorite(), resp)
}

func Test_GameContext_GetUserFavorite_FromRPC_GRPCError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetUserFavoriteRequest{
		GameKind: constants.BBSlot,
		UserId:   seeder.UserGTPTestID,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())

	redisMock.ExpectGet(key).RedisNil()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GetUserFavorite", ctx, mockRequest).Return(nil, mockError)
	gameCtx := New(ctx, redisCache, gameRPC)

	resp, err := gameCtx.GetUserFavorite(mockRequest.GetUserId(), mockRequest.GetGameKind())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_GetUserFavorite_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetUserFavoriteRequest{
		GameKind: constants.BBSlot,
		UserId:   seeder.UserGTPTestID,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())
	jsonUserFavorite, err := json.Marshal(seeder.UserFavorite.GetUserFavorite())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).RedisNil()
	redisMock.ExpectSet(key, string(jsonUserFavorite), 0).SetErr(redis.ErrClosed)

	gameRPC.On("GetUserFavorite", ctx, mockRequest).Return(&seeder.UserFavorite, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	resp, err := gameCtx.GetUserFavorite(mockRequest.GetUserId(), mockRequest.GetGameKind())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.UserFavorite.GetUserFavorite(), resp)
}

func Test_GameContext_GetUserFavorite_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetUserFavoriteRequest{
		GameKind: constants.BBSlot,
		UserId:   seeder.UserGTPTestID,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())
	jsonUserFavorite, err := json.Marshal(seeder.UserFavorite.GetUserFavorite())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal(string(jsonUserFavorite))

	gameCtx := New(ctx, redisCache, gameRPC)

	resp, err := gameCtx.GetUserFavorite(mockRequest.GetUserId(), mockRequest.GetGameKind())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.UserFavorite.GetUserFavorite(), resp)
}

func Test_GameContext_GetUserFavorite_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetUserFavoriteRequest{
		GameKind: constants.BBSlot,
		UserId:   seeder.UserGTPTestID,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())
	jsonUserFavorite, err := json.Marshal(seeder.UserFavorite.GetUserFavorite())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetErr(redis.ErrClosed)
	redisMock.ExpectSet(key, string(jsonUserFavorite), 0).SetVal("ok")

	gameRPC.On("GetUserFavorite", ctx, mockRequest).Return(&seeder.UserFavorite, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	resp, err := gameCtx.GetUserFavorite(mockRequest.GetUserId(), mockRequest.GetGameKind())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.UserFavorite.GetUserFavorite(), resp)
}

func Test_GameContext_GetUserFavorite_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	mockRequest := &gameclient.GetUserFavoriteRequest{
		GameKind: constants.BBSlot,
		UserId:   seeder.UserGTPTestID,
	}

	key := fmt.Sprintf(userFavoriteKey, mockRequest.GetUserId(), mockRequest.GetGameKind())
	jsonUserFavorite, err := json.Marshal(seeder.UserFavorite.GetUserFavorite())
	assert.NoError(t, err)

	redisMock.ExpectGet(key).SetVal("ok")
	redisMock.ExpectSet(key, string(jsonUserFavorite), 0).SetVal("ok")

	gameRPC.On("GetUserFavorite", ctx, mockRequest).Return(&seeder.UserFavorite, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	resp, err := gameCtx.GetUserFavorite(mockRequest.GetUserId(), mockRequest.GetGameKind())

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.UserFavorite.GetUserFavorite(), resp)
}

func Test_GameContext_GetSlotBulletinList_Get(t *testing.T) {
	gameRPC := newMockGameRPC()
	mockRequest := &gameclient.BulletinRequest{
		StartDate: "2024-09-19",
		EndDate:   "2024-09-20",
		Lang:      constants.ZhTw,
	}

	gameRPC.On("BulletinList", ctx, mockRequest).Return(&seeder.SlotBulletinList, nil)
	gameCtx := New(ctx, redisCache, gameRPC)

	request := SlotBulletinListRequest{
		Lang:      mockRequest.GetLang(),
		StartDate: mockRequest.GetStartDate(),
		EndDate:   mockRequest.GetEndDate(),
	}

	resp, err := gameCtx.GetSlotBulletinList(request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.SlotBulletinList, resp)
}

func Test_GameContext_GetSlotBulletinList_GetError(t *testing.T) {
	gameRPC := newMockGameRPC()
	mockRequest := &gameclient.BulletinRequest{
		StartDate: "2024-09-19",
		EndDate:   "2024-09-20",
		Lang:      constants.ZhTw,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("BulletinList", ctx, mockRequest).Return(nil, mockError)
	gameCtx := New(ctx, redisCache, gameRPC)

	request := SlotBulletinListRequest{
		Lang:      mockRequest.GetLang(),
		StartDate: mockRequest.GetStartDate(),
		EndDate:   mockRequest.GetEndDate(),
	}

	resp, err := gameCtx.GetSlotBulletinList(request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheGameKindList_FromRPC(t *testing.T) {
	gameRPC := newMockGameRPC()

	jsonGameKindList, err := json.Marshal(seeder.GameKindListResponse.GetGameKindList())
	assert.NoError(t, err)

	redisMock.ExpectGet(gameKindListKey).RedisNil()
	redisMock.ExpectSetEx(gameKindListKey, string(jsonGameKindList), cacheGameListExpiry).SetVal("ok")

	gameRPC.On("GameKindList", ctx, &gameclient.EmptyRequest{}).Return(&seeder.GameKindListResponse, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameKindList()

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameKindListResponse.GetGameKindList(), resp)
}

func Test_GameContext_CacheGameKindList_FromRPC_GRPCError_GameKindList(t *testing.T) {
	gameRPC := newMockGameRPC()

	redisMock.ExpectGet(gameKindListKey).RedisNil()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	gameRPC.On("GameKindList", ctx, &gameclient.EmptyRequest{}).Return(nil, mockError)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameKindList()

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CacheGameKindList_FromRPC_SetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	jsonGameKindList, err := json.Marshal(seeder.GameKindListResponse.GetGameKindList())
	assert.NoError(t, err)

	redisMock.ExpectGet(gameKindListKey).RedisNil()
	redisMock.ExpectSetEx(gameKindListKey, string(jsonGameKindList), cacheGameListExpiry).SetErr(redis.ErrClosed)

	gameRPC.On("GameKindList", ctx, &gameclient.EmptyRequest{}).Return(&seeder.GameKindListResponse, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameKindList()

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameKindListResponse.GetGameKindList(), resp)
}

func Test_GameContext_CacheGameKindList_FromCache(t *testing.T) {
	gameRPC := newMockGameRPC()

	jsonGameKindList, err := json.Marshal(seeder.GameKindListResponse.GetGameKindList())
	assert.NoError(t, err)

	redisMock.ExpectGet(gameKindListKey).SetVal(string(jsonGameKindList))

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameKindList()

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameKindListResponse.GetGameKindList(), resp)
}

func Test_GameContext_CacheGameKindList_FromCache_GetRedisError(t *testing.T) {
	gameRPC := newMockGameRPC()

	jsonGameKindList, err := json.Marshal(seeder.GameKindListResponse.GetGameKindList())
	assert.NoError(t, err)

	redisMock.ExpectGet(gameKindListKey).SetErr(redis.ErrClosed)
	redisMock.ExpectSetEx(gameKindListKey, string(jsonGameKindList), cacheGameListExpiry).SetVal("ok")

	gameRPC.On("GameKindList", ctx, &gameclient.EmptyRequest{}).Return(&seeder.GameKindListResponse, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameKindList()

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameKindListResponse.GetGameKindList(), resp)
}

func Test_GameContext_CacheGameKindList_FromCache_GetRedisJSONError(t *testing.T) {
	gameRPC := newMockGameRPC()

	jsonGameKindList, err := json.Marshal(seeder.GameKindListResponse.GetGameKindList())
	assert.NoError(t, err)

	redisMock.ExpectGet(gameKindListKey).SetVal("ok")
	redisMock.ExpectSetEx(gameKindListKey, string(jsonGameKindList), cacheGameListExpiry).SetVal("ok")

	gameRPC.On("GameKindList", ctx, &gameclient.EmptyRequest{}).Return(&seeder.GameKindListResponse, nil)

	gameCtx := New(ctx, redisCache, gameRPC)
	resp, err := gameCtx.CacheGameKindList()

	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()

	assert.NoError(t, err)
	assert.Equal(t, seeder.GameKindListResponse.GetGameKindList(), resp)
}
