package game

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gbh/errorx"
	"gbh/frontend/internal/constants"
	"gbh/frontend/internal/types"
	"gbh/game/gameclient"
	"gbh/redis"
	"gbh/utils/strutil"
	"time"
)

type Context interface {
	GameDomain(gameKind uint32, hallId uint32) (*gameclient.GameDomainResponse, error)
	CacheGameDomain(gameKind uint32, hallId uint32) ([]string, error)
	GetLobbySwitch(req GetLobbySwitchRequest) ([]*gameclient.LobbySwitch, error)
	GetLobbySwitchByHallID(hallId uint32, filterLobbyEnable bool, lobbySwitchEnable *bool) ([]*gameclient.LobbySwitch, error)
	LobbyGameLink(req LobbyGameLinkRequest) (*gameclient.LobbyLinkResponse, error)
	AddUserFavorite(req SetUserFavoriteRequest) error
	DeleteUserFavorite(req SetUserFavoriteRequest) error
	CacheMenuInfo(gameKind uint32) (map[uint32]*types.MenuInfoData, error)
	CacheGameSettingInfo(gameKind uint32, hallId uint32) (map[uint32]*gameclient.GameInfo, error)
	CacheMenuName(lang string) ([]*gameclient.MenuInfo, error)
	CacheGameList(gameKind uint32, lang string) ([]*gameclient.GameList, error)
	GameIconKind() (map[uint32]string, error)
	GetCacheGuestCasinoLink(hallId uint32, gameKind uint32, deviceTag string, lang string) []types.CasinoLinkData
	SetCacheGuestCasinoLink(hallId uint32, gameKind uint32, deviceTag string, lang string, gameLinkList []types.CasinoLinkData)
	GetUserFavorite(userId uint64, gameKind uint32) ([]*gameclient.UserFavorite, error)
	GetSlotBulletinList(req SlotBulletinListRequest) (*gameclient.BulletinResponse, error)
	CacheGameKindList() ([]*gameclient.GameKindList, error)
}

type gameContext struct {
	ctx     context.Context
	cache   redis.Redis
	GameRPC gameclient.Game
}

type GetLobbySwitchRequest struct {
	AgentId    uint32
	AllParents []uint32
	UserId     *uint32
}

type SlotBulletinListRequest struct {
	StartDate string
	EndDate   string
	Lang      string
}

const (
	cacheLobbySwitchExpiry         = 30 * time.Second
	cacheGameDomainExpiry          = 60 * time.Second
	cacheHallLobbySwitchListExpiry = 14400 * time.Second
	cacheMenuInfoExpiry            = 14400 * time.Second
	cacheGameSettingInfoExpiry     = 600 * time.Second
	cacheMenuNameExpiry            = 600 * time.Second
	cacheGameListExpiry            = 600 * time.Second
	cacheGuestCasinoLinkExpiry     = 600 * time.Second
	cacheGameKindListExpiry        = 600 * time.Second
	gameDomainKey                  = "game_domain:%d:%d"
	agentLobbySwitchKey            = "agent:lobby_switch:%d"
	hallLobbySwitchListKey         = "hall:lobby_switch:%d:filter:%t:enable:%v"
	userFavoriteKey                = "user_favorite:%d:%d"
	menuInfoKey                    = "menu_info:%d"
	gameSettingInfoKey             = "game_setting_info:%d:%d"
	menuNameKey                    = "menu_name:%s"
	gameListKey                    = "game_list:%d:%s"
	guestCasinoLinkKey             = "guest_casino_link:%d:%d:%s:%s"
	gameKindListKey                = "game_kind_list"
)

type LobbyGameLinkRequest struct {
	GameKind     uint32
	Session      string
	Lang         *string
	IP           *string
	ExitOption   *uint32
	ExitURLParam *string
}

type SetUserFavoriteRequest struct {
	UserId   uint32
	GameKind uint32
	GameId   uint32
}

func New(ctx context.Context, cache redis.Redis, gameRPC gameclient.Game) Context {
	return &gameContext{
		ctx:     ctx,
		cache:   cache,
		GameRPC: gameRPC,
	}
}

func (c *gameContext) GameDomain(gameKind uint32, hallId uint32) (*gameclient.GameDomainResponse, error) {
	game, err := c.GameRPC.GameDomain(c.ctx, &gameclient.GameDomainRequest{
		GameKind: gameKind,
		HallId:   hallId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return game, nil
}

func (c *gameContext) CacheGameDomain(gameKind uint32, hallId uint32) ([]string, error) {
	cacheGameDomain := c.getCacheGameDomain(gameKind, hallId)

	if cacheGameDomain != nil {
		return cacheGameDomain, nil
	}

	resp, err := c.GameDomain(gameKind, hallId)

	if err != nil {
		return nil, err
	}

	gameDomain := resp.GetDomain()

	c.setCacheGameDomain(gameKind, hallId, gameDomain)

	return gameDomain, nil
}

func (c *gameContext) getCacheGameDomain(gameKind uint32, hallId uint32) []string {
	key := fmt.Sprintf(gameDomainKey, gameKind, hallId)
	jsonGameDomain, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	var result []string
	jsonErr := json.Unmarshal([]byte(jsonGameDomain), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheGameDomain(gameKind uint32, hallId uint32, gameDomain []string) {
	jsonData, err := json.Marshal(gameDomain)

	if err != nil {
		return
	}

	key := fmt.Sprintf(gameDomainKey, gameKind, hallId)
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheGameDomainExpiry).Err()

	if err != nil {
		return
	}
}

func (c *gameContext) GetLobbySwitchByHallID(hallId uint32, filterLobbyEnable bool, lobbySwitchEnable *bool) ([]*gameclient.LobbySwitch, error) {
	cacheLobbySwitch := c.getCacheHallLobbySwitch(hallId, filterLobbyEnable, lobbySwitchEnable)

	if cacheLobbySwitch != nil {
		return cacheLobbySwitch, nil
	}

	request := &gameclient.GetLobbySwitchByHallIDRequest{
		HallId:            hallId,
		FilterLobbyEnable: filterLobbyEnable,
	}

	if lobbySwitchEnable != nil {
		request.LobbySwitchEnable = &gameclient.BoolValue{Value: *lobbySwitchEnable}
	}

	resp, err := c.GameRPC.GetLobbySwitchByHallID(c.ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	lobbySwitch := resp.GetLobbySwitch()

	c.setCacheHallLobbySwitch(hallId, filterLobbyEnable, lobbySwitchEnable, lobbySwitch)

	return lobbySwitch, nil
}

func (c *gameContext) getCacheHallLobbySwitch(hallId uint32, filterLobbyEnable bool, lobbySwitchEnable *bool) []*gameclient.LobbySwitch {
	key := fmt.Sprintf(hallLobbySwitchListKey, hallId, filterLobbyEnable, "nil")
	if lobbySwitchEnable != nil {
		key = fmt.Sprintf(hallLobbySwitchListKey, hallId, filterLobbyEnable, *lobbySwitchEnable)
	}
	jsonHallLobbySwitchList, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	var result []*gameclient.LobbySwitch
	jsonErr := json.Unmarshal([]byte(jsonHallLobbySwitchList), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheHallLobbySwitch(hallId uint32, filterLobbyEnable bool, lobbySwitchEnable *bool, lobbySwitch []*gameclient.LobbySwitch) {
	jsonData, err := json.Marshal(lobbySwitch)

	if err != nil {
		return
	}

	key := fmt.Sprintf(hallLobbySwitchListKey, hallId, filterLobbyEnable, "nil")
	if lobbySwitchEnable != nil {
		key = fmt.Sprintf(hallLobbySwitchListKey, hallId, filterLobbyEnable, *lobbySwitchEnable)
	}
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheHallLobbySwitchListExpiry).Err()

	if err != nil {
		return
	}
}

func (c *gameContext) LobbyGameLink(req LobbyGameLinkRequest) (*gameclient.LobbyLinkResponse, error) {
	request := &gameclient.LobbyLinkRequest{
		GameKind: req.GameKind,
		Session:  req.Session,
	}

	if req.Lang != nil {
		request.Lang = &gameclient.StringValue{Value: *req.Lang}
	}

	if req.IP != nil {
		request.Ip = &gameclient.StringValue{Value: *req.IP}
	}

	if req.ExitOption != nil {
		request.ExitOption = &gameclient.Uint32Value{Value: *req.ExitOption}
		if *req.ExitOption == 2 && req.ExitURLParam != nil {
			request.ExitUrl = &gameclient.StringValue{Value: *req.ExitURLParam}
		}
	}

	resp, err := c.GameRPC.LobbyLink(c.ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *gameContext) GetLobbySwitch(req GetLobbySwitchRequest) ([]*gameclient.LobbySwitch, error) {
	cacheLobbySwitch := c.getCacheLobbySwitch(req.AgentId)

	if cacheLobbySwitch != nil {
		return cacheLobbySwitch, nil
	}

	request := &gameclient.GetLobbySwitchRequest{
		AgentId:    req.AgentId,
		AllParents: req.AllParents,
	}

	if req.UserId != nil {
		request.UserId = &gameclient.Uint32Value{
			Value: *req.UserId,
		}
	}

	resp, err := c.GameRPC.GetLobbySwitch(c.ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	lobbySwitch := resp.GetLobbySwitch()

	c.setCacheLobbySwitch(req.AgentId, lobbySwitch)

	return lobbySwitch, nil
}

func (c *gameContext) getCacheLobbySwitch(agentId uint32) []*gameclient.LobbySwitch {
	key := fmt.Sprintf(agentLobbySwitchKey, agentId)
	resp, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	var result []*gameclient.LobbySwitch
	jsonErr := json.Unmarshal([]byte(resp), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheLobbySwitch(agentId uint32, lobbySwitch []*gameclient.LobbySwitch) {
	jsonData, err := json.Marshal(lobbySwitch)

	if err != nil {
		return
	}

	key := fmt.Sprintf(agentLobbySwitchKey, agentId)
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheLobbySwitchExpiry).Err()

	if err != nil {
		return
	}
}

func (c *gameContext) AddUserFavorite(req SetUserFavoriteRequest) error {
	_, err := c.GameRPC.AddUserFavorite(c.ctx, &gameclient.AddUserFavoriteRequest{
		UserId:   req.UserId,
		GameKind: req.GameKind,
		GameId:   req.GameId,
	})

	if err != nil {
		returnErr := errorx.GRPCErrorToErrorx(err)
		if !errors.Is(returnErr, errorx.GameDuplicateUserFavorite) {
			return returnErr
		}
	}

	if delErr := c.delCacheUserFavorite(req.UserId, req.GameKind); delErr != nil {
		return delErr
	}

	return nil
}

func (c *gameContext) delCacheUserFavorite(userId uint32, gameKind uint32) error {
	key := fmt.Sprintf(userFavoriteKey, userId, gameKind)

	err := c.cache.Del(c.ctx, key).Err()
	if err != nil {
		return errorx.RedisError
	}

	return nil
}

func (c *gameContext) DeleteUserFavorite(req SetUserFavoriteRequest) error {
	_, err := c.GameRPC.DeleteUserFavorite(c.ctx, &gameclient.DeleteUserFavoriteRequest{
		UserId:   req.UserId,
		GameKind: req.GameKind,
		GameId:   req.GameId,
	})

	if err != nil {
		returnErr := errorx.GRPCErrorToErrorx(err)
		if !errors.Is(returnErr, errorx.GameUserFavoriteNoFound) {
			return returnErr
		}
	}

	if delErr := c.delCacheUserFavorite(req.UserId, req.GameKind); delErr != nil {
		return delErr
	}

	return nil
}

// 選單資料 ByCache
func (c *gameContext) CacheMenuInfo(gameKind uint32) (map[uint32]*types.MenuInfoData, error) {
	cacheMenuSort := c.getCacheMenuInfo(gameKind)
	if len(cacheMenuSort) > 0 {
		return cacheMenuSort, nil
	}

	menuCategory, err := c.GetMenuCategory(gameKind)
	if err != nil {
		return nil, err
	}

	menuSort, err := c.GetMenuSort(gameKind)
	if err != nil {
		return nil, err
	}

	menuInfo := getMenuInfo(menuSort.GetData(), menuCategory.GetData())

	c.setCacheMenuInfo(gameKind, menuInfo)

	return menuInfo, nil
}

func getMenuInfo(menuSortInfo []*gameclient.MenuSortInfo, menuCategoryInfo []*gameclient.CategoryInfo) map[uint32]*types.MenuInfoData {
	menuInfo := map[uint32]*types.MenuInfoData{}

	// 組遊戲選單 games
	menuInfoGames := map[uint32][]types.MenuInfoGameData{}
	for _, m := range menuSortInfo {
		menuInfoGames[m.GetMenuId()] = append(menuInfoGames[m.GetMenuId()], types.MenuInfoGameData{
			Sort:     m.GetSort(),
			GameType: m.GetGameType(),
		})
	}

	// 組選單資料
	for _, c := range menuCategoryInfo {
		menuInfo[c.GetMenuId()] = &types.MenuInfoData{
			MenuID: c.GetMenuId(),
			TopID:  c.GetTopId(),
			Depth:  c.GetDepth(),
			Sort:   c.GetSort(),
			Games:  []types.MenuInfoGameData{},
		}

		menuInfo[c.GetMenuId()].Games = menuInfoGames[c.GetMenuId()]
	}

	return menuInfo
}

// 取得遊戲分類資訊
func (c *gameContext) GetMenuCategory(gameKind uint32) (*gameclient.GetCategoryResponse, error) {
	category, err := c.GameRPC.GetCategory(c.ctx, &gameclient.GetCategoryRequest{
		GameKind: gameKind,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return category, nil
}

// 取得遊戲產品排序
func (c *gameContext) GetMenuSort(gameKind uint32) (*gameclient.GetMenuSortResponse, error) {
	menuSort, err := c.GameRPC.GetMenuSort(c.ctx, &gameclient.GetMenuSortRequest{
		GameKind: gameKind,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return menuSort, nil
}

func (c *gameContext) getCacheMenuInfo(gameKind uint32) map[uint32]*types.MenuInfoData {
	key := fmt.Sprintf(menuInfoKey, gameKind)
	jsonMenuInfo, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	result := map[uint32]*types.MenuInfoData{}
	jsonErr := json.Unmarshal([]byte(jsonMenuInfo), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheMenuInfo(gameKind uint32, menuInfo map[uint32]*types.MenuInfoData) {
	jsonData, err := json.Marshal(menuInfo)

	if err != nil {
		return
	}

	key := fmt.Sprintf(menuInfoKey, gameKind)
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheMenuInfoExpiry).Err()

	if err != nil {
		return
	}
}

// 取得遊戲產品相關設定 ByCache
func (c *gameContext) CacheGameSettingInfo(gameKind uint32, hallId uint32) (map[uint32]*gameclient.GameInfo, error) {
	cacheGameInfo := c.getCacheGameSettingInfo(gameKind, hallId)
	if len(cacheGameInfo) > 0 {
		return cacheGameInfo, nil
	}

	gameInfo, err := c.GetGameInfo(gameKind)
	if err != nil {
		return nil, err
	}

	lobbyGameSwitch, err := c.GetLobbyGameEntranceSwitch(gameKind, hallId)
	if err != nil {
		return nil, err
	}

	gameSettingInfo := getGameSettingInfo(gameInfo, lobbyGameSwitch)

	c.setCacheGameSettingInfo(gameKind, hallId, gameSettingInfo)

	return gameSettingInfo, nil
}

// 加入大廳遊戲開關重組 gameInfo
func getGameSettingInfo(gameInfo *gameclient.GetGameInfoResponse, lobbyGameSwitch []*gameclient.LobbyGameEntranceSwitch) map[uint32]*gameclient.GameInfo {
	gameInfoMap := map[string]*gameclient.GameInfo{}
	for _, data := range gameInfo.GetData() {
		// 彩票外接遊戲ID, 例如:BBLT, BBPK
		if data.GetLobby() == constants.BBLottery && data.GetExternalId() != "" {
			gameInfoMap[data.GetExternalId()] = data
			continue
		}

		gameInfoMap[strutil.Uint32ToString(data.GetGameType())] = data
	}

	gameSettingInfo := map[uint32]*gameclient.GameInfo{}
	for _, game := range lobbyGameSwitch {
		if gameInfoMap[game.GetGameType()] == nil {
			continue
		}

		gameInfoData := gameInfoMap[game.GetGameType()]
		gameType := gameInfoData.GetGameType()

		var enable, pcEnable, mobileEnable bool

		// pc版判斷
		if game.GetPcEnable() {
			enable = true
			pcEnable = true
		}

		// 手機版判斷
		if game.GetMobileEnable() {
			enable = true
			mobileEnable = true
		}

		gameInfoData.Enable = enable             // 大開關
		gameInfoData.PcEnable = pcEnable         // PC版開關
		gameInfoData.MobileEnable = mobileEnable // 手機版開關
		gameInfoData.AllowList = []uint32{}      // 白名單
		gameInfoData.BlockList = []uint32{}      // 黑名單

		gameSettingInfo[gameType] = gameInfoData
	}

	return gameSettingInfo
}

// 取得遊戲產品相關設定 DB 資料
func (c *gameContext) GetGameInfo(gameKind uint32) (*gameclient.GetGameInfoResponse, error) {
	// 取得遊戲產品相關設定 DB 資料
	gameInfo, err := c.GameRPC.GetGameInfo(c.ctx, &gameclient.GetGameInfoRequest{
		GameKind: gameKind,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return gameInfo, nil
}

// 取得單遊戲入口開關
func (c *gameContext) GetLobbyGameEntranceSwitch(gameKind uint32, hallId uint32) ([]*gameclient.LobbyGameEntranceSwitch, error) {
	lobbyGameSwitch, err := c.GameRPC.GetLobbyGameEntranceSwitch(c.ctx, &gameclient.GetLobbyGameEntranceSwitchRequest{
		GameKind: gameKind,
		HallId:   hallId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return lobbyGameSwitch.GetData(), nil
}

func (c *gameContext) getCacheGameSettingInfo(gameKind uint32, hallId uint32) map[uint32]*gameclient.GameInfo {
	key := fmt.Sprintf(gameSettingInfoKey, gameKind, hallId)
	jsonGameSettingInfo, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	result := map[uint32]*gameclient.GameInfo{}

	jsonErr := json.Unmarshal([]byte(jsonGameSettingInfo), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheGameSettingInfo(gameKind uint32, hallId uint32, gameInfo map[uint32]*gameclient.GameInfo) {
	jsonData, err := json.Marshal(gameInfo)

	if err != nil {
		return
	}

	key := fmt.Sprintf(gameSettingInfoKey, gameKind, hallId)
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheGameSettingInfoExpiry).Err()

	if err != nil {
		return
	}
}

// 取得遊戲分類名稱 ByCache
func (c *gameContext) CacheMenuName(lang string) ([]*gameclient.MenuInfo, error) {
	cacheMenuName := c.getCacheMenuName(lang)
	if len(cacheMenuName) > 0 {
		return cacheMenuName, nil
	}

	menuName, err := c.GetMenuName(lang)
	if err != nil {
		return nil, err
	}

	c.setCacheMenuName(lang, menuName)

	return menuName, nil
}

// 取得遊戲分類名稱
func (c *gameContext) GetMenuName(lang string) ([]*gameclient.MenuInfo, error) {
	menuName, err := c.GameRPC.GetMenuName(c.ctx, &gameclient.GetMenuNameRequest{
		Lang: lang,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return menuName.GetMenuList(), nil
}

func (c *gameContext) getCacheMenuName(lang string) []*gameclient.MenuInfo {
	key := fmt.Sprintf(menuNameKey, lang)
	jsonMenuName, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	result := []*gameclient.MenuInfo{}

	jsonErr := json.Unmarshal([]byte(jsonMenuName), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheMenuName(lang string, menuName []*gameclient.MenuInfo) {
	jsonData, err := json.Marshal(menuName)

	if err != nil {
		return
	}

	key := fmt.Sprintf(menuNameKey, lang)
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheMenuNameExpiry).Err()

	if err != nil {
		return
	}
}

// 取得遊戲產品列表 ByCache
func (c *gameContext) CacheGameList(gameKind uint32, lang string) ([]*gameclient.GameList, error) {
	cacheGameList := c.getCacheGameList(gameKind, lang)
	if len(cacheGameList) > 0 {
		return cacheGameList, nil
	}

	gameList, err := c.GetGameList(gameKind, lang)
	if err != nil {
		return nil, err
	}

	c.setCacheGameList(gameKind, lang, gameList)

	return gameList, nil
}

// 取得遊戲產品列表
func (c *gameContext) GetGameList(gameKind uint32, lang string) ([]*gameclient.GameList, error) {
	gameList, err := c.GameRPC.GameList(c.ctx, &gameclient.GameListRequest{
		GameKind: gameKind,
		Lang:     lang,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return gameList.GetData(), nil
}

func (c *gameContext) getCacheGameList(gameKind uint32, lang string) []*gameclient.GameList {
	key := fmt.Sprintf(gameListKey, gameKind, lang)
	jsonGameList, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	result := []*gameclient.GameList{}

	jsonErr := json.Unmarshal([]byte(jsonGameList), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheGameList(gameKind uint32, lang string, gameList []*gameclient.GameList) {
	jsonData, err := json.Marshal(gameList)

	if err != nil {
		return
	}

	key := fmt.Sprintf(gameListKey, gameKind, lang)
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheGameListExpiry).Err()

	if err != nil {
		return
	}
}

// 取得客端遊戲標籤
func (c *gameContext) GameIconKind() (map[uint32]string, error) {
	gameIcon, err := c.GameRPC.GameIconKind(c.ctx, &gameclient.EmptyRequest{})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	gameIconKindMap := map[uint32]string{}
	for _, icon := range gameIcon.GetIconKind() {
		gameIconKindMap[icon.GetKind()] = icon.GetIcon()
	}

	return gameIconKindMap, nil
}

// 取得未登入會員遊戲連結 ByCache
func (c *gameContext) GetCacheGuestCasinoLink(hallId uint32, gameKind uint32, deviceTag string, lang string) []types.CasinoLinkData {
	key := fmt.Sprintf(guestCasinoLinkKey, hallId, gameKind, deviceTag, lang)
	jsonGuestCasinoLink, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	var result []types.CasinoLinkData
	jsonErr := json.Unmarshal([]byte(jsonGuestCasinoLink), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

// 未登入會員遊戲連結資料寫進 redis
func (c *gameContext) SetCacheGuestCasinoLink(hallId uint32, gameKind uint32, deviceTag string, lang string, gameLinkList []types.CasinoLinkData) {
	jsonData, err := json.Marshal(gameLinkList)

	if err != nil {
		return
	}

	key := fmt.Sprintf(guestCasinoLinkKey, hallId, gameKind, deviceTag, lang)
	err = c.cache.SetEx(c.ctx, key, string(jsonData), cacheGuestCasinoLinkExpiry).Err()

	if err != nil {
		return
	}
}

func (c *gameContext) GetUserFavorite(userId uint64, gameKind uint32) ([]*gameclient.UserFavorite, error) {
	cacheUserFavorite := c.getCacheUserFavorite(userId, gameKind)

	if cacheUserFavorite != nil {
		return cacheUserFavorite, nil
	}

	resp, err := c.GameRPC.GetUserFavorite(c.ctx, &gameclient.GetUserFavoriteRequest{
		UserId:   userId,
		GameKind: gameKind,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	userFavorite := resp.GetUserFavorite()

	c.setCacheUserFavorite(userId, gameKind, userFavorite)

	return userFavorite, nil
}

func (c *gameContext) getCacheUserFavorite(userId uint64, gameKind uint32) []*gameclient.UserFavorite {
	key := fmt.Sprintf(userFavoriteKey, userId, gameKind)
	resp, err := c.cache.Get(c.ctx, key).Result()

	if err != nil {
		return nil
	}

	var result []*gameclient.UserFavorite
	jsonErr := json.Unmarshal([]byte(resp), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheUserFavorite(userId uint64, gameKind uint32, userFavorite []*gameclient.UserFavorite) {
	jsonData, err := json.Marshal(userFavorite)

	if err != nil {
		return
	}

	key := fmt.Sprintf(userFavoriteKey, userId, gameKind)
	err = c.cache.Set(c.ctx, key, string(jsonData), 0).Err()

	if err != nil {
		return
	}
}

func (c *gameContext) GetSlotBulletinList(req SlotBulletinListRequest) (*gameclient.BulletinResponse, error) {
	bulletinReq := gameclient.BulletinRequest{
		Lang:      req.Lang,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}

	resp, err := c.GameRPC.BulletinList(c.ctx, &bulletinReq)
	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

// 取得遊戲大廳代碼 ByCache
func (c *gameContext) CacheGameKindList() ([]*gameclient.GameKindList, error) {
	cacheGameKindList := c.getCacheGameKindList()
	if len(cacheGameKindList) > 0 {
		return cacheGameKindList, nil
	}

	gameKindList, err := c.GameKindList()
	if err != nil {
		return nil, err
	}

	c.setCacheGameKindList(gameKindList)

	return gameKindList, nil
}

// 取得遊戲大廳代碼
func (c *gameContext) GameKindList() ([]*gameclient.GameKindList, error) {
	gameKindList, err := c.GameRPC.GameKindList(c.ctx, &gameclient.EmptyRequest{})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return gameKindList.GetGameKindList(), nil
}

func (c *gameContext) getCacheGameKindList() []*gameclient.GameKindList {
	jsonGameKindList, err := c.cache.Get(c.ctx, gameKindListKey).Result()

	if err != nil {
		return nil
	}

	result := []*gameclient.GameKindList{}

	jsonErr := json.Unmarshal([]byte(jsonGameKindList), &result)

	if jsonErr != nil {
		return nil
	}

	return result
}

func (c *gameContext) setCacheGameKindList(gameKindList []*gameclient.GameKindList) {
	jsonData, err := json.Marshal(gameKindList)

	if err != nil {
		return
	}

	err = c.cache.SetEx(c.ctx, gameKindListKey, string(jsonData), cacheGameKindListExpiry).Err()

	if err != nil {
		return
	}
}
