package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/livegame/internal/config"
	"gbh/livegame/internal/server"
	"gbh/livegame/internal/svc"
	"gbh/logger"
	"gbh/proto/livegame"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/livegame.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	db, err := database.New(c.WagersDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	tipDB, err := database.New(c.TipWagersDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	gameDB, err := database.New(c.GameDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	ctx := svc.NewServiceContext(c)
	ctx.WagersDB = db
	ctx.TipWagersDB = tipDB
	ctx.GameDB = gameDB

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		livegame.RegisterLiveGameServer(grpcServer, server.NewLiveGameServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "livegame",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
