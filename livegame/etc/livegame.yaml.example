Name: livegame.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: livegame.rpc

LiveConf:
  Schema: http
  Host: bbgp-alpaca.dev.net
  IP: 127.0.0.1
  Port: 80
  Token: token

WagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers3
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers3
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

TipWagersDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers99
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: PIDWagers99
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

GameDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: GameDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Username: mysql
    Password: mysql
    Database: GameDB
    MaxOpenConn: 10
    MaxIdleConn: 5
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

Middlewares:
  Stat: false
