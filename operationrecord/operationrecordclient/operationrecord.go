// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.7
// Source: operationrecord.proto

package operationrecordclient

import (
	"context"

	"gbh/proto/operationrecord"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AddDownloadRecordRequest               = operationrecord.AddDownloadRecordRequest
	AddLogChangeRequest                    = operationrecord.AddLogChangeRequest
	CreateAGRecordRequest                  = operationrecord.CreateAGRecordRequest
	CreateGameTypeSwitchRecordRequest      = operationrecord.CreateGameTypeSwitchRecordRequest
	CreateSystemMaintenanceRecordRequest   = operationrecord.CreateSystemMaintenanceRecordRequest
	CreateSystemMonitorRecordRequest       = operationrecord.CreateSystemMonitorRecordRequest
	CreateTransferRecordRequest            = operationrecord.CreateTransferRecordRequest
	CreateUserRecordRequest                = operationrecord.CreateUserRecordRequest
	DownloadRecord                         = operationrecord.DownloadRecord
	EmptyResponse                          = operationrecord.EmptyResponse
	GameTypeSwitchRecordInfo               = operationrecord.GameTypeSwitchRecordInfo
	GameTypeSwitchRecordRequest            = operationrecord.GameTypeSwitchRecordRequest
	GameTypeSwitchRecordResponse           = operationrecord.GameTypeSwitchRecordResponse
	GetDownloadRecordListRequest           = operationrecord.GetDownloadRecordListRequest
	GetDownloadRecordListResponse          = operationrecord.GetDownloadRecordListResponse
	GetMonthlyReconciliationRecordRequest  = operationrecord.GetMonthlyReconciliationRecordRequest
	GetMonthlyReconciliationRecordResponse = operationrecord.GetMonthlyReconciliationRecordResponse
	GetSystemMaintenanceRecordRequest      = operationrecord.GetSystemMaintenanceRecordRequest
	GetSystemMaintenanceRecordResponse     = operationrecord.GetSystemMaintenanceRecordResponse
	GetWhitelistRecordRequest              = operationrecord.GetWhitelistRecordRequest
	GetWhitelistRecordResponse             = operationrecord.GetWhitelistRecordResponse
	KeyValue                               = operationrecord.KeyValue
	MonthlyReconciliationRecord            = operationrecord.MonthlyReconciliationRecord
	Pagination                             = operationrecord.Pagination
	SystemMaintenanceRecord                = operationrecord.SystemMaintenanceRecord
	SystemMonitor                          = operationrecord.SystemMonitor
	SystemMonitorRequest                   = operationrecord.SystemMonitorRequest
	SystemMonitorResponse                  = operationrecord.SystemMonitorResponse
	Uint32Value                            = operationrecord.Uint32Value
	WhitelistRecord                        = operationrecord.WhitelistRecord

	OperationRecord interface {
		CreateUserRecord(ctx context.Context, in *CreateUserRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		CreateTransferRecord(ctx context.Context, in *CreateTransferRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		CreateAGRecord(ctx context.Context, in *CreateAGRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		AddLogChange(ctx context.Context, in *AddLogChangeRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		AddDownloadRecord(ctx context.Context, in *AddDownloadRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetWhitelistRecord(ctx context.Context, in *GetWhitelistRecordRequest, opts ...grpc.CallOption) (*GetWhitelistRecordResponse, error)
		GetMonthlyReconciliationRecord(ctx context.Context, in *GetMonthlyReconciliationRecordRequest, opts ...grpc.CallOption) (*GetMonthlyReconciliationRecordResponse, error)
		GetDownloadRecordList(ctx context.Context, in *GetDownloadRecordListRequest, opts ...grpc.CallOption) (*GetDownloadRecordListResponse, error)
		CreateGameTypeSwitchRecord(ctx context.Context, in *CreateGameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GameTypeSwitchRecord(ctx context.Context, in *GameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*GameTypeSwitchRecordResponse, error)
		GetSystemMonitor(ctx context.Context, in *SystemMonitorRequest, opts ...grpc.CallOption) (*SystemMonitorResponse, error)
		CreateSystemMonitorRecord(ctx context.Context, in *CreateSystemMonitorRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
		GetSystemMaintenanceRecord(ctx context.Context, in *GetSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*GetSystemMaintenanceRecordResponse, error)
		CreateSystemMaintenanceRecord(ctx context.Context, in *CreateSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	}

	defaultOperationRecord struct {
		cli zrpc.Client
	}
)

func NewOperationRecord(cli zrpc.Client) OperationRecord {
	return &defaultOperationRecord{
		cli: cli,
	}
}

func (m *defaultOperationRecord) CreateUserRecord(ctx context.Context, in *CreateUserRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.CreateUserRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) CreateTransferRecord(ctx context.Context, in *CreateTransferRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.CreateTransferRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) CreateAGRecord(ctx context.Context, in *CreateAGRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.CreateAGRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) AddLogChange(ctx context.Context, in *AddLogChangeRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.AddLogChange(ctx, in, opts...)
}

func (m *defaultOperationRecord) AddDownloadRecord(ctx context.Context, in *AddDownloadRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.AddDownloadRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) GetWhitelistRecord(ctx context.Context, in *GetWhitelistRecordRequest, opts ...grpc.CallOption) (*GetWhitelistRecordResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.GetWhitelistRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) GetMonthlyReconciliationRecord(ctx context.Context, in *GetMonthlyReconciliationRecordRequest, opts ...grpc.CallOption) (*GetMonthlyReconciliationRecordResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.GetMonthlyReconciliationRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) GetDownloadRecordList(ctx context.Context, in *GetDownloadRecordListRequest, opts ...grpc.CallOption) (*GetDownloadRecordListResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.GetDownloadRecordList(ctx, in, opts...)
}

func (m *defaultOperationRecord) CreateGameTypeSwitchRecord(ctx context.Context, in *CreateGameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.CreateGameTypeSwitchRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) GameTypeSwitchRecord(ctx context.Context, in *GameTypeSwitchRecordRequest, opts ...grpc.CallOption) (*GameTypeSwitchRecordResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.GameTypeSwitchRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) GetSystemMonitor(ctx context.Context, in *SystemMonitorRequest, opts ...grpc.CallOption) (*SystemMonitorResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.GetSystemMonitor(ctx, in, opts...)
}

func (m *defaultOperationRecord) CreateSystemMonitorRecord(ctx context.Context, in *CreateSystemMonitorRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.CreateSystemMonitorRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) GetSystemMaintenanceRecord(ctx context.Context, in *GetSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*GetSystemMaintenanceRecordResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.GetSystemMaintenanceRecord(ctx, in, opts...)
}

func (m *defaultOperationRecord) CreateSystemMaintenanceRecord(ctx context.Context, in *CreateSystemMaintenanceRecordRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	client := operationrecord.NewOperationRecordClient(m.cli.Conn())
	return client.CreateSystemMaintenanceRecord(ctx, in, opts...)
}
