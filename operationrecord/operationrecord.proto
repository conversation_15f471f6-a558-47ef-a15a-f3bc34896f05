syntax = "proto3";

package operationrecord;
option go_package = "proto/operationrecord";

message Uint32Value { uint32 value = 1; }

message CreateUserRecordRequest {
  string client_ip = 1;
  uint32 operator_id = 2;
  uint32 user_id = 3;
  uint32 set_type = 4;
}

message CreateTransferRecordRequest {
  string client_ip = 1;
  uint32 operator_id = 2;
  uint32 user_id = 3;
  uint32 set_type = 4;
  double sub_balance = 5;
  double old_balance = 6;
  double balance = 7;
}

message CreateAGRecordRequest {
  uint32 hall_id = 1;
  uint32 operator_id = 2;
  uint32 level_id = 3;
  uint32 action_id = 4;
  uint32 sub_action_id = 5;
  uint32 target_id = 6;
  uint32 target_role_id = 7;
  string boss_key = 8;
  string user_key = 9;
  repeated KeyValue xml_data_msg = 10;
  bool xml_cancel_tag = 11;
  string uri = 12;
  string ip = 13;
}

message KeyValue {
  string key = 1;
  string value = 2;
}

message EmptyResponse {}

message AddLogChangeRequest {
  double balance = 1;
  string area = 2;
  uint32 hall_id = 3;
  string applet = 4;
  string act = 5;
  string action_sql = 6;
  string content = 7;
  string bywho = 8;
  string byfile = 9;
  string ip = 10;
}

message AddDownloadRecordRequest {
  uint32 site = 1;
  uint32 action = 2;
  uint32 hall_id = 3;
  string content = 4;
  uint32 operator_id = 5;
  uint32 operator_site = 6;
  string operator_ip = 7;
  uint32 operator_target = 8;
}

message GetWhitelistRecordRequest {
  uint32 hall_id = 1;
  uint32 type = 2;
  string lang = 3;
  string start_time = 4;
  string end_time = 5;
  string operator = 6;
  string message = 7;
  uint32 page = 8;
  uint32 page_limit = 9;
}

message GetWhitelistRecordResponse {
  repeated WhitelistRecord list = 1;
  Pagination pagination = 2;
}

message WhitelistRecord {
  uint32 id = 1;
  uint32 hall_id = 2;
  uint32 site = 3;
  uint32 website = 4;
  uint32 type = 5;
  string method = 6;
  string operator = 7;
  uint32 operator_id = 8;
  string client_ip = 9;
  string created_at = 10;
  string message = 11;
}

message Pagination {
  uint32 current_page = 1;
  uint32 page_limit = 2;
  uint32 total = 3;
  uint32 total_page = 4;
}

message GetMonthlyReconciliationRecordRequest {
  uint32 hall_id = 1;
  uint32 tab = 2;
  uint32 action = 3;
  uint32 operator_id = 4;
  string start_time = 5;
  string end_time = 6;
  string sort = 7;
  string order = 8;
  uint32 page = 9;
  uint32 page_limit = 10;
}

message GetMonthlyReconciliationRecordResponse {
  repeated MonthlyReconciliationRecord list = 1;
  Pagination pagination = 2;
}

message MonthlyReconciliationRecord {
  uint32 id = 1;
  uint32 tab = 2;
  uint32 action = 3;
  repeated uint32 hall_id = 4;
  string content = 5;
  uint32 operator_id = 6;
  uint32 site = 7;
  string created_at = 8;
}

message GetDownloadRecordListRequest {
  string start_time = 1;
  string end_time = 2;
  uint32 site = 3;
  uint32 hall_id = 4;
  uint32 operator_id = 5;
  uint32 operator_site = 6;
  string operator_ip = 7;
  uint32 operator_target = 8;
  bool sort = 9;
  string order = 10;
}

message GetDownloadRecordListResponse { repeated DownloadRecord data = 1; }

message DownloadRecord {
  uint32 id = 1;
  uint32 site = 2;
  uint32 action = 3;
  uint32 domain = 4;
  string content = 5;
  uint32 operator_id = 6;
  uint32 operator_site = 7;
  string operator_ip = 8;
  uint32 operator_target = 9;
  string create_at = 10;
}

message CreateGameTypeSwitchRecordRequest {
  uint32 action_id = 1;
  uint32 game_kind = 2;
  string game_id = 3;
  string content = 4;
  uint32 operator_id = 5;
  string create_at = 6;
}

message GameTypeSwitchRecordRequest {
  string start_time = 1;
  string end_time = 2;
  uint32 game_kind = 3;
  repeated string game_id = 4;
  uint32 hall_id = 5;
  uint32 action_id = 6;
  uint32 operator_id = 7;
  string sort = 8;
  string order = 9;
  uint32 page = 10;
  uint32 page_limit = 11;
}

message GameTypeSwitchRecordResponse {
  repeated GameTypeSwitchRecordInfo list = 1;
  Pagination pagination = 2;
}

message GameTypeSwitchRecordInfo {
  uint32 id = 1;
  uint32 action_id = 2;
  uint32 game_kind = 3;
  string game_id = 4;
  string content = 5;
  uint32 operator_id = 6;
  string created_at = 7;
}

message SystemMonitorRequest {
  string start_time = 1;
  string end_time = 2;
  uint32 group_id = 3;
  uint32 hall_id = 4;
  string act = 5;
  string category = 6;
  uint32 member = 7;
  uint32 page = 8;
  uint32 page_limit = 9;
  string order = 10;
}

message SystemMonitorResponse {
  repeated SystemMonitor list = 1;
  Pagination pagination = 2;
}

message SystemMonitor {
  uint32 group_id = 1;
  uint32 hall_id = 2;
  string act = 3;
  string category = 4;
  string content = 5;
  string operator = 6;
  string operator_ip = 7;
  string created_at = 8;
}

message CreateSystemMonitorRecordRequest {
  uint32 group_id = 1;
  uint32 hall_id = 2;
  string category = 3;
  string act = 4;
  string content = 5;
  bool is_admin_operator = 6;
  string operator = 7;
  uint32 operator_id = 8;
  string operator_ip = 9;
}

message GetSystemMaintenanceRecordRequest {
  string start_time = 1;
  string end_time = 2;
  string website = 3;
  repeated uint32 hall_ids = 4;
  uint32 operator_id = 5;
  Uint32Value admin_read_only = 6;
  uint32 page = 7;
  uint32 page_limit = 8;
}

message SystemMaintenanceRecord {
  uint32 id = 1;
  uint32 site = 2;
  string website = 3;
  string category = 4;
  string content = 5;
  string operator = 6;
  uint32 operator_id = 7;
  string operator_ip = 8;
  string created_at = 9;
}

message GetSystemMaintenanceRecordResponse {
  repeated SystemMaintenanceRecord list = 1;
  Pagination pagination = 2;
}

message CreateSystemMaintenanceRecordRequest {
  uint32 site = 1;
  string website = 2;
  uint32 hall_id = 3;
  string category = 4;
  string content = 5;
  bool admin_read_only = 6;
  string operator = 7;
  uint32 operator_id = 8;
  string operator_ip = 9;
}

service OperationRecord {
  rpc CreateUserRecord(CreateUserRecordRequest) returns (EmptyResponse);
  rpc CreateTransferRecord(CreateTransferRecordRequest) returns (EmptyResponse);
  rpc CreateAGRecord(CreateAGRecordRequest) returns (EmptyResponse);
  rpc AddLogChange(AddLogChangeRequest) returns (EmptyResponse);
  rpc AddDownloadRecord(AddDownloadRecordRequest) returns (EmptyResponse);
  rpc GetWhitelistRecord(GetWhitelistRecordRequest)
      returns (GetWhitelistRecordResponse);
  rpc GetMonthlyReconciliationRecord(GetMonthlyReconciliationRecordRequest)
      returns (GetMonthlyReconciliationRecordResponse);
  rpc GetDownloadRecordList(GetDownloadRecordListRequest)
      returns (GetDownloadRecordListResponse);
  rpc CreateGameTypeSwitchRecord(CreateGameTypeSwitchRecordRequest)
      returns (EmptyResponse);
  rpc GameTypeSwitchRecord(GameTypeSwitchRecordRequest)
      returns (GameTypeSwitchRecordResponse);
  rpc GetSystemMonitor(SystemMonitorRequest) returns (SystemMonitorResponse);
  rpc CreateSystemMonitorRecord(CreateSystemMonitorRecordRequest)
      returns (EmptyResponse);
  rpc GetSystemMaintenanceRecord(GetSystemMaintenanceRecordRequest)
      returns (GetSystemMaintenanceRecordResponse);
  rpc CreateSystemMaintenanceRecord(CreateSystemMaintenanceRecordRequest)
      returns (EmptyResponse);
}
