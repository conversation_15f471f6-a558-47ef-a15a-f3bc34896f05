Name: operationrecord.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: operationrecord.rpc

PDHexConf:
  Schema: http
  Host: bgp-api.vir777.net
  IP: 127.0.0.1
  Port: 80
  AuthenticateKey: Rd3Xuc8LSa02MmI

ACCConf:
  Schema: http
  Host: bgp.durian
  IP: 127.0.0.1
  Port: 80

MainDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: MainDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: MainDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

LogDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: Log
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: Log
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

RecordDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: RecordDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: RecordDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

GameDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: GameDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: GameDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

Middlewares:
  Stat: false
