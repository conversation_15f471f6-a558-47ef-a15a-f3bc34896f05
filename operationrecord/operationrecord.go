package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/logger"
	"gbh/operationrecord/internal/config"
	"gbh/operationrecord/internal/server"
	"gbh/operationrecord/internal/svc"
	"gbh/proto/operationrecord"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/operationrecord.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	mainDB, err := database.New(c.<PERSON>, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	logDB, err := database.New(c.Log<PERSON>onf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	recordDB, err := database.New(c.RecordDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	gameDB, err := database.New(c.GameDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	ctx := svc.NewServiceContext(c, svc.ExternalContext{
		MainDB:   mainDB,
		LogDB:    logDB,
		RecordDB: recordDB,
		GameDB:   gameDB,
	})

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		operationrecord.RegisterOperationRecordServer(grpcServer, server.NewOperationRecordServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "operationrecord",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
