// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.7
// Source: operationrecord.proto

package server

import (
	"context"

	"gbh/operationrecord/internal/logic"
	"gbh/operationrecord/internal/svc"
	"gbh/proto/operationrecord"
)

type OperationRecordServer struct {
	svcCtx *svc.ServiceContext
	operationrecord.UnimplementedOperationRecordServer
}

func NewOperationRecordServer(svcCtx *svc.ServiceContext) *OperationRecordServer {
	return &OperationRecordServer{
		svcCtx: svcCtx,
	}
}

func (s *OperationRecordServer) CreateUserRecord(ctx context.Context, in *operationrecord.CreateUserRecordRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewCreateUserRecordLogic(ctx, s.svcCtx)
	return l.CreateUserRecord(in)
}

func (s *OperationRecordServer) CreateTransferRecord(ctx context.Context, in *operationrecord.CreateTransferRecordRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewCreateTransferRecordLogic(ctx, s.svcCtx)
	return l.CreateTransferRecord(in)
}

func (s *OperationRecordServer) CreateAGRecord(ctx context.Context, in *operationrecord.CreateAGRecordRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewCreateAGRecordLogic(ctx, s.svcCtx)
	return l.CreateAGRecord(in)
}

func (s *OperationRecordServer) AddLogChange(ctx context.Context, in *operationrecord.AddLogChangeRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewAddLogChangeLogic(ctx, s.svcCtx)
	return l.AddLogChange(in)
}

func (s *OperationRecordServer) AddDownloadRecord(ctx context.Context, in *operationrecord.AddDownloadRecordRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewAddDownloadRecordLogic(ctx, s.svcCtx)
	return l.AddDownloadRecord(in)
}

func (s *OperationRecordServer) GetWhitelistRecord(ctx context.Context, in *operationrecord.GetWhitelistRecordRequest) (*operationrecord.GetWhitelistRecordResponse, error) {
	l := logic.NewGetWhitelistRecordLogic(ctx, s.svcCtx)
	return l.GetWhitelistRecord(in)
}

func (s *OperationRecordServer) GetMonthlyReconciliationRecord(ctx context.Context, in *operationrecord.GetMonthlyReconciliationRecordRequest) (*operationrecord.GetMonthlyReconciliationRecordResponse, error) {
	l := logic.NewGetMonthlyReconciliationRecordLogic(ctx, s.svcCtx)
	return l.GetMonthlyReconciliationRecord(in)
}

func (s *OperationRecordServer) GetDownloadRecordList(ctx context.Context, in *operationrecord.GetDownloadRecordListRequest) (*operationrecord.GetDownloadRecordListResponse, error) {
	l := logic.NewGetDownloadRecordListLogic(ctx, s.svcCtx)
	return l.GetDownloadRecordList(in)
}

func (s *OperationRecordServer) CreateGameTypeSwitchRecord(ctx context.Context, in *operationrecord.CreateGameTypeSwitchRecordRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewCreateGameTypeSwitchRecordLogic(ctx, s.svcCtx)
	return l.CreateGameTypeSwitchRecord(in)
}

func (s *OperationRecordServer) GameTypeSwitchRecord(ctx context.Context, in *operationrecord.GameTypeSwitchRecordRequest) (*operationrecord.GameTypeSwitchRecordResponse, error) {
	l := logic.NewGameTypeSwitchRecordLogic(ctx, s.svcCtx)
	return l.GameTypeSwitchRecord(in)
}

func (s *OperationRecordServer) GetSystemMonitor(ctx context.Context, in *operationrecord.SystemMonitorRequest) (*operationrecord.SystemMonitorResponse, error) {
	l := logic.NewGetSystemMonitorLogic(ctx, s.svcCtx)
	return l.GetSystemMonitor(in)
}

func (s *OperationRecordServer) CreateSystemMonitorRecord(ctx context.Context, in *operationrecord.CreateSystemMonitorRecordRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewCreateSystemMonitorRecordLogic(ctx, s.svcCtx)
	return l.CreateSystemMonitorRecord(in)
}

func (s *OperationRecordServer) GetSystemMaintenanceRecord(ctx context.Context, in *operationrecord.GetSystemMaintenanceRecordRequest) (*operationrecord.GetSystemMaintenanceRecordResponse, error) {
	l := logic.NewGetSystemMaintenanceRecordLogic(ctx, s.svcCtx)
	return l.GetSystemMaintenanceRecord(in)
}

func (s *OperationRecordServer) CreateSystemMaintenanceRecord(ctx context.Context, in *operationrecord.CreateSystemMaintenanceRecordRequest) (*operationrecord.EmptyResponse, error) {
	l := logic.NewCreateSystemMaintenanceRecordLogic(ctx, s.svcCtx)
	return l.CreateSystemMaintenanceRecord(in)
}
