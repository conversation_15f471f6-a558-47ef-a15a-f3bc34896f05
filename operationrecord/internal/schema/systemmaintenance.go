package schema

type SystemMaintenance struct {
	ID            uint32 `gorm:"column:id;primaryKey"`
	Site          uint32 `gorm:"column:site"`
	Website       string `gorm:"column:website"`
	Domain        uint32 `gorm:"column:domain"`
	Category      string `gorm:"column:category"`
	Content       string `gorm:"column:content"`
	AdminReadOnly bool   `gorm:"column:admin_read_only"`
	Operator      string `gorm:"column:operator"`
	OperatorID    uint32 `gorm:"column:operator_id"`
	OperatorIP    []byte `gorm:"column:operator_ip"`
	CreatedAt     string `gorm:"column:created_at"`
}
