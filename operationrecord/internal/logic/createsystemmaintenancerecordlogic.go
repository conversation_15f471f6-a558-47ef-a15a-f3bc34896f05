package logic

import (
	"context"

	"gbh/errorx"
	"gbh/operationrecord/internal/constants"
	"gbh/operationrecord/internal/schema"
	"gbh/operationrecord/internal/svc"
	"gbh/proto/operationrecord"
	"gbh/utils/iputil"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CreateSystemMaintenanceRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateSystemMaintenanceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateSystemMaintenanceRecordLogic {
	return &CreateSystemMaintenanceRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateSystemMaintenanceRecordLogic) CreateSystemMaintenanceRecord(in *operationrecord.CreateSystemMaintenanceRecordRequest) (*operationrecord.EmptyResponse, error) {
	var now = carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	var saveData = schema.SystemMaintenance{
		Site:          in.GetSite(),
		Website:       in.GetWebsite(),
		Domain:        in.GetHallId(),
		Category:      in.GetCategory(),
		Content:       in.GetContent(),
		AdminReadOnly: in.GetAdminReadOnly(),
		Operator:      in.GetOperator(),
		OperatorID:    in.GetOperatorId(),
		OperatorIP:    iputil.ConvertIPToBinary(in.GetOperatorIp()),
		CreatedAt:     now,
	}

	err := l.svcCtx.RecordDB.Transaction(func(tx *gorm.DB) error {
		systemMaintenceErr := tx.Table("SystemMaintenance").Create(&saveData).Error

		if systemMaintenceErr != nil {
			return systemMaintenceErr
		}

		systemMaintenceDomainErr := tx.Table("SystemMaintenanceDomain").
			Create(&schema.SystemMaintenanceDomain{
				SystemMaintenanceID: saveData.ID,
				Domain:              in.GetHallId(),
				CreatedAt:           now,
			}).Error

		if systemMaintenceDomainErr != nil {
			return systemMaintenceDomainErr
		}

		return nil
	})

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &operationrecord.EmptyResponse{}, nil
}
