package logic

import (
	"context"
	"math"
	"strings"

	"gbh/errorx"
	"gbh/operationrecord/internal/constants"
	"gbh/operationrecord/internal/schema"
	"gbh/operationrecord/internal/svc"
	"gbh/proto/operationrecord"
	"gbh/utils/gormutil"
	"gbh/utils/iputil"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetSystemMaintenanceRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

type GetSystemMaintenanceRecord struct {
}

func NewGetSystemMaintenanceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSystemMaintenanceRecordLogic {
	return &GetSystemMaintenanceRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetSystemMaintenanceRecordLogic) GetSystemMaintenanceRecord(in *operationrecord.GetSystemMaintenanceRecordRequest) (*operationrecord.GetSystemMaintenanceRecordResponse, error) {
	selectFields := []string{
		"SystemMaintenance.id",
		"SystemMaintenance.site",
		"SystemMaintenance.website",
		"SystemMaintenance.category",
		"SystemMaintenance.content",
		"SystemMaintenance.operator",
		"SystemMaintenance.operator_id",
		"SystemMaintenance.operator_ip",
		"SystemMaintenance.created_at",
	}

	records := make([]schema.SystemMaintenance, 0)
	query := l.svcCtx.RecordDB.Table("SystemMaintenance").
		Select(selectFields).
		Where("SystemMaintenance.created_at BETWEEN ? AND ?", in.GetStartTime(), in.GetEndTime()).
		Joins("JOIN SystemMaintenanceDomain ON SystemMaintenanceDomain.system_maintenance_id = SystemMaintenance.id")

	if in.GetWebsite() != "" {
		query.Where("SystemMaintenance.website = ?", in.GetWebsite())
	}

	if len(in.GetHallIds()) > 0 {
		query.Where("SystemMaintenanceDomain.domain IN ?", in.GetHallIds())
	}

	if in.GetOperatorId() > 0 {
		query.Where("SystemMaintenance.operator_id = ?", in.GetOperatorId())
	}

	if in.GetAdminReadOnly() != nil && in.GetAdminReadOnly().GetValue() == 0 {
		query.Where("SystemMaintenance.admin_read_only = ?", in.GetAdminReadOnly().GetValue())
	}

	totalCount := int64(0)
	query.Count(&totalCount)

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	groupByStr := strings.Join(selectFields, ", ")
	query.Group(groupByStr)

	query.Order("SystemMaintenance.created_at DESC")

	page := constants.DefaultPage
	if in.GetPage() > 0 {
		page = in.GetPage()
	}

	limit := constants.DefaultLimit
	if in.GetPageLimit() > 0 {
		limit = in.GetPageLimit()
	}

	err := query.Scopes(gormutil.Paginate(int(page), int(limit))).Find(&records).Error

	if err != nil {
		return nil, errorx.DatabaseError
	}

	systemMaintenance := make([]*operationrecord.SystemMaintenanceRecord, 0, len(records))
	for _, v := range records {
		systemMaintenance = append(systemMaintenance, &operationrecord.SystemMaintenanceRecord{
			Id:         v.ID,
			Site:       v.Site,
			Website:    v.Website,
			Category:   v.Category,
			Content:    v.Content,
			Operator:   v.Operator,
			OperatorId: v.OperatorID,
			OperatorIp: iputil.ConvertBinaryToIP(v.OperatorIP),
			CreatedAt:  carbon.Parse(v.CreatedAt, constants.TimezoneGMT4).ToRfc3339String(),
		})
	}

	totalPage := int(math.Ceil(float64(totalCount) / float64(limit)))

	return &operationrecord.GetSystemMaintenanceRecordResponse{
		List: systemMaintenance,
		Pagination: &operationrecord.Pagination{
			Total:       uint32(totalCount),
			TotalPage:   uint32(totalPage),
			CurrentPage: page,
			PageLimit:   limit,
		},
	}, nil
}
