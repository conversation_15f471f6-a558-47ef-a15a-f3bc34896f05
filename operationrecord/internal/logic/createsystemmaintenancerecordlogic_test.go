package logic

import (
	"gbh/errorx"
	"gbh/operationrecord/internal/constants"
	"gbh/proto/operationrecord"
	"gbh/utils/iputil"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreateSystemMaintenanceRecordLogic_Success(t *testing.T) {
	request := operationrecord.CreateSystemMaintenanceRecordRequest{
		Site:          0,
		Website:       "domain",
		HallId:        3820474,
		Category:      "domain",
		Content:       `{"start_at":"2021-06-09 00:00:00","send":false,"end_at":"2021-06-10 00:00:00"}`,
		AdminReadOnly: false,
		Operator:      "annatest",
		OperatorId:    11579,
		OperatorIp:    "127.0.0.1",
	}

	operatorIP := iputil.ConvertIPToBinary(request.GetOperatorIp())
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockRecord.ExpectBegin()

	sqlMockRecord.ExpectExec("INSERT INTO `SystemMaintenance` (`site`,`website`,`domain`,`category`,`content`,`admin_read_only`,`operator`,`operator_id`,`operator_ip`,`created_at`) VALUES (?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetSite(), request.GetWebsite(), request.GetHallId(), request.GetCategory(), request.GetContent(), request.GetAdminReadOnly(), request.GetOperator(), request.GetOperatorId(), operatorIP, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockRecord.ExpectExec("INSERT INTO `SystemMaintenanceDomain` (`system_maintenance_id`,`domain`,`created_at`) VALUES (?,?,?)").
		WithArgs(1, request.GetHallId(), now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockRecord.ExpectCommit()

	l := NewCreateSystemMaintenanceRecordLogic(ctx, svcCtx)
	resp, err := l.CreateSystemMaintenanceRecord(&request)

	assert.Equal(t, &operationrecord.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockRecord.ExpectationsWereMet())
}

func TestCreateSystemMaintenanceRecordLogic_SystemMaintenance_DatabaseError(t *testing.T) {
	request := operationrecord.CreateSystemMaintenanceRecordRequest{
		Site:          0,
		Website:       "domain",
		HallId:        3820474,
		Category:      "domain",
		Content:       `{"start_at":"2021-06-09 00:00:00","send":false,"end_at":"2021-06-10 00:00:00"}`,
		AdminReadOnly: false,
		Operator:      "annatest",
		OperatorId:    11579,
		OperatorIp:    "127.0.0.1",
	}

	operatorIP := iputil.ConvertIPToBinary(request.GetOperatorIp())
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockRecord.ExpectBegin()

	sqlMockRecord.ExpectExec("INSERT INTO `SystemMaintenance` (`site`,`website`,`domain`,`category`,`content`,`admin_read_only`,`operator`,`operator_id`,`operator_ip`,`created_at`) VALUES (?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetSite(), request.GetWebsite(), request.GetHallId(), request.GetCategory(), request.GetContent(), request.GetAdminReadOnly(), request.GetOperator(), request.GetOperatorId(), operatorIP, now).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockRecord.ExpectRollback()

	l := NewCreateSystemMaintenanceRecordLogic(ctx, svcCtx)
	resp, err := l.CreateSystemMaintenanceRecord(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockRecord.ExpectationsWereMet())
}

func TestCreateSystemMaintenanceRecordLogic_SystemMaintenanceDomain_DatabaseError(t *testing.T) {
	request := operationrecord.CreateSystemMaintenanceRecordRequest{
		Site:          0,
		Website:       "domain",
		HallId:        3820474,
		Category:      "domain",
		Content:       `{"start_at":"2021-06-09 00:00:00","send":false,"end_at":"2021-06-10 00:00:00"}`,
		AdminReadOnly: false,
		Operator:      "annatest",
		OperatorId:    11579,
		OperatorIp:    "127.0.0.1",
	}

	operatorIP := iputil.ConvertIPToBinary(request.GetOperatorIp())
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockRecord.ExpectBegin()

	sqlMockRecord.ExpectExec("INSERT INTO `SystemMaintenance` (`site`,`website`,`domain`,`category`,`content`,`admin_read_only`,`operator`,`operator_id`,`operator_ip`,`created_at`) VALUES (?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetSite(), request.GetWebsite(), request.GetHallId(), request.GetCategory(), request.GetContent(), request.GetAdminReadOnly(), request.GetOperator(), request.GetOperatorId(), operatorIP, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockRecord.ExpectExec("INSERT INTO `SystemMaintenanceDomain` (`system_maintenance_id`,`domain`,`created_at`) VALUES (?,?,?)").
		WithArgs(1, request.GetHallId(), now).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockRecord.ExpectRollback()

	l := NewCreateSystemMaintenanceRecordLogic(ctx, svcCtx)
	resp, err := l.CreateSystemMaintenanceRecord(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockRecord.ExpectationsWereMet())
}
