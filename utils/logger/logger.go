package logger

import (
	"bytes"
	"context"
	"gbh/logger/constants"
	"net/http"
	"os"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"
	"google.golang.org/grpc/status"
)

type ResponseLogger struct {
	http.ResponseWriter
	Body       bytes.Buffer
	StatusCode int
}

func (r *ResponseLogger) Write(b []byte) (int, error) {
	r.Body.Write(b)
	return r.ResponseWriter.Write(b)
}

func (r *ResponseLogger) WriteHeader(code int) {
	r.StatusCode = code
	r.ResponseWriter.WriteHeader(code)
}

func WithAPI(req *http.Request, tags []string, requestBody string, resp *ResponseLogger, duration time.Duration, logLevel string) logx.Logger {
	hostname, hostnameErr := os.Hostname()

	tags = append([]string{
		constants.LogTagAccess,
		constants.LogTagGti,
	}, tags...)

	log := map[string]interface{}{
		"method":           req.Method,
		"proto":            req.Proto,
		"remote_addr":      req.RemoteAddr,
		"path":             req.RequestURI,
		"parameters":       req.Form,
		"headers":          req.Header,
		"referrer":         req.Referer(),
		"cookies":          req.Cookies(),
		"host":             req.Host,
		"status_code":      resp.StatusCode,
		"request_body":     requestBody,
		"latency":          duration.Milliseconds(),
		"response_size":    resp.Body.Len(),
		"response_headers": resp.Header(),
		"response_body":    resp.Body.String(),
		"client_ip":        req.RemoteAddr,
		"hostname":         hostname,
		"hostname_error":   hostnameErr,
		"api_route":        req.URL.Path,
	}

	fields := []logx.LogField{
		logx.Field("log", log),
		logx.Field("tags", tags),
		logx.Field("log_level", logLevel),
	}

	return logx.WithContext(req.Context()).WithFields(fields...)
}

func WithGRPC(ctx context.Context, tags []string, method string, req, resp interface{}, err error, duration time.Duration, logLevel string) logx.Logger {
	tags = append([]string{
		constants.LogTagGRPC,
		constants.LogTagGti,
	}, tags...)

	log := map[string]interface{}{
		"method":   method,
		"request":  req,
		"response": resp,
		"latency":  duration.Milliseconds(),
	}

	if err != nil {
		log["error"] = err.Error()
		if s, ok := status.FromError(err); ok {
			log["grpc_code"] = s.Code().String()
			log["grpc_message"] = s.Message()
		}
	}

	fields := []logx.LogField{
		logx.Field("log", log),
		logx.Field("tags", tags),
		logx.Field("log_level", logLevel),
		logx.Field("trace_id", trace.TraceIDFromContext(ctx)),
	}

	return logx.WithContext(ctx).WithFields(fields...)
}

func WithResty(ctx context.Context, tags []string, req *resty.Request, resp *resty.Response, err error, logLevel string) logx.Logger {
	tags = append([]string{
		constants.LogTagResty,
		constants.LogTagGti,
	}, tags...)

	errorInfo := ""
	if err != nil {
		errorInfo = err.Error()
	}

	requestLog := map[string]interface{}{
		"method":     req.Method,
		"url":        req.URL,
		"headers":    req.Header,
		"cookies":    req.Cookies,
		"form_data":  req.FormData,
		"body":       req.Body,
		"error":      errorInfo,
		"trace_info": req.TraceInfo(),
	}

	var responseLog map[string]interface{}

	if resp != nil {
		requestLog = map[string]interface{}{
			"method":     resp.Request.Method,
			"url":        resp.Request.URL,
			"headers":    resp.Request.Header,
			"cookies":    resp.Request.Cookies,
			"form_data":  resp.Request.FormData,
			"body":       resp.Request.Body,
			"error":      errorInfo,
			"trace_info": resp.Request.TraceInfo(),
		}

		responseLog = map[string]interface{}{
			"proto":       resp.Proto(),
			"status_code": resp.StatusCode(),
			"size":        resp.Size(),
			"headers":     resp.Header(),
			"cookies":     resp.Cookies(),
			"body":        resp.String(),
			"latency":     resp.Time().Milliseconds(),
			"error":       resp.Error(),
		}
	}

	fields := []logx.LogField{
		logx.Field("log", map[string]interface{}{
			"request":  requestLog,
			"response": responseLog,
		}),
		logx.Field("tags", tags),
		logx.Field("log_level", logLevel),
		logx.Field("trace_id", trace.TraceIDFromContext(ctx)),
	}

	return logx.WithContext(ctx).WithFields(fields...)
}

func WithSQL(ctx context.Context, log map[string]interface{}, tags []string, logLevel string, data ...interface{}) logx.Logger {
	tags = append([]string{
		constants.LogTagSQL,
		constants.LogTagGti,
	}, tags...)

	if data != nil {
		log["data"] = data
	}

	fields := []logx.LogField{
		logx.Field("log", log),
		logx.Field("tags", tags),
		logx.Field("log_level", logLevel),
		logx.Field("trace_id", trace.TraceIDFromContext(ctx)),
	}

	return logx.WithContext(ctx).WithFields(fields...)
}

func WithRedis(ctx context.Context, tags []string, log map[string]interface{}, logLevel string) logx.Logger {
	tags = append([]string{
		constants.LogTagSQL,
		constants.LogTagGti,
	}, tags...)

	fields := []logx.LogField{
		logx.Field("log", log),
		logx.Field("tags", tags),
		logx.Field("log_level", logLevel),
		logx.Field("trace_id", trace.TraceIDFromContext(ctx)),
	}

	return logx.WithContext(ctx).WithFields(fields...)
}
