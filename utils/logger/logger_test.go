package logger

import (
	"bytes"
	"context"
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var ErrRequestFailed = errors.New("request failed")

// Mock ResponseWriter
type mockResponseWriter struct {
	buf *bytes.Buffer
}

func (m *mockResponseWriter) Header() http.Header {
	return http.Header{}
}

func (m *mockResponseWriter) Write(data []byte) (int, error) {
	return m.buf.Write(data)
}

func (m *mockResponseWriter) WriteHeader(_ int) {
	// Mock WriteHeader
}

func TestResponseLogger_Write(t *testing.T) {
	var buf bytes.Buffer
	mockWriter := &ResponseLogger{
		ResponseWriter: &mockResponseWriter{&buf},
	}

	data := []byte("test response")
	n, err := mockWriter.Write(data)
	assert.NoError(t, err)
	assert.Equal(t, len(data), n)
	assert.Equal(t, "test response", buf.String())
}

func TestResponseLogger_WriteHeader(t *testing.T) {
	mockWriter := &ResponseLogger{
		ResponseWriter: &mockResponseWriter{},
	}

	mockWriter.WriteHeader(http.StatusOK)
	assert.Equal(t, http.StatusOK, mockWriter.StatusCode)
}

func TestWithAPI(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, "/test", http.NoBody)
	assert.NoError(t, err)
	req.RemoteAddr = "127.0.0.1:8080"

	respLogger := &ResponseLogger{
		ResponseWriter: &mockResponseWriter{},
		StatusCode:     http.StatusOK,
	}

	logger := WithAPI(req, []string{"test_tag"}, "test body", respLogger, 100*time.Millisecond, "info")
	assert.NotNil(t, logger)
}

func TestWithGRPC(t *testing.T) {
	ctx := context.Background()

	logger := WithGRPC(ctx, []string{"test_tag"}, "TestMethod", "test request", "test response", nil, 100*time.Millisecond, "info")
	assert.NotNil(t, logger)
}

func TestWithGRPC_Error(t *testing.T) {
	ctx := context.Background()
	mockErr := status.Error(codes.Internal, "internal error")

	logger := WithGRPC(ctx, []string{"test_tag"}, "TestMethod", "test request", nil, mockErr, 100*time.Millisecond, "severe")
	assert.NotNil(t, logger)
}

func TestWithResty(t *testing.T) {
	client := resty.New()

	req := client.R()
	req.Method = "GET"
	req.SetHeader("Content-Type", "application/json")
	req.SetBody(`{"key": "value"}`)

	resp := &resty.Response{
		Request:     req,
		RawResponse: nil,
	}

	logger := WithResty(context.Background(), []string{"test_tag"}, req, resp, nil, "info")
	assert.NotNil(t, logger)
}

func TestWithResty_Error(t *testing.T) {
	client := resty.New()

	req := client.R()
	req.Method = "GET"
	req.SetHeader("Content-Type", "application/json")
	req.SetBody(`{"key": "value"}`)

	logger := WithResty(context.Background(), []string{"test_tag"}, req, nil, ErrRequestFailed, "severe")
	assert.NotNil(t, logger)
}

func TestWithSQL(t *testing.T) {
	logger := WithSQL(context.Background(), map[string]interface{}{"query": "SELECT * FROM test"}, []string{"test_tag"}, "info")
	assert.NotNil(t, logger)
}

func TestWithRedis(t *testing.T) {
	logger := WithRedis(context.Background(), []string{"test_tag"}, map[string]interface{}{"command": "GET test"}, "info")
	assert.NotNil(t, logger)
}
