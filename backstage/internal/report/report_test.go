package report

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/report/reportclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	reportRPC := newMockReportRPC()
	reportCtx := New(reportRPC)

	expectedResponse := &reportContext{
		ReportRPC: reportRPC,
	}

	assert.Equal(t, expectedResponse, reportCtx)
}

func Test_ReportContext_GetReportCloseDate_Get(t *testing.T) {
	reportRPC := newMockReportRPC()

	reportRPC.On("ReportCloseDate", ctx, &reportclient.EmptyRequest{}).Return(&seeder.CloseDate, nil)
	reportCtx := New(reportRPC)

	resp, err := reportCtx.GetReportCloseDate(ctx)

	expectedResponse := seeder.CloseDate.GetCloseDate()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_ReportContext_GetReportCloseDate_GRPCError(t *testing.T) {
	reportRPC := newMockReportRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	reportRPC.On("ReportCloseDate", ctx, &reportclient.EmptyRequest{}).Return(nil, mockError)
	reportCtx := New(reportRPC)

	resp, err := reportCtx.GetReportCloseDate(ctx)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Empty(t, resp)
}

func Test_ReportContext_GetGameAnalysisEvent_GRPCError(t *testing.T) {
	reportRPC := newMockReportRPC()

	in := &reportclient.GetGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		Years:    []uint32{seeder.GameAnalysisEventYear},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	reportRPC.On("GetGameAnalysisEvent", ctx, in).Return(nil, mockError)

	reportCtx := New(reportRPC)

	resp, err := reportCtx.GetGameAnalysisEvent(ctx, in)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_ReportContext_GetGameAnalysisEvent(t *testing.T) {
	reportRPC := newMockReportRPC()

	in := &reportclient.GetGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		Years:    []uint32{seeder.GameAnalysisEventYear},
	}

	reportRPC.On("GetGameAnalysisEvent", ctx, in).Return(&seeder.GameAnalysisEvent, nil)

	reportCtx := New(reportRPC)

	resp, err := reportCtx.GetGameAnalysisEvent(ctx, in)

	assert.Equal(t, &seeder.GameAnalysisEvent, resp)
	assert.NoError(t, err)
}

func Test_ReportContext_DeleteGameAnalysisEvent_GRPCError(t *testing.T) {
	reportRPC := newMockReportRPC()

	in := &reportclient.DeleteGameAnalysisEventRequest{
		Ids: []uint32{seeder.GameAnalysisEventId},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	reportRPC.On("DeleteGameAnalysisEvent", ctx, in).Return(nil, mockError)

	reportCtx := New(reportRPC)

	err := reportCtx.DeleteGameAnalysisEvent(ctx, in)

	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_ReportContext_DeleteGameAnalysisEvent(t *testing.T) {
	reportRPC := newMockReportRPC()

	in := &reportclient.DeleteGameAnalysisEventRequest{
		Ids: []uint32{seeder.GameAnalysisEventId},
	}

	reportRPC.On("DeleteGameAnalysisEvent", ctx, in).Return(&reportclient.EmptyResponse{}, nil)

	reportCtx := New(reportRPC)

	err := reportCtx.DeleteGameAnalysisEvent(ctx, in)

	assert.NoError(t, err)
}

func Test_ReportContext_CreateGameAnalysisEvent_GRPCError(t *testing.T) {
	reportRPC := newMockReportRPC()

	in := &reportclient.CreateGameAnalysisEventRequest{
		GameKind:       constants.BBLive,
		GameType:       "3001",
		Content:        "",
		OccurrenceDate: "2025-01-01 00:00:00",
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	reportRPC.On("CreateGameAnalysisEvent", ctx, in).Return(nil, mockError)

	reportCtx := New(reportRPC)

	err := reportCtx.CreateGameAnalysisEvent(ctx, in)

	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_ReportContext_CreateGameAnalysisEvent(t *testing.T) {
	reportRPC := newMockReportRPC()

	in := &reportclient.CreateGameAnalysisEventRequest{
		GameKind:       constants.BBLive,
		GameType:       "3001",
		Content:        "",
		OccurrenceDate: "2025-01-01 00:00:00",
	}

	reportRPC.On("CreateGameAnalysisEvent", ctx, in).Return(&reportclient.EmptyResponse{}, nil)

	reportCtx := New(reportRPC)

	err := reportCtx.CreateGameAnalysisEvent(ctx, in)

	assert.NoError(t, err)
}
