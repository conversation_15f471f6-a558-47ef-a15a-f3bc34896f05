package report

import (
	"context"
	"gbh/errorx"
	"gbh/report/reportclient"
)

type Context interface {
	GetReportCloseDate(ctx context.Context) (string, error)
	GetGameAnalysisEvent(ctx context.Context, in *reportclient.GetGameAnalysisEventRequest) (*reportclient.GetGameAnalysisEventResponse, error)
	DeleteGameAnalysisEvent(ctx context.Context, in *reportclient.DeleteGameAnalysisEventRequest) error
	CreateGameAnalysisEvent(ctx context.Context, in *reportclient.CreateGameAnalysisEventRequest) error
}

type reportContext struct {
	ReportRPC reportclient.Report
}

func New(reportRPC reportclient.Report) Context {
	return &reportContext{
		ReportRPC: reportRPC,
	}
}

func (c *reportContext) GetReportCloseDate(ctx context.Context) (string, error) {
	resp, err := c.ReportRPC.ReportCloseDate(ctx, &reportclient.EmptyRequest{})

	if err != nil {
		return "", errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetCloseDate(), nil
}

func (c *reportContext) GetGameAnalysisEvent(ctx context.Context, in *reportclient.GetGameAnalysisEventRequest) (*reportclient.GetGameAnalysisEventResponse, error) {
	resp, err := c.ReportRPC.GetGameAnalysisEvent(ctx, in)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *reportContext) DeleteGameAnalysisEvent(ctx context.Context, in *reportclient.DeleteGameAnalysisEventRequest) error {
	_, err := c.ReportRPC.DeleteGameAnalysisEvent(ctx, in)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *reportContext) CreateGameAnalysisEvent(ctx context.Context, in *reportclient.CreateGameAnalysisEventRequest) error {
	_, err := c.ReportRPC.CreateGameAnalysisEvent(ctx, in)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}
