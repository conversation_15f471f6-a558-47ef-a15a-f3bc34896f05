package hall

import (
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/hall/hallclient"
	"gbh/proto/hall"
	"testing"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/stretchr/testify/assert"
)

func TestNew(t *testing.T) {
	hallRPC := newMockHallRPC()
	hallCtx := New(hallRPC)

	expectedResponse := &hallContext{
		HallRPC: hallRPC,
	}
	assert.Equal(t, expectedResponse, hallCtx)
}

func Test_HallContext_GetHallList(t *testing.T) {
	hallRPC := newMockHallRPC()

	enable := true
	mockRequest := &hallclient.HallListRequest{
		Enable: &hall.BoolValue{
			Value: enable,
		},
	}

	hallRPC.On("GetHallList", ctx, mockRequest).Return(&seeder.HallList, nil)

	hallCtx := New(hallRPC)
	resp, err := hallCtx.GetHallList(ctx, true)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.HallList, resp)
}

func Test_HallContext_GetHallList_GRPCError(t *testing.T) {
	hallRPC := newMockHallRPC()

	enable := true
	mockRequest := &hallclient.HallListRequest{
		Enable: &hall.BoolValue{
			Value: enable,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	hallRPC.On("GetHallList", ctx, mockRequest).Return(nil, mockError)

	hallCtx := New(hallRPC)
	resp, err := hallCtx.GetHallList(ctx, true)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_HallContext_GetHallById(t *testing.T) {
	hallRPC := newMockHallRPC()

	mockResuest := &hallclient.GetHallByIdRequest{
		HallId: seeder.HallId,
	}

	mockResponse := seeder.HallList.GetData()[0]
	hallRPC.On("GetHallById", ctx, mockResuest).Return(mockResponse, nil)

	hallCtx := New(hallRPC)
	resp, err := hallCtx.GetHallById(ctx, seeder.HallId)

	assert.NoError(t, err)
	assert.Equal(t, mockResponse, resp)
}

func Test_HallContext_GetHallById_GRPCError(t *testing.T) {
	hallRPC := newMockHallRPC()

	mockResuest := &hallclient.GetHallByIdRequest{
		HallId: seeder.HallId,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	hallRPC.On("GetHallById", ctx, mockResuest).Return(nil, mockError)

	hallCtx := New(hallRPC)
	resp, err := hallCtx.GetHallById(ctx, seeder.HallId)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
