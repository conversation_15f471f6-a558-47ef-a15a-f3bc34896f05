package hall

import (
	"context"
	"gbh/errorx"
	"gbh/hall/hallclient"
	"gbh/proto/hall"
)

type Context interface {
	GetHallList(ctx context.Context, enable bool) (*hallclient.HallListResponse, error)
	GetHallById(ctx context.Context, hallId uint32) (*hallclient.GetHallByIdResponse, error)
}

type hallContext struct {
	HallRPC hallclient.Hall
}

func New(hallRPC hallclient.Hall) Context {
	return &hallContext{
		HallRPC: hallRPC,
	}
}

func (c *hallContext) GetHallList(ctx context.Context, enable bool) (*hallclient.HallListResponse, error) {
	userData, err := c.HallRPC.GetHallList(ctx, &hall.HallListRequest{
		Enable: &hall.BoolValue{
			Value: enable,
		},
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return userData, nil
}

func (c *hallContext) GetHallById(ctx context.Context, hallId uint32) (*hall.GetHallByIdResponse, error) {
	resp, err := c.HallRPC.GetHallById(ctx, &hallclient.GetHallByIdRequest{
		HallId: hallId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
