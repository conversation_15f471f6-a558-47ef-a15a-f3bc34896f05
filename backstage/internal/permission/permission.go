package permission

import (
	"context"
	"encoding/json"
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/permission/permissionclient"
	"gbh/redis"
	"gbh/utils/maputil"
	"slices"
	"sort"

	"github.com/zeromicro/go-zero/core/collection"
	"github.com/zeromicro/go-zero/core/logx"
)

type Context interface {
	AdminControllerPermissionIDs(ctx context.Context, controllerName string) ([]uint32, error)
	AdminPermissions(ctx context.Context, req AdminPermissionsRequest) ([]*permissionclient.AdminPermissionInfo, error)
	AdminPermissionInfoList(ctx context.Context, req AdminPermissionInfoListRequest) ([]*permissionclient.PermissionListInfo, error)
	GetAdminEnabledPermission(ctx context.Context) (*permissionclient.GetAdminPermissionEnabledResponse, error)
	GetUserPermissionAffectList(ctx context.Context) (map[uint32]*permissionclient.PermissionAffectList, error)
	GetUserAllPermission(ctx context.Context, user types.UserInfo, permissionId []uint32, enable *bool) ([]*permissionclient.UserPermission, error)
	GetUserPermEnabled(ctx context.Context, permissionId []uint32) ([]uint32, error)
	GetUserPermissionConfigList(ctx context.Context, req UserPermissionConfigListRequest) ([]*permissionclient.UserPermissionConfig, error)
	GetAllPermConfigMap(ctx context.Context) (map[uint32]types.UserPermissionConfig, error)
	GetAdminVisitedHistory(ctx context.Context, adminId uint32) ([]*permissionclient.VisitedHistoryDetail, error)
	DeleteUserPermissionByUserID(ctx context.Context, userId uint32) error
	FilterUserPermission(ctx context.Context, allPermConfigMap map[uint32]types.UserPermissionConfig, operator types.UserInfo, permissionId []uint32) (Result, error)
	CheckUserPermission(ctx context.Context, operator types.UserInfo, permissionId uint32, permissionAction string) (bool, error)
	GetPermissionConfigById(ctx context.Context, permissionId uint32) *types.UserPermissionConfig
	UserPermList(ctx context.Context, req UserPermListRequest) (*permissionclient.UserPermListResponse, error)
	GetPermissionParents(ctx context.Context, permissionId uint32) ([]*types.UserPermissionConfig, error)
}

type permissionContext struct {
	PermissionRPC permissionclient.Permission
	MemoryCache   *collection.Cache
	RedisCache    redis.Redis
	logx.Logger
}

type AdminPermissionsRequest struct {
	AdminID      []uint32
	PermissionID []uint32
	Enable       *bool
	Favorite     *bool
	Modify       *bool
}

type AdminPermissionInfoListRequest struct {
	PermissionID []uint32
	ParentID     []uint32
	Type         []string
	Name         []string
	Enable       *bool
	Weight       *uint32
	Partial      *bool
	Modify       *bool
	OldPerm      *bool
	Maintenance  *bool
}

type UserPermListRequest struct {
	UserId       []uint32
	PermissionId []uint32
	RoleId       []uint32
	Enable       *bool
}

const (
	CacheUserPermEnabledKey = "PermissionDB:UserPermEnabled"
	CacheUserPermConfigKey  = "PermissionDB:UserPermConfig"
	CacheUserPermActionKey  = "perm:%d:%d:%s"
)

type UserPermissionConfigListRequest struct {
	PermissionId  []uint32
	ParentId      uint32
	Enable        *bool
	HierarchyPerm *bool
	Type          []string
	Name          []string
}

type UserPermission struct {
	Depth        uint32
	UserId       uint32
	PermissionId uint32
	Enable       bool
	Modify       bool
}

type Result struct {
	Enable map[uint32]map[uint32]bool
	Modify map[uint32]map[uint32]bool
}

type UserPermissionResult struct {
	Enable bool
	Modify bool
}

func New(permissionRPC permissionclient.Permission, redisCache redis.Redis, memoryCache *collection.Cache) Context {
	return &permissionContext{
		PermissionRPC: permissionRPC,
		RedisCache:    redisCache,
		MemoryCache:   memoryCache,
		Logger:        logx.WithContext(context.Background()),
	}
}

func (c *permissionContext) AdminControllerPermissionIDs(ctx context.Context, controllerName string) ([]uint32, error) {
	request := &permissionclient.AdminControllerPermissionIDsRequest{
		ControllerName: controllerName,
	}

	resp, err := c.PermissionRPC.AdminControllerPermissionIDs(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetPermissionId(), nil
}

func (c *permissionContext) AdminPermissions(ctx context.Context, req AdminPermissionsRequest) ([]*permissionclient.AdminPermissionInfo, error) {
	request := &permissionclient.AdminPermissionsRequest{
		AdminId:      req.AdminID,
		PermissionId: req.PermissionID,
	}

	if req.Enable != nil {
		request.Enable = &permissionclient.BoolValue{Value: *req.Enable}
	}

	if req.Favorite != nil {
		request.Favorite = &permissionclient.BoolValue{Value: *req.Favorite}
	}

	if req.Modify != nil {
		request.Modify = &permissionclient.BoolValue{Value: *req.Modify}
	}

	resp, err := c.PermissionRPC.AdminPermissions(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetPermissionInfo(), nil
}

func (c *permissionContext) AdminPermissionInfoList(ctx context.Context, req AdminPermissionInfoListRequest) ([]*permissionclient.PermissionListInfo, error) {
	request := &permissionclient.AdminPermissionInfoListRequest{
		PermissionId: req.PermissionID,
		ParentId:     req.ParentID,
		Type:         req.Type,
		Name:         req.Name,
	}

	if req.Enable != nil {
		request.Enable = &permissionclient.BoolValue{Value: *req.Enable}
	}

	if req.Weight != nil {
		request.Weight = &permissionclient.Uint32Value{Value: *req.Weight}
	}

	if req.Partial != nil {
		request.Partial = &permissionclient.BoolValue{Value: *req.Partial}
	}

	if req.Modify != nil {
		request.Modify = &permissionclient.BoolValue{Value: *req.Modify}
	}

	if req.OldPerm != nil {
		request.OldPerm = &permissionclient.BoolValue{Value: *req.OldPerm}
	}

	if req.Maintenance != nil {
		request.Maintenance = &permissionclient.BoolValue{Value: *req.Maintenance}
	}

	resp, err := c.PermissionRPC.AdminPermissionInfoList(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetPermissionInfo(), nil
}

func (c *permissionContext) GetAdminEnabledPermission(ctx context.Context) (*permissionclient.GetAdminPermissionEnabledResponse, error) {
	request := &permissionclient.GetAdminPermissionEnabledRequest{
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}

	resp, err := c.PermissionRPC.GetAdminPermissionEnabled(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *permissionContext) GetUserPermissionAffectList(ctx context.Context) (map[uint32]*permissionclient.PermissionAffectList, error) {
	resp, err := c.PermissionRPC.GetUserPermissionAffectList(ctx, &permissionclient.EmptyRequest{})
	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetList(), nil
}

func (c *permissionContext) GetUserPermissionConfigList(ctx context.Context, req UserPermissionConfigListRequest) ([]*permissionclient.UserPermissionConfig, error) {
	request := &permissionclient.UserPermissionConfigListRequest{
		PermissionId: req.PermissionId,
		ParentId:     req.ParentId,
		Type:         req.Type,
		Name:         req.Name,
	}

	if req.Enable != nil && *req.Enable {
		request.Enable = &permissionclient.BoolValue{
			Value: *req.Enable,
		}
	}

	if req.HierarchyPerm != nil && *req.HierarchyPerm {
		request.HierarchyPerm = &permissionclient.BoolValue{
			Value: *req.HierarchyPerm,
		}
	}

	resp, err := c.PermissionRPC.UserPermissionConfigList(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetPermissionConfig(), nil
}

func (c *permissionContext) GetUserPermEnabledFromRedis(ctx context.Context) []uint32 {
	result, err := c.RedisCache.Get(ctx, CacheUserPermEnabledKey).Result()

	if err != nil {
		return []uint32{}
	}

	var permission []uint32
	jsonErr := json.Unmarshal([]byte(result), &permission)

	if jsonErr != nil {
		c.Error("user permission enable json parse error: ", jsonErr)
		return []uint32{}
	}

	return permission
}

func (c *permissionContext) GetUserPermEnabled(ctx context.Context, permissionId []uint32) ([]uint32, error) {
	redisData := c.GetUserPermEnabledFromRedis(ctx)

	if len(redisData) > 0 {
		if len(permissionId) > 0 {
			redisDataTmp := make([]uint32, 0)
			for _, v := range permissionId {
				if slices.Contains(redisData, v) {
					redisDataTmp = append(redisDataTmp, v)
				}
			}
			redisData = redisDataTmp
		}

		return redisData, nil
	}

	// database
	dbData, err := c.GetUserPermEnabledFromRPC(ctx, []uint32{})

	if err != nil {
		return []uint32{}, err
	}

	c.SetUserPermEnabledToRedis(ctx, dbData)

	if len(permissionId) > 0 {
		dbDataTmp := make([]uint32, 0)
		for _, v := range permissionId {
			if slices.Contains(dbData, v) {
				dbDataTmp = append(dbDataTmp, v)
			}
		}
		dbData = dbDataTmp
	}

	return dbData, nil
}

func (c *permissionContext) GetUserPermEnabledFromRPC(ctx context.Context, permissionID []uint32) ([]uint32, error) {
	result, err := c.PermissionRPC.GetUserPermissionEnabled(ctx, &permissionclient.UserPermissionEnabledRequest{
		PermissionId: permissionID,
	})

	if err != nil {
		return []uint32{}, errorx.GRPCErrorToErrorx(err)
	}

	return result.GetPermissionId(), nil
}

func (c *permissionContext) SetUserPermEnabledToRedis(ctx context.Context, permissionID []uint32) {
	data, jsonErr := json.Marshal(permissionID)

	if jsonErr != nil {
		c.Error("marshal user permission enable error: ", jsonErr)
		return
	}

	_, err := c.RedisCache.Set(ctx, CacheUserPermEnabledKey, string(data), 0).Result()
	if err != nil {
		c.Error("redis set user permission enable error: ", err)
		return
	}
}

func (c *permissionContext) GetUserAllPermission(ctx context.Context, user types.UserInfo, permissionId []uint32, enable *bool) ([]*permissionclient.UserPermission, error) {
	params := &permissionclient.UserAllPermissionRequest{
		AllParents:   user.AllParentsId,
		UserId:       user.Id,
		Role:         user.Role,
		Sub:          user.IsSub,
		PermissionId: permissionId,
	}

	if enable != nil && *enable {
		params.Enable = &permissionclient.BoolValue{
			Value: *enable,
		}
	}

	result, err := c.PermissionRPC.GetUserAllPermission(ctx, params)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return result.GetData(), nil
}

func (c *permissionContext) GetAllPermConfigMap(ctx context.Context) (map[uint32]types.UserPermissionConfig, error) {
	if cachePermConfigs, exists := c.MemoryCache.Get(CacheUserPermConfigKey); exists {
		if res, ok := cachePermConfigs.(map[uint32]types.UserPermissionConfig); ok {
			return res, nil
		}
	}

	// 獲取所有啟用的用戶權限ID
	userPermEnableList, userPermEnableListErr := c.GetUserPermEnabled(ctx, []uint32{})
	if userPermEnableListErr != nil {
		return nil, userPermEnableListErr
	}

	allPermConfigList, allPermConfigListErr := c.GetUserPermissionConfigList(ctx, UserPermissionConfigListRequest{
		PermissionId: userPermEnableList,
	})

	if allPermConfigListErr != nil {
		return nil, allPermConfigListErr
	}

	allPermConfigMap := TransferUserPermissionConfig(allPermConfigList)

	c.MemoryCache.Set(CacheUserPermConfigKey, allPermConfigMap)

	return allPermConfigMap, nil
}

func (c *permissionContext) FilterUserPermission(ctx context.Context, allPermConfigMap map[uint32]types.UserPermissionConfig, operator types.UserInfo, permissionId []uint32) (Result, error) {
	// 獲取有效的權限ID列表
	permsOpenEnable := allPermConfigMap

	if len(permissionId) > 0 {
		permsOpenEnable = maputil.GetMapValuesByKeys(allPermConfigMap, permissionId)
	}

	if len(permsOpenEnable) == 0 {
		return Result{}, nil
	}

	roleMap := c.getRoleMap(operator)
	rolePerms := c.getRolePermission(permsOpenEnable, roleMap, len(operator.AllParentsId))

	permsOpenEnableId := maputil.GetMapKeys(permsOpenEnable)
	userPerms, userPermsErr := c.getUserPermissions(ctx, operator, permsOpenEnableId)
	if userPermsErr != nil {
		return Result{}, userPermsErr
	}

	// 合併角色權限和用戶權限
	rolePerms = append(rolePerms, userPerms...)

	return c.buildPermissionResult(rolePerms), nil
}

func (c *permissionContext) getRoleMap(user types.UserInfo) []uint32 {
	roleMap := []uint32{constants.Role7, constants.Role5, constants.Role4, constants.Role3, constants.Role2}

	i := slices.Index(roleMap, user.Role)
	roleMap = roleMap[0 : i+1]

	if user.IsSub {
		roleMap = append(roleMap, user.Role+constants.SubRole)
	}

	return roleMap
}

func (c *permissionContext) getRolePermission(permConfigList map[uint32]types.UserPermissionConfig, roleMap []uint32, parentsCount int) []UserPermission {
	rolePerms := make([]UserPermission, 0)
	for depth := 0; depth <= parentsCount; depth++ {
		for _, v := range permConfigList {
			var userPerms = UserPermission{
				Depth:        uint32(depth),
				UserId:       0,
				PermissionId: v.PermissionId,
				Enable:       false,
				Modify:       false,
			}
			// 找到層級權限
			for _, role := range v.RolePerm {
				if role.RoleId == roleMap[depth] {
					userPerms.Enable = true
					userPerms.Modify = role.Modify
					break
				}
			}

			rolePerms = append(rolePerms, userPerms)
		}
	}

	return rolePerms
}

func (c *permissionContext) getUserPermissions(ctx context.Context, operator types.UserInfo, permIDs []uint32) ([]UserPermission, error) {
	// 從權限服務獲取用戶權限
	rowPerms, err := c.GetUserAllPermission(ctx, operator, permIDs, nil)
	if err != nil {
		return nil, err
	}

	// 轉換為內部權限結構
	userPerms := make([]UserPermission, 0, len(rowPerms))
	for _, perm := range rowPerms {
		userPerms = append(userPerms, UserPermission{
			Depth:        c.getPermDepth(operator, perm),
			UserId:       perm.GetUserId(),
			PermissionId: perm.GetPermissionId(),
			Enable:       perm.GetEnable(),
			Modify:       perm.GetModify(),
		})
	}

	return userPerms, nil
}

func (c *permissionContext) getPermDepth(operator types.UserInfo, perm *permissionclient.UserPermission) uint32 {
	users := operator.AllParentsId
	slices.Reverse(users)
	users = append(users, operator.Id)

	// 各層自身權限
	if perm.GetRoleId() == 0 {
		return uint32(slices.Index(users, perm.GetUserId()))
	}

	roleMap := c.getRoleMap(operator)

	return uint32(slices.Index(roleMap, perm.GetRoleId()))
}

func (c *permissionContext) buildPermissionResult(perms []UserPermission) Result {
	permResultEnable := make(map[uint32]map[uint32]bool)
	permResultModify := make(map[uint32]map[uint32]bool)

	keysMap := make(map[uint32]struct{})

	for _, perm := range perms {
		if permResultEnable[perm.PermissionId] == nil {
			permResultEnable[perm.PermissionId] = make(map[uint32]bool)
		}
		permResultEnable[perm.PermissionId][perm.Depth] = perm.Enable

		if permResultModify[perm.PermissionId] == nil {
			permResultModify[perm.PermissionId] = make(map[uint32]bool)
		}
		permResultModify[perm.PermissionId][perm.Depth] = perm.Modify

		keysMap[perm.PermissionId] = struct{}{}
	}

	keys := make([]uint32, 0, len(keysMap))
	for k := range keysMap {
		keys = append(keys, k)
	}
	sort.Slice(keys, func(i, j int) bool { return keys[i] < keys[j] })

	// 初始化結果結構
	permResult := Result{
		Enable: make(map[uint32]map[uint32]bool),
		Modify: make(map[uint32]map[uint32]bool),
	}

	// 保留enable=true
	for _, permId := range keys {
		if enableMap, exists := permResultEnable[permId]; exists {
			for depth, state := range enableMap {
				if state {
					if permResult.Enable[permId] == nil {
						permResult.Enable[permId] = make(map[uint32]bool)
					}

					permResult.Enable[permId][depth] = true
				}
			}
		}

		if modifyMap, exists := permResultModify[permId]; exists {
			for depth, state := range modifyMap {
				if state {
					if permResult.Modify[permId] == nil {
						permResult.Modify[permId] = make(map[uint32]bool)
					}

					permResult.Modify[permId][depth] = true
				}
			}
		}
	}

	return permResult
}

func TransferUserPermissionConfig(permConfig []*permissionclient.UserPermissionConfig) map[uint32]types.UserPermissionConfig {
	userPermissionConfigMap := map[uint32]types.UserPermissionConfig{}

	for _, v := range permConfig {
		rolePerm := []types.RolePerm{}
		for _, data := range v.GetRolePerm() {
			rolePerm = append(rolePerm, types.RolePerm{
				RoleId: data.GetRoleId(),
				View:   data.GetView(),
				Modify: data.GetModify(),
			})
		}

		userPermissionConfigMap[v.GetPermissionId()] = types.UserPermissionConfig{
			PermissionId:  v.GetPermissionId(),
			ParentId:      v.GetParentId(),
			Type:          v.GetType(),
			Name:          v.GetName(),
			Note:          v.GetNote(),
			Dict:          v.GetDict(),
			Rd3Dict:       v.GetRd3Dict(),
			Enable:        v.GetEnable(),
			Sort:          v.GetSort(),
			DomainOnly:    v.GetDomainOnly(),
			TopSubDomain:  v.GetTopSubDomain(),
			OpenNew:       v.GetOpenNew(),
			Strict:        v.GetStrict(),
			Modifiable:    v.GetModifiable(),
			OldPerm:       v.GetOldPerm(),
			HierarchyPerm: v.GetHierarchyPerm(),
			Maintenance:   v.GetMaintenance(),
			Extra: types.Extra{
				Icon:       v.GetExtra().GetIcon(),
				File:       v.GetExtra().GetFile(),
				Host:       v.GetExtra().GetHost(),
				Qstr:       v.GetExtra().GetQstr(),
				Route:      v.GetExtra().GetRoute(),
				Conditions: v.GetExtra().GetConditions(),
				APIName:    v.GetExtra().GetApiName(),
				APIType:    v.GetExtra().GetApiType(),
			},
			RolePerm:     rolePerm,
			RoleSettable: v.GetRoleSettable(),
		}
	}

	return userPermissionConfigMap
}

func (c *permissionContext) GetAdminVisitedHistory(ctx context.Context, adminId uint32) ([]*permissionclient.VisitedHistoryDetail, error) {
	result, err := c.PermissionRPC.GetVisitedHistory(ctx, &permissionclient.GetVisitedHistoryRequest{
		AdminId: adminId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return result.GetVisitedHistoryDetail(), nil
}

func (c *permissionContext) DeleteUserPermissionByUserID(ctx context.Context, userId uint32) error {
	_, err := c.PermissionRPC.DeleteUserPermissionByUserID(ctx, &permissionclient.DeleteUserPermissionByUserIDRequest{
		UserId: userId,
	})

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *permissionContext) CheckUserPermission(ctx context.Context, operator types.UserInfo, permissionId uint32, permissionAction string) (bool, error) {
	if permissionAction != constants.Enable && permissionAction != constants.Modify {
		return false, errorx.BackstageInvalidPermissionAction
	}

	cacheKey := fmt.Sprintf(CacheUserPermActionKey, operator.Id, permissionId, permissionAction)
	if cachedResult, found := c.MemoryCache.Get(cacheKey); found {
		if res, ok := cachedResult.(bool); ok {
			return res, nil
		}
	}

	// 取得該權限的Config，並檢查權限，未開放直接回傳
	allPermConfig, allPermConfigErr := c.GetAllPermConfigMap(ctx)
	if allPermConfigErr != nil {
		return false, allPermConfigErr
	}

	// 上層名稱
	var permConfig types.UserPermissionConfig
	var exists bool
	if permConfig, exists = allPermConfig[permissionId]; !exists {
		c.MemoryCache.Set(cacheKey, false)

		return false, nil
	}

	if !permConfig.Enable {
		c.MemoryCache.Set(cacheKey, false)

		return false, nil
	}

	// 檢查user跟user上層權限並回傳最後user的權限
	permResult, permResultErr := c.checkUserAllPermission(ctx, operator, permConfig)
	if permResultErr != nil {
		return false, permResultErr
	}

	// 無上層直接回傳 或是 已經是false
	if permissionAction == constants.Enable && (permConfig.ParentId == 0 || !permResult.Enable) {
		c.MemoryCache.Set(cacheKey, permResult.Enable)

		return permResult.Enable, nil
	}

	if permissionAction == constants.Modify && (permConfig.ParentId == 0 || !permResult.Modify) {
		c.MemoryCache.Set(cacheKey, permResult.Modify)

		return permResult.Modify, nil
	}

	// 檢查上層權限
	upperPerm, upperPermErr := c.CheckUserPermission(ctx, operator, permConfig.ParentId, permissionAction)
	if upperPermErr != nil {
		return false, upperPermErr
	}

	c.MemoryCache.Set(cacheKey, upperPerm)

	return upperPerm, nil
}

func (c *permissionContext) checkUserAllPermission(ctx context.Context, operator types.UserInfo, permissionConfig types.UserPermissionConfig) (UserPermissionResult, error) {
	// [depth]UserPermissionResult
	permissionInfo := make(map[uint32]UserPermissionResult)

	// 設定預設層級權限
	roleMap := c.getRoleMap(operator)

	for k, role := range roleMap {
		userPermission := UserPermissionResult{
			Enable: len(permissionConfig.RolePerm) == 0,
			Modify: len(permissionConfig.RolePerm) == 0,
		}

		for _, rolePermInfo := range permissionConfig.RolePerm {
			if rolePermInfo.RoleId == role {
				userPermission = UserPermissionResult{
					Enable: true,
					Modify: rolePermInfo.Modify,
				}
				break
			}
		}

		permissionInfo[uint32(k)] = userPermission
	}

	permissionId := permissionConfig.PermissionId

	userAllPermission, err := c.getUserPermissions(ctx, operator, []uint32{permissionId})
	if err != nil {
		return UserPermissionResult{}, err
	}

	// 取最大的depth-自己
	maxDepth := uint32(0)
	// 分開每個層級的權限
	for _, v := range userAllPermission {
		if v.PermissionId != permissionId {
			continue
		}

		permissionInfo[v.Depth] = UserPermissionResult{
			Enable: v.Enable,
			Modify: v.Modify,
		}

		if v.Depth > maxDepth {
			maxDepth = v.Depth
		}
	}

	if maxDepth == 0 {
		return permissionInfo[maxDepth], nil
	}

	result := permissionInfo[maxDepth]

	// 嚴格:所有上層跟自己都要開 ; 非嚴格:自己要開,若為子帳號，上層也要開
	if permissionConfig.Strict {
		// 最大的depth是自己, 檢查上層是否有關閉
		result.Modify = result.Modify && checkUpperPermission(permissionInfo, maxDepth, constants.Modify)
		result.Enable = result.Enable && checkUpperPermission(permissionInfo, maxDepth, constants.Enable)
	} else if operator.IsSub {
		result.Modify = result.Modify && permissionInfo[maxDepth-1].Modify
		result.Enable = result.Enable && permissionInfo[maxDepth-1].Enable
	}

	return result, nil
}

func checkUpperPermission(info map[uint32]UserPermissionResult, maxDepth uint32, field string) bool {
	for i := uint32(0); i < maxDepth; i++ {
		if (field == constants.Modify && !info[i].Modify) || (field == constants.Enable && !info[i].Enable) {
			return false
		}
	}

	return true
}

func (c *permissionContext) GetPermissionConfigById(ctx context.Context, permissionId uint32) *types.UserPermissionConfig {
	// 取得該權限的Config，並檢查權限，未開放直接回傳
	allPermConfig, allPermConfigErr := c.GetAllPermConfigMap(ctx)
	if allPermConfigErr != nil {
		return nil
	}

	// 上層名稱
	if permConfig, exists := allPermConfig[permissionId]; exists {
		return &permConfig
	}

	return nil
}

func (c *permissionContext) UserPermList(ctx context.Context, req UserPermListRequest) (*permissionclient.UserPermListResponse, error) {
	request := &permissionclient.UserPermListRequest{
		UserId:       req.UserId,
		PermissionId: req.PermissionId,
		RoleId:       req.RoleId,
	}

	if req.Enable != nil {
		request.Enable = &permissionclient.BoolValue{
			Value: *req.Enable,
		}
	}

	result, err := c.PermissionRPC.UserPermList(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return result, nil
}

// 回傳所有上層權限(包括本身) 不判斷userPermEnable
func (c *permissionContext) GetPermissionParents(ctx context.Context, permissionId uint32) ([]*types.UserPermissionConfig, error) {
	enable := true
	allUserPermResp, err := c.GetUserPermissionConfigList(ctx, UserPermissionConfigListRequest{
		Enable: &enable,
	})

	if err != nil {
		return nil, err
	}

	allUserPermConfig := TransferUserPermissionConfig(allUserPermResp)

	return GetPermissionParentsRecursive(allUserPermConfig, permissionId), nil
}

// 遞迴獲取上層
func GetPermissionParentsRecursive(allUserPermConfig map[uint32]types.UserPermissionConfig, permissionId uint32) []*types.UserPermissionConfig {
	parentPerm := make([]*types.UserPermissionConfig, 0)

	if perm, ok := allUserPermConfig[permissionId]; ok {
		parentPerm = append(parentPerm, &perm)

		if perm.ParentId != 0 {
			parentPerm = append(parentPerm, GetPermissionParentsRecursive(allUserPermConfig, perm.ParentId)...)
		}
	}

	return parentPerm
}
