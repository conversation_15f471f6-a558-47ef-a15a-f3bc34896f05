package permission

import (
	"context"

	"gbh/permission/permissionclient"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx        context.Context
	redisCache *redis.Client
	redisMock  redismock.ClientMock
)

type mockPermissionRPC struct{ mock.Mock }

func (m *mockPermissionRPC) GetVisitedHistory(ctx context.Context, in *permissionclient.GetVisitedHistoryRequest, _ ...grpc.CallOption) (*permissionclient.GetVisitedHistoryResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.GetVisitedHistoryResponse), nil
}

func (m *mockPermissionRPC) AdminControllerPermissionIDs(ctx context.Context, in *permissionclient.AdminControllerPermissionIDsRequest, _ ...grpc.CallOption) (*permissionclient.AdminControllerPermissionIDsResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.AdminControllerPermissionIDsResponse), nil
}

func (m *mockPermissionRPC) AdminPermissions(ctx context.Context, in *permissionclient.AdminPermissionsRequest, _ ...grpc.CallOption) (*permissionclient.AdminPermissionsResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.AdminPermissionsResponse), nil
}

func (m *mockPermissionRPC) ModifyVisited(ctx context.Context, in *permissionclient.VisitedRequest, _ ...grpc.CallOption) (*permissionclient.EmptyResponse, error) {
	args := m.Called(ctx, in)

	return nil, args.Error(1)
}

func (m *mockPermissionRPC) GetAdminPermissionEnabled(ctx context.Context, in *permissionclient.GetAdminPermissionEnabledRequest, _ ...grpc.CallOption) (*permissionclient.GetAdminPermissionEnabledResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.GetAdminPermissionEnabledResponse), nil
}

func (m *mockPermissionRPC) AdminPermissionInfoList(ctx context.Context, in *permissionclient.AdminPermissionInfoListRequest, _ ...grpc.CallOption) (*permissionclient.AdminPermissionInfoListResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.AdminPermissionInfoListResponse), nil
}

func (m *mockPermissionRPC) GetUserPermissionAffectList(ctx context.Context, in *permissionclient.EmptyRequest, _ ...grpc.CallOption) (*permissionclient.GetUserPermissionAffectListResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.GetUserPermissionAffectListResponse), nil
}

func (m *mockPermissionRPC) UserPermissionConfigList(ctx context.Context, in *permissionclient.UserPermissionConfigListRequest, _ ...grpc.CallOption) (*permissionclient.UserPermissionConfigListResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.UserPermissionConfigListResponse), nil
}

func (m *mockPermissionRPC) GetUserPermissionEnabled(ctx context.Context, in *permissionclient.UserPermissionEnabledRequest, _ ...grpc.CallOption) (*permissionclient.UserPermissionEnabledResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.UserPermissionEnabledResponse), nil
}

func (m *mockPermissionRPC) GetUserAllPermission(ctx context.Context, in *permissionclient.UserAllPermissionRequest, _ ...grpc.CallOption) (*permissionclient.UserAllPermissionResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.UserAllPermissionResponse), nil
}

func (m *mockPermissionRPC) DeleteUserPermissionByUserID(ctx context.Context, in *permissionclient.DeleteUserPermissionByUserIDRequest, _ ...grpc.CallOption) (*permissionclient.EmptyResponse, error) {
	args := m.Called(ctx, in)

	return nil, args.Error(1)
}

func (m *mockPermissionRPC) UserPermList(ctx context.Context, in *permissionclient.UserPermListRequest, _ ...grpc.CallOption) (*permissionclient.UserPermListResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.UserPermListResponse), nil
}

func (m *mockPermissionRPC) DeleteUserDepartmentByUserID(ctx context.Context, in *permissionclient.DeleteUserDepartmentByUserIDRequest, _ ...grpc.CallOption) (*permissionclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.EmptyResponse), nil
}

func (m *mockPermissionRPC) SetUserPermission(ctx context.Context, in *permissionclient.SetUserPermissionRequest, _ ...grpc.CallOption) (*permissionclient.EmptyResponse, error) {
	args := m.Called(ctx, in)

	return nil, args.Error(1)
}

func (m *mockPermissionRPC) SetAdminPermissionByUser(ctx context.Context, in *permissionclient.SetAdminPermissionByUserRequest, _ ...grpc.CallOption) (*permissionclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.EmptyResponse), nil
}

func (m *mockPermissionRPC) SetAdminPermission(ctx context.Context, in *permissionclient.SetAdminPermissionRequest, _ ...grpc.CallOption) (*permissionclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*permissionclient.EmptyResponse), nil
}

func newMockPermissionRPC() *mockPermissionRPC {
	return &mockPermissionRPC{}
}

func init() {
	ctx = context.Background()
	redisCache, redisMock = redismock.NewClientMock()
}
