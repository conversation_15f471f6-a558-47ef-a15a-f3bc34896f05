package permission

import (
	"encoding/json"
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/permission/permissionclient"
	"testing"

	"github.com/zeromicro/go-zero/core/collection"
	"github.com/zeromicro/go-zero/core/logx"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	_m "github.com/stretchr/testify/mock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	expectedResponse := &permissionContext{
		PermissionRPC: permissionRPC,
		RedisCache:    redisCache,
		MemoryCache:   memoryCache,
		Logger:        logx.WithContext(ctx),
	}

	assert.Equal(t, expectedResponse, permissionCtx)
}

func Test_PermissionContext_AdminControllerPermissionIDs(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.AdminControllerPermissionIDsRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionRPC.On("AdminControllerPermissionIDs", ctx, mockRequest).Return(&seeder.AdminControllerPermissionIDs, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.AdminControllerPermissionIDs(ctx, seeder.AdminControllerName)

	expectedResponse := seeder.AdminControllerPermissionIDs.GetPermissionId()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_PermissionContext_AdminControllerPermissionIDs_GRPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &permissionclient.AdminControllerPermissionIDsRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionRPC.On("AdminControllerPermissionIDs", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.AdminControllerPermissionIDs(ctx, seeder.AdminControllerName)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_PermissionContext_AdminPermissions(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	enable := true
	favorite := false
	modify := true
	mockRequest := &permissionclient.AdminPermissionsRequest{
		AdminId:      []uint32{seeder.AdminID},
		PermissionId: []uint32{seeder.AdminPermID63, seeder.AdminPermID86, seeder.AdminPermID149, seeder.AdminPermID707},
		Enable: &permissionclient.BoolValue{
			Value: enable,
		},
		Favorite: &permissionclient.BoolValue{
			Value: favorite,
		},
		Modify: &permissionclient.BoolValue{
			Value: modify,
		},
	}

	permissionRPC.On("AdminPermissions", ctx, mockRequest).Return(&seeder.AdminPermissions, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.AdminPermissions(ctx, AdminPermissionsRequest{
		AdminID:      []uint32{seeder.AdminID},
		PermissionID: []uint32{seeder.AdminPermID63, seeder.AdminPermID86, seeder.AdminPermID149, seeder.AdminPermID707},
		Enable:       &enable,
		Favorite:     &favorite,
		Modify:       &modify,
	})

	expectedResponse := seeder.AdminPermissions.GetPermissionInfo()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_PermissionContext_AdminPermissions_GRPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &permissionclient.AdminPermissionsRequest{
		AdminId:      []uint32{seeder.AdminID},
		PermissionId: []uint32{seeder.AdminPermID63, seeder.AdminPermID86, seeder.AdminPermID149, seeder.AdminPermID707},
	}

	permissionRPC.On("AdminPermissions", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.AdminPermissions(ctx, AdminPermissionsRequest{
		AdminID:      []uint32{seeder.AdminID},
		PermissionID: []uint32{seeder.AdminPermID63, seeder.AdminPermID86, seeder.AdminPermID149, seeder.AdminPermID707},
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_PermissionContext_AdminPermissionInfoList(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	enable := true
	weight := seeder.PermissionWeight0
	partial := false
	modify := true
	oldPerm := true
	maintenance := true
	mockRequest := &permissionclient.AdminPermissionInfoListRequest{
		PermissionId: []uint32{seeder.AdminPermID63, seeder.AdminPermID149},
		ParentId:     []uint32{seeder.AdminPermID9, seeder.AdminPermID16},
		Type:         []string{"Menu"},
		Name:         []string{"Menu_2", "ReportSearch"},
		Enable: &permissionclient.BoolValue{
			Value: enable,
		},
		Weight: &permissionclient.Uint32Value{
			Value: weight,
		},
		Partial: &permissionclient.BoolValue{
			Value: partial,
		},
		Modify: &permissionclient.BoolValue{
			Value: modify,
		},
		OldPerm: &permissionclient.BoolValue{
			Value: oldPerm,
		},
		Maintenance: &permissionclient.BoolValue{
			Value: maintenance,
		},
	}

	permissionRPC.On("AdminPermissionInfoList", ctx, mockRequest).Return(&seeder.AdminPermissionInfoList, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.AdminPermissionInfoList(ctx, AdminPermissionInfoListRequest{
		PermissionID: []uint32{seeder.AdminPermID63, seeder.AdminPermID149},
		ParentID:     []uint32{seeder.AdminPermID9, seeder.AdminPermID16},
		Type:         []string{"Menu"},
		Name:         []string{"Menu_2", "ReportSearch"},
		Enable:       &enable,
		Weight:       &weight,
		Partial:      &partial,
		Modify:       &modify,
		OldPerm:      &oldPerm,
		Maintenance:  &maintenance,
	})

	expectedResponse := seeder.AdminPermissionInfoList.GetPermissionInfo()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_PermissionContext_AdminPermissionInfoList_GRPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &permissionclient.AdminPermissionInfoListRequest{
		PermissionId: []uint32{seeder.AdminPermID63, seeder.AdminPermID149},
		ParentId:     []uint32{seeder.AdminPermID9, seeder.AdminPermID16},
		Type:         []string{"Menu"},
		Name:         []string{"Menu_2", "ReportSearch"},
	}

	permissionRPC.On("AdminPermissionInfoList", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.AdminPermissionInfoList(ctx, AdminPermissionInfoListRequest{
		PermissionID: []uint32{seeder.AdminPermID63, seeder.AdminPermID149},
		ParentID:     []uint32{seeder.AdminPermID9, seeder.AdminPermID16},
		Type:         []string{"Menu"},
		Name:         []string{"Menu_2", "ReportSearch"},
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_PermissionContext_GetAdminPermissionEnabled(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.GetAdminPermissionEnabledRequest{
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}

	permissionRPC.On("GetAdminPermissionEnabled", ctx, mockRequest).Return(&seeder.AdminPermissionEnabled, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetAdminEnabledPermission(ctx)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.AdminPermissionEnabled, resp)
}

func Test_PermissionContext_GetAdminPermissionEnabled_GRPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.GetAdminPermissionEnabledRequest{
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	permissionRPC.On("GetAdminPermissionEnabled", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetAdminEnabledPermission(ctx)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_PermissionContext_GetUserPermissionAffectList(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.EmptyRequest{}

	permissionRPC.On("GetUserPermissionAffectList", ctx, mockRequest).Return(&seeder.UserPermissionAffectList, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetUserPermissionAffectList(ctx)

	expectedResponse := seeder.UserPermissionAffectList.GetList()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_PermissionContext_GetUserPermissionAffectList_GRPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.EmptyRequest{}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	permissionRPC.On("GetUserPermissionAffectList", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetUserPermissionAffectList(ctx)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GetUserAllPermission(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents:   []uint32{},
		UserId:       seeder.HallId,
		Role:         constants.Role7,
		Sub:          true,
		PermissionId: []uint32{},
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(&seeder.UserAllPermission, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	userReq := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        true,
	}

	enable := true
	resp, err := permissionCtx.GetUserAllPermission(ctx, userReq, []uint32{}, &enable)

	expectedResponse := seeder.UserAllPermission.GetData()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GetUserAllPermission_GRPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents:   []uint32{},
		UserId:       seeder.HallId,
		Role:         constants.Role7,
		Sub:          true,
		PermissionId: []uint32{},
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	userReq := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        true,
	}

	enable := true
	resp, err := permissionCtx.GetUserAllPermission(ctx, userReq, []uint32{}, &enable)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GetUserPermEnabled_FromRedis(t *testing.T) {
	jsonUserPermEnabled, err := json.Marshal(seeder.UserPermEnabled.GetPermissionId())
	assert.NoError(t, err)

	redisMock.ExpectGet(CacheUserPermEnabledKey).SetVal(string(jsonUserPermEnabled))

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetUserPermEnabled(ctx, []uint32{seeder.UserPermID401, seeder.UserPermID402})

	expectedResponse := seeder.UserPermEnabled.GetPermissionId()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
	redisMock.ClearExpect()
}

func Test_GetUserPermEnabled_FromRedis_GetError(t *testing.T) {
	jsonUserPermEnabled, err := json.Marshal(seeder.UserPermEnabled.GetPermissionId())
	assert.NoError(t, err)

	redisMock.ExpectGet(CacheUserPermEnabledKey).SetErr(redis.ErrClosed)
	redisMock.ExpectSet(CacheUserPermEnabledKey, string(jsonUserPermEnabled), 0).SetVal("ok")

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}

	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(&seeder.UserPermEnabled, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetUserPermEnabled(ctx, []uint32{seeder.UserPermID401, seeder.UserPermID402})

	expectedResponse := seeder.UserPermEnabled.GetPermissionId()

	assert.Equal(t, expectedResponse, resp)
	assert.NoError(t, err)
	redisMock.ClearExpect()
}

func Test_GetUserPermEnabled_FromRedis_JsonError(t *testing.T) {
	redisMock.ExpectGet(CacheUserPermEnabledKey).SetVal("test")
	redisMock.ExpectSet(CacheUserPermEnabledKey, "[401,402]", 0).SetVal("ok")

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}

	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(&seeder.UserPermEnabled, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetUserPermEnabled(ctx, []uint32{seeder.UserPermID401, seeder.UserPermID402})

	expectedResponse := seeder.UserPermEnabled.GetPermissionId()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
	redisMock.ClearExpect()
}

func Test_GetUserPermEnabled_FromRPC(t *testing.T) {
	redisMock.ExpectGet(CacheUserPermEnabledKey).RedisNil()
	redisMock.ExpectSet(CacheUserPermEnabledKey, "[401,402]", 0).SetVal("ok")

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}

	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(&seeder.UserPermEnabled, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetUserPermEnabled(ctx, []uint32{seeder.UserPermID401, seeder.UserPermID402})

	expectedResponse := seeder.UserPermEnabled.GetPermissionId()

	assert.Equal(t, expectedResponse, resp)
	assert.NoError(t, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func Test_GetUserPermEnabled_FromRPC_GetError(t *testing.T) {
	redisMock.ExpectGet(CacheUserPermEnabledKey).RedisNil()

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)
	resp, err := permissionCtx.GetUserPermEnabled(ctx, []uint32{seeder.UserPermID401, seeder.UserPermID402})

	assert.Equal(t, []uint32{}, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func Test_GetUserPermEnabled_FromRPC_SetRedisError(t *testing.T) {
	jsonData, err := json.Marshal([]uint32{seeder.UserPermID401, seeder.UserPermID402})
	assert.NoError(t, err)

	redisMock.ExpectGet(CacheUserPermEnabledKey).RedisNil()
	redisMock.ExpectSet(CacheUserPermEnabledKey, string(jsonData), 0).SetErr(redis.ErrClosed)

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}

	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(&seeder.UserPermEnabled, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetUserPermEnabled(ctx, []uint32{seeder.UserPermID401, seeder.UserPermID402})

	expectedResponse := seeder.UserPermEnabled.GetPermissionId()

	assert.Equal(t, expectedResponse, resp)
	assert.NoError(t, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func Test_GetUserPermissionConfigList(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	enable := true
	hierarchyPerm := true
	mockRequest := &permissionclient.UserPermissionConfigListRequest{
		Enable: &permissionclient.BoolValue{
			Value: enable,
		},
		HierarchyPerm: &permissionclient.BoolValue{
			Value: hierarchyPerm,
		},
	}

	permissionRPC.On("UserPermissionConfigList", ctx, mockRequest).Return(&seeder.UserPermissionConfigList, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	req := UserPermissionConfigListRequest{
		Enable:        &enable,
		HierarchyPerm: &hierarchyPerm,
	}
	resp, err := permissionCtx.GetUserPermissionConfigList(ctx, req)

	expectedResponse := seeder.UserPermissionConfigList.GetPermissionConfig()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GetUserPermissionConfigList_RPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionConfigListRequest{}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("UserPermissionConfigList", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	req := UserPermissionConfigListRequest{}
	resp, err := permissionCtx.GetUserPermissionConfigList(ctx, req)
	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GetAllPermConfigMap_GetFromRedis(t *testing.T) {
	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	configMap := map[uint32]types.UserPermissionConfig{
		configInfo.GetPermissionId(): {
			PermissionId:  configInfo.GetPermissionId(),
			ParentId:      configInfo.GetParentId(),
			Type:          configInfo.GetType(),
			Name:          configInfo.GetName(),
			Note:          configInfo.GetNote(),
			Dict:          configInfo.GetDict(),
			Rd3Dict:       configInfo.GetRd3Dict(),
			Enable:        configInfo.GetEnable(),
			Sort:          configInfo.GetSort(),
			DomainOnly:    configInfo.GetDomainOnly(),
			TopSubDomain:  configInfo.GetTopSubDomain(),
			OpenNew:       configInfo.GetOpenNew(),
			Strict:        configInfo.GetStrict(),
			Modifiable:    configInfo.GetModifiable(),
			OldPerm:       configInfo.GetOldPerm(),
			HierarchyPerm: configInfo.GetHierarchyPerm(),
			Maintenance:   configInfo.GetMaintenance(),
			Extra: types.Extra{
				Icon:       configInfo.GetExtra().GetIcon(),
				File:       configInfo.GetExtra().GetFile(),
				Host:       configInfo.GetExtra().GetHost(),
				Qstr:       configInfo.GetExtra().GetQstr(),
				Route:      configInfo.GetExtra().GetRoute(),
				Conditions: configInfo.GetExtra().GetConditions(),
				APIName:    configInfo.GetExtra().GetApiName(),
				APIType:    configInfo.GetExtra().GetApiType(),
			},
			RolePerm:     seeder.TransferRolePerm(configInfo.GetRolePerm()),
			RoleSettable: configInfo.GetRoleSettable(),
		},
	}

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, configMap)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetAllPermConfigMap(ctx)

	expectedResponse := configMap

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GetAllPermConfigMap_GetFromRpc(t *testing.T) {
	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	configMap := map[uint32]types.UserPermissionConfig{
		configInfo.GetPermissionId(): {
			PermissionId:  configInfo.GetPermissionId(),
			ParentId:      configInfo.GetParentId(),
			Type:          configInfo.GetType(),
			Name:          configInfo.GetName(),
			Note:          configInfo.GetNote(),
			Dict:          configInfo.GetDict(),
			Rd3Dict:       configInfo.GetRd3Dict(),
			Enable:        configInfo.GetEnable(),
			Sort:          configInfo.GetSort(),
			DomainOnly:    configInfo.GetDomainOnly(),
			TopSubDomain:  configInfo.GetTopSubDomain(),
			OpenNew:       configInfo.GetOpenNew(),
			Strict:        configInfo.GetStrict(),
			Modifiable:    configInfo.GetModifiable(),
			OldPerm:       configInfo.GetOldPerm(),
			HierarchyPerm: configInfo.GetHierarchyPerm(),
			Maintenance:   configInfo.GetMaintenance(),
			Extra: types.Extra{
				Icon:       configInfo.GetExtra().GetIcon(),
				File:       configInfo.GetExtra().GetFile(),
				Host:       configInfo.GetExtra().GetHost(),
				Qstr:       configInfo.GetExtra().GetQstr(),
				Route:      configInfo.GetExtra().GetRoute(),
				Conditions: configInfo.GetExtra().GetConditions(),
				APIName:    configInfo.GetExtra().GetApiName(),
				APIType:    configInfo.GetExtra().GetApiType(),
			},
			RolePerm:     seeder.TransferRolePerm(configInfo.GetRolePerm()),
			RoleSettable: configInfo.GetRoleSettable(),
		},
	}

	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, configMap)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionConfigListRequest{
		PermissionId: seeder.UserPermEnabled.GetPermissionId(),
	}

	mockResponse := permissionclient.UserPermissionConfigListResponse{
		PermissionConfig: []*permissionclient.UserPermissionConfig{configInfo},
	}
	permissionRPC.On("UserPermissionConfigList", ctx, mockRequest).Return(&mockResponse, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetAllPermConfigMap(ctx)

	expectedResponse := configMap

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GetAllPermConfigMap_GetFromRpc_UserEnableError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)
	resp, err := permissionCtx.GetAllPermConfigMap(ctx)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_GetAllPermConfigMap_GetFromRpc_ConfigListError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	jsonUserPermEnabled, err := json.Marshal(seeder.UserPermEnabled.GetPermissionId())
	assert.NoError(t, err)
	redisMock.ExpectGet(CacheUserPermEnabledKey).SetVal(string(jsonUserPermEnabled))

	permissionRPC := newMockPermissionRPC()

	mockEnableRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}
	permissionRPC.On("GetUserPermissionEnabled", ctx, mockEnableRequest).Return(&seeder.UserPermEnabled, nil)

	mockConfigRequest := &permissionclient.UserPermissionConfigListRequest{
		PermissionId: seeder.UserPermEnabled.GetPermissionId(),
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("UserPermissionConfigList", ctx, mockConfigRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)
	resp, err := permissionCtx.GetAllPermConfigMap(ctx)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func Test_GetAdminVisitedHistory(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.GetVisitedHistoryRequest{
		AdminId: seeder.AdminID,
	}

	permissionRPC.On("GetVisitedHistory", ctx, mockRequest).Return(&seeder.AdminVisitedHistoryResponse, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetAdminVisitedHistory(ctx, seeder.AdminID)

	expectedResponse := seeder.AdminVisitedHistoryResponse.GetVisitedHistoryDetail()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GetAdminVisitedHistory_RPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.GetVisitedHistoryRequest{
		AdminId: seeder.AdminID,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetVisitedHistory", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetAdminVisitedHistory(ctx, seeder.AdminID)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_DeleteUserPermissionByUserID(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.DeleteUserPermissionByUserIDRequest{
		UserId: seeder.UserId,
	}

	permissionRPC.On("DeleteUserPermissionByUserID", ctx, mockRequest).Return(nil, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	err := permissionCtx.DeleteUserPermissionByUserID(ctx, seeder.UserId)

	assert.NoError(t, err)
}

func Test_DeleteUserPermissionByUserID_RPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.DeleteUserPermissionByUserIDRequest{
		UserId: seeder.UserId,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("DeleteUserPermissionByUserID", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	err := permissionCtx.DeleteUserPermissionByUserID(ctx, seeder.UserId)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_FilterUserPermission_EmptyPermission(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	allPermConfigMap := map[uint32]types.UserPermissionConfig{
		seeder.UserPermID1471: {
			PermissionId: seeder.UserPermID1471,
		},
	}

	permissionCtx := New(permissionRPC, redisCache, memoryCache)
	resp, err := permissionCtx.FilterUserPermission(ctx, allPermConfigMap, operator, make([]uint32, seeder.UserPermID1))

	assert.Equal(t, Result{
		Enable: nil,
		Modify: nil,
	}, resp)
	assert.NoError(t, err)
}

func Test_FilterUserPermission_SupremeShareholderGet(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	operator := types.UserInfo{
		Id:           seeder.SubSupremeShareholderID,
		Domain:       seeder.HallId,
		ParentId:     seeder.SupremeShareholderID,
		AllParentsId: seeder.SubSupremeShareholder.GetAllParents(),
		Role:         seeder.SubSupremeShareholder.GetRole(),
		IsSub:        seeder.SubSupremeShareholder.GetSub(),
	}

	userPerm := &permissionclient.UserPermission{
		RoleId:       0,
		UserId:       seeder.HallId,
		PermissionId: seeder.UserPermID400,
		Enable:       true,
		Modify:       true,
	}

	mockResult := &permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			userPerm,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, _m.Anything).Return(mockResult, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	allPermConfigMap := map[uint32]types.UserPermissionConfig{
		seeder.UserPermID400: {
			PermissionId: seeder.UserPermID400,
			RolePerm: []types.RolePerm{
				{
					RoleId: constants.Role7,
					View:   true,
					Modify: true,
				},
			},
		},
		seeder.UserPermID2038: {
			PermissionId: seeder.UserPermID2038,
		},
		seeder.UserPermID1471: {
			PermissionId: seeder.UserPermID1471,
		},
	}

	resp, err := permissionCtx.FilterUserPermission(ctx, allPermConfigMap, operator, []uint32{seeder.UserPermID400})

	assert.Equal(t, Result{
		Enable: map[uint32]map[uint32]bool{
			seeder.UserPermID400: {0: true},
		},
		Modify: map[uint32]map[uint32]bool{
			seeder.UserPermID400: {0: true},
		},
	}, resp)
	assert.NoError(t, err)
}

func Test_FilterUserPermission_GetUserAllPermissionError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	operator := types.UserInfo{
		Id:           seeder.SubSupremeShareholderID,
		Domain:       seeder.HallId,
		ParentId:     seeder.SupremeShareholderID,
		AllParentsId: seeder.SubSupremeShareholder.GetAllParents(),
		Role:         seeder.SubSupremeShareholder.GetRole(),
		IsSub:        seeder.SubSupremeShareholder.GetSub(),
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	permissionRPC.On("GetUserAllPermission", ctx, _m.Anything).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	allPermConfigMap := map[uint32]types.UserPermissionConfig{
		seeder.UserPermID400: {
			PermissionId: seeder.UserPermID400,
			RolePerm: []types.RolePerm{
				{
					RoleId: constants.Role7,
					View:   true,
					Modify: true,
				},
			},
		},
		seeder.UserPermID2038: {
			PermissionId: seeder.UserPermID2038,
		},
		seeder.UserPermID1471: {
			PermissionId: seeder.UserPermID1471,
		},
	}

	resp, err := permissionCtx.FilterUserPermission(ctx, allPermConfigMap, operator, nil)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Equal(t, Result{
		Enable: nil,
		Modify: nil,
	}, resp)
}

func Test_CheckUserPermission_PermissionActionError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionCtx := New(newMockPermissionRPC(), redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, types.UserInfo{}, seeder.UserPermID400, "test")

	assert.False(t, resp)
	assert.ErrorIs(t, errorx.BackstageInvalidPermissionAction, err)
}

func Test_CheckUserPermission_GetAllPermConfigMapError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, 100, "enable")

	assert.False(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_CheckUserPermission_PermissionConfigCached(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	cacheKey := fmt.Sprintf(CacheUserPermActionKey, seeder.UserInfo.Id, 100, "enable")
	memoryCache.Set(cacheKey, false)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, 100, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckUserPermission_PermissionConfigIsNil(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, 100, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckUserPermission_PermissionConfigIsNotEnable(t *testing.T) {
	temp := seeder.UserPermission[seeder.UserPermID400]
	temp.Enable = false
	seeder.UserPermission[seeder.UserPermID400] = temp
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)

	temp.Enable = true
	seeder.UserPermission[seeder.UserPermID400] = temp
}

func Test_CheckUserPermission_CheckUserAllPermission_GetUserAllPermissionError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.UserInfo.AllParentsId,
		UserId:     seeder.UserInfo.Id,
		Role:       seeder.UserInfo.Role,
		Sub:        seeder.UserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_CheckUserPermission_MaxUser_NoUpperMenu_Enable(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.MaxUserInfo.AllParentsId,
		UserId:     seeder.MaxUserInfo.Id,
		Role:       seeder.MaxUserInfo.Role,
		Sub:        seeder.MaxUserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.MaxUserInfo.Id,
				PermissionId: seeder.UserPermID400,
				Enable:       false,
				Modify:       false,
			},
			{
				RoleId:       0,
				UserId:       seeder.MaxUserInfo.Id,
				PermissionId: 402,
				Enable:       false,
				Modify:       false,
			},
		},
	}, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.MaxUserInfo, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckUserPermission_MaxUser_NoUpperMenu_Modify(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.MaxUserInfo.AllParentsId,
		UserId:     seeder.MaxUserInfo.Id,
		Role:       seeder.MaxUserInfo.Role,
		Sub:        seeder.MaxUserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.MaxUserInfo.Id,
				PermissionId: seeder.UserPermID400,
				Enable:       false,
				Modify:       true,
			},
		},
	}, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.MaxUserInfo, seeder.UserPermID400, "modify")

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_CheckUserPermission_User_NoUpperMenu_UpperIsTrue(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.UserInfo.AllParentsId,
		UserId:     seeder.UserInfo.Id,
		Role:       seeder.UserInfo.Role,
		Sub:        seeder.UserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.UserInfo.AllParentsId[0],
				PermissionId: seeder.UserPermID400,
				Enable:       true,
				Modify:       true,
			},
			{
				RoleId:       constants.Role5,
				UserId:       seeder.UserInfo.Id,
				PermissionId: seeder.UserPermID400,
				Enable:       true,
				Modify:       true,
			},
		},
	}, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, seeder.UserPermID400, "enable")

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_CheckUserPermission_User_NoUpperMenu_UpperIsFalse(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.UserInfo.AllParentsId,
		UserId:     seeder.UserInfo.Id,
		Role:       seeder.UserInfo.Role,
		Sub:        seeder.UserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.UserInfo.AllParentsId[0],
				PermissionId: seeder.UserPermID400,
				Enable:       false,
				Modify:       false,
			},
			{
				RoleId:       constants.Role5,
				UserId:       seeder.UserInfo.Id,
				PermissionId: seeder.UserPermID400,
				Enable:       true,
				Modify:       true,
			},
		},
	}, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckUserPermission_User_NoUpperMenu_StrictIsFalse(t *testing.T) {
	temp := seeder.UserPermission[seeder.UserPermID400]
	temp.Strict = false
	seeder.UserPermission[seeder.UserPermID400] = temp
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.UserInfo.AllParentsId,
		UserId:     seeder.UserInfo.Id,
		Role:       seeder.UserInfo.Role,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.UserInfo.AllParentsId[0],
				PermissionId: seeder.UserPermID400,
				Enable:       false,
				Modify:       false,
			},
			{
				RoleId:       constants.Role5,
				UserId:       seeder.UserInfo.Id,
				PermissionId: seeder.UserPermID400,
				Enable:       true,
				Modify:       false,
			},
		},
	}, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.UserInfo, seeder.UserPermID400, "enable")

	assert.True(t, resp)
	assert.NoError(t, err)

	temp.Strict = true
	seeder.UserPermission[seeder.UserPermID400] = temp
}

func Test_CheckUserPermission_SubUser_NoUpperMenu_StrictIsFalse(t *testing.T) {
	temp := seeder.UserPermission[seeder.UserPermID400]
	temp.Strict = false
	seeder.UserPermission[seeder.UserPermID400] = temp
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.SubUserInfo.AllParentsId,
		UserId:     seeder.SubUserInfo.Id,
		Role:       seeder.SubUserInfo.Role,
		Sub:        seeder.SubUserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}

	permissionRPC.On("GetUserAllPermission", ctx, mockRequest).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.SubUserInfo.AllParentsId[0],
				PermissionId: seeder.UserPermID400,
				Enable:       false,
				Modify:       false,
			},
			{
				RoleId:       constants.Role5,
				UserId:       seeder.SubUserInfo.AllParentsId[1],
				PermissionId: seeder.UserPermID400,
				Enable:       false,
				Modify:       false,
			},
			{
				RoleId:       constants.Role15,
				UserId:       seeder.SubUserInfo.Id,
				PermissionId: seeder.UserPermID400,
				Enable:       true,
				Modify:       false,
			},
		},
	}, nil)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.SubUserInfo, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)

	temp.Strict = true
	seeder.UserPermission[seeder.UserPermID400] = temp
}

func Test_CheckUserPermission_MaxUser_UpperMenu_Enable(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()

	permissionRPC.On("GetUserAllPermission", ctx, &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.MaxUserInfo.AllParentsId,
		UserId:     seeder.MaxUserInfo.Id,
		Role:       seeder.MaxUserInfo.Role,
		Sub:        seeder.MaxUserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID1471,
		},
	}).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.MaxUserInfo.Id,
				PermissionId: seeder.UserPermID1471,
				Enable:       true,
				Modify:       true,
			},
		},
	}, nil).Once()

	permissionRPC.On("GetUserAllPermission", ctx, &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.MaxUserInfo.AllParentsId,
		UserId:     seeder.MaxUserInfo.Id,
		Role:       seeder.MaxUserInfo.Role,
		Sub:        seeder.MaxUserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.MaxUserInfo.Id,
				PermissionId: seeder.UserPermID400,
				Enable:       false,
				Modify:       false,
			},
		},
	}, nil).Once()

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.MaxUserInfo, seeder.UserPermID1471, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckUserPermission_MaxUser_UpperMenu_GetUserAllPermissionError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)
	permissionRPC := newMockPermissionRPC()

	permissionRPC.On("GetUserAllPermission", ctx, &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.MaxUserInfo.AllParentsId,
		UserId:     seeder.MaxUserInfo.Id,
		Role:       seeder.MaxUserInfo.Role,
		Sub:        seeder.MaxUserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID1471,
		},
	}).Return(&permissionclient.UserAllPermissionResponse{
		Data: []*permissionclient.UserPermission{
			{
				RoleId:       0,
				UserId:       seeder.MaxUserInfo.Id,
				PermissionId: seeder.UserPermID1471,
				Enable:       true,
				Modify:       true,
			},
		},
	}, nil).Once()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetUserAllPermission", ctx, &permissionclient.UserAllPermissionRequest{
		AllParents: seeder.MaxUserInfo.AllParentsId,
		UserId:     seeder.MaxUserInfo.Id,
		Role:       seeder.MaxUserInfo.Role,
		Sub:        seeder.MaxUserInfo.IsSub,
		PermissionId: []uint32{
			seeder.UserPermID400,
		},
	}).Return(nil, mockError).Once()

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.CheckUserPermission(ctx, seeder.MaxUserInfo, seeder.UserPermID1471, "enable")

	assert.False(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_GetPermissionConfigById_GetAllPermConfigMapError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionEnabledRequest{
		PermissionId: []uint32{},
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("GetUserPermissionEnabled", ctx, mockRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp := permissionCtx.GetPermissionConfigById(ctx, 100)

	assert.Nil(t, resp)
}

func Test_GetPermissionConfigById_PermissionConfigIsNil(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp := permissionCtx.GetPermissionConfigById(ctx, 100)

	assert.Nil(t, resp)
}

func Test_GetPermissionConfigById(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	memoryCache.Set(CacheUserPermConfigKey, seeder.UserPermission)

	permissionRPC := newMockPermissionRPC()
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp := permissionCtx.GetPermissionConfigById(ctx, seeder.UserPermID400)

	assert.Equal(t, seeder.UserPermission[seeder.UserPermID400], *resp)
}

func Test_PermissionContext_UserPermList(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermListRequest{
		UserId: []uint32{seeder.HallId},
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}

	permissionRPC.On("UserPermList", ctx, mockRequest).Return(&seeder.UserPermList, nil)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	enable := true
	resp, err := permissionCtx.UserPermList(ctx, UserPermListRequest{
		UserId: []uint32{seeder.HallId},
		Enable: &enable,
	})

	assert.NoError(t, err)
	assert.Equal(t, &seeder.UserPermList, resp)
}

func Test_PermissionContext_UserPermList_GRPCError(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermListRequest{
		UserId: []uint32{seeder.HallId},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("UserPermList", ctx, mockRequest).Return(nil, mockError)
	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.UserPermList(ctx, UserPermListRequest{
		UserId: []uint32{seeder.HallId},
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_PermissionContext_GetPermissionParents(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionConfigListRequest{
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}
	permissionRPC.On("UserPermissionConfigList", ctx, mockRequest).Return(&seeder.UserPermissionConfigList, nil)

	expectedRespMap := TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetPermissionParents(ctx, seeder.UserPermID1402)

	expectedResp := []*types.UserPermissionConfig{}
	currentID := seeder.UserPermID1402

	for currentID != 0 {
		perm := expectedRespMap[currentID]
		expectedResp = append(expectedResp, &perm)
		currentID = perm.ParentId
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
}

func Test_GetPermissionParents_GetPermissionParentsErr(t *testing.T) {
	memoryCache, cacheErr := collection.NewCache(seeder.MemoryCacheExpiry)
	assert.NoError(t, cacheErr)

	permissionRPC := newMockPermissionRPC()

	mockRequest := &permissionclient.UserPermissionConfigListRequest{
		Enable: &permissionclient.BoolValue{
			Value: true,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	permissionRPC.On("UserPermissionConfigList", ctx, mockRequest).Return(nil, mockError)

	permissionCtx := New(permissionRPC, redisCache, memoryCache)

	resp, err := permissionCtx.GetPermissionParents(ctx, seeder.UserPermID1402)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
