package fishgame

import (
	"context"
	"gbh/errorx"
	"gbh/fishgame/fishgameclient"
)

type Context interface {
	GetMultiSubWagersURL(ctx context.Context, lang string, wagersId []uint64) (*fishgameclient.GetMultiSubWagersURLResponse, error)
}

type fishGameContext struct {
	FishGameRPC fishgameclient.FishGame
}

func New(fishGameRPC fishgameclient.FishGame) Context {
	return &fishGameContext{
		FishGameRPC: fishGameRPC,
	}
}

func (c *fishGameContext) GetMultiSubWagersURL(ctx context.Context, lang string, wagersId []uint64) (*fishgameclient.GetMultiSubWagersURLResponse, error) {
	request := fishgameclient.GetMultiSubWagersURLRequest{
		Lang:     lang,
		WagersId: wagersId,
	}

	resp, err := c.FishGameRPC.GetMultiSubWagersURL(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
