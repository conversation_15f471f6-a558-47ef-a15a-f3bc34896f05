package fishgame

import (
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/fishgame/fishgameclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	fishGameRPC := newMockFishGameRPC()
	fishGameCtx := New(fishGameRPC)

	expectedResponse := &fishGameContext{
		FishGameRPC: fishGameRPC,
	}

	assert.Equal(t, expectedResponse, fishGameCtx)
}

func Test_FishGameContext_Get(t *testing.T) {
	fishGameRPC := newMockFishGameRPC()

	mockRequest := &fishgameclient.GetMultiSubWagersURLRequest{
		Lang:     "zh-tw",
		WagersId: []uint64{seeder.WagersID},
	}

	fishGameRPC.On("GetMultiSubWagersURL", ctx, mockRequest).Return(&seeder.MultiSubWagersURL, nil)

	fishGameCtx := New(fishGameRPC)

	resp, err := fishGameCtx.GetMultiSubWagersURL(ctx, mockRequest.GetLang(), mockRequest.GetWagersId())

	assert.NoError(t, err)
	assert.Equal(t, &seeder.MultiSubWagersURL, resp)
}

func Test_FishGameContext_GRPCError(t *testing.T) {
	fishGameRPC := newMockFishGameRPC()

	mockRequest := &fishgameclient.GetMultiSubWagersURLRequest{
		Lang:     "zh-tw",
		WagersId: []uint64{seeder.WagersID},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	fishGameRPC.On("GetMultiSubWagersURL", ctx, mockRequest).Return(nil, mockError)

	fishGameCtx := New(fishGameRPC)

	resp, err := fishGameCtx.GetMultiSubWagersURL(ctx, mockRequest.GetLang(), mockRequest.GetWagersId())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
