package live

import (
	"testing"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/stretchr/testify/assert"
)

func TestRounds_RoundInfoError(t *testing.T) {
	wagersType := uint32(0)
	request := &types.LiveRoundsRequest{
		StartDate:  seeder.StartDate,
		EndDate:    seeder.EndDate,
		GameID:     constants.GameID3001,
		RoundNo:    "51-23",
		WagersType: &wagersType,
		GameCode:   93,
		Page:       1,
		PageLimit:  1,
		Sort:       "asc",
	}

	liveGameCtx := mock.NewMockLiveGameCtx()

	roundInfoMockRequest := livegame.RoundInfoRequest{
		StartDate:  request.StartDate,
		EndDate:    request.EndDate,
		GameID:     request.GameID,
		RoundNo:    request.RoundNo,
		WagersType: request.WagersType,
		GameCode:   request.GameCode,
		Page:       request.Page,
		PageLimit:  request.PageLimit,
		Sort:       request.Sort,
	}
	liveGameCtx.On("RoundInfo", ctx, roundInfoMockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewRoundsLogic(ctx, svcCtx)
	resp, err := l.Rounds(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestRounds(t *testing.T) {
	wagersType := uint32(0)
	request := &types.LiveRoundsRequest{
		StartDate:  seeder.StartDate,
		EndDate:    seeder.EndDate,
		GameID:     constants.GameID3001,
		RoundNo:    "51-23",
		WagersType: &wagersType,
		GameCode:   93,
		Page:       1,
		PageLimit:  1,
		Sort:       "asc",
	}

	liveGameCtx := mock.NewMockLiveGameCtx()

	roundInfoMockRequest := livegame.RoundInfoRequest{
		StartDate:  request.StartDate,
		EndDate:    request.EndDate,
		GameID:     request.GameID,
		RoundNo:    request.RoundNo,
		WagersType: request.WagersType,
		GameCode:   request.GameCode,
		Page:       request.Page,
		PageLimit:  request.PageLimit,
		Sort:       request.Sort,
	}
	liveGameCtx.On("RoundInfo", ctx, roundInfoMockRequest).Return(&seeder.RoundInfo, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewRoundsLogic(ctx, svcCtx)
	resp, err := l.Rounds(request)

	pagination := seeder.RoundInfo.GetPagination()
	roundInfo := seeder.RoundInfo.GetRoundInfo()[0]
	expectedResponse := &types.BaseResponse{
		Data: roundResponse{
			Rounds: []round{
				{
					Datetime:           roundInfo.GetDatetime(),
					RoundSerial:        roundInfo.GetRoundserial(),
					ForeignRoundSerial: roundInfo.GetForeignRoundserial(),
					RoundNO:            roundInfo.GetRoundNo(),
					GameID:             roundInfo.GetGameId(),
					GameCode:           roundInfo.GetGameCode(),
					RoundXML:           roundInfo.GetRoundXml(),
					BetAmount:          roundInfo.GetBetAmount(),
					Payoff:             roundInfo.GetPayoff(),
					ResultStatus:       roundInfo.GetResultStatus(),
					ParsedXML:          analyzeRoundXML(roundInfo.GetGameId(), roundInfo.GetRoundXml()),
				},
			},
			Pagination: types.PaginateResponse{
				Page:        pagination.GetCurrentPage(),
				PageLimit:   pagination.GetPageLimit(),
				TotalNumber: pagination.GetTotal(),
				TotalPage:   pagination.GetTotalPage(),
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestRounds_RoundCanceled(t *testing.T) {
	originResultStatus := seeder.RoundInfo.GetRoundInfo()[0].GetResultStatus()
	seeder.RoundInfo.RoundInfo[0].ResultStatus = -1

	wagersType := uint32(0)
	request := &types.LiveRoundsRequest{
		StartDate:  seeder.StartDate,
		EndDate:    seeder.EndDate,
		GameID:     constants.GameID3001,
		RoundNo:    "51-23",
		WagersType: &wagersType,
		GameCode:   93,
		Page:       1,
		PageLimit:  1,
		Sort:       "asc",
	}

	liveGameCtx := mock.NewMockLiveGameCtx()

	roundInfoMockRequest := livegame.RoundInfoRequest{
		StartDate:  request.StartDate,
		EndDate:    request.EndDate,
		GameID:     request.GameID,
		RoundNo:    request.RoundNo,
		WagersType: request.WagersType,
		GameCode:   request.GameCode,
		Page:       request.Page,
		PageLimit:  request.PageLimit,
		Sort:       request.Sort,
	}
	liveGameCtx.On("RoundInfo", ctx, roundInfoMockRequest).Return(&seeder.RoundInfo, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewRoundsLogic(ctx, svcCtx)
	resp, err := l.Rounds(request)

	pagination := seeder.RoundInfo.GetPagination()
	roundInfo := seeder.RoundInfo.GetRoundInfo()[0]
	expectedResponse := &types.BaseResponse{
		Data: roundResponse{
			Rounds: []round{
				{
					Datetime:           roundInfo.GetDatetime(),
					RoundSerial:        roundInfo.GetRoundserial(),
					ForeignRoundSerial: roundInfo.GetForeignRoundserial(),
					RoundNO:            roundInfo.GetRoundNo(),
					GameID:             roundInfo.GetGameId(),
					GameCode:           roundInfo.GetGameCode(),
					RoundXML:           roundInfo.GetRoundXml(),
					BetAmount:          roundInfo.GetBetAmount(),
					Payoff:             roundInfo.GetPayoff(),
					ResultStatus:       roundInfo.GetResultStatus(),
					ParsedXML:          "",
				},
			},
			Pagination: types.PaginateResponse{
				Page:        pagination.GetCurrentPage(),
				PageLimit:   pagination.GetPageLimit(),
				TotalNumber: pagination.GetTotal(),
				TotalPage:   pagination.GetTotalPage(),
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)

	seeder.RoundInfo.RoundInfo[0].ResultStatus = originResultStatus
}

func TestRounds_AnalyzeRoundXML_RoundXMLIsEmpty(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3001, "")

	expectedResponse := ""

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3001(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3001, "bankerCast=C.7,H.11,&playerCast=S.7,H.8,D.10&bankerPoint=7&playerPoint=5")

	expectedResponse := map[string]interface{}{
		"banker_card": []string{
			"C.7",
			"H.11",
		},
		"player_card": []string{
			"S.7",
			"H.8",
			"D.10",
		},
		"banker_point": "7",
		"player_point": "5",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3025(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3025, "bankerCast=D.10,H.13,H.6&playerCast=D.6,C.1,&bankerPoint=6&playerPoint=7&DeckID=********&CardSeq=256")

	expectedResponse := map[string]interface{}{
		"banker_card": []string{
			"D.10",
			"H.13",
			"H.6",
		},
		"player_card": []string{
			"D.6",
			"C.1",
		},
		"banker_point": "6",
		"player_point": "7",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3027(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3027, "bankerCast=D.9,S.9,&playerCast=H.1,H.6,&bankerPoint=8&playerPoint=7&DeckID=********&CardSeq=81")

	expectedResponse := map[string]interface{}{
		"banker_card": []string{
			"D.9",
			"S.9",
		},
		"player_card": []string{
			"H.1",
			"H.6",
		},
		"banker_point": "8",
		"player_point": "7",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3007(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3007, "35")

	expectedResponse := []string{
		"35",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3008(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3008, "2,4,5")

	expectedResponse := []string{
		"2",
		"4",
		"5",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3303(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3303, "L,5")

	expectedResponse := []string{
		"L",
		"5",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3003(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3003, "dragonCast=D.3&tigerCast=D.5&dragonPoint=3&tigerPoint=5")

	expectedResponse := map[string]string{
		"dragon_card":  "D.3",
		"tiger_card":   "D.5",
		"dragon_point": "3",
		"tiger_point":  "5",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3026(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3026, "dragonCast=S.7&tigerCast=S.9&dragonPoint=7&tigerPoint=9&DeckID=52071325&CardSeq=64")

	expectedResponse := map[string]string{
		"dragon_card":  "S.7",
		"tiger_card":   "S.9",
		"dragon_point": "7",
		"tiger_point":  "9",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3028(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3028, "Num=5&DeckID=52076890&CardSeq=1")

	expectedResponse := []string{
		"5",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3029_HighCard(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3029, "dragonCast=H.10,S.6,D.9&phoenixCast=H.9,D.3,D.11&dragonResult=2&phoenixResult=2&notOpCast=205-1&DeckID=52077054&CardSeq=6")

	expectedResponse := map[string]interface{}{
		"dragon_type":   "2",
		"phoenix_type":  "2",
		"dragon_point":  "10",
		"phoenix_point": "J",
		"dragon_cast": []string{
			"H.10",
			"S.6",
			"D.9",
		},
		"phoenix_cast": []string{
			"H.9",
			"D.3",
			"D.11",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3029_TripleKillerAndPair(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3029, "dragonCast=C.3,D.2,D.5&phoenixCast=S.8,D.3,D.8&dragonResult=1&phoenixResult=3&notOpCast=100-100&DeckID=52348992&CardSeq=6")

	expectedResponse := map[string]interface{}{
		"dragon_type":   "1",
		"phoenix_type":  "3",
		"dragon_point":  "",
		"phoenix_point": "8",
		"dragon_cast": []string{
			"C.3",
			"D.2",
			"D.5",
		},
		"phoenix_cast": []string{
			"S.8",
			"D.3",
			"D.8",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3029_StraightAndFlush(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3029, "dragonCast=D.2,D.5,D.3&phoenixCast=H.6,D.7,C.8&dragonResult=5&phoenixResult=4&notOpCast=177-308&DeckID=52396869&CardSeq=6")

	expectedResponse := map[string]interface{}{
		"dragon_type":   "5",
		"phoenix_type":  "4",
		"dragon_point":  "",
		"phoenix_point": "",
		"dragon_cast": []string{
			"D.2",
			"D.5",
			"D.3",
		},
		"phoenix_cast": []string{
			"H.6",
			"D.7",
			"C.8",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3029_StraightFlush(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3029, "dragonCast=D.8,D.7,D.6&phoenixCast=C.13,H.8,C.9&dragonResult=6&phoenixResult=2&notOpCast=0-131&DeckID=53125626&CardSeq=6")

	expectedResponse := map[string]interface{}{
		"dragon_type":   "6",
		"phoenix_type":  "2",
		"dragon_point":  "",
		"phoenix_point": "K",
		"dragon_cast": []string{
			"D.8",
			"D.7",
			"D.6",
		},
		"phoenix_cast": []string{
			"C.13",
			"H.8",
			"C.9",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3029_Triple(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3029, "dragonCast=S.5,C.4,S.3&phoenixCast=D.6,H.6,S.6&dragonResult=4&phoenixResult=7&notOpCast=86-0&DeckID=53350768&CardSeq=6")

	expectedResponse := map[string]interface{}{
		"dragon_type":   "4",
		"phoenix_type":  "7",
		"dragon_point":  "",
		"phoenix_point": "",
		"dragon_cast": []string{
			"S.5",
			"C.4",
			"S.3",
		},
		"phoenix_cast": []string{
			"D.6",
			"H.6",
			"S.6",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3029_PokerCountWrong(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3029, "dragonCast=&phoenixCast=")

	expectedResponse := map[string]interface{}{
		"dragon_type":   "",
		"phoenix_type":  "",
		"dragon_point":  "",
		"phoenix_point": "",
		"dragon_cast":   []string{""},
		"phoenix_cast":  []string{""},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3030(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3030, "firstPoker=C.2&bankerCast=D.7,H.7,H.1&player1Cast=C.6,H.13,H.10&player2Cast=S.7,D.10,D.5&player3Cast=D.4,D.6,H.9&bankerLevel=16&player1Level=12&player2Level=25&player3Level=4&DeckID=********&CardSeq=13&roadmap=111,222,224,112,112,212,111,222,222,111,141,11")

	expectedResponse := map[string][]string{
		"banker_cast": {
			"D.7",
			"H.7",
			"H.1",
		},
		"banker_level": {
			"16",
		},
		"player1_cast": {
			"C.6",
			"H.13",
			"H.10",
		},
		"player1_level": {
			"12",
		},
		"player2_cast": {
			"S.7",
			"D.10",
			"D.5",
		},
		"player2_level": {
			"25",
		},
		"player3_cast": {
			"D.4",
			"D.6",
			"H.9",
		},
		"player3_level": {
			"4",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3031(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3031, "firstPoker=C.11&bankerCast=C.10,C.2,C.1,D.9,H.10&player1Cast=S.13,D.3,S.12,H.7,H.2&player2Cast=C.6,D.12,H.3,S.8,C.8&player3Cast=H.8,S.10,C.9,S.6,D.5&bankerLevel=10&player1Level=10&player2Level=12&player3Level=4&DeckID=********&CardSeq=21&roadmap=99860,25413,30a46,a9920,00024,00024,00024,5a001,53802,75260,3aa03,00417,09077,89511,07917,00456,30064,93770,7a051,0a007")

	expectedResponse := map[string][]string{
		"banker_cast": {
			"C.10",
			"C.2",
			"C.1",
			"D.9",
			"H.10",
		},
		"banker_level": {
			"10",
		},
		"player1_cast": {
			"S.13",
			"D.3",
			"S.12",
			"H.7",
			"H.2",
		},
		"player1_level": {
			"10",
		},
		"player2_cast": {
			"C.6",
			"D.12",
			"H.3",
			"S.8",
			"C.8",
		},
		"player2_level": {
			"12",
		},
		"player3_cast": {
			"H.8",
			"S.10",
			"C.9",
			"S.6",
			"D.5",
		},
		"player3_level": {
			"4",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3041(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3041, "firstPoker=C.3&bankerCast=D.10,D.8,D.3,D.11,H.6&player1Cast=C.10,H.7,S.13,S.11,C.9&player2Cast=D.2,D.6,S.5,H.12,H.1&player3Cast=S.7,S.1,S.6,D.4,C.1&bankerLevel=0&player1Level=6&player2Level=0&player3Level=0&roadmap=04021,15121,80088,60695,67404,31086,00111,33881,60028,40907,05371,a0508,67004,01401,676a1,62008,a3038,08921,30585,84078,07092,57404,30a17,09551,51086,a70a6&DeckID=********&CardSeq=21")

	expectedResponse := map[string][]string{
		"banker_cast": {
			"D.10",
			"D.8",
			"D.3",
			"D.11",
			"H.6",
		},
		"banker_level": {
			"0",
		},
		"player1_cast": {
			"C.10",
			"H.7",
			"S.13",
			"S.11",
			"C.9",
		},
		"player1_level": {
			"6",
		},
		"player2_cast": {
			"D.2",
			"D.6",
			"S.5",
			"H.12",
			"H.1",
		},
		"player2_level": {
			"0",
		},
		"player3_cast": {
			"S.7",
			"S.1",
			"S.6",
			"D.4",
			"C.1",
		},
		"player3_level": {
			"0",
		},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3032(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3032, "poker=1121&DeckID=********&CardSeq=1&droadmap=*********-4,*********-3,*********-2,*********-3,*********-3,*********-4,*********-2")

	expectedResponse := []string{
		"1",
		"1",
		"2",
		"1",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3033(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3033, "cast=6,4,4&DeckID=********&CardSeq=1&sicSum=0,0,0,2,0,1&coldHot=0,0,0,0,0,0,0,0,0,0,1,0,0,0&notTk=1")

	expectedResponse := []string{
		"6",
		"4",
		"4",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3034(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3034, "cast=2,1,3&DeckID=52077712&CardSeq=1&sicSum=20,20,22,21,18,22&coldHot=0,3,2,1,3,8,3,4,6,4,3,2,2,0&notTk=2")

	expectedResponse := []string{
		"2",
		"1",
		"3",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3039(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3039, "cast=6,6,5&DeckID=52077719&CardSeq=1&sicSum=16,17,16,16,16,21&coldHot=0,1,1,3,3,4,3,6,5,3,0,1,2,2&notTk=39")

	expectedResponse := []string{
		"6",
		"6",
		"5",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3042(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3042, "cast=3,4,4&DeckID=52053683&CardSeq=1&sicSum=0,0,1,2,0,0&coldHot=0,0,0,0,0,0,0,1,0,0,0,0,0,0&notTk=1")

	expectedResponse := []string{
		"3",
		"4",
		"4",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3044(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3044, "cast=1,2,4")

	expectedResponse := []string{
		"1",
		"2",
		"4",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3045(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3045, "cast=5,2,2&DeckID=52077723&CardSeq=1&sicSum=6,7,17,18,10,14&coldHot=0,0,0,1,2,1,3,5,3,3,5,1,0,0&notTk=5")

	expectedResponse := []string{
		"5",
		"2",
		"2",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3036(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3036, "hiloCast=H.2&hiloPoint=2&previousCast=P.7&previousPoint=7&DeckID=********&CardSeq=4")

	expectedResponse := []string{
		"H.2",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3037(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3037, "poker=4&DeckID=********&CardSeq=1")

	expectedResponse := []int{
		1, 1, 1, 1,
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3038(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3038, "dicePoint=5,1&bankerCast=11,31&player1Cast=33,22&player2Cast=46,15&player3Cast=32,16&DeckID=********&CardSeq=9&roadmap=51-11-31-33-22-46-15-32-16&level=14,14,14,14&point=6,0,6,2")

	expectedResponse := map[string][]string{
		"banker_cast":  {"11", "31"},
		"player1_cast": {"33", "22"},
		"player2_cast": {"46", "15"},
		"player3_cast": {"32", "16"},
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3043(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3043, "bankerCast=S.5,H.6,S.7&playerCast=D.8,D.11,&bankerPoint=18&playerPoint=18&DeckID=********&CardSeq=70&NotOpBJ=12-12&CardCount=5,8,5,5,7,10,7,5,2,3,6,2,1")

	expectedResponse := map[string]interface{}{
		"banker_cast": []string{
			"S.5",
			"H.6",
			"S.7",
		},
		"player_cast": []string{
			"D.8",
			"D.11",
		},
		"banker_point": "18",
		"player_point": "18",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3043_BankerIsBJ(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3043, "bankerCast=C.1,D.12,&playerCast=H.12,C.8,&bankerPoint=21&playerPoint=18&DeckID=********&CardSeq=207&NotOpBJ=0-8&CardCount=5,0,1,3,3,5,4,4,4,3,0,6,1")

	expectedResponse := map[string]interface{}{
		"banker_cast": []string{
			"C.1",
			"D.12",
		},
		"player_cast": []string{
			"H.12",
			"C.8",
		},
		"banker_point": "BJ",
		"player_point": "18",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3043_PlayerIsBJ(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3043, "bankerCast=H.5,S.2,&playerCast=S.13,D.1,&bankerPoint=7&playerPoint=21&DeckID=********&CardSeq=236&NotOpBJ=6-0&CardCount=7,2,4,5,5,8,6,5,7,5,2,10,2")

	expectedResponse := map[string]interface{}{
		"banker_cast": []string{
			"H.5",
			"S.2",
		},
		"player_cast": []string{
			"S.13",
			"D.1",
		},
		"banker_point": "7",
		"player_point": "BJ",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3046(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3046, "spadeCast=C.9,C.6,D.4,D.9,H.3&heartCast=S.3,S.9,D.10,S.1,S.11&diamondCast=D.1,H.1,C.5,H.6,C.1&clubCast=S.8,H.7,S.5,D.2,S.10&bankerCast=C.13,C.8,D.13,H.8,D.12&spadePoint=31&heartPoint=33&diamondPoint=14&clubPoint=32&bankerPoint=46&DeckID=********&CardSeq=25")

	expectedResponse := map[string]interface{}{
		"banker_cast": []string{
			"C.13",
			"C.8",
			"D.13",
			"H.8",
			"D.12",
		},
		"spade_cast": []string{
			"C.9",
			"C.6",
			"D.4",
			"D.9",
			"H.3",
		},
		"heart_cast": []string{
			"S.3",
			"S.9",
			"D.10",
			"S.1",
			"S.11",
		},
		"diamond_cast": []string{
			"D.1",
			"H.1",
			"C.5",
			"H.6",
			"C.1",
		},
		"club_cast": []string{
			"S.8",
			"H.7",
			"S.5",
			"D.2",
			"S.10",
		},
		"banker_point":  "46",
		"spade_point":   "31",
		"heart_point":   "33",
		"diamond_point": "14",
		"club_point":    "32",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3048(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3048, "leftCast=H.11&rightCast=C.1&ballCast=H.13&DeckID=********&CardSeq=3")

	expectedResponse := map[string]interface{}{
		"left_cast":   "H.11",
		"ball_cast":   "H.13",
		"right_cast":  "C.1",
		"left_point":  "11",
		"ball_point":  "13",
		"right_point": "1",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}

func TestRounds_AnalyzeRoundXML_3050(t *testing.T) {
	analyzedResult := analyzeRoundXML(constants.GameID3050, "bankerCast=8,1&playerCast=9,4&bankerGrade=43&playerGrade=16&bankerPair=0&playerPair=0&bankerBar=0&playerBar=0&bankerPoint=9&playerPoint=3&DeckID=********&CardSeq=123")

	expectedResponse := map[string]interface{}{
		"banker_cast":  []string{"8", "1"},
		"player_cast":  []string{"9", "4"},
		"banker_bar":   false,
		"player_bar":   false,
		"banker_pair":  "0",
		"player_pair":  "0",
		"banker_point": "9",
		"player_point": "3",
	}

	assert.Equal(t, expectedResponse, analyzedResult)
}
