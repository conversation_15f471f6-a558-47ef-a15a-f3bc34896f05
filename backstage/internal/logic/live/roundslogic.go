package live

import (
	"context"
	"net/url"
	"slices"
	"sort"
	"strings"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/livegame/livegameclient"
	"gbh/utils/arrutil"
	"gbh/utils/strutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type roundResponse struct {
	Rounds     []round                `json:"rounds"`
	Pagination types.PaginateResponse `json:"pagination"`
}

type round struct {
	Datetime           string      `json:"datetime"`
	RoundSerial        uint32      `json:"round_serial"`
	ForeignRoundSerial string      `json:"foreign_round_serial"`
	RoundNO            string      `json:"round_no"`
	GameID             uint32      `json:"game_id"`
	GameCode           uint32      `json:"game_code"`
	RoundXML           string      `json:"round_xml"`
	BetAmount          float64     `json:"bet_amount"`
	Payoff             float64     `json:"payoff"`
	ResultStatus       int32       `json:"result_status"`
	ParsedXML          interface{} `json:"parsed_xml"`
}

type RoundsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRoundsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RoundsLogic {
	return &RoundsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RoundsLogic) Rounds(req *types.LiveRoundsRequest) (resp *types.BaseResponse, err error) {
	request := livegame.RoundInfoRequest{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		GameID:    req.GameID,
		RoundNo:   req.RoundNo,
		GameCode:  req.GameCode,
		Page:      req.Page,
		PageLimit: req.PageLimit,
		Sort:      req.Sort,
	}

	if req.WagersType != nil {
		request.WagersType = req.WagersType
	}

	roundInfoResponse, roundInfoResponseErr := l.svcCtx.LiveGameCtx.RoundInfo(l.ctx, request)
	if roundInfoResponseErr != nil {
		return nil, roundInfoResponseErr
	}

	pagination := roundInfoResponse.GetPagination()

	return &types.BaseResponse{
		Data: roundResponse{
			Rounds: handleRoundXML(roundInfoResponse.GetRoundInfo()),
			Pagination: types.PaginateResponse{
				Page:        pagination.GetCurrentPage(),
				PageLimit:   pagination.GetPageLimit(),
				TotalNumber: pagination.GetTotal(),
				TotalPage:   pagination.GetTotalPage(),
			},
		},
	}, nil
}

func handleRoundXML(roundInfo []*livegameclient.RoundInfo) []round {
	rounds := make([]round, 0, len(roundInfo))
	for _, info := range roundInfo {
		roundTmp := round{
			Datetime:           info.GetDatetime(),
			RoundSerial:        info.GetRoundserial(),
			ForeignRoundSerial: info.GetForeignRoundserial(),
			RoundNO:            info.GetRoundNo(),
			GameID:             info.GetGameId(),
			GameCode:           info.GetGameCode(),
			RoundXML:           info.GetRoundXml(),
			BetAmount:          info.GetBetAmount(),
			Payoff:             info.GetPayoff(),
			ResultStatus:       info.GetResultStatus(),
			ParsedXML:          "",
		}

		if info.GetResultStatus() > 0 {
			roundTmp.ParsedXML = analyzeRoundXML(info.GetGameId(), roundTmp.RoundXML)
		}

		rounds = append(rounds, roundTmp)
	}

	return rounds
}

func analyzeRoundXML(gameId uint32, roundXML string) interface{} {
	// TODO: 以下幾款遊戲沒有了，呼叫視訊api也都會回傳GameType不存在，故移除相關邏輯
	// GameID3017 uint32 = 3017 // 保險百家樂
	// GameID3016 uint32 = 3016 // 魚蝦蟹
	// GameID3011 uint32 = 3011 // 色碟
	// GameID3015 uint32 = 3015 // 番攤
	// GameID3018 uint32 = 3018 // 炸金花
	// GameID3005 uint32 = 3005 // 三公
	// GameID3012 uint32 = 3012 // 牛牛
	// GameID3023 uint32 = 3023 // 搶莊牛牛
	// GameID3024 uint32 = 3024 // 搶莊三公
	// GameID3021 uint32 = 3021 // BB HiLo
	// GameID3006 uint32 = 3006 // 溫州牌九

	var analyzedResult interface{}
	analyzedResult = ""

	// 不需解析XML
	if slices.Contains([]uint32{constants.GameID3007, constants.GameID3008, constants.GameID3303}, gameId) {
		switch gameId {
		case constants.GameID3007:
			// 輪盤
			analyzedResult = []string{roundXML}
		case constants.GameID3008, constants.GameID3303:
			// 骰寶、瘋狂賽車
			analyzedResult = strings.Split(roundXML, ",")
		}

		return analyzedResult
	}

	// 需解析XML
	var parsedXML url.Values
	if parsedXML = parseXML(roundXML); parsedXML == nil {
		return analyzedResult
	}

	switch gameId {
	case constants.GameID3001, constants.GameID3025, constants.GameID3027:
		// 百家樂、區塊鏈百家樂、區塊鏈保險百家樂
		analyzedResult = analyzeBaccarat(parsedXML)
	case constants.GameID3003, constants.GameID3026:
		// 龍虎鬥、區塊鏈龍虎鬥
		analyzedResult = analyzeDragonTiger(parsedXML)
	case constants.GameID3028:
		// 區塊鏈輪盤
		analyzedResult = analyzeRoulette(parsedXML)
	case constants.GameID3029:
		// 區塊鏈炸金花
		analyzedResult = analyzeThreeCardPoker(parsedXML)
	case constants.GameID3030, constants.GameID3031, constants.GameID3041:
		// 區塊鏈三公、區塊鏈牛牛、區塊鏈搶莊牛牛
		analyzedResult = analyzeBullBullAnd3Face(parsedXML)
	case constants.GameID3032:
		// 區塊鏈色碟
		analyzedResult = analyzeSeDie(parsedXML)
	case constants.GameID3033, constants.GameID3034, constants.GameID3039, constants.GameID3042, constants.GameID3044, constants.GameID3045:
		// 區塊鏈骰寶、區塊鏈魚蝦蟹、區塊鏈越南魚蝦蟹、區塊鏈越南骰寶、泰國魚蝦蟹、區塊鏈泰國魚蝦蟹
		analyzedResult = analyzeSicBoAndFPCDice(parsedXML)
	case constants.GameID3036:
		// 區塊鏈HiLo
		analyzedResult = analyzeHiLo(parsedXML)
	case constants.GameID3037:
		// 區塊鏈番攤
		analyzedResult = analyzeFanTan(parsedXML)
	case constants.GameID3038:
		// 區塊鏈溫州牌九
		analyzedResult = analyzeWenhouPaiGow(parsedXML)
	case constants.GameID3043, constants.GameID3047:
		// 區塊鏈21點百家樂、21點百家樂
		analyzedResult = analyzeBJBaccarat(parsedXML)
	case constants.GameID3046:
		// 區塊鏈幸運5張
		analyzedResult = analyzeLucky5Cards(parsedXML)
	case constants.GameID3048:
		// 區塊鏈射龍門
		analyzedResult = analyzeAceyDeucey(parsedXML)
	case constants.GameID3050:
		// 區塊鏈28槓
		analyzedResult = analyze28Baccarat(parsedXML)
	}

	return analyzedResult
}

func analyzeBaccarat(parsedXML url.Values) map[string]interface{} {
	return map[string]interface{}{
		"banker_card":  filterLastVal(strings.Split(parsedXML.Get("bankerCast"), ",")),
		"player_card":  filterLastVal(strings.Split(parsedXML.Get("playerCast"), ",")),
		"banker_point": parsedXML.Get("bankerPoint"),
		"player_point": parsedXML.Get("playerPoint"),
	}
}

func analyzeDragonTiger(parsedXML url.Values) map[string]string {
	return map[string]string{
		"dragon_card":  parsedXML.Get("dragonCast"),
		"tiger_card":   parsedXML.Get("tigerCast"),
		"dragon_point": parsedXML.Get("dragonPoint"),
		"tiger_point":  parsedXML.Get("tigerPoint"),
	}
}

func analyzeRoulette(parsedXML url.Values) []string {
	return []string{parsedXML.Get("Num")}
}

func analyzeThreeCardPoker(parsedXML url.Values) interface{} {
	dragonCard := strings.Split(parsedXML.Get("dragonCast"), ",")
	phoenixCard := strings.Split(parsedXML.Get("phoenixCast"), ",")

	// 整理+計算牌型
	processedDragonCard := processThreeCardPokerCard(dragonCard)
	processPhoenixCard := processThreeCardPokerCard(phoenixCard)

	return map[string]interface{}{
		"dragon_type":   processedDragonCard["type"],
		"phoenix_type":  processPhoenixCard["type"],
		"dragon_point":  processedDragonCard["point"],
		"phoenix_point": processPhoenixCard["point"],
		"dragon_cast":   dragonCard,
		"phoenix_cast":  phoenixCard,
	}
}

func analyzeBullBullAnd3Face(parsedXML url.Values) map[string][]string {
	return map[string][]string{
		"banker_cast":   strings.Split(parsedXML.Get("bankerCast"), ","),
		"banker_level":  strings.Split(parsedXML.Get("bankerLevel"), ","),
		"player1_cast":  strings.Split(parsedXML.Get("player1Cast"), ","),
		"player1_level": strings.Split(parsedXML.Get("player1Level"), ","),
		"player2_cast":  strings.Split(parsedXML.Get("player2Cast"), ","),
		"player2_level": strings.Split(parsedXML.Get("player2Level"), ","),
		"player3_cast":  strings.Split(parsedXML.Get("player3Cast"), ","),
		"player3_level": strings.Split(parsedXML.Get("player3Level"), ","),
	}
}

func analyzeSeDie(parsedXML url.Values) []string {
	return strings.Split(parsedXML.Get("poker"), "")
}

func analyzeSicBoAndFPCDice(parsedXML url.Values) []string {
	return strings.Split(parsedXML.Get("cast"), ",")
}

func analyzeHiLo(parsedXML url.Values) []string {
	return []string{parsedXML.Get("hiloCast")}
}

func analyzeFanTan(parsedXML url.Values) []int {
	count := strutil.StringToInt(parsedXML.Get("poker"))
	result := make([]int, 0, count)
	for i := 0; i < count; i++ {
		result = append(result, 1)
	}

	return result
}

func analyzeWenhouPaiGow(parsedXML url.Values) map[string][]string {
	return map[string][]string{
		"banker_cast":  strings.Split(parsedXML.Get("bankerCast"), ","),
		"player1_cast": strings.Split(parsedXML.Get("player1Cast"), ","),
		"player2_cast": strings.Split(parsedXML.Get("player2Cast"), ","),
		"player3_cast": strings.Split(parsedXML.Get("player3Cast"), ","),
	}
}

func analyzeBJBaccarat(parsedXML url.Values) map[string]interface{} {
	bankerCast := filterLastVal(strings.Split(parsedXML.Get("bankerCast"), ","))
	playerCast := filterLastVal(strings.Split(parsedXML.Get("playerCast"), ","))

	bankerPoint := parsedXML.Get("bankerPoint")
	if bankerPoint == "21" && len(bankerCast) == 2 {
		bankerPoint = "BJ"
	}

	playerPoint := parsedXML.Get("playerPoint")
	if playerPoint == "21" && len(playerCast) == 2 {
		playerPoint = "BJ"
	}

	return map[string]interface{}{
		"banker_cast":  bankerCast,
		"player_cast":  playerCast,
		"banker_point": bankerPoint,
		"player_point": playerPoint,
	}
}

func analyzeLucky5Cards(parsedXML url.Values) map[string]interface{} {
	return map[string]interface{}{
		"banker_cast":   strings.Split(parsedXML.Get("bankerCast"), ","),
		"spade_cast":    strings.Split(parsedXML.Get("spadeCast"), ","),
		"heart_cast":    strings.Split(parsedXML.Get("heartCast"), ","),
		"diamond_cast":  strings.Split(parsedXML.Get("diamondCast"), ","),
		"club_cast":     strings.Split(parsedXML.Get("clubCast"), ","),
		"banker_point":  parsedXML.Get("bankerPoint"),
		"spade_point":   parsedXML.Get("spadePoint"),
		"heart_point":   parsedXML.Get("heartPoint"),
		"diamond_point": parsedXML.Get("diamondPoint"),
		"club_point":    parsedXML.Get("clubPoint"),
	}
}

func analyzeAceyDeucey(parsedXML url.Values) map[string]interface{} {
	return map[string]interface{}{
		"left_cast":   parsedXML.Get("leftCast"),
		"ball_cast":   parsedXML.Get("ballCast"),
		"right_cast":  parsedXML.Get("rightCast"),
		"left_point":  strings.Split(parsedXML.Get("leftCast"), ".")[1],
		"ball_point":  strings.Split(parsedXML.Get("ballCast"), ".")[1],
		"right_point": strings.Split(parsedXML.Get("rightCast"), ".")[1],
	}
}

func analyze28Baccarat(parsedXML url.Values) map[string]interface{} {
	return map[string]interface{}{
		"banker_cast":  strings.Split(parsedXML.Get("bankerCast"), ","),
		"player_cast":  strings.Split(parsedXML.Get("playerCast"), ","),
		"banker_bar":   strutil.StringToBool(parsedXML.Get("bankerBar")),
		"player_bar":   strutil.StringToBool(parsedXML.Get("playerBar")),
		"banker_pair":  parsedXML.Get("bankerPair"),
		"player_pair":  parsedXML.Get("playerPair"),
		"banker_point": parsedXML.Get("bankerPoint"),
		"player_point": parsedXML.Get("playerPoint"),
	}
}

func parseXML(roundXML string) url.Values {
	parsedXML, err := url.ParseQuery(strings.TrimSpace(roundXML))
	if err != nil || len(parsedXML) < 1 {
		return nil
	}

	return parsedXML
}

// 牌型最後欄位若為空則移除
func filterLastVal(data []string) []string {
	if len(data) > 0 && data[len(data)-1] == "" {
		data = data[:len(data)-1]
	}

	return data
}

// 整理+判斷炸金花牌型
func processThreeCardPokerCard(card []string) map[string]string {
	point := []int{}            // 點數
	suit := map[string]string{} // 花色
	// 整理牌型
	for _, value := range card {
		cardParts := strings.Split(value, ".")

		tmpPoint := 0
		if len(cardParts) > 1 {
			tmpPoint = strutil.StringToInt(cardParts[1])
		}

		tmpSuit := ""
		if len(cardParts) > 0 {
			tmpSuit = cardParts[0]
		}

		point = append(point, tmpPoint)
		suit[tmpSuit] = tmpSuit
	}

	result := map[string]string{
		"type":  "",
		"point": "",
	}

	if len(point) != constants.PokerPointCount {
		return result
	}

	sort.Ints(point)

	// 單張牌大小順序
	pokerNumCard := []string{"2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "1"}
	// 順子大小順序
	straightCard := []string{"", "1-2-3", "2-3-4", "3-4-5", "4-5-6",
		"5-6-7", "6-7-8", "7-8-9", "8-9-10", "9-10-11",
		"10-11-12", "11-12-13", "1-12-13"}
	// 豹子牌型
	leopardCard := []string{"2-3-5"}
	// 撲克牌
	pokerCard := []string{"2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A"}

	stringPoint := arrutil.IntsToStrings(point)
	sortPoint := strings.Join(stringPoint, "-") // 順序
	suitCount := len(suit)

	// 判斷豹子殺手
	if slices.Contains(leopardCard, sortPoint) && suitCount > 1 {
		result["type"] = "1"
	}

	// 判斷散牌
	highCardPoint := []int{}
	for _, pointValue := range stringPoint {
		for cardKey, cardValue := range pokerNumCard {
			if pointValue == cardValue {
				highCardPoint = append(highCardPoint, cardKey)
				break
			}
		}
	}

	if result["type"] == "" {
		result["type"] = "2"
		result["point"] = pokerCard[slices.Max(highCardPoint)]
	}

	// 判斷對子
	if point[0] == point[1] || point[1] == point[2] {
		result["type"] = "3"
		result["point"] = pokerCard[highCardPoint[1]]
	}

	// 判斷順子
	isStraight := false
	for _, cardValue := range straightCard {
		if sortPoint == cardValue {
			isStraight = true
			break
		}
	}
	if isStraight {
		result["type"] = "4"
		result["point"] = ""
	}

	// 判斷同花
	if suitCount == 1 {
		result["type"] = "5"
		result["point"] = ""
	}

	// 判斷同花順
	if suitCount == 1 && isStraight {
		result["type"] = "6"
		result["point"] = ""
	}

	// 判斷豹子
	if point[0] == point[1] && point[1] == point[2] {
		result["type"] = "7"
		result["point"] = ""
	}

	return result
}
