package live

import (
	"testing"

	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/stretchr/testify/assert"
)

func TestRoundsParsedXML_RoundXMLInfoError(t *testing.T) {
	request := &types.LiveRoundsParsedXMLRequest{
		RoundSerials: []uint32{seeder.RoundInfoRoundSerial},
		StartDate:    seeder.StartDate,
		EndDate:      seeder.EndDate,
	}

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundXMLInfo", ctx, request.RoundSerials, request.StartDate, request.EndDate).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewRoundsParsedXMLLogic(ctx, svcCtx)
	resp, err := l.RoundsParsedXML(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestRoundsParsedXML(t *testing.T) {
	request := &types.LiveRoundsParsedXMLRequest{
		RoundSerials: []uint32{seeder.RoundInfoRoundSerial},
		StartDate:    seeder.StartDate,
		EndDate:      seeder.EndDate,
	}

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundXMLInfo", ctx, request.RoundSerials, request.StartDate, request.EndDate).Return(seeder.RoundXMLInfo.GetRoundXmlInfo(), nil)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewRoundsParsedXMLLogic(ctx, svcCtx)
	resp, err := l.RoundsParsedXML(request)

	roundXMLInfo := seeder.RoundXMLInfo.GetRoundXmlInfo()[0]
	expectedResponse := &types.BaseResponse{
		Data: map[uint32]interface{}{
			roundXMLInfo.GetRoundSerial(): analyzeRoundXML(roundXMLInfo.GetGameId(), roundXMLInfo.GetRoundXml()),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
