package live

import (
	"context"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTipSwitchByHallIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTipSwitchByHallIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTipSwitchByHallIdLogic {
	return &GetTipSwitchByHallIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTipSwitchByHallIdLogic) GetTipSwitchByHallId(req *types.GetTipSwitchByHallIdRequest) (*types.BaseResponse, error) {
	// TODO: 之後統一只取一個小費開關
	gameDetailMap := map[string]uint32{}
	for _, v := range constants.GameDetail {
		gameDetailMap[v.Name] = v.ID
	}

	tipSwitchName := "tip_switch"
	gameSetting, gameSettingErr := l.svcCtx.GameCtx.GetGameDetail(l.ctx, game.GetGameDetailRequest{
		HallId: []uint32{req.HallID},
		Id:     []uint32{gameDetailMap[tipSwitchName]},
	})

	if gameSettingErr != nil {
		return nil, gameSettingErr
	}

	if len(gameSetting) > 0 {
		return &types.BaseResponse{
			Data: gameSetting[0].GetSwitch(),
		}, nil
	}

	tipSwitchData, tipSwitchErr := l.svcCtx.LiveGameCtx.GetHallTipSwitch(l.ctx, req.HallID)
	if tipSwitchErr != nil {
		return nil, tipSwitchErr
	}

	var tipSwitch bool
	if len(tipSwitchData) > 0 {
		tipSwitch = tipSwitchData[0].GetSwitch()

		_, createErr := l.svcCtx.GameCtx.CreateGameDetail(l.ctx, game.CreateGameDetailRequest{
			HallID:   req.HallID,
			GameKind: constants.BBLive,
			ID:       gameDetailMap[tipSwitchName],
			Enable:   tipSwitch,
		})

		if createErr != nil {
			return nil, createErr
		}
	}

	return &types.BaseResponse{
		Data: tipSwitch,
	}, nil
}
