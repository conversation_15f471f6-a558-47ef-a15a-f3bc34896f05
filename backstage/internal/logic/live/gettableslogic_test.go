package live

import (
	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetTables(t *testing.T) {
	liveGameCtx := mock.NewMockLiveGameCtx()
	mockRequest := livegame.GameCodeSettingRequest{}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	request := &types.TablesRequest{}
	l := NewGetTablesLogic(ctx, svcCtx)
	resp, err := l.GetTables(request)

	expected := &types.BaseResponse{
		Data: []types.Tables{
			{
				RelationID: 1,
				WagersType: 0,
				State:      0,
				GameID:     3001,
				GameCode:   1,
				DealerType: 1,
				Enable:     true,
				UpdateTime: "2025-01-13T21:33:18-04:00",
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTables_WithEnable(t *testing.T) {
	liveGameCtx := mock.NewMockLiveGameCtx()

	enable := true
	mockRequest := livegame.GameCodeSettingRequest{
		Enable: &enable,
	}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	request := &types.TablesRequest{
		Enable: &enable,
	}
	l := NewGetTablesLogic(ctx, svcCtx)
	resp, err := l.GetTables(request)

	expected := &types.BaseResponse{
		Data: []types.Tables{
			{
				RelationID: 1,
				WagersType: 0,
				State:      0,
				GameID:     3001,
				GameCode:   1,
				DealerType: 1,
				Enable:     true,
				UpdateTime: "2025-01-13T21:33:18-04:00",
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTables_WithGameId(t *testing.T) {
	liveGameCtx := mock.NewMockLiveGameCtx()

	gameId := uint32(3001)
	mockRequest := livegame.GameCodeSettingRequest{
		GameId: &gameId,
	}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	request := &types.TablesRequest{
		GameId: &gameId,
	}
	l := NewGetTablesLogic(ctx, svcCtx)
	resp, err := l.GetTables(request)

	expected := &types.BaseResponse{
		Data: []types.Tables{
			{
				RelationID: 1,
				WagersType: 0,
				State:      0,
				GameID:     3001,
				GameCode:   1,
				DealerType: 1,
				Enable:     true,
				UpdateTime: "2025-01-13T21:33:18-04:00",
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTables_WithState(t *testing.T) {
	liveGameCtx := mock.NewMockLiveGameCtx()

	state := uint32(0)
	mockRequest := livegame.GameCodeSettingRequest{
		State: &state,
	}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	request := &types.TablesRequest{
		State: &state,
	}
	l := NewGetTablesLogic(ctx, svcCtx)
	resp, err := l.GetTables(request)

	expected := &types.BaseResponse{
		Data: []types.Tables{
			{
				RelationID: 1,
				WagersType: 0,
				State:      0,
				GameID:     3001,
				GameCode:   1,
				DealerType: 1,
				Enable:     true,
				UpdateTime: "2025-01-13T21:33:18-04:00",
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTables_WithHallID(t *testing.T) {
	liveGameCtx := mock.NewMockLiveGameCtx()

	hallId := uint32(3820474)
	mockRequest := livegame.GameCodeSettingRequest{
		HallID: &hallId,
	}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	request := &types.TablesRequest{
		HallID: &hallId,
	}
	l := NewGetTablesLogic(ctx, svcCtx)
	resp, err := l.GetTables(request)

	expected := &types.BaseResponse{
		Data: []types.Tables{
			{
				RelationID: 1,
				WagersType: 0,
				State:      0,
				GameID:     3001,
				GameCode:   1,
				DealerType: 1,
				Enable:     true,
				UpdateTime: "2025-01-13T21:33:18-04:00",
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTables_GetDomainGameCodeSetting_ConnectionFailed(t *testing.T) {
	liveGameCtx := mock.NewMockLiveGameCtx()
	mockRequest := livegame.GameCodeSettingRequest{}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	request := &types.TablesRequest{}
	l := NewGetTablesLogic(ctx, svcCtx)
	resp, err := l.GetTables(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
