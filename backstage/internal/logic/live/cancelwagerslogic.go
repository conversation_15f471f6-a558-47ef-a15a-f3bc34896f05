package live

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CancelWagersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCancelWagersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CancelWagersLogic {
	return &CancelWagersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CancelWagersLogic) CancelWagers(req *types.CancelWagersRequest) (*types.BaseResponse, error) {
	err := l.svcCtx.LiveGameCtx.CancelWagers(l.ctx, req)

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{}, nil
}
