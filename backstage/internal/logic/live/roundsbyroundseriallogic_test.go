package live

import (
	"testing"

	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/stretchr/testify/assert"
)

func TestRoundsByRoundSerial_RoundsByRoundSerialError(t *testing.T) {
	roundSerial := uint32(123)
	stateId := uint32(0)
	request := &types.LiveRoundsByRoundSerialRequest{
		RoundSerial: roundSerial,
		StateId:     &stateId,
		Sort:        "asc",
	}

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundInfoByRoundSerial", ctx, request.RoundSerial, *request.StateId, request.Sort).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewRoundsByRoundSerialLogic(ctx, svcCtx)
	resp, err := l.RoundsByRoundSerial(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestRoundsByRoundSerial(t *testing.T) {
	roundSerial := uint32(123)
	stateId := uint32(0)
	request := &types.LiveRoundsByRoundSerialRequest{
		RoundSerial: roundSerial,
		StateId:     &stateId,
		Sort:        "asc",
	}

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundInfoByRoundSerial", ctx, request.RoundSerial, *request.StateId, request.Sort).Return(seeder.RoundInfoByRoundSerial.GetRoundInfo(), nil)

	svcCtx.LiveGameCtx = liveGameCtx

	l := NewRoundsByRoundSerialLogic(ctx, svcCtx)
	resp, err := l.RoundsByRoundSerial(request)

	roundInfo := seeder.RoundInfoByRoundSerial.GetRoundInfo()[0]
	expectedResponse := &types.BaseResponse{
		Data: roundByRoundSerialResponse{
			Rounds: []round{
				{
					Datetime:           roundInfo.GetDatetime(),
					RoundSerial:        roundInfo.GetRoundserial(),
					ForeignRoundSerial: roundInfo.GetForeignRoundserial(),
					RoundNO:            roundInfo.GetRoundNo(),
					GameID:             roundInfo.GetGameId(),
					GameCode:           roundInfo.GetGameCode(),
					RoundXML:           roundInfo.GetRoundXml(),
					BetAmount:          roundInfo.GetBetAmount(),
					Payoff:             roundInfo.GetPayoff(),
					ResultStatus:       roundInfo.GetResultStatus(),
					ParsedXML:          analyzeRoundXML(roundInfo.GetGameId(), roundInfo.GetRoundXml()),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
