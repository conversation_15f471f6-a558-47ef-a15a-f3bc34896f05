package live

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RoundsParsedXMLLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRoundsParsedXMLLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RoundsParsedXMLLogic {
	return &RoundsParsedXMLLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RoundsParsedXMLLogic) RoundsParsedXML(req *types.LiveRoundsParsedXMLRequest) (resp *types.BaseResponse, err error) {
	roundXMLInfo, roundXMLInfoResponseErr := l.svcCtx.LiveGameCtx.RoundXMLInfo(l.ctx, req.RoundSerials, req.StartDate, req.EndDate)
	if roundXMLInfoResponseErr != nil {
		return nil, roundXMLInfoResponseErr
	}

	parsedXML := make(map[uint32]interface{}, len(roundXMLInfo))

	for _, info := range roundXMLInfo {
		parsedXML[info.GetRoundSerial()] = analyzeRoundXML(info.GetGameId(), info.GetRoundXml())
	}

	return &types.BaseResponse{
		Data: parsedXML,
	}, nil
}
