package live

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/game/gameclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetTipSwitchByHallIdLogic_GetGameDetail(t *testing.T) {
	request := &types.GetTipSwitchByHallIdRequest{
		HallID: seeder.HallId,
	}

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{request.HallID},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.GameCtx = gameCtx

	l := NewGetTipSwitchByHallIdLogic(ctx, svcCtx)
	resp, err := l.GetTipSwitchByHallId(request)

	expected := &types.BaseResponse{
		Data: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTipSwitchByHallIdLogic_WithoutGetGameDetail(t *testing.T) {
	request := &types.GetTipSwitchByHallIdRequest{
		HallID: seeder.HallId,
	}

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{request.HallID},
		Id:     []uint32{constants.GameDetailID1},
	}).Return([]*gameclient.GameDetail{}, nil)

	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, request.HallID).Return(seeder.HallTipSwitch.GetSwitchList(), nil)

	gameCtx.On("CreateGameDetail", ctx, game.CreateGameDetailRequest{
		HallID:   request.HallID,
		GameKind: constants.BBLive,
		ID:       constants.GameDetailID1,
		Enable:   seeder.HallTipSwitch.GetSwitchList()[0].GetSwitch(),
	}).Return(&gameclient.EmptyResponse{}, nil)

	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx

	l := NewGetTipSwitchByHallIdLogic(ctx, svcCtx)
	resp, err := l.GetTipSwitchByHallId(request)

	expected := &types.BaseResponse{
		Data: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTipSwitchByHallIdLogic_GetGameDetailRPCError(t *testing.T) {
	request := &types.GetTipSwitchByHallIdRequest{
		HallID: seeder.HallId,
	}

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{request.HallID},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	l := NewGetTipSwitchByHallIdLogic(ctx, svcCtx)
	resp, err := l.GetTipSwitchByHallId(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetTipSwitchByHallIdLogic_GetHallTipSwitchRPCError(t *testing.T) {
	request := &types.GetTipSwitchByHallIdRequest{
		HallID: seeder.HallId,
	}

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{request.HallID},
		Id:     []uint32{constants.GameDetailID1},
	}).Return([]*gameclient.GameDetail{}, nil)

	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, request.HallID).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx

	l := NewGetTipSwitchByHallIdLogic(ctx, svcCtx)
	resp, err := l.GetTipSwitchByHallId(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetTipSwitchByHallIdLogic_CreateGameDetailRPCError(t *testing.T) {
	request := &types.GetTipSwitchByHallIdRequest{
		HallID: seeder.HallId,
	}

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{request.HallID},
		Id:     []uint32{constants.GameDetailID1},
	}).Return([]*gameclient.GameDetail{}, nil)

	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, request.HallID).Return(seeder.HallTipSwitch.GetSwitchList(), nil)

	gameCtx.On("CreateGameDetail", ctx, game.CreateGameDetailRequest{
		HallID:   request.HallID,
		GameKind: constants.BBLive,
		ID:       constants.GameDetailID1,
		Enable:   seeder.HallTipSwitch.GetSwitchList()[0].GetSwitch(),
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx

	l := NewGetTipSwitchByHallIdLogic(ctx, svcCtx)
	resp, err := l.GetTipSwitchByHallId(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
