package live

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCancelWagersLogic_InvalidResponse(t *testing.T) {
	mockLiveGameCtx := mock.NewMockLiveGameCtx()

	req := &types.CancelWagersRequest{
		RoundSerial: 1,
	}

	mockLiveGameCtx.On("CancelWagers", ctx, req).Return(errorx.InvalidResponse)

	svcCtx.LiveGameCtx = mockLiveGameCtx

	l := NewCancelWagersLogic(ctx, svcCtx)
	resp, err := l.CancelWagers(req)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.InvalidResponse, err)
}

func TestCancelWagersLogic(t *testing.T) {
	mockLiveGameCtx := mock.NewMockLiveGameCtx()

	req := &types.CancelWagersRequest{
		RoundSerial: 1,
	}

	mockLiveGameCtx.On("CancelWagers", ctx, req).Return(nil)

	svcCtx.LiveGameCtx = mockLiveGameCtx

	l := NewCancelWagersLogic(ctx, svcCtx)
	resp, err := l.CancelWagers(req)

	assert.Equal(t, &types.BaseResponse{}, resp)
	assert.NoError(t, err)
}
