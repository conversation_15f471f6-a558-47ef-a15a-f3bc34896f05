package live

import (
	"context"

	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTablesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTablesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTablesLogic {
	return &GetTablesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTablesLogic) GetTables(req *types.TablesRequest) (*types.BaseResponse, error) {
	liveGameRequest := livegame.GameCodeSettingRequest{}
	if req.Enable != nil {
		liveGameRequest.Enable = req.Enable
	}
	if req.GameId != nil {
		liveGameRequest.GameId = req.GameId
	}
	if req.State != nil {
		liveGameRequest.State = req.State
	}
	if req.HallID != nil {
		liveGameRequest.HallID = req.HallID
	}

	liveGameResp, err := l.svcCtx.LiveGameCtx.GetDomainGameCodeSetting(l.ctx, liveGameRequest)
	if err != nil {
		return nil, err
	}

	tableInfo := []types.Tables{}
	for _, v := range liveGameResp.GetData() {
		tableInfo = append(tableInfo, types.Tables{
			RelationID: v.GetRelationId(),
			WagersType: v.GetWagersType(),
			State:      v.GetState(),
			GameID:     v.GetGameType(),
			GameCode:   v.GetGameCode(),
			DealerType: v.GetDealerType(),
			Enable:     v.GetEnable(),
			UpdateTime: v.GetUpdateTime(),
		})
	}

	resp := &types.BaseResponse{
		Data: tableInfo,
	}

	return resp, nil
}
