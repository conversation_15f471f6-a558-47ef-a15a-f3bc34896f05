package fish

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubWagersURL(t *testing.T) {
	mockFishGameCtx := mock.NewMockFishGameCtx()

	req := &types.SubWagersURLRequest{
		WagersID: []uint64{1, 2},
		Lang:     "",
	}

	mockFishGameCtx.On("GetMultiSubWagersURL", ctx, "zh-cn", req.WagersID).Return(&seeder.MultiSubWagersURL, nil)

	svcCtx.FishGameCtx = mockFishGameCtx

	l := NewSubWagersURLLogic(ctx, svcCtx)
	resp, err := l.SubWagersURL(req)

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: seeder.MultiSubWagersURL.GetSubWagersUrl(),
	}, resp)
}

func TestSubWagersURL_GetError(t *testing.T) {
	mockFishGameCtx := mock.NewMockFishGameCtx()

	req := &types.SubWagersURLRequest{
		WagersID: []uint64{1, 2},
		Lang:     "",
	}

	mockFishGameCtx.On("GetMultiSubWagersURL", ctx, "zh-cn", req.WagersID).Return(nil, errorx.ConnectionFailed)

	svcCtx.FishGameCtx = mockFishGameCtx

	l := NewSubWagersURLLogic(ctx, svcCtx)
	resp, err := l.SubWagersURL(req)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}
