package fish

import (
	"context"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/fishgame/fishgameclient"
	"slices"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubWagersURLLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSubWagersURLLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubWagersURLLogic {
	return &SubWagersURLLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubWagersURLLogic) SubWagersURL(req *types.SubWagersURLRequest) (*types.BaseResponse, error) {
	if req.Lang == "" {
		req.Lang = "zh-cn"
	}

	wagersIDs := req.WagersID
	slices.Sort(wagersIDs)
	wagersIDs = slices.Compact(wagersIDs)

	// TODO - 待升級 golang 1.23.0 以上版本後改用 slices.Chunk
	reqWagersId := ArrayChunk(wagersIDs, constants.Count500)

	wagersURL := make([]*fishgameclient.SubWagersURL, 0)
	for _, v := range reqWagersId {
		wagersURLTmp, err := l.svcCtx.FishGameCtx.GetMultiSubWagersURL(l.ctx, req.Lang, v)

		if err != nil {
			return nil, err
		}

		wagersURL = append(wagersURL, wagersURLTmp.GetSubWagersUrl()...)
	}

	return &types.BaseResponse{
		Data: wagersURL,
	}, nil
}

func ArrayChunk[T any](s []T, size int) [][]T {
	var chunks [][]T
	for i := 0; i < len(s); i += size {
		end := i + size
		if end > len(s) {
			end = len(s)
		}
		chunks = append(chunks, s[i:end])
	}
	return chunks
}
