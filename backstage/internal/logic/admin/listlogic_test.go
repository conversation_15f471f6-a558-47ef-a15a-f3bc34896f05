package admin

import (
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminList_GetGMUserList(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()

	enable := true
	page := uint32(1)
	limit := uint32(1)
	request := &types.AdminListRequest{
		Enable:       &enable,
		Page:         &page,
		PageLimit:    &limit,
		SortField:    "username",
		Sort:         "asc",
		DepartmentId: 1,
		PositionId:   1,
		Period:       nil,
		Fuzzy:        true,
		Username:     "test",
		Alias:        "",
	}

	mockGMUserListRequest := admin.GetGMUserListRequest{
		Username: "%test%",
		Alias:    request.Alias,
		Enable:   request.Enable,
		Sort:     request.SortField,
		Order:    request.Sort,
	}

	mockAdminRPC.On("GetGMUserList", ctx, mockGMUserListRequest).Return(&seeder.GMUserList, nil)

	mockUsersDepartmentRequest := admin.GetUsersDepartmentRequest{
		Users:        seeder.GMUserList.GetUserList(),
		PositionId:   request.PositionId,
		DepartmentId: request.DepartmentId,
		Page:         *request.Page,
		PageLimit:    *request.PageLimit,
	}

	mockAdminRPC.On("GetUsersDepartment", ctx, mockUsersDepartmentRequest).Return(&seeder.UsersDepartment, nil)
	svcCtx.AdminCtx = mockAdminRPC

	l := NewAdminListLogic(ctx, svcCtx)
	resp, err := l.AdminList(request)

	user := seeder.UsersDepartment.GetUsers()[0]
	expected := &types.BaseResponse{
		Data: types.AdminListResp{
			Users: []types.AdminGMUser{
				{
					Id:           user.GetId(),
					Username:     user.GetUsername(),
					Alias:        user.GetAlias(),
					Enable:       user.GetEnable(),
					Block:        user.GetBlock(),
					EnableUbAuth: user.GetEnableUbAuth(),
					Bind:         user.GetBind(),
					CreatedAt:    user.GetCreatedAt(),
					DepartmentId: user.GetDepartmentId(),
					Department:   user.GetDepartment(),
					PositionId:   user.GetPositionId(),
					Position:     user.GetPosition(),
				},
			},
			Total: seeder.UsersDepartment.GetTotal(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestAdminList_GetGMUserIdleList(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()

	enable := true
	page := uint32(1)
	limit := uint32(1)
	period := uint32(7)
	request := &types.AdminListRequest{
		Enable:       &enable,
		Page:         &page,
		PageLimit:    &limit,
		SortField:    "username",
		Sort:         "asc",
		DepartmentId: 1,
		PositionId:   1,
		Period:       &period,
		Fuzzy:        true,
		Username:     "",
		Alias:        "test",
	}

	mockGMUserListRequest := admin.GetGMUserIdleListRequest{
		Period:   *request.Period,
		Username: "",
		Alias:    "%test%",
		Enable:   request.Enable,
		Sort:     request.SortField,
		Order:    request.Sort,
	}

	mockAdminRPC.On("GetGMUserIdleList", ctx, mockGMUserListRequest).Return(&seeder.GMUserList, nil)

	mockUsersDepartmentRequest := admin.GetUsersDepartmentRequest{
		Users:        seeder.GMUserList.GetUserList(),
		PositionId:   request.PositionId,
		DepartmentId: request.DepartmentId,
		Page:         *request.Page,
		PageLimit:    *request.PageLimit,
	}

	mockAdminRPC.On("GetUsersDepartment", ctx, mockUsersDepartmentRequest).Return(&seeder.UsersDepartment, nil)
	svcCtx.AdminCtx = mockAdminRPC

	l := NewAdminListLogic(ctx, svcCtx)
	resp, err := l.AdminList(request)

	user := seeder.UsersDepartment.GetUsers()[0]
	expected := &types.BaseResponse{
		Data: types.AdminListResp{
			Users: []types.AdminGMUser{
				{
					Id:           user.GetId(),
					Username:     user.GetUsername(),
					Alias:        user.GetAlias(),
					Enable:       user.GetEnable(),
					Block:        user.GetBlock(),
					EnableUbAuth: user.GetEnableUbAuth(),
					Bind:         user.GetBind(),
					CreatedAt:    user.GetCreatedAt(),
					DepartmentId: user.GetDepartmentId(),
					Department:   user.GetDepartment(),
					PositionId:   user.GetPositionId(),
					Position:     user.GetPosition(),
				},
			},
			Total: seeder.UsersDepartment.GetTotal(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestAdminList_GetGMUserList_GetError(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()

	enable := true
	page := uint32(1)
	limit := uint32(1)
	request := &types.AdminListRequest{
		Enable:       &enable,
		Page:         &page,
		PageLimit:    &limit,
		SortField:    "username",
		Sort:         "asc",
		DepartmentId: 1,
		PositionId:   1,
		Period:       nil,
		Fuzzy:        true,
		Username:     "test",
		Alias:        "",
	}

	mockGMUserListRequest := admin.GetGMUserListRequest{
		Username: "%test%",
		Alias:    request.Alias,
		Enable:   request.Enable,
		Sort:     request.SortField,
		Order:    request.Sort,
	}

	mockAdminRPC.On("GetGMUserList", ctx, mockGMUserListRequest).Return("", errorx.ConnectionFailed)
	svcCtx.AdminCtx = mockAdminRPC

	l := NewAdminListLogic(ctx, svcCtx)
	resp, err := l.AdminList(request)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminList_GetGMUserIdleList_GetError(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()

	enable := true
	page := uint32(1)
	limit := uint32(1)
	period := uint32(7)
	request := &types.AdminListRequest{
		Enable:       &enable,
		Page:         &page,
		PageLimit:    &limit,
		SortField:    "username",
		Sort:         "asc",
		DepartmentId: 1,
		PositionId:   1,
		Period:       &period,
		Fuzzy:        true,
		Username:     "",
		Alias:        "test",
	}

	mockGMUserListRequest := admin.GetGMUserIdleListRequest{
		Period:   *request.Period,
		Username: "",
		Alias:    "%test%",
		Enable:   request.Enable,
		Sort:     request.SortField,
		Order:    request.Sort,
	}

	mockAdminRPC.On("GetGMUserIdleList", ctx, mockGMUserListRequest).Return("", errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC

	l := NewAdminListLogic(ctx, svcCtx)
	resp, err := l.AdminList(request)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminList_GetGMUserList_DepartmentError(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()

	enable := true
	page := uint32(1)
	limit := uint32(1)
	request := &types.AdminListRequest{
		Enable:       &enable,
		Page:         &page,
		PageLimit:    &limit,
		SortField:    "username",
		Sort:         "asc",
		DepartmentId: 1,
		PositionId:   1,
		Period:       nil,
		Fuzzy:        true,
		Username:     "test",
		Alias:        "",
	}

	mockGMUserListRequest := admin.GetGMUserListRequest{
		Username: "%test%",
		Alias:    request.Alias,
		Enable:   request.Enable,
		Sort:     request.SortField,
		Order:    request.Sort,
	}

	mockAdminRPC.On("GetGMUserList", ctx, mockGMUserListRequest).Return(&seeder.GMUserList, nil)

	mockUsersDepartmentRequest := admin.GetUsersDepartmentRequest{
		Users:        seeder.GMUserList.GetUserList(),
		PositionId:   request.PositionId,
		DepartmentId: request.DepartmentId,
		Page:         *request.Page,
		PageLimit:    *request.PageLimit,
	}

	mockAdminRPC.On("GetUsersDepartment", ctx, mockUsersDepartmentRequest).Return("", errorx.ConnectionFailed)
	svcCtx.AdminCtx = mockAdminRPC

	l := NewAdminListLogic(ctx, svcCtx)
	resp, err := l.AdminList(request)

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}
