package admin

import (
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetPositionLogic_GetPosition(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockRequest := admin.PositionListRequest{}

	mockAdminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	svcCtx.AdminCtx = mockAdminRPC

	request := &types.PositionRequest{}

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	expected := &types.PositionResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.Position{
				{
					ID:        111,
					Name:      "GM測試1",
					Note:      " ",
					CreatedAt: "2019-06-17T11:34:08-04:00",
					Count:     1,
				},
				{
					ID:        192,
					Name:      "GMMP新人專用",
					Note:      " ",
					CreatedAt: "2020-10-18T07:51:01-04:00",
					Count:     10,
				},
			},
		},
		Total: seeder.GetPositionList.GetPagination().GetTotal(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetPositionLogic_GetPosition_WithSort(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	sort := "id"
	mockRequest := admin.PositionListRequest{
		Sort: &sort,
	}

	mockAdminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	svcCtx.AdminCtx = mockAdminRPC

	request := &types.PositionRequest{
		Sort: &sort,
	}

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	expected := &types.PositionResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.Position{
				{
					ID:        111,
					Name:      "GM測試1",
					Note:      " ",
					CreatedAt: "2019-06-17T11:34:08-04:00",
					Count:     1,
				},
				{
					ID:        192,
					Name:      "GMMP新人專用",
					Note:      " ",
					CreatedAt: "2020-10-18T07:51:01-04:00",
					Count:     10,
				},
			},
		},
		Total: seeder.GetPositionList.GetPagination().GetTotal(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetPositionLogic_GetPosition_WithOrder(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	order := "ASC"
	mockRequest := admin.PositionListRequest{
		Order: &order,
	}

	mockAdminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	svcCtx.AdminCtx = mockAdminRPC

	request := &types.PositionRequest{
		Order: &order,
	}

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	expected := &types.PositionResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.Position{
				{
					ID:        111,
					Name:      "GM測試1",
					Note:      " ",
					CreatedAt: "2019-06-17T11:34:08-04:00",
					Count:     1,
				},
				{
					ID:        192,
					Name:      "GMMP新人專用",
					Note:      " ",
					CreatedAt: "2020-10-18T07:51:01-04:00",
					Count:     10,
				},
			},
		},
		Total: seeder.GetPositionList.GetPagination().GetTotal(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetPositionLogic_GetPosition_WithPosition(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	position := "GM"
	mockRequest := admin.PositionListRequest{
		Position: &position,
	}

	mockAdminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	svcCtx.AdminCtx = mockAdminRPC

	request := &types.PositionRequest{
		Position: &position,
	}

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	expected := &types.PositionResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.Position{
				{
					ID:        111,
					Name:      "GM測試1",
					Note:      " ",
					CreatedAt: "2019-06-17T11:34:08-04:00",
					Count:     1,
				},
				{
					ID:        192,
					Name:      "GMMP新人專用",
					Note:      " ",
					CreatedAt: "2020-10-18T07:51:01-04:00",
					Count:     10,
				},
			},
		},
		Total: seeder.GetPositionList.GetPagination().GetTotal(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetPositionLogic_GetPosition_WithPage(t *testing.T) {
	page := uint32(1)
	mockAdminRPC := mock.NewMockAdminCtx()
	mockRequest := admin.PositionListRequest{
		Page: &page,
	}

	mockAdminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	svcCtx.AdminCtx = mockAdminRPC

	request := &types.PositionRequest{
		Page: &page,
	}

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	expected := &types.PositionResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.Position{
				{
					ID:        111,
					Name:      "GM測試1",
					Note:      " ",
					CreatedAt: "2019-06-17T11:34:08-04:00",
					Count:     1,
				},
				{
					ID:        192,
					Name:      "GMMP新人專用",
					Note:      " ",
					CreatedAt: "2020-10-18T07:51:01-04:00",
					Count:     10,
				},
			},
		},
		Total: seeder.GetPositionList.GetPagination().GetTotal(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetPositionLogic_GetPosition_WithLimit(t *testing.T) {
	limit := uint32(2)
	mockAdminRPC := mock.NewMockAdminCtx()
	mockRequest := admin.PositionListRequest{
		Limit: &limit,
	}

	mockAdminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	svcCtx.AdminCtx = mockAdminRPC

	request := &types.PositionRequest{
		Limit: &limit,
	}

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	expected := &types.PositionResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.Position{
				{
					ID:        111,
					Name:      "GM測試1",
					Note:      " ",
					CreatedAt: "2019-06-17T11:34:08-04:00",
					Count:     1,
				},
				{
					ID:        192,
					Name:      "GMMP新人專用",
					Note:      " ",
					CreatedAt: "2020-10-18T07:51:01-04:00",
					Count:     10,
				},
			},
		},
		Total: seeder.GetPositionList.GetPagination().GetTotal(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetPositionLogic_GetPosition_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockRequest := admin.PositionListRequest{}

	mockAdminRPC.On("PositionList", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)
	svcCtx.AdminCtx = mockAdminRPC

	request := &types.PositionRequest{}

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}
