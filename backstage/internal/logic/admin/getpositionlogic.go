package admin

import (
	"context"

	"gbh/admin/adminclient"
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPositionLogic {
	return &GetPositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPositionLogic) GetPosition(req *types.PositionRequest) (*types.PositionResponse, error) {
	request := admin.PositionListRequest{}

	if req.Page != nil && *req.Page > 0 {
		request.Page = req.Page
	}

	if req.Limit != nil && *req.Limit > 0 {
		request.Limit = req.Limit
	}

	if req.Sort != nil {
		request.Sort = req.Sort
	}

	if req.Order != nil {
		request.Order = req.Order
	}

	if req.Position != nil {
		request.Position = req.Position
	}

	// 取得職務列表
	positionResp, err := l.getPositionList(request)
	if err != nil {
		return nil, err
	}

	positionInfo := []types.Position{}
	for _, v := range positionResp.GetList() {
		positionInfo = append(positionInfo, types.Position{
			ID:        v.GetId(),
			Name:      v.GetName(),
			Note:      v.GetNote(),
			CreatedAt: v.GetCreatedDateTime(),
			Count:     v.GetTotal(),
		})
	}

	resp := &types.PositionResponse{
		BaseResponse: types.BaseResponse{
			Data: positionInfo,
		},
		Total: positionResp.GetPagination().GetTotal(),
	}

	return resp, nil
}

func (l *GetPositionLogic) getPositionList(request admin.PositionListRequest) (*adminclient.PositionListResponse, error) {
	resp, err := l.svcCtx.AdminCtx.PositionList(l.ctx, request)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
