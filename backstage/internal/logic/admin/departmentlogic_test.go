package admin

import (
	"testing"

	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/stretchr/testify/assert"
)

func TestDepartment_DepartmentError(t *testing.T) {
	request := &types.AdminDepartmentRequest{
		Department: "test",
		Sort:       "id",
		Order:      "asc",
		Page:       1,
		PageLimit:  1,
	}

	adminCtx := mock.NewMockAdminCtx()

	departmentListMockRequest := admin.DepartmentListRequest{
		Department: "test",
		Sort:       "id",
		Order:      "asc",
		Page:       1,
		PageLimit:  1,
	}

	adminCtx.On("DepartmentList", ctx, departmentListMockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx

	l := NewDepartmentLogic(ctx, svcCtx)
	resp, err := l.Department(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDepartment(t *testing.T) {
	request := &types.AdminDepartmentRequest{
		Department: "test",
		Sort:       "id",
		Order:      "asc",
		Page:       1,
		PageLimit:  1,
	}

	adminCtx := mock.NewMockAdminCtx()

	departmentListMockRequest := admin.DepartmentListRequest{
		Department: "test",
		Sort:       "id",
		Order:      "asc",
		Page:       1,
		PageLimit:  1,
	}

	adminCtx.On("DepartmentList", ctx, departmentListMockRequest).Return(&seeder.DepartmentList, nil)

	svcCtx.AdminCtx = adminCtx

	l := NewDepartmentLogic(ctx, svcCtx)
	resp, err := l.Department(request)

	pagination := seeder.DepartmentList.GetPagination()
	expectedResponse := &types.BaseResponse{
		Data: departmentResponse{
			Departments: []department{
				{
					ID:        seeder.AdminDepartmentID2222,
					Name:      "PD-BM",
					Note:      "控管端組",
					CreatedAt: "2024-09-09T23:39:05-04:00",
					TotalUser: seeder.AdminDepartmentID2222Total,
				},
				{
					ID:        seeder.AdminDepartmentID2211,
					Name:      "PIDunitTEST",
					Note:      "",
					CreatedAt: "2021-10-25T15:10:43-04:00",
					TotalUser: 1,
				},
			},
			Pagination: types.PaginateResponse{
				Page:        pagination.GetCurrentPage(),
				PageLimit:   pagination.GetPageLimit(),
				TotalNumber: pagination.GetTotal(),
				TotalPage:   pagination.GetTotalPage(),
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
