package admin

import (
	"context"
	"fmt"
	"regexp"

	"gbh/admin/adminclient"
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) AdminList(req *types.AdminListRequest) (*types.BaseResponse, error) {
	var sortField = "username"
	if req.SortField != "" {
		sortField = req.SortField
	}

	var sort = "asc"
	if req.Sort != "" {
		sort = req.Sort
	}

	if req.Username != "" {
		req.Alias = ""
	}

	var username = req.Username
	if req.Username != "" && req.Fuzzy {
		username = getFuzzySearchValue(req.Username)
	}

	var alias = req.Alias
	if req.Alias != "" && req.Fuzzy {
		if req.Fuzzy {
			alias = getFuzzySearchValue(req.Alias)
		}
	}

	// 7 or 30天內是否登入
	var users []*adminclient.GMUser
	if req.Period != nil {
		usersTmp, err := l.svcCtx.AdminCtx.GetGMUserIdleList(l.ctx, admin.GetGMUserIdleListRequest{
			Period:   *req.Period,
			Username: username,
			Alias:    alias,
			Enable:   req.Enable,
			Sort:     sortField,
			Order:    sort,
		})

		if err != nil {
			return nil, err
		}

		users = usersTmp.GetUserList()
	} else {
		usersTmp, err := l.svcCtx.AdminCtx.GetGMUserList(l.ctx, admin.GetGMUserListRequest{
			Username: username,
			Alias:    alias,
			Enable:   req.Enable,
			Sort:     sortField,
			Order:    sort,
		})

		if err != nil {
			return nil, err
		}

		users = usersTmp.GetUserList()
	}

	var page uint32 = 1
	if req.Page != nil {
		page = *req.Page
	}

	param := admin.GetUsersDepartmentRequest{
		Users:        users,
		DepartmentId: req.DepartmentId,
		PositionId:   req.PositionId,
		Page:         page,
	}

	if req.PageLimit != nil {
		param.PageLimit = *req.PageLimit
	}

	resp, err := l.svcCtx.AdminCtx.GetUsersDepartment(l.ctx, param)

	if err != nil {
		return nil, err
	}

	gmUsers := make([]types.AdminGMUser, 0, len(resp.GetUsers()))
	for _, user := range resp.GetUsers() {
		gmUsers = append(gmUsers, types.AdminGMUser{
			Id:           user.GetId(),
			Username:     user.GetUsername(),
			Alias:        user.GetAlias(),
			Enable:       user.GetEnable(),
			Block:        user.GetBlock(),
			EnableUbAuth: user.GetEnableUbAuth(),
			Bind:         user.GetBind(),
			CreatedAt:    user.GetCreatedAt(),
			DepartmentId: user.GetDepartmentId(),
			Department:   user.GetDepartment(),
			PositionId:   user.GetPositionId(),
			Position:     user.GetPosition(),
		})
	}

	return &types.BaseResponse{
		Data: types.AdminListResp{
			Users: gmUsers,
			Total: resp.GetTotal(),
		},
	}, nil
}

func getFuzzySearchValue(value string) string {
	re := regexp.MustCompile(`\_`)
	value = re.ReplaceAllString(value, "\\_")

	return fmt.Sprintf("%%%s%%", value)
}
