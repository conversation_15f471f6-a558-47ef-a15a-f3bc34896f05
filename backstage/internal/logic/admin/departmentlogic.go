package admin

import (
	"context"

	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type departmentResponse struct {
	Departments []department           `json:"departments"`
	Pagination  types.PaginateResponse `json:"pagination"`
}

type department struct {
	ID        uint32 `json:"id"`
	Name      string `json:"name"`
	Note      string `json:"note"`
	CreatedAt string `json:"created_at"`
	TotalUser uint32 `json:"total_user"`
}

type DepartmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDepartmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DepartmentLogic {
	return &DepartmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DepartmentLogic) Department(req *types.AdminDepartmentRequest) (resp *types.BaseResponse, err error) {
	request := admin.DepartmentListRequest{
		Department: req.Department,
		Sort:       req.Sort,
		Order:      req.Order,
		Page:       req.Page,
		PageLimit:  req.PageLimit,
	}

	departmentListResponse, departmentListResponseErr := l.svcCtx.AdminCtx.DepartmentList(l.ctx, request)
	if departmentListResponseErr != nil {
		return nil, departmentListResponseErr
	}

	departmentList := departmentListResponse.GetList()
	departments := make([]department, 0, len(departmentList))
	for _, info := range departmentList {
		departments = append(departments, department{
			ID:        info.GetId(),
			Name:      info.GetName(),
			Note:      info.GetNote(),
			CreatedAt: info.GetCreatedDateTime(),
			TotalUser: info.GetTotal(),
		})
	}

	pagination := departmentListResponse.GetPagination()

	return &types.BaseResponse{
		Data: departmentResponse{
			Departments: departments,
			Pagination: types.PaginateResponse{
				Page:        pagination.GetCurrentPage(),
				PageLimit:   pagination.GetPageLimit(),
				TotalNumber: pagination.GetTotal(),
				TotalPage:   pagination.GetTotalPage(),
			},
		},
	}, nil
}
