package report

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/report/reportclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetGameAnalysisEvent_CtxError(t *testing.T) {
	in := &reportclient.GetGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		Years:    []uint32{seeder.GameAnalysisEventYear},
	}

	mockReportCtx := mock.NewMockReportCtx()

	mockReportCtx.On("GetGameAnalysisEvent", ctx, in).Return(nil, errorx.ConnectionFailed)

	svcCtx.ReportCtx = mockReportCtx

	l := NewGetGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.GetGameAnalysisEvent(&types.GetGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		Years:    []uint32{seeder.GameAnalysisEventYear},
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestGetGameAnalysisEvent(t *testing.T) {
	in := &reportclient.GetGameAnalysisEventRequest{
		GameKind:  constants.BBLive,
		Years:     []uint32{seeder.GameAnalysisEventYear},
		GameTypes: []string{"3001"},
	}

	mockReportCtx := mock.NewMockReportCtx()

	mockReportCtx.On("GetGameAnalysisEvent", ctx, in).Return(&seeder.GameAnalysisEvent, nil)

	svcCtx.ReportCtx = mockReportCtx

	l := NewGetGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.GetGameAnalysisEvent(&types.GetGameAnalysisEventRequest{
		GameKind:  constants.BBLive,
		Years:     []uint32{seeder.GameAnalysisEventYear},
		GameTypes: []string{"3001"},
	})

	expected := &types.BaseResponse{
		Data: []GameAnalysisEvent{
			{
				ID:             seeder.GameAnalysisEventId,
				GameKind:       constants.BBLive,
				GameType:       "3001",
				Content:        "",
				Year:           seeder.GameAnalysisEventYear,
				OccurrenceDate: "2025-01-01",
			},
		},
	}

	assert.Equal(t, expected, resp)
	assert.NoError(t, err)
}
