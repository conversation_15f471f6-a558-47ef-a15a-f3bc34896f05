package report

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/proto/report"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCreateGameAnalysisEvent_FlattenLobbyCategoryError(t *testing.T) {
	mockGameCtx := mock.NewMockGameCtx()

	enableReport := true

	in := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, in).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = mockGameCtx

	l := NewCreateGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.CreateGameAnalysisEvent(&types.CreateGameAnalysisEventRequest{
		Content: "",
		Date:    "2025-01-01 00:00:00",
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestCreateGameAnalysisEvent_InvalidGameKind(t *testing.T) {
	mockGameCtx := mock.NewMockGameCtx()

	enableReport := true

	in := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, in).Return([]string{}, nil)

	svcCtx.GameCtx = mockGameCtx

	l := NewCreateGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.CreateGameAnalysisEvent(&types.CreateGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		Content:  "",
		Date:     "2025-01-01 00:00:00",
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.BackstageInvalidGameKind, err)
}

func TestCreateGameAnalysisEvent_GetGameListWithSwitchError(t *testing.T) {
	mockGameCtx := mock.NewMockGameCtx()

	enableReport := true

	lobbyRequest := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	gameListRequest := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLive},
		PlatformEnable: &gameclient.BoolValue{
			Value: true,
		},
		GameId: []uint32{constants.GameID3001},
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, lobbyRequest).Return(seeder.FlattenLobbyCategory, nil)

	mockGameCtx.On("GetGameListWithSwitch", ctx, gameListRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = mockGameCtx

	l := NewCreateGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.CreateGameAnalysisEvent(&types.CreateGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		GameID:   constants.GameID3001,
		Content:  "",
		Date:     "2025-01-01 00:00:00",
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestCreateGameAnalysisEvent_InvalidGameType(t *testing.T) {
	mockGameCtx := mock.NewMockGameCtx()

	enableReport := true

	lobbyRequest := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	gameListRequest := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLive},
		PlatformEnable: &gameclient.BoolValue{
			Value: true,
		},
		GameId: []uint32{constants.GameID3001},
	}

	gameListReturn := &gameclient.GetGameListWithSwitchResponse{
		Data: []*gameclient.GameListWithSwitch{},
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, lobbyRequest).Return(seeder.FlattenLobbyCategory, nil)

	mockGameCtx.On("GetGameListWithSwitch", ctx, gameListRequest).Return(gameListReturn, nil)

	svcCtx.GameCtx = mockGameCtx

	l := NewCreateGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.CreateGameAnalysisEvent(&types.CreateGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		GameID:   constants.GameID3001,
		Content:  "",
		Date:     "2025-01-01 00:00:00",
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.BackstageInvalidGameID, err)
}

func TestCreateGameAnalysisEvent_CreateGameAnalysisEventError(t *testing.T) {
	mockGameCtx := mock.NewMockGameCtx()
	mockReportCtx := mock.NewMockReportCtx()

	enableReport := true

	lobbyRequest := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	gameListRequest := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLive},
		PlatformEnable: &gameclient.BoolValue{
			Value: true,
		},
		GameId: []uint32{constants.GameID3001},
	}

	gameListReturn := &gameclient.GetGameListWithSwitchResponse{
		Data: []*gameclient.GameListWithSwitch{
			{
				GameKind:            3,
				GameId:              3001,
				Name:                "百家樂",
				Device:              0,
				PlatformEnable:      true,
				PcEnable:            true,
				MobileEnable:        true,
				DemoEnable:          false,
				IsJackpot:           false,
				CommissionableGroup: "1",
				OpenDate:            "2014-01-01",
				UpdatedAt:           "2014-01-01 00:00:00",
				ExternalId:          "",
				IconKind:            "",
				WhiteList:           []uint32{3820325},
			},
		},
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, lobbyRequest).Return(seeder.FlattenLobbyCategory, nil)

	mockGameCtx.On("GetGameListWithSwitch", ctx, gameListRequest).Return(gameListReturn, nil)

	mockReportCtx.On("CreateGameAnalysisEvent", ctx, &report.CreateGameAnalysisEventRequest{
		GameKind:       constants.BBLive,
		GameType:       "3001",
		Content:        "",
		OccurrenceDate: "2025-01-01 00:00:00",
	}).Return(errorx.ConnectionFailed)

	svcCtx.GameCtx = mockGameCtx
	svcCtx.ReportCtx = mockReportCtx

	l := NewCreateGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.CreateGameAnalysisEvent(&types.CreateGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		GameID:   constants.GameID3001,
		Content:  "",
		Date:     "2025-01-01 00:00:00",
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestCreateGameAnalysisEvent_CreateGameAnalysis(t *testing.T) {
	mockGameCtx := mock.NewMockGameCtx()
	mockReportCtx := mock.NewMockReportCtx()

	enableReport := true

	lobbyRequest := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	gameListRequest := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLive},
		PlatformEnable: &gameclient.BoolValue{
			Value: true,
		},
		GameId: []uint32{constants.GameID3001},
	}

	gameListReturn := &gameclient.GetGameListWithSwitchResponse{
		Data: []*gameclient.GameListWithSwitch{
			{
				GameKind:            3,
				GameId:              3001,
				Name:                "百家樂",
				Device:              0,
				PlatformEnable:      true,
				PcEnable:            true,
				MobileEnable:        true,
				DemoEnable:          false,
				IsJackpot:           false,
				CommissionableGroup: "1",
				OpenDate:            "2014-01-01",
				UpdatedAt:           "2014-01-01 00:00:00",
				ExternalId:          "",
				IconKind:            "",
				WhiteList:           []uint32{3820325},
			},
		},
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, lobbyRequest).Return(seeder.FlattenLobbyCategory, nil)

	mockGameCtx.On("GetGameListWithSwitch", ctx, gameListRequest).Return(gameListReturn, nil)

	mockReportCtx.On("CreateGameAnalysisEvent", ctx, &report.CreateGameAnalysisEventRequest{
		GameKind:       constants.BBLive,
		GameType:       "3001",
		Content:        "",
		OccurrenceDate: "2025-01-01 00:00:00",
	}).Return(nil)

	svcCtx.GameCtx = mockGameCtx
	svcCtx.ReportCtx = mockReportCtx

	l := NewCreateGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.CreateGameAnalysisEvent(&types.CreateGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		GameID:   constants.GameID3001,
		Content:  "",
		Date:     "2025-01-01 00:00:00",
	})

	assert.Equal(t, &types.BaseResponse{}, resp)
	assert.NoError(t, err)
}

func TestCreateGameAnalysisEvent_CreateGameAnalysisWithLotteryGameType(t *testing.T) {
	mockGameCtx := mock.NewMockGameCtx()
	mockReportCtx := mock.NewMockReportCtx()

	enableReport := true

	lobbyRequest := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	gameListRequest := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLottery},
		PlatformEnable: &gameclient.BoolValue{
			Value: true,
		},
		GameId: []uint32{constants.GameID12063},
	}

	gameListReturn := &gameclient.GetGameListWithSwitchResponse{
		Data: []*gameclient.GameListWithSwitch{
			{
				GameKind:            12,
				GameId:              12063,
				Name:                "香港六合彩",
				Device:              0,
				PlatformEnable:      true,
				PcEnable:            true,
				MobileEnable:        true,
				DemoEnable:          false,
				IsJackpot:           false,
				CommissionableGroup: "1",
				OpenDate:            "2014-01-01",
				UpdatedAt:           "2014-01-01 00:00:00",
				ExternalId:          "HKLT",
				IconKind:            "",
			},
		},
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, lobbyRequest).Return(seeder.FlattenLobbyCategory, nil)

	mockGameCtx.On("GetGameListWithSwitch", ctx, gameListRequest).Return(gameListReturn, nil)

	mockReportCtx.On("CreateGameAnalysisEvent", ctx, &report.CreateGameAnalysisEventRequest{
		GameKind:       constants.BBLottery,
		GameType:       "HKLT",
		Content:        "",
		OccurrenceDate: "2025-01-01 00:00:00",
	}).Return(nil)

	svcCtx.GameCtx = mockGameCtx
	svcCtx.ReportCtx = mockReportCtx

	l := NewCreateGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.CreateGameAnalysisEvent(&types.CreateGameAnalysisEventRequest{
		GameKind: constants.BBLottery,
		GameID:   constants.GameID12063,
		Content:  "",
		Date:     "2025-01-01 00:00:00",
	})

	assert.Equal(t, &types.BaseResponse{}, resp)
	assert.NoError(t, err)
}
