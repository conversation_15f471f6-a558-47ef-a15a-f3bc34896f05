package report

import (
	"context"
	"slices"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/proto/report"
	"gbh/utils/strutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateGameAnalysisEventLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateGameAnalysisEventLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateGameAnalysisEventLogic {
	return &CreateGameAnalysisEventLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateGameAnalysisEventLogic) CreateGameAnalysisEvent(req *types.CreateGameAnalysisEventRequest) (*types.BaseResponse, error) {
	enableReport := true

	in := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	lobby, err := l.svcCtx.GameCtx.FlattenLobbyCategory(l.ctx, in)

	if err != nil {
		return nil, err
	}

	if req.GameKind != 0 && !slices.Contains(lobby, strutil.Uint32ToString(req.GameKind)) {
		return nil, errorx.BackstageInvalidGameKind
	}

	gameType := ""

	if req.GameID != 0 {
		gameListRequest := &gameclient.GetGameListWithSwitchRequest{
			GameKind: []uint32{req.GameKind},
			PlatformEnable: &gameclient.BoolValue{
				Value: true,
			},
			GameId: []uint32{req.GameID},
		}

		games, err := l.svcCtx.GameCtx.GetGameListWithSwitch(l.ctx, gameListRequest)

		if err != nil {
			return nil, err
		}

		if len(games.GetData()) == 0 {
			return nil, errorx.BackstageInvalidGameID
		}

		gameType = strutil.Uint32ToString(games.GetData()[0].GetGameId())

		if req.GameKind == constants.BBLottery {
			gameType = games.GetData()[0].GetExternalId()
		}
	}

	err = l.svcCtx.ReportCtx.CreateGameAnalysisEvent(l.ctx, &report.CreateGameAnalysisEventRequest{
		GameKind:       req.GameKind,
		GameType:       gameType,
		Content:        req.Content,
		OccurrenceDate: req.Date,
	})

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{}, nil
}
