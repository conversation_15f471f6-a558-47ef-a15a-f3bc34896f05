package report

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestReportCloseDate(t *testing.T) {
	reportCtx := mock.NewMockReportCtx()

	reportCtx.On("GetReportCloseDate", ctx).Return(seeder.CloseDate.GetCloseDate(), nil)
	svcCtx.ReportCtx = reportCtx

	l := NewCloseDateLogic(ctx, svcCtx)
	resp, err := l.CloseDate()

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: seeder.CloseDate.GetCloseDate(),
	}, resp)
}

func TestReportCloseDate_GetError(t *testing.T) {
	reportCtx := mock.NewMockReportCtx()

	reportCtx.On("GetReportCloseDate", ctx).Return("", errorx.ConnectionFailed)
	svcCtx.ReportCtx = reportCtx

	l := NewCloseDateLogic(ctx, svcCtx)
	resp, err := l.CloseDate()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
