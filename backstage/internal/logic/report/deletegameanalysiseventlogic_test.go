package report

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/report/reportclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDeleteGameAnalysisEvent_CtxError(t *testing.T) {
	mockReportCtx := mock.NewMockReportCtx()

	mockReportCtx.On("DeleteGameAnalysisEvent", ctx, &reportclient.DeleteGameAnalysisEventRequest{
		Ids: []uint32{seeder.GameAnalysisEventId},
	}).Return(errorx.ConnectionFailed)

	svcCtx.ReportCtx = mockReportCtx

	l := NewDeleteGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.DeleteGameAnalysisEvent(&types.DeleteGameAnalysisEventRequest{
		IDs: []uint32{seeder.GameAnalysisEventId},
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestDeleteGameAnalysisEvent(t *testing.T) {
	mockReportCtx := mock.NewMockReportCtx()

	mockReportCtx.On("DeleteGameAnalysisEvent", ctx, &reportclient.DeleteGameAnalysisEventRequest{
		Ids: []uint32{seeder.GameAnalysisEventId},
	}).Return(nil)

	svcCtx.ReportCtx = mockReportCtx

	l := NewDeleteGameAnalysisEventLogic(ctx, svcCtx)
	resp, err := l.DeleteGameAnalysisEvent(&types.DeleteGameAnalysisEventRequest{
		IDs: []uint32{seeder.GameAnalysisEventId},
	})

	assert.Equal(t, &types.BaseResponse{}, resp)
	assert.NoError(t, err)
}
