package report

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CloseDateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCloseDateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CloseDateLogic {
	return &CloseDateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CloseDateLogic) CloseDate() (*types.BaseResponse, error) {
	closeDate, err := l.svcCtx.ReportCtx.GetReportCloseDate(l.ctx)

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: closeDate,
	}, nil
}
