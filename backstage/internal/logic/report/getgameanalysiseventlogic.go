package report

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/report/reportclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGameAnalysisEventLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type GameAnalysisEvent struct {
	ID             uint32 `json:"id"`
	GameKind       uint32 `json:"game_kind"`
	GameType       string `json:"game_type"`
	Content        string `json:"content"`
	Year           uint32 `json:"year"`
	OccurrenceDate string `json:"occurrence_date"`
}

func NewGetGameAnalysisEventLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGameAnalysisEventLogic {
	return &GetGameAnalysisEventLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGameAnalysisEventLogic) GetGameAnalysisEvent(req *types.GetGameAnalysisEventRequest) (*types.BaseResponse, error) {
	in := &reportclient.GetGameAnalysisEventRequest{
		GameKind: req.GameKind,
		Years:    req.Years,
	}

	if len(req.GameTypes) > 0 {
		in.GameTypes = req.GameTypes
	}

	events, err := l.svcCtx.ReportCtx.GetGameAnalysisEvent(l.ctx, in)

	if err != nil {
		return nil, err
	}

	getGameAnalysisEventResponse := make([]GameAnalysisEvent, 0, len(events.GetEvents()))

	for _, e := range events.GetEvents() {
		getGameAnalysisEventResponse = append(getGameAnalysisEventResponse, GameAnalysisEvent{
			ID:             e.GetId(),
			GameKind:       e.GetGameKind(),
			GameType:       e.GetGameType(),
			Content:        e.GetContent(),
			Year:           e.GetYear(),
			OccurrenceDate: e.GetOccurrenceDate(),
		})
	}

	return &types.BaseResponse{
		Data: getGameAnalysisEventResponse,
	}, nil
}
