package report

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/report/reportclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteGameAnalysisEventLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteGameAnalysisEventLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteGameAnalysisEventLogic {
	return &DeleteGameAnalysisEventLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteGameAnalysisEventLogic) DeleteGameAnalysisEvent(req *types.DeleteGameAnalysisEventRequest) (*types.BaseResponse, error) {
	in := &reportclient.DeleteGameAnalysisEventRequest{
		Ids: req.IDs,
	}

	err := l.svcCtx.ReportCtx.DeleteGameAnalysisEvent(l.ctx, in)

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{}, nil
}
