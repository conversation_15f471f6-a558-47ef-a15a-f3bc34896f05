package user

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/user/userclient"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

var (
	r *http.Request
)

func TestSubAccountCountLogic_SubAccountCount(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "last_login"},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(&seeder.SubUsers, nil)

	svcCtx.UserCtx = mockUserRPC

	request := &types.SubAccountCountRequest{
		HallID: seeder.HallId,
		Role:   7,
	}

	r = &http.Request{RemoteAddr: "111.235.135.57:8080"}
	l := NewSubAccountCountLogic(ctx, svcCtx)
	resp, err := l.SubAccountCount(request)

	expected := &types.BaseResponse{
		Data: types.SubAccountCount{
			All:        1,
			SevenDays:  1,
			ThirtyDays: 1,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountCountLogic_SubAccountCount_WithUsername_WithFuzzy(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		Username: &userclient.StringValue{
			Value: "%test%",
		},
		FetchFields: []string{"id", "last_login"},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(&seeder.SubUsers, nil)

	svcCtx.UserCtx = mockUserRPC

	fuzzy := true
	request := &types.SubAccountCountRequest{
		HallID:   seeder.HallId,
		Role:     7,
		Username: "test",
		Fuzzy:    &fuzzy,
	}

	l := NewSubAccountCountLogic(ctx, svcCtx)
	resp, err := l.SubAccountCount(request)

	expected := &types.BaseResponse{
		Data: types.SubAccountCount{
			All:        1,
			SevenDays:  1,
			ThirtyDays: 1,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountCountLogic_SubAccountCount_WithBlock(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		Block: &userclient.BoolValue{
			Value: true,
		},
		FetchFields: []string{"id", "last_login"},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(&seeder.SubUsers, nil)

	svcCtx.UserCtx = mockUserRPC

	block := true
	request := &types.SubAccountCountRequest{
		HallID: seeder.HallId,
		Role:   7,
		Block:  &block,
	}

	l := NewSubAccountCountLogic(ctx, svcCtx)
	resp, err := l.SubAccountCount(request)

	expected := &types.BaseResponse{
		Data: types.SubAccountCount{
			All:        1,
			SevenDays:  1,
			ThirtyDays: 1,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountCountLogic_GetUserList_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "last_login"},
	}
	mockUserRPC.On("GetUserList", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC

	request := &types.SubAccountCountRequest{
		HallID: seeder.HallId,
		Role:   7,
	}

	l := NewSubAccountCountLogic(ctx, svcCtx)
	resp, err := l.SubAccountCount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
