package user

import (
	"context"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/operationrecord/operationrecordclient"
	"gbh/proto/user"
	"gbh/user/userclient"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUpdateSubAccountLogic_UpdateSubAccount(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: true,
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(mockUniqueResponse, nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", ctx, mockEditRPCRequest).Return(nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", ctx, mockPositionInfoRPCRequest).Return(&seeder.GetPositionInfo, nil)

	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5174},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5174
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5164},
	}
	mockParameterSetPositionResponse := &seeder.GetParameterSet
	mockParameterSetPositionResponse.GetParameterSet()[0].Id = 5164
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5174,
	}
	mockAgentRPC.On("UpdateUserDepartment", ctx, mockUpdateDepartmentRPCRequest).Return(nil)

	mockUpdatePositionRPCRequest := &agentclient.UpdateUserPositionRequest{
		UserId:     seeder.SubUserId,
		PositionId: 5164,
	}
	mockAgentRPC.On("UpdateUserPosition", ctx, mockUpdatePositionRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "upperlevelid",
			Value: "R_hall",
		},
		{
			Key:   "upperloginname",
			Value: seeder.User.GetUsername(),
		},
		{
			Key:   "levelid",
			Value: "R_hallsub",
		},
		{
			Key:   "loginname",
			Value: "gtptest",
		},
		{
			Key:   "typeloginname",
			Value: "R_type_loginname",
		},
		{
			Key:   "chg_loginname",
			Value: "R_loginname",
		},
		{
			Key:   "old_loginname",
			Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
		},
		{
			Key:   "new_loginname",
			Value: "gtptest",
		},
		{
			Key:   "typenickname",
			Value: "R_type_nickname",
		},
		{
			Key:   "chg_nickname",
			Value: "R_nickname",
		},
		{
			Key:   "old_nickname",
			Value: seeder.SubUsers.GetUsers()[0].GetAlias(),
		},
		{
			Key:   "new_nickname",
			Value: "gtptest",
		},
		{
			Key:   "typepassword",
			Value: "",
		},
		{
			Key:   "chg_password",
			Value: "",
		},
		{
			Key:   "old_password",
			Value: "",
		},
		{
			Key:   "new_password",
			Value: "",
		},
	}
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     2,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_all_usersubedit",
		UserKey:      "R_all_usersubedit",
		XMLDataMsg:   xmlDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split("", "?")[0],
		Act:       "change",
		ActionSql: "",
		Content:   "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />",
		Bywho:     "admin",
		Byfile:    strings.Split("", "?")[0],
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", ctx, mockLogRPCRequest).Return(nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC
	svcCtx.HallCtx = mockHallRPC

	positionId := uint32(5164)
	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
		PositionID:   &positionId,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	expected := &types.BaseResponse{
		Data: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestUpdateSubAccountLogic_GetOperator_Error(t *testing.T) {
	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(context.Background(), svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_GetUserByUserId_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC

	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_UpdateSubAccount_checkPermission_Error(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	positionId := uint32(5164)
	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
		PositionID:   &positionId,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_BackstagePositionErrorPut(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.BackstagePositionErrorPut, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_UpdateSubAccount_isValid_Error(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(nil, errorx.ConnectionFailed)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC

	positionId := uint32(5164)
	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
		PositionID:   &positionId,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_UpdateSubAccount_processUserInfo_Error(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(nil, errorx.ConnectionFailed)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", ctx, mockPositionInfoRPCRequest).Return(&seeder.GetPositionInfo, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC

	positionId := uint32(5164)
	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
		PositionID:   &positionId,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_putUserData_processUserInfo_Error(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: true,
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(mockUniqueResponse, nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", ctx, mockEditRPCRequest).Return(errorx.ConnectionFailed)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", ctx, mockPositionInfoRPCRequest).Return(&seeder.GetPositionInfo, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC

	positionId := uint32(5164)
	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
		PositionID:   &positionId,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_UpdateSubAccount_putUserDepartment_Error(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: true,
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(mockUniqueResponse, nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", ctx, mockEditRPCRequest).Return(nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", ctx, mockPositionInfoRPCRequest).Return(&seeder.GetPositionInfo, nil)

	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5174},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5174
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5164},
	}
	mockParameterSetPositionResponse := &seeder.GetParameterSet
	mockParameterSetPositionResponse.GetParameterSet()[0].Id = 5164
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5174,
	}
	mockAgentRPC.On("UpdateUserDepartment", ctx, mockUpdateDepartmentRPCRequest).Return(errorx.ConnectionFailed)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC

	positionId := uint32(5164)
	request := &types.UpdateSubAccountRequest{
		UserID:       seeder.SubUserId,
		Username:     "gtptest",
		Alias:        "gtptest",
		DepartmentID: 5174,
		PositionID:   &positionId,
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp, err := l.UpdateSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateSubAccountLogic_checkPermission_CheckUserPermission_ConnectionFailed(t *testing.T) {
	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = mockPermissionRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.checkPermission(seeder.SubUsers.GetUsers()[0], seeder.SubUsers.GetUsers()[0].GetId())

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_checkPermission_CheckUserPermission_BackstageNoAccountActionSwitchPermPut(t *testing.T) {
	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(false, nil)

	svcCtx.PermissionCtx = mockPermissionRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.checkPermission(seeder.SubUsers.GetUsers()[0], seeder.SubUsers.GetUsers()[0].GetId())

	assert.Equal(t, errorx.BackstageNoAccountActionSwitchPermPut, err)
}

func TestUpdateSubAccountLogic_checkPermission_GetGMHallPrivilege_BackstageOperatorNoDomainPerm(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(nil, errorx.ConnectionFailed)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.checkPermission(seeder.SubUsers.GetUsers()[0], seeder.AdminID)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
}

func TestUpdateSubAccountLogic_GetNamePrefix(t *testing.T) {
	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp := l.getNamePrefix(constants.Role5, "gtptest")

	assert.Equal(t, "agtptest", resp)
}

func TestUpdateSubAccountLogic_isValid(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", ctx, mockPositionInfoRPCRequest).Return(&seeder.GetPositionInfo, nil)

	svcCtx.AgentCtx = mockAgentRPC

	positionId := uint32(5164)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isValid(5174, seeder.HallId, constants.Role7, &positionId)

	assert.NoError(t, err)
}

func TestUpdateSubAccountLogic_isValid_GetParameterSet_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	positionId := uint32(5164)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isValid(5174, seeder.HallId, constants.Role7, &positionId)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_isValid_DepartmentInfoList_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	positionId := uint32(5164)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isValid(5174, seeder.HallId, constants.Role7, &positionId)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_isDepartmentBySameParent(t *testing.T) {
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   3820325,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isDepartmentBySameParent(mockDepartmentInfoResponse, seeder.HallId)

	assert.False(t, err)
}

func TestUpdateSubAccountLogic_isValid_BackstageDepartmentInfoNotFoundModify(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    3820325,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	svcCtx.AgentCtx = mockAgentRPC

	positionId := uint32(5164)
	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isValid(5174, seeder.HallId, constants.Role7, &positionId)

	assert.Equal(t, errorx.BackstageDepartmentInfoNotFoundModify, err)
}

func TestUpdateSubAccountLogic_isValid_GetPositionInfo_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", ctx, mockPositionInfoRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	positionId := uint32(5164)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isValid(5174, seeder.HallId, constants.Role7, &positionId)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_isPositionBySameParent(t *testing.T) {
	mockPositionInfoResponse := &agentclient.GetPositionInfoResponse{
		PositionInfo: []*agentclient.PositionInfo{
			{
				Id:          1,
				HallId:      3820325,
				ParameterId: 5164,
			},
		},
	}

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isPositionBySameParent(mockPositionInfoResponse, seeder.HallId)

	assert.False(t, err)
}

func TestUpdateSubAccountLogic_isValid_BackstagePositionInfoNotFoundModify(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", ctx, mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", ctx, mockPositionInfoRPCRequest).Return(&seeder.GetPositionInfo, nil)

	svcCtx.AgentCtx = mockAgentRPC

	positionId := uint32(5164)
	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.isValid(5174, seeder.HallId, constants.Role7, &positionId)

	assert.Equal(t, errorx.BackstagePositionInfoNotFoundModify, err)
}

func TestUpdateSubAccountLogic_processUserInfo_CheckUsernameUnique_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	editUserRequest, log, parent, err := l.processUserInfo(seeder.SubUsers.GetUsers()[0], "gtptest", "gtptest")

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, editUserRequest)
	assert.Nil(t, log)
	assert.Nil(t, parent)
}

func TestUpdateSubAccountLogic_processUserInfo_BackstageUserIsExistPut(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: false,
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(mockUniqueResponse, nil)

	svcCtx.UserCtx = mockUserRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	editUserRequest, log, parent, err := l.processUserInfo(seeder.SubUsers.GetUsers()[0], "gtptest", "gtptest")

	assert.Equal(t, errorx.BackstageUserIsExistPut, err)
	assert.Nil(t, editUserRequest)
	assert.Nil(t, log)
	assert.Nil(t, parent)
}

func TestUpdateSubAccountLogic_processUserInfo_BackstageUsernameIsInvalidPut(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtp",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: true,
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(mockUniqueResponse, nil)

	svcCtx.UserCtx = mockUserRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	editUserRequest, log, parent, err := l.processUserInfo(seeder.SubUsers.GetUsers()[0], "gtp", "gtp")

	assert.Equal(t, errorx.BackstageUsernameIsInvalidPut, err)
	assert.Nil(t, editUserRequest)
	assert.Nil(t, log)
	assert.Nil(t, parent)
}

func TestUpdateSubAccountLogic_processUserInfo_BackstageAliasIsInvalidPut(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: true,
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(mockUniqueResponse, nil)

	svcCtx.UserCtx = mockUserRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	editUserRequest, log, parent, err := l.processUserInfo(seeder.SubUsers.GetUsers()[0], "gtptest", "")

	assert.Equal(t, errorx.BackstageAliasIsInvalidPut, err)
	assert.Nil(t, editUserRequest)
	assert.Nil(t, log)
	assert.Nil(t, parent)
}

func TestUpdateSubAccountLogic_processUserInfo_GetUserByParentId_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: true,
	}
	mockUserRPC.On("CheckUsernameUnique", ctx, mockUniqueRPCRequest).Return(mockUniqueResponse, nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	editUserRequest, log, parent, err := l.processUserInfo(seeder.SubUsers.GetUsers()[0], "gtptest", "gtptest")

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, editUserRequest)
	assert.Nil(t, log)
	assert.Nil(t, parent)
}

func TestUpdateSubAccountLogic_checkUsernameLength_UsernameMaxTwelve(t *testing.T) {
	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp := l.checkUsernameLength("gtptest123456", constants.Role7)

	assert.False(t, resp)
}

func TestUpdateSubAccountLogic_checkUsernameLength_UsernameMinFive(t *testing.T) {
	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	resp := l.checkUsernameLength("test", constants.Role5)

	assert.False(t, resp)
}

func TestUpdateSubAccountLogic_putUserData(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", ctx, mockEditRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "upperlevelid",
			Value: "R_hall",
		},
		{
			Key:   "upperloginname",
			Value: seeder.User.GetUsername(),
		},
		{
			Key:   "levelid",
			Value: "R_hallsub",
		},
		{
			Key:   "loginname",
			Value: "gtptest",
		},
		{
			Key:   "typeloginname",
			Value: "R_type_loginname",
		},
		{
			Key:   "chg_loginname",
			Value: "R_loginname",
		},
		{
			Key:   "old_loginname",
			Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
		},
		{
			Key:   "new_loginname",
			Value: "gtptest",
		},
		{
			Key:   "typenickname",
			Value: "R_type_nickname",
		},
		{
			Key:   "chg_nickname",
			Value: "R_nickname",
		},
		{
			Key:   "old_nickname",
			Value: seeder.SubUsers.GetUsers()[0].GetAlias(),
		},
		{
			Key:   "new_nickname",
			Value: "gtptest",
		},
		{
			Key:   "typepassword",
			Value: "",
		},
		{
			Key:   "chg_password",
			Value: "",
		},
		{
			Key:   "old_password",
			Value: "",
		},
		{
			Key:   "new_password",
			Value: "",
		},
	}
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     2,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_all_usersubedit",
		UserKey:      "R_all_usersubedit",
		XMLDataMsg:   xmlDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split("", "?")[0],
		Act:       "change",
		ActionSql: "",
		Content:   "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />",
		Bywho:     "admin",
		Byfile:    strings.Split("", "?")[0],
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", ctx, mockLogRPCRequest).Return(nil)

	svcCtx.UserCtx = mockUserRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserData(mockEditRPCRequest, seeder.SubUsers.GetUsers()[0], &seeder.User, adminOperator, "gtptest", "gtptest", "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />")

	assert.NoError(t, err)
}

func TestUpdateSubAccountLogic_putUserData_EditUser_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", ctx, mockEditRPCRequest).Return(errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserData(mockEditRPCRequest, seeder.SubUsers.GetUsers()[0], &seeder.User, adminOperator, "gtptest", "gtptest", "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />")

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserData_CreateAGRecord_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", ctx, mockEditRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "upperlevelid",
			Value: "R_hall",
		},
		{
			Key:   "upperloginname",
			Value: seeder.User.GetUsername(),
		},
		{
			Key:   "levelid",
			Value: "R_hallsub",
		},
		{
			Key:   "loginname",
			Value: "gtptest",
		},
		{
			Key:   "typeloginname",
			Value: "R_type_loginname",
		},
		{
			Key:   "chg_loginname",
			Value: "R_loginname",
		},
		{
			Key:   "old_loginname",
			Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
		},
		{
			Key:   "new_loginname",
			Value: "gtptest",
		},
		{
			Key:   "typenickname",
			Value: "R_type_nickname",
		},
		{
			Key:   "chg_nickname",
			Value: "R_nickname",
		},
		{
			Key:   "old_nickname",
			Value: seeder.SubUsers.GetUsers()[0].GetAlias(),
		},
		{
			Key:   "new_nickname",
			Value: "gtptest",
		},
		{
			Key:   "typepassword",
			Value: "",
		},
		{
			Key:   "chg_password",
			Value: "",
		},
		{
			Key:   "old_password",
			Value: "",
		},
		{
			Key:   "new_password",
			Value: "",
		},
	}
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     2,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_all_usersubedit",
		UserKey:      "R_all_usersubedit",
		XMLDataMsg:   xmlDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserData(mockEditRPCRequest, seeder.SubUsers.GetUsers()[0], &seeder.User, adminOperator, "gtptest", "gtptest", "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />")

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserData_AddLogChange_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", ctx, mockEditRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "upperlevelid",
			Value: "R_hall",
		},
		{
			Key:   "upperloginname",
			Value: seeder.User.GetUsername(),
		},
		{
			Key:   "levelid",
			Value: "R_hallsub",
		},
		{
			Key:   "loginname",
			Value: "gtptest",
		},
		{
			Key:   "typeloginname",
			Value: "R_type_loginname",
		},
		{
			Key:   "chg_loginname",
			Value: "R_loginname",
		},
		{
			Key:   "old_loginname",
			Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
		},
		{
			Key:   "new_loginname",
			Value: "gtptest",
		},
		{
			Key:   "typenickname",
			Value: "R_type_nickname",
		},
		{
			Key:   "chg_nickname",
			Value: "R_nickname",
		},
		{
			Key:   "old_nickname",
			Value: seeder.SubUsers.GetUsers()[0].GetAlias(),
		},
		{
			Key:   "new_nickname",
			Value: "gtptest",
		},
		{
			Key:   "typepassword",
			Value: "",
		},
		{
			Key:   "chg_password",
			Value: "",
		},
		{
			Key:   "old_password",
			Value: "",
		},
		{
			Key:   "new_password",
			Value: "",
		},
	}
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     2,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_all_usersubedit",
		UserKey:      "R_all_usersubedit",
		XMLDataMsg:   xmlDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split("", "?")[0],
		Act:       "change",
		ActionSql: "",
		Content:   "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />",
		Bywho:     "admin",
		Byfile:    strings.Split("", "?")[0],
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", ctx, mockLogRPCRequest).Return(errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserData(mockEditRPCRequest, seeder.SubUsers.GetUsers()[0], &seeder.User, adminOperator, "gtptest", "gtptest", "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />")

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserDepartment(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5168},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5168
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5175},
	}
	mockParameterSetPositionResponse := &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			{
				Id:           5175,
				Type:         "position",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5168,
	}
	mockAgentRPC.On("UpdateUserDepartment", ctx, mockUpdateDepartmentRPCRequest).Return(nil)

	mockUpdatePositionRPCRequest := &agentclient.UpdateUserPositionRequest{
		UserId:     seeder.SubUserId,
		PositionId: 5175,
	}
	mockAgentRPC.On("UpdateUserPosition", ctx, mockUpdatePositionRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	updateDepartmentXMLDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "role",
			Value: "R_hall",
		},
		{
			Key:   "account",
			Value: "0318test1",
		},
		{
			Key:   "olddepartment",
			Value: "",
		},
		{
			Key:   "department",
			Value: "test",
		},
	}
	mockUpdateDepartmentAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     76,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_Set_Department",
		UserKey:      "R_Set_Department",
		XMLDataMsg:   updateDepartmentXMLDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockUpdateDepartmentAGRecordRPCRequest).Return(nil)

	updatePositionXMLDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "role",
			Value: "R_hall",
		},
		{
			Key:   "account",
			Value: "0318test1",
		},
		{
			Key:   "olddepartment",
			Value: "",
		},
		{
			Key:   "department",
			Value: "test",
		},
	}
	mockUpdatePositionAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     76,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_Set_Position",
		UserKey:      "",
		XMLDataMsg:   updatePositionXMLDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockUpdatePositionAGRecordRPCRequest).Return(nil)

	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.NoError(t, err)
}

func TestUpdateSubAccountLogic_putUserDepartment_GetDepartmentList_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserDepartment_GetParameterSet_GetDepartment_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5168},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserDepartment_GetParameterSet_GetPosition_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5168},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5168
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5175},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserDepartment_UpdateUserDepartment_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5168},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5168
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5175},
	}
	mockParameterSetPositionResponse := &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			{
				Id:           5175,
				Type:         "position",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5168,
	}
	mockAgentRPC.On("UpdateUserDepartment", ctx, mockUpdateDepartmentRPCRequest).Return(errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserDepartment_UpdateUserPosition_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5168},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5168
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5175},
	}
	mockParameterSetPositionResponse := &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			{
				Id:           5175,
				Type:         "position",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5168,
	}
	mockAgentRPC.On("UpdateUserDepartment", ctx, mockUpdateDepartmentRPCRequest).Return(nil)

	mockUpdatePositionRPCRequest := &agentclient.UpdateUserPositionRequest{
		UserId:     seeder.SubUserId,
		PositionId: 5175,
	}
	mockAgentRPC.On("UpdateUserPosition", ctx, mockUpdatePositionRPCRequest).Return(errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserDepartment_CreateAGRecord_UpdateDepartment_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5168},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5168
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5175},
	}
	mockParameterSetPositionResponse := &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			{
				Id:           5175,
				Type:         "position",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5168,
	}
	mockAgentRPC.On("UpdateUserDepartment", ctx, mockUpdateDepartmentRPCRequest).Return(nil)

	mockUpdatePositionRPCRequest := &agentclient.UpdateUserPositionRequest{
		UserId:     seeder.SubUserId,
		PositionId: 5175,
	}
	mockAgentRPC.On("UpdateUserPosition", ctx, mockUpdatePositionRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	updateDepartmentXMLDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "role",
			Value: "R_hall",
		},
		{
			Key:   "account",
			Value: "0318test1",
		},
		{
			Key:   "olddepartment",
			Value: "",
		},
		{
			Key:   "department",
			Value: "test",
		},
	}
	mockUpdateDepartmentAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     76,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_Set_Department",
		UserKey:      "R_Set_Department",
		XMLDataMsg:   updateDepartmentXMLDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockUpdateDepartmentAGRecordRPCRequest).Return(errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestUpdateSubAccountLogic_putUserDepartment_CreateAGRecord_UpdatePosition_ConnectionFailed(t *testing.T) {
	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5168
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5168, 5168},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5168
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5175},
	}
	mockParameterSetPositionResponse := &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			{
				Id:           5175,
				Type:         "position",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", ctx, mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5168,
	}
	mockAgentRPC.On("UpdateUserDepartment", ctx, mockUpdateDepartmentRPCRequest).Return(nil)

	mockUpdatePositionRPCRequest := &agentclient.UpdateUserPositionRequest{
		UserId:     seeder.SubUserId,
		PositionId: 5175,
	}
	mockAgentRPC.On("UpdateUserPosition", ctx, mockUpdatePositionRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	updatePositionXMLDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "role",
			Value: "R_hall",
		},
		{
			Key:   "account",
			Value: "0318test1",
		},
		{
			Key:   "olddepartment",
			Value: "test->",
		},
		{
			Key:   "department",
			Value: "test",
		},
	}
	mockUpdatePositionAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     76,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_Set_Position",
		UserKey:      "",
		XMLDataMsg:   updatePositionXMLDataMsg,
		URI:          "",
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockUpdatePositionAGRecordRPCRequest).Return(errorx.ConnectionFailed)

	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC

	departmentId := uint32(5168)
	positionId := uint32(5175)

	r = &http.Request{}
	l := NewUpdateSubAccountLogic(ctx, svcCtx, r)
	err := l.putUserDepartment(&departmentId, &positionId, seeder.SubUsers.GetUsers()[0], adminOperator)

	assert.Equal(t, errorx.ConnectionFailed, err)
}
