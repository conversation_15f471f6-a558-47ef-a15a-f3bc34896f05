package user

import (
	"context"
	"net/http"
	"regexp"
	"slices"
	"strings"

	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/operationrecord/operationrecordclient"
	"gbh/user/userclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateSubAccountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewUpdateSubAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *UpdateSubAccountLogic {
	return &UpdateSubAccountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *UpdateSubAccountLogic) UpdateSubAccount(req *types.UpdateSubAccountRequest) (*types.BaseResponse, error) {
	// 驗證admin session是否正確
	admin, err := operator.GetOperator(l.ctx)
	if err != nil {
		return nil, err
	}

	user, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	checkPermissionErr := l.checkPermission(user, admin.ID())
	if checkPermissionErr != nil {
		return nil, checkPermissionErr
	}

	// 廳主需帶職務
	if user.GetRole() == 7 && req.PositionID == nil {
		return nil, errorx.BackstagePositionErrorPut
	}

	isValidErr := l.isValid(req.DepartmentID, user.GetParent(), user.GetRole(), req.PositionID)
	if isValidErr != nil {
		return nil, isValidErr
	}

	changeData, log, parent, err := l.processUserInfo(user, req.Username, req.Alias)
	if err != nil {
		return nil, err
	}

	if changeData.GetUsername() != nil || changeData.GetAlias() != nil {
		// 修改研五
		changeData.Id = req.UserID
		err := l.putUserData(changeData, user, parent, admin, req.Username, req.Alias, *log)
		if err != nil {
			return nil, err
		}
	}

	// 套用職務單位
	putDepartmentErr := l.putUserDepartment(&req.DepartmentID, req.PositionID, user, admin)
	if putDepartmentErr != nil {
		return nil, putDepartmentErr
	}

	resp := &types.BaseResponse{
		Data: true,
	}

	return resp, nil
}

func (l *UpdateSubAccountLogic) checkPermission(user *userclient.GetResponse, adminId uint32) error {
	// 檢查該user"帳號列表功能開關"是否開啟
	op := types.UserInfo{
		Id:           user.GetId(),
		Domain:       user.GetDomain(),
		ParentId:     user.GetParent(),
		AllParentsId: user.GetAllParents(),
		Role:         user.GetRole(),
		IsSub:        user.GetSub(),
	}
	checkPerm, err := repository.CheckMenuPermission(l.ctx, l.svcCtx, op, constants.UserPermID1392, "enable")
	if err != nil {
		return err
	}

	if !checkPerm {
		return errorx.BackstageNoAccountActionSwitchPermPut
	}

	// 檢查開放部分管理公司
	checkErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, adminId, user.GetDomain())
	if checkErr != nil {
		return errorx.BackstageOperatorNoDomainPerm
	}

	return nil
}

func (l *UpdateSubAccountLogic) processUserInfo(user *userclient.GetResponse, username, alias string) (*userclient.EditUserRequest, *string, *userclient.GetResponse, error) {
	changeData := &userclient.EditUserRequest{}
	log := ""

	// 帳號
	fullUsername := l.getNamePrefix(user.GetRole(), username)
	if fullUsername != user.GetUsername() {
		// 檢查帳號是否被使用
		request := &userclient.CheckUsernameUniqueRequest{
			HallId:   user.GetDomain(),
			Username: fullUsername,
		}
		unique, err := l.svcCtx.UserCtx.CheckUsernameUnique(l.ctx, request)
		if err != nil {
			return nil, nil, nil, err
		}

		if !unique.GetUnique() {
			return nil, nil, nil, errorx.BackstageUserIsExistPut
		}

		// 檢查帳號規則
		checkName := l.checkUsernameLength(fullUsername, user.GetRole())
		if !checkName {
			return nil, nil, nil, errorx.BackstageUsernameIsInvalidPut
		}

		changeData.Username = &userclient.StringValue{
			Value: username,
		}

		log = "帳號：" + user.GetUsername() + "→" + username + "<br />"
	}

	// 暱稱
	if alias != user.GetAlias() {
		// 避免名稱為空
		if alias == "" {
			return nil, nil, nil, errorx.BackstageAliasIsInvalidPut
		}

		changeData.Alias = &userclient.StringValue{
			Value: alias,
		}

		log += "名稱：" + user.GetAlias() + "→" + alias + "<br />"
	}

	parent, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, user.GetParent())
	if err != nil {
		return nil, nil, nil, err
	}

	return changeData, &log, parent, nil
}

// 檢查是否有type為department or position的資料
func (l *UpdateSubAccountLogic) isInParamSet(paramSet *agentclient.GetParameterSetResponse, paramSetType string) bool {
	// 將department轉換為map
	paramSetMap := make([]string, len(paramSet.GetParameterSet()))
	for _, info := range paramSet.GetParameterSet() {
		paramSetMap = append(paramSetMap, info.GetType())
	}

	return slices.Contains(paramSetMap, paramSetType)
}

// 檢查上層帳號是否一致
func (l *UpdateSubAccountLogic) isDepartmentBySameParent(departmentInfo *agentclient.DepartmentInfoListResponse, parentId uint32) bool {
	for _, v := range departmentInfo.GetList() {
		if v.GetOwnerId() != parentId {
			return false
		}
	}

	return true
}

// 檢查上層帳號是否一致
func (l *UpdateSubAccountLogic) isPositionBySameParent(positionInfo *agentclient.GetPositionInfoResponse, parentId uint32) bool {
	for _, v := range positionInfo.GetPositionInfo() {
		if v.GetHallId() != parentId {
			return false
		}
	}

	return true
}

// 檢查部門和職位
func (l *UpdateSubAccountLogic) isValid(departmentId, parentId, userRole uint32, positionId *uint32) error {
	var ids []uint32
	ids = append(ids, departmentId)
	if positionId != nil {
		ids = append(ids, *positionId)
	}

	// 取得選取之單位和職務資訊
	paramSetRequest := &agentclient.GetParameterSetRequest{
		Id: ids,
	}
	paramSetList, err := l.svcCtx.AgentCtx.GetParameterSet(l.ctx, paramSetRequest)
	if err != nil {
		return err
	}

	departmentInfoRequest := agent.DepartmentInfoListRequest{
		ParameterID: departmentId,
	}
	departmentInfo, err := l.svcCtx.AgentCtx.DepartmentInfoList(l.ctx, departmentInfoRequest)
	if err != nil {
		return err
	}

	isDepartmentInParamSet := l.isInParamSet(paramSetList, "department")
	isDepartment := l.isDepartmentBySameParent(departmentInfo, parentId)
	if !isDepartmentInParamSet || !isDepartment {
		return errorx.BackstageDepartmentInfoNotFoundModify
	}

	positionInfoRequest := agent.GetPositionInfoRequest{}
	if positionId != nil {
		positionInfoRequest.ParameterID = *positionId
	}
	positionInfo, err := l.svcCtx.AgentCtx.GetPositionInfo(l.ctx, positionInfoRequest)
	if err != nil {
		return err
	}

	isPositionInParamSet := l.isInParamSet(paramSetList, "position")
	isPosition := l.isPositionBySameParent(positionInfo, parentId)
	if (userRole == constants.Role7 && !isPositionInParamSet) || (userRole == constants.Role7 && !isPosition) {
		return errorx.BackstagePositionInfoNotFoundModify
	}

	return nil
}

// 取得帳號前置代碼
func (l *UpdateSubAccountLogic) getNamePrefix(role uint32, username string) string {
	namePrefix := map[uint32]string{
		5: "a",
		4: "b",
		3: "c",
		2: "d",
		1: "",
	}

	if val, exists := namePrefix[role]; exists {
		username = val + username
	}

	return username
}

// 檢查帳號規則
func (l *UpdateSubAccountLogic) checkUsernameLength(username string, role uint32) bool {
	// 使用者帳號長度
	usernameLen := len(username)

	if usernameLen > constants.Twelve {
		return false
	}

	// 帳號最短字元限制
	if role == 1 || role == constants.Role7 {
		if usernameLen < constants.Four {
			return false
		}
	}
	if usernameLen < constants.Five {
		return false
	}

	re := regexp.MustCompile("^[a-z0-9]+$")
	return re.MatchString(username)
}

// 修改使用者資料
func (l *UpdateSubAccountLogic) putUserData(changeData *userclient.EditUserRequest, user, parent *userclient.GetResponse, op *operator.Operator, username, alias, log string) error {
	// 修改研五
	editUserErr := l.svcCtx.UserCtx.EditUser(l.ctx, changeData)
	if editUserErr != nil {
		return editUserErr
	}

	// 寫操作紀錄
	agRecordRequest := operationrecord.CreateAGRecordRequest{
		HallId:       user.GetDomain(),
		OperatorId:   op.ID(),
		LevelId:      0,
		ActionId:     constants.AGRecordActionID2,
		TargetId:     user.GetDomain(),
		TargetRoleId: user.GetRole(),
		BossKey:      "R_all_usersubedit",
		UserKey:      "R_all_usersubedit",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "upperlevelid",
				Value: coverRoleDict(parent.GetRole()),
			},
			{
				Key:   "upperloginname",
				Value: parent.GetUsername(),
			},
			{
				Key:   "levelid",
				Value: coverSubRoleDict(user.GetRole()),
			},
			{
				Key:   "loginname",
				Value: username,
			},
			{
				Key:   "typeloginname",
				Value: "R_type_loginname",
			},
			{
				Key:   "chg_loginname",
				Value: "R_loginname",
			},
			{
				Key:   "old_loginname",
				Value: user.GetUsername(),
			},
			{
				Key:   "new_loginname",
				Value: username,
			},
			{
				Key:   "typenickname",
				Value: "R_type_nickname",
			},
			{
				Key:   "chg_nickname",
				Value: "R_nickname",
			},
			{
				Key:   "old_nickname",
				Value: user.GetAlias(),
			},
			{
				Key:   "new_nickname",
				Value: alias,
			},
			{
				Key:   "typepassword",
				Value: "",
			},
			{
				Key:   "chg_password",
				Value: "",
			},
			{
				Key:   "old_password",
				Value: "",
			},
			{
				Key:   "new_password",
				Value: "",
			},
		},
		URI: l.r.RequestURI,
		IP:  op.IP(),
	}
	agRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, agRecordRequest)
	if agRecordErr != nil {
		return agRecordErr
	}

	// 寫記錄檔查詢
	logChangeRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    user.GetDomain(),
		Applet:    strings.Split(l.r.RequestURI, "?")[0],
		Act:       "change",
		ActionSql: "",
		Content:   log,
		Bywho:     op.Username(),
		Byfile:    strings.Split(l.r.RequestURI, "?")[0],
		Ip:        op.IP(),
	}
	logChangeErr := l.svcCtx.OperationRecordCtx.AddLogChange(l.ctx, logChangeRequest)
	if logChangeErr != nil {
		return logChangeErr
	}

	return nil
}

// 更新使用者單位職務
func (l *UpdateSubAccountLogic) putUserDepartment(departmentId, positionId *uint32, user *userclient.GetResponse, op *operator.Operator) error {
	isModifyDepartment := false
	isModifyPosition := false
	var originDepartment, originPosition *agentclient.GetParameterSetResponse

	departmentRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{user.GetId()},
	}
	departmentList, err := l.svcCtx.AgentCtx.GetDepartmentList(l.ctx, departmentRequest)
	if err != nil {
		return err
	}

	// 存單位/職務資訊 (寫操作紀錄用)
	if departmentId != nil {
		paramSetRequest := &agentclient.GetParameterSetRequest{
			Id: []uint32{departmentList.GetList()[0].GetDepartmentParameterId(), *departmentId},
		}
		originDepartment, err = l.svcCtx.AgentCtx.GetParameterSet(l.ctx, paramSetRequest)
		if err != nil {
			return err
		}

		if departmentList.GetList()[0].GetDepartmentParameterId() != *departmentId {
			isModifyDepartment = true
		}
	}

	if positionId != nil {
		paramSetRequest := &agentclient.GetParameterSetRequest{
			Id: []uint32{departmentList.GetList()[0].GetPositionParameterId(), *positionId},
		}
		originPosition, err = l.svcCtx.AgentCtx.GetParameterSet(l.ctx, paramSetRequest)
		if err != nil {
			return err
		}

		if departmentList.GetList()[0].GetPositionParameterId() != *positionId {
			isModifyPosition = true
		}
	}

	updateDepartmentRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       user.GetId(),
		DepartmentId: *departmentId,
	}
	updateDepartmentErr := l.svcCtx.AgentCtx.UpdateUserDepartment(l.ctx, updateDepartmentRequest)
	if updateDepartmentErr != nil {
		return updateDepartmentErr
	}

	updatePositionRequest := &agentclient.UpdateUserPositionRequest{
		UserId: user.GetId(),
	}
	if positionId != nil {
		updatePositionRequest.PositionId = *positionId
	}
	updatePositionErr := l.svcCtx.AgentCtx.UpdateUserPosition(l.ctx, updatePositionRequest)
	if updatePositionErr != nil {
		return updatePositionErr
	}

	var oldDepartment, department string
	if departmentId != nil {
		for _, v := range originDepartment.GetParameterSet() {
			if v.GetId() == departmentList.GetList()[0].GetDepartmentParameterId() {
				oldDepartment = v.GetName()
				if v.GetName() != "" {
					oldDepartment = v.GetName() + "->"
				}
			}

			if v.GetId() == *departmentId {
				department = v.GetName()
			}
		}
	}

	// 寫操作紀錄
	if isModifyDepartment {
		agRecordRequest := operationrecord.CreateAGRecordRequest{
			HallId:       user.GetDomain(),
			OperatorId:   op.ID(),
			LevelId:      0,
			ActionId:     constants.AGRecordActionID76,
			TargetId:     user.GetDomain(),
			TargetRoleId: user.GetRole(),
			BossKey:      constants.AGRecordMainKey["boss"][21],
			UserKey:      constants.AGRecordMainKey["user"][21],
			XMLDataMsg: []*operationrecord.KeyValue{
				{
					Key:   "role",
					Value: coverRoleDict(user.GetRole()),
				},
				{
					Key:   "account",
					Value: user.GetUsername(),
				},
				{
					Key:   "olddepartment",
					Value: oldDepartment,
				},
				{
					Key:   "department",
					Value: department,
				},
			},
			URI: l.r.RequestURI,
			IP:  op.IP(),
		}
		agRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, agRecordRequest)
		if agRecordErr != nil {
			return agRecordErr
		}
	}

	if positionId != nil {
		for _, v := range originPosition.GetParameterSet() {
			if v.GetId() == departmentList.GetList()[0].GetPositionParameterId() {
				oldDepartment = v.GetName()
				if v.GetName() != "" {
					oldDepartment = v.GetName() + "->"
				}
			}

			if v.GetId() == *positionId {
				department = v.GetName()
			}
		}
	}

	if isModifyPosition {
		agRecordRequest := operationrecord.CreateAGRecordRequest{
			HallId:       user.GetDomain(),
			OperatorId:   op.ID(),
			LevelId:      0,
			ActionId:     constants.ActionId76,
			TargetId:     user.GetDomain(),
			TargetRoleId: user.GetRole(),
			BossKey:      constants.AGRecordMainKey["boss"][22],
			UserKey:      constants.AGRecordMainKey["user"][0],
			XMLDataMsg: []*operationrecord.KeyValue{
				{
					Key:   "role",
					Value: coverRoleDict(user.GetRole()),
				},
				{
					Key:   "account",
					Value: user.GetUsername(),
				},
				{
					Key:   "olddepartment",
					Value: oldDepartment,
				},
				{
					Key:   "department",
					Value: department,
				},
			},
			URI: l.r.RequestURI,
			IP:  op.IP(),
		}
		agRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, agRecordRequest)
		if agRecordErr != nil {
			return agRecordErr
		}
	}

	return nil
}
