package user

import (
	"context"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/proto/user"
	"gbh/user/userclient"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestSubAccountListLogic_SubAccountList(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(&seeder.SubUsers, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 1,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_BackstageRoleIsError(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      1,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.BackstageRoleIsError, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_SubAccountList_BackstageSortError(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
		Sort:      "id",
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.BackstageSortError, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_SubAccountList_BackstageLastLoginError(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC

	lastLoginDays := uint32(1)
	request := &types.SubAccountListRequest{
		HallID:        seeder.HallId,
		Page:          1,
		PageLimit:     10,
		Role:          7,
		LastLoginDays: &lastLoginDays,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.BackstageLastLoginError, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_SubAccountList_BackstageLastLoginNotFilter(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC

	lastLoginDays := uint32(7)
	departmentId := uint32(5514)
	request := &types.SubAccountListRequest{
		HallID:        seeder.HallId,
		Page:          1,
		PageLimit:     10,
		Role:          7,
		DepartmentID:  &departmentId,
		LastLoginDays: &lastLoginDays,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.BackstageLastLoginNotFilter, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_SubAccountList_WithSort_WithOrder(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(&seeder.SubUsers, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
		Sort:      "username",
		Order:     constants.ASC,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 1,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithUsername_WithFuzzy(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		Username: &userclient.StringValue{
			Value: "%test%",
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(&seeder.SubUsers, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	fuzzy := true
	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
		Username:  "test",
		Fuzzy:     &fuzzy,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 1,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithBlock(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		Block: &userclient.BoolValue{
			Value: false,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(&seeder.SubUsers, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	block := false
	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
		Block:     &block,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 1,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithLastLoginDays(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUsersResponse := &seeder.SubUsers
	now := carbon.Now(constants.TimezoneGMT4).ToRfc3339String()
	mockUsersResponse = &user.GetUserListResponse{
		Users: []*user.GetResponse{
			mockUsersResponse.GetUsers()[0],
			{
				Id:               *********,
				Parent:           seeder.HallId,
				Alias:            "kuan",
				CreatedAt:        "2021-03-18T14:11:44+0800",
				Domain:           seeder.HallId,
				ModifiedAt:       "2021-03-18T14:11:44+0800",
				LastLogin:        carbon.Parse(now).SubDays(1).ToRfc3339String(),
				Currency:         "CNY",
				Role:             constants.Role7,
				PasswordReset:    false,
				PasswordExpireAt: "2021-04-17T14:11:44+0800",
				Username:         "kuan",
				Enable:           true,
				Block:            false,
				Bankrupt:         false,
				Sub:              true,
				Test:             false,
				HiddenTest:       false,
				PersonalEdit:     true,
				Size:             0,
				ErrNum:           0,
				Name:             "BGP API 測試廳",
				LoginCode:        "bgp",
				LastLoginIp:      "**************",
				Lock:             false,
			},
		},
		Parents: []*user.GetResponse{
			{
				Id:               3820474,
				Alias:            "BGP API 我是名稱",
				CreatedAt:        "2021-02-04T17:09:45+08:00",
				Domain:           3820474,
				ModifiedAt:       "2024-09-26T11:31:02+08:00",
				LastLogin:        "2025-03-25T16:04:12+08:00",
				Currency:         "RMB",
				Role:             7,
				PasswordReset:    false,
				PasswordExpireAt: "2025-04-18T23:56:15+08:00",
				Username:         "bbinbgp",
				Enable:           true,
				Block:            false,
				Bankrupt:         false,
				Sub:              false,
				Test:             false,
				HiddenTest:       false,
				PersonalEdit:     true,
				Size:             696,
				ErrNum:           0,
			},
		},
		Pagination: &user.Pagination{
			CurrentPage: 1,
			PageLimit:   seeder.UserListPageLimit,
			Total:       2,
			TotalPage:   1,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockListRequest).Return(mockUsersResponse, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId, *********},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	userInfo = types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	lastLoginDays := uint32(7)
	request := &types.SubAccountListRequest{
		HallID:        seeder.HallId,
		Page:          1,
		PageLimit:     10,
		Role:          7,
		LastLoginDays: &lastLoginDays,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 1,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(&seeder.Users, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 1,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_getUserListByDepartReturnError(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(nil, errorx.ConnectionFailed)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.ErrorIs(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID_WithBlock(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(&seeder.Users, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	block := true
	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
		Block:        &block,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{},
		},
		Total: 0,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID_WithUsername_WithFuzzy(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(&seeder.Users, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	fuzzy := true
	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
		Username:     "abc",
		Fuzzy:        &fuzzy,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{},
		},
		Total: 0,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID_WithUsername(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(&seeder.Users, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
		Username:     "abc",
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{},
		},
		Total: 0,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithPositionID(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	positionID := uint32(5164)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		PositionParameterId: positionID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(&seeder.Users, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:     seeder.HallId,
		Page:       1,
		PageLimit:  10,
		Role:       7,
		PositionID: &positionID,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 1,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID_WithSortUsernameAsc(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUsersResponse := &seeder.Users
	mockUsersResponse = &userclient.GetUsersResponse{
		Users: []*userclient.GetResponse{
			mockUsersResponse.GetUsers()[0],
			{
				Id:               *********,
				Parent:           seeder.HallId,
				Alias:            "kuan",
				CreatedAt:        "2021-03-18T14:11:44+0800",
				Domain:           seeder.HallId,
				ModifiedAt:       "2021-03-18T14:11:44+0800",
				LastLogin:        "",
				Currency:         "CNY",
				Role:             constants.Role7,
				PasswordReset:    false,
				PasswordExpireAt: "2021-04-17T14:11:44+0800",
				Username:         "kuan",
				Enable:           true,
				Block:            false,
				Bankrupt:         false,
				Sub:              true,
				Test:             false,
				HiddenTest:       false,
				PersonalEdit:     true,
				Size:             0,
				ErrNum:           0,
				Name:             "BGP API 測試廳",
				LoginCode:        "bgp",
				LastLoginIp:      "**************",
				Lock:             false,
			},
		},
		Parents: []*userclient.GetResponse{
			mockUsersResponse.GetParents()[0],
		},
	}
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(mockUsersResponse, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	userInfo = types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
		Sort:         "username",
		Order:        "asc",
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
				{
					ID:           *********,
					ParentID:     seeder.HallId,
					Username:     "kuan",
					Alias:        "kuan",
					Block:        false,
					CreatedAt:    "2021-03-18T02:11:44-04:00",
					LastLoginAt:  "",
					Role:         7,
					DepartmentID: 0,
					PositionID:   0,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 2,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID_WithSortCreatedAtAsc(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUsersResponse := &seeder.Users
	mockUsersResponse = &userclient.GetUsersResponse{
		Users: []*userclient.GetResponse{
			mockUsersResponse.GetUsers()[0],
			{
				Id:               *********,
				Parent:           seeder.HallId,
				Alias:            "kuan",
				CreatedAt:        "2021-03-18T14:11:44+0800",
				Domain:           seeder.HallId,
				ModifiedAt:       "2021-03-18T14:11:44+0800",
				LastLogin:        "",
				Currency:         "CNY",
				Role:             constants.Role7,
				PasswordReset:    false,
				PasswordExpireAt: "2021-04-17T14:11:44+0800",
				Username:         "kuan",
				Enable:           true,
				Block:            false,
				Bankrupt:         false,
				Sub:              true,
				Test:             false,
				HiddenTest:       false,
				PersonalEdit:     true,
				Size:             0,
				ErrNum:           0,
				Name:             "BGP API 測試廳",
				LoginCode:        "bgp",
				LastLoginIp:      "**************",
				Lock:             false,
			},
		},
		Parents: []*userclient.GetResponse{
			mockUsersResponse.GetParents()[0],
		},
	}
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(mockUsersResponse, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	userInfo = types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
		Sort:         "created_at",
		Order:        "asc",
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
				{
					ID:           *********,
					ParentID:     seeder.HallId,
					Username:     "kuan",
					Alias:        "kuan",
					Block:        false,
					CreatedAt:    "2021-03-18T02:11:44-04:00",
					LastLoginAt:  "",
					Role:         7,
					DepartmentID: 0,
					PositionID:   0,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 2,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID_WithSortLastLoginAtAsc(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUsersResponse := &seeder.Users
	mockUsersResponse = &userclient.GetUsersResponse{
		Users: []*userclient.GetResponse{
			mockUsersResponse.GetUsers()[0],
			{
				Id:               *********,
				Parent:           seeder.HallId,
				Alias:            "kuan",
				CreatedAt:        "2021-03-18T14:11:44+0800",
				Domain:           seeder.HallId,
				ModifiedAt:       "2021-03-18T14:11:44+0800",
				LastLogin:        "",
				Currency:         "CNY",
				Role:             constants.Role7,
				PasswordReset:    false,
				PasswordExpireAt: "2021-04-17T14:11:44+0800",
				Username:         "kuan",
				Enable:           true,
				Block:            false,
				Bankrupt:         false,
				Sub:              true,
				Test:             false,
				HiddenTest:       false,
				PersonalEdit:     true,
				Size:             0,
				ErrNum:           0,
				Name:             "BGP API 測試廳",
				LoginCode:        "bgp",
				LastLoginIp:      "**************",
				Lock:             false,
			},
		},
		Parents: []*userclient.GetResponse{
			mockUsersResponse.GetParents()[0],
		},
	}
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(mockUsersResponse, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	userInfo = types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
		Sort:         "last_login",
		Order:        "asc",
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{
				{
					ID:           *********,
					ParentID:     seeder.HallId,
					Username:     "kuan",
					Alias:        "kuan",
					Block:        false,
					CreatedAt:    "2021-03-18T02:11:44-04:00",
					LastLoginAt:  "",
					Role:         7,
					DepartmentID: 0,
					PositionID:   0,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
				{
					ID:           seeder.SubUserId,
					ParentID:     seeder.HallId,
					Username:     "0318test1",
					Alias:        "QA-3C",
					Block:        false,
					CreatedAt:    "2021-03-17T22:53:01-04:00",
					LastLoginAt:  "2024-10-30T04:07:14-04:00",
					Role:         7,
					DepartmentID: 5514,
					PositionID:   5164,
					ParentName:   "bbinbgp",
					ActionSwitch: true,
				},
			},
		},
		Total: 2,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_SubAccountList_WithDepartmentID_WithPage(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentID := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentID,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(&seeder.Users, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	userInfo = types.UserInfo{
		Id:       *********,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         2,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentID,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	expected := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: []types.SubAccountList{},
		},
		Total: 0,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestSubAccountListLogic_GetOperator_Error(t *testing.T) {
	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
	}

	l := NewSubAccountListLogic(context.Background(), svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_GetGMHallPrivilege_BackstageOperatorNoDomainPerm(t *testing.T) {
	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = mockHallRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_GetUserList_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.UserCtx = mockUserRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_GetDepartmentList_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockRequest).Return(&seeder.SubUsers, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_CheckMenuPermission_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", ctx, mockRequest).Return(&seeder.SubUsers, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(false, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:    seeder.HallId,
		Page:      1,
		PageLimit: 10,
		Role:      7,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_GetUsers_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(nil, errorx.ConnectionFailed)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentId := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentId,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentId,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestSubAccountListLogic_getUserListByDepart_CheckMenuPermission_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockUserRPC := mock.NewMockUserCtx()
	extraInfo := true
	mockUserRPC.On("GetUsers", ctx, []uint32{seeder.SubUserId}, &extraInfo).Return(&seeder.Users, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	departmentId := uint32(5514)
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		DepartmentParameterId: departmentId,
	}
	mockAgentRPC.On("GetDepartmentList", ctx, mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(1392), "enable").Return(false, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.HallCtx = mockHallRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.SubAccountListRequest{
		HallID:       seeder.HallId,
		Page:         1,
		PageLimit:    10,
		Role:         7,
		DepartmentID: &departmentId,
	}

	l := NewSubAccountListLogic(ctx, svcCtx)
	resp, err := l.SubAccountList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
