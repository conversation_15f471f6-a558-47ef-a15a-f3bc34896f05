package user

import (
	"context"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/backstage/internal/user"
	"gbh/errorx"
	"gbh/hall/hallclient"
	"testing"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func TestLoginRecord_GetOperatorError(t *testing.T) {
	req := types.LoginRecordRequest{
		UserID:    seeder.GTIHallId,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}

	l := NewLoginRecordLogic(context.Background(), svcCtx)
	resp, err := l.LoginRecord(&req)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestLoginRecord_GetUserByUserIdError(t *testing.T) {
	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.GTIHallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserCtx

	req := types.LoginRecordRequest{
		UserID:    seeder.GTIHallId,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}
	l := NewLoginRecordLogic(ctx, svcCtx)
	resp, err := l.LoginRecord(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestLoginRecord_CheckAdminPermissionForHallIdError(t *testing.T) {
	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.GTIHallId).Return(&seeder.User, nil)

	mockHallCtx := mock.NewMockHallCtx()

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&hallclient.GetHallByIdResponse{
		HallId: seeder.HallId,
		Enable: false,
	}, nil)

	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx

	req := types.LoginRecordRequest{
		UserID:    seeder.GTIHallId,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}
	l := NewLoginRecordLogic(ctx, svcCtx)
	resp, err := l.LoginRecord(&req)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestLoginRecord_GetLoginRecordError(t *testing.T) {
	req := types.LoginRecordRequest{
		UserID:    seeder.GTIHallId,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.GTIHallId).Return(&seeder.User, nil)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockRequest := user.GetLoginRecordRequest{
		StartTime: "2025-01-01T00:00:00-04:00",
		EndTime:   "2025-01-02T23:59:59-04:00",
		UserID:    req.UserID,
		HallID:    seeder.HallId,
		IP:        req.IP,
		Page:      req.Page,
		PageLimit: req.PageLimit,
	}

	mockUserCtx.On("GetLoginRecord", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx

	l := NewLoginRecordLogic(ctx, svcCtx)
	resp, err := l.LoginRecord(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestLoginRecord_GetLoginRecord(t *testing.T) {
	req := types.LoginRecordRequest{
		UserID:    seeder.GTIHallId,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.GTIHallId).Return(&seeder.User, nil)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil).Once()

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockRequest := user.GetLoginRecordRequest{
		StartTime: "2025-01-01T00:00:00-04:00",
		EndTime:   "2025-01-02T23:59:59-04:00",
		UserID:    req.UserID,
		HallID:    seeder.HallId,
		IP:        req.IP,
		Page:      req.Page,
		PageLimit: req.PageLimit,
	}

	mockUserCtx.On("GetLoginRecord", ctx, mockRequest).Return(&seeder.LoginRecord, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx

	l := NewLoginRecordLogic(ctx, svcCtx)
	resp, err := l.LoginRecord(&req)

	record := seeder.LoginRecord.GetLoginRecord()[0]

	expected := &types.BaseResponse{
		Code:    0,
		Message: "",
		Data: LoginRecordResponse{
			Record: []LoginRecordInfo{
				{
					Role:          record.GetRole(),
					Domain:        record.GetHallId(),
					Name:          seeder.GetHallById.GetName(),
					LoginTime:     carbon.Parse(record.GetAt(), constants.TimezoneGMT4).ToRfc3339String(),
					Username:      record.GetUsername(),
					IP:            record.GetIp(),
					Result:        record.GetResult(),
					Host:          record.GetHost(),
					Ingress:       record.GetIngress(),
					ClientOs:      record.GetClientOs(),
					Country:       record.GetCountry(),
					Region:        record.GetRegion(),
					City:          record.GetCity(),
					ClientBrowser: record.GetClientBrowser(),
					IsSlide:       record.GetIsSlide(),
				},
			},
			Pagination: types.PaginateResponse{
				Page:        seeder.LoginRecord.GetPagination().GetCurrentPage(),
				PageLimit:   seeder.LoginRecord.GetPagination().GetPageLimit(),
				TotalNumber: seeder.LoginRecord.GetPagination().GetTotal(),
				TotalPage:   seeder.LoginRecord.GetPagination().GetTotalPage(),
			},
		},
	}

	assert.Equal(t, expected, resp)
	assert.NoError(t, err)
}

func TestLoginRecord_GetHallByIdError(t *testing.T) {
	req := types.LoginRecordRequest{
		UserID:    seeder.GTIHallId,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.GTIHallId).Return(&seeder.User, nil)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil).Once()

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockRequest := user.GetLoginRecordRequest{
		StartTime: "2025-01-01T00:00:00-04:00",
		EndTime:   "2025-01-02T23:59:59-04:00",
		UserID:    req.UserID,
		HallID:    seeder.HallId,
		IP:        req.IP,
		Page:      req.Page,
		PageLimit: req.PageLimit,
	}

	mockUserCtx.On("GetLoginRecord", ctx, mockRequest).Return(&seeder.LoginRecord, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx

	l := NewLoginRecordLogic(ctx, svcCtx)
	resp, err := l.LoginRecord(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
