package user

import (
	"context"
	"net/http"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/proto/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type PutMemberPasswordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewPutMemberPasswordLogic(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *PutMemberPasswordLogic {
	return &PutMemberPasswordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *PutMemberPasswordLogic) PutMemberPassword(req *types.PutMemberPasswordRequest) (*types.BaseResponse, error) {
	admin, adminErr := operator.GetOperator(l.ctx)
	if adminErr != nil {
		return nil, adminErr
	}

	member, memberErr := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.UserID)
	if memberErr != nil {
		return nil, memberErr
	}
	if member.GetRole() != constants.MemberRole {
		return nil, errorx.BackstageRoleIsError
	}
	if req.Password == member.GetUsername() {
		return nil, errorx.BackstagePasswordWithNameIdentical
	}

	// 檢查開放部分管理公司
	checkErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), member.GetDomain())
	if checkErr != nil {
		return nil, errorx.BackstageOperatorNoDomainPerm
	}

	// 修改密碼的請求
	request := &user.SetPasswordRequest{
		UserId:      req.UserID,
		NewPassword: req.Password,
		RequestUrl:  req.RequestURI,
		ClientIp:    admin.IP(),
		Entrance:    constants.EntranceControl,
		OperatorId:  admin.ID(),
		Operator:    admin.Username(),
	}
	// 登入後修改密碼
	if req.PasswordReset != nil && *req.PasswordReset {
		request.PasswordReset = &user.BoolValue{Value: true}
	}

	err := l.svcCtx.UserCtx.SetPassword(l.ctx, request)
	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: true,
	}, nil
}
