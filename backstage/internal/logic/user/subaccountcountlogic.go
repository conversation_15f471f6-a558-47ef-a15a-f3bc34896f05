package user

import (
	"context"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/user/userclient"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type SubAccountCountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSubAccountCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubAccountCountLogic {
	return &SubAccountCountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubAccountCountLogic) SubAccountCount(req *types.SubAccountCountRequest) (*types.BaseResponse, error) {
	depth := getDepth(req.Role)

	// 要撈取的條件
	conditions := userclient.GetUserListRequest{
		ParentId: req.HallID,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: depth,
		},
		FetchFields: []string{"id", "last_login"},
	}

	if req.Username != "" {
		conditions.Username = &userclient.StringValue{
			Value: req.Username,
		}
		if req.Fuzzy != nil && *req.Fuzzy {
			conditions.Username = &userclient.StringValue{
				Value: "%" + req.Username + "%",
			}
		}
	}

	if req.Block != nil {
		conditions.Block = &userclient.BoolValue{
			Value: *req.Block,
		}
	}

	userList, err := l.svcCtx.UserCtx.GetUserList(l.ctx, &conditions)
	if err != nil {
		return nil, err
	}

	now := carbon.Now(constants.TimezoneGMT4)

	var sevenDaysCount uint32
	var thirtyDaysCount uint32
	for _, v := range userList.GetUsers() {
		lastLoginParse := carbon.Parse(v.GetLastLogin(), constants.TimezoneGMT4)
		if now.DiffAbsInDays(lastLoginParse) >= constants.SixDays {
			sevenDaysCount++
		}

		if now.DiffAbsInDays(lastLoginParse) >= constants.TwentyNineDays {
			thirtyDaysCount++
		}
	}

	resp := &types.BaseResponse{
		Data: types.SubAccountCount{
			All:        userList.GetPagination().GetTotal(),
			SevenDays:  sevenDaysCount,
			ThirtyDays: thirtyDaysCount,
		},
	}

	return resp, nil
}

func getDepth(role uint32) uint32 {
	depth := map[uint32]uint32{
		constants.Role7: constants.Depth1,
		constants.Role5: constants.Depth2,
		constants.Role4: constants.Depth3,
		constants.Role3: constants.Depth4,
		constants.Role2: constants.Depth5,
	}

	depthId := depth[role]

	return depthId
}
