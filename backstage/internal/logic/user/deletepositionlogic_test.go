package user

import (
	"fmt"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/hall/hallclient"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDeletePositionLogic(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)
	r.RequestURI = uri
	r.RemoteAddr = seeder.ClientIP

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", ctx, agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(&seeder.GetPositionInfo, nil)

	agentCtx.On("DeletePosition", ctx, seeder.PositionParameterID).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "parent",
			Value: "bbinbgp",
		},
		{
			Key:   "domain",
			Value: seeder.User.GetName(),
		},
		{
			Key:   "name",
			Value: "test",
		},
		{
			Key:   "note",
			Value: "test note",
		},
	}
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     seeder.AGRecordActionId,
		TargetId:     seeder.HallId,
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      seeder.AGRecordBossKey,
		UserKey:      "",
		XMLDataMsg:   xmlDataMsg,
		XMLCancelTag: false,
		URI:          seeder.RequestURI,
		IP:           seeder.ClientIP,
	}).Return(nil)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, &types.BaseResponse{
		Data: true,
	}, resp)
	assert.NoError(t, err)
}

func TestDeletePositionLogic_GetParameterSetErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeletePositionLogic_PositionNotFound(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", ctx, agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(&agentclient.GetPositionInfoResponse{}, nil)

	svcCtx.AgentCtx = agentCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, errorx.PositionNotFound, err)
	assert.Nil(t, resp)
}

func TestDeletePositionLogic_GetPositionInfoErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", ctx, agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeletePositionLogic_CheckAdminPermissionForHallIdErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", ctx, agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(&seeder.GetPositionInfo, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&hallclient.GetHallByIdResponse{
		HallId: seeder.HallId,
		Enable: false,
	}, nil)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestDeletePositionLogic_GetUserByUserIdErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", ctx, agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(&seeder.GetPositionInfo, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.UserCtx = userCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeletePositionLogic_DeletePositionErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", ctx, agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(&seeder.GetPositionInfo, nil)

	agentCtx.On("DeletePosition", ctx, seeder.PositionParameterID).Return(errorx.ConnectionFailed)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.UserCtx = userCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeletePositionLogic_CreateAGRecordErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID, query.Encode())

	r := httptest.NewRequest(http.MethodDelete, uri, http.NoBody)
	r.RequestURI = uri
	r.RemoteAddr = seeder.ClientIP

	request := &types.DeletePositionRequest{
		ParameterID: seeder.PositionParameterID,
		OperatorID:  seeder.AdminID,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", ctx, agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(&seeder.GetPositionInfo, nil)

	agentCtx.On("DeletePosition", ctx, seeder.PositionParameterID).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "parent",
			Value: "bbinbgp",
		},
		{
			Key:   "domain",
			Value: seeder.User.GetName(),
		},
		{
			Key:   "name",
			Value: "test",
		},
		{
			Key:   "note",
			Value: "test note",
		},
	}
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     seeder.AGRecordActionId,
		TargetId:     seeder.HallId,
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      seeder.AGRecordBossKey,
		UserKey:      "",
		XMLDataMsg:   xmlDataMsg,
		XMLCancelTag: false,
		URI:          seeder.RequestURI,
		IP:           seeder.ClientIP,
	}).Return(errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewDeletePositionLogic(ctx, svcCtx, r)
	resp, err := l.DeletePosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
