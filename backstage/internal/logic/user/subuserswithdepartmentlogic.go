package user

import (
	"context"

	"gbh/agent/agentclient"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/user/userclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubUsersWithDepartmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type SubUserWithDepartment struct {
	UserID                uint32 `json:"user_id"`
	Username              string `json:"username"`
	DepartmentParameterId uint32 `json:"department_parameter_id"`
	PositionParameterId   uint32 `json:"position_parameter_id"`
}

func NewSubUsersWithDepartmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubUsersWithDepartmentLogic {
	return &SubUsersWithDepartmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubUsersWithDepartmentLogic) SubUsersWithDepartment(req *types.GetSubUsersWithDepartmentRequest) (resp *types.BaseResponse, err error) {
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	parent, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.ParentID)

	if err != nil {
		return nil, err
	}

	err = repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), parent.GetDomain())

	if err != nil {
		return nil, err
	}

	subUsers, err := l.svcCtx.UserCtx.GetUserList(l.ctx, &userclient.GetUserListRequest{
		ParentId: parent.GetId(),
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Enable: &userclient.BoolValue{
			Value: true,
		},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		FetchFields: []string{"id", "username"},
	})

	if err != nil {
		return nil, err
	}

	totalUsers := len(subUsers.GetUsers())

	subUserIDs := make([]uint32, 0, totalUsers)

	subUsersWithDepartment := make([]SubUserWithDepartment, 0, totalUsers)

	for _, u := range subUsers.GetUsers() {
		subUserIDs = append(subUserIDs, u.GetId())
	}

	departmentList, err := l.svcCtx.AgentCtx.GetDepartmentList(l.ctx, &agentclient.DepartmentListRequest{
		UserId: subUserIDs,
	})

	if err != nil {
		return nil, err
	}

	for _, u := range subUsers.GetUsers() {
		subUserWithDepartment := SubUserWithDepartment{
			UserID:                u.GetId(),
			Username:              u.GetUsername(),
			DepartmentParameterId: 0,
			PositionParameterId:   0,
		}

		for _, d := range departmentList.GetList() {
			if u.GetId() == d.GetUserId() {
				subUserWithDepartment.DepartmentParameterId = d.GetDepartmentParameterId()
				subUserWithDepartment.PositionParameterId = d.GetPositionParameterId()
			}
		}

		subUsersWithDepartment = append(subUsersWithDepartment, subUserWithDepartment)
	}

	return &types.BaseResponse{
		Data: subUsersWithDepartment,
	}, nil
}
