package user

import (
	"context"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/backstage/internal/user"
	"gbh/user/userclient"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type LoginRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLoginRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginRecordLogic {
	return &LoginRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type LoginRecordResponse struct {
	Record     []LoginRecordInfo      `json:"record"`
	Pagination types.PaginateResponse `json:"pagination"`
}

type LoginRecordInfo struct {
	Role          uint32 `json:"role"`
	Domain        uint32 `json:"domain"`
	Name          string `json:"name"`
	LoginTime     string `json:"login_time"`
	Username      string `json:"username"`
	IP            string `json:"ip"`
	Result        uint32 `json:"result"`
	Host          string `json:"host"`
	Ingress       uint32 `json:"ingress"`
	ClientOs      string `json:"client_os"`
	Country       string `json:"country"`
	City          string `json:"city"`
	Region        string `json:"regin"`
	ClientBrowser string `json:"client_browser"`
	IsSlide       bool   `json:"is_slide"`
}

func (l *LoginRecordLogic) LoginRecord(req *types.LoginRecordRequest) (resp *types.BaseResponse, err error) {
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	userInfo, userInfoErr := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.UserID)
	if userInfoErr != nil {
		return nil, userInfoErr
	}

	privlegeErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), userInfo.GetDomain())
	if privlegeErr != nil {
		return nil, privlegeErr
	}

	startTime := carbon.Parse(req.StartDate, constants.TimezoneGMT4).StartOfDay().ToRfc3339String()
	endTime := carbon.Parse(req.EndDate, constants.TimezoneGMT4).EndOfDay().ToRfc3339String()

	loginRecord, loginRecordErr := l.svcCtx.UserCtx.GetLoginRecord(l.ctx, user.GetLoginRecordRequest{
		StartTime: startTime,
		EndTime:   endTime,
		UserID:    req.UserID,
		HallID:    userInfo.GetDomain(),
		IP:        req.IP,
		Page:      req.Page,
		PageLimit: req.PageLimit,
	})

	if loginRecordErr != nil {
		return nil, loginRecordErr
	}

	loginRecordInfo, loginRecordInfoErr := l.getRecordInfo(loginRecord.GetLoginRecord())
	if loginRecordInfoErr != nil {
		return nil, loginRecordInfoErr
	}

	return &types.BaseResponse{
		Data: LoginRecordResponse{
			Record: loginRecordInfo,
			Pagination: types.PaginateResponse{
				Page:        loginRecord.GetPagination().GetCurrentPage(),
				PageLimit:   loginRecord.GetPagination().GetPageLimit(),
				TotalNumber: loginRecord.GetPagination().GetTotal(),
				TotalPage:   loginRecord.GetPagination().GetTotalPage(),
			},
		},
	}, nil
}

func (l *LoginRecordLogic) getRecordInfo(loginRecord []*userclient.LoginRecordData) ([]LoginRecordInfo, error) {
	var hallName string
	if len(loginRecord) > 0 {
		hall, hallErr := l.svcCtx.HallCtx.GetHallById(l.ctx, loginRecord[0].GetHallId())
		if hallErr != nil {
			return nil, hallErr
		}
		hallName = hall.GetName()
	}

	loginRecordInfo := make([]LoginRecordInfo, 0, len(loginRecord))

	for _, v := range loginRecord {
		loginRecordInfo = append(loginRecordInfo, LoginRecordInfo{
			Role:          v.GetRole(),
			Domain:        v.GetHallId(),
			Name:          hallName,
			LoginTime:     carbon.Parse(v.GetAt(), constants.TimezoneGMT4).ToRfc3339String(),
			Username:      v.GetUsername(),
			IP:            v.GetIp(),
			Result:        v.GetResult(),
			Host:          v.GetHost(),
			Ingress:       v.GetIngress(),
			ClientOs:      v.GetClientOs(),
			Country:       v.GetCountry(),
			Region:        v.GetRegion(),
			City:          v.GetCity(),
			ClientBrowser: v.GetClientBrowser(),
			IsSlide:       v.GetIsSlide(),
		})
	}

	return loginRecordInfo, nil
}
