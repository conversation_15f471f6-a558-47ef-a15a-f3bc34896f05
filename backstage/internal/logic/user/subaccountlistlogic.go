package user

import (
	"context"
	"slices"
	"sort"
	"strings"

	"gbh/agent/agentclient"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/user/userclient"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type departmentRequest struct {
	Users        []*userclient.GetResponse
	DepartmentID *uint32
	PositionID   *uint32
}

type getUserListByDepartRequest struct {
	DepartmentID *uint32
	PositionID   *uint32
	Block        *bool
	Username     string
	Fuzzy        bool
	Sort         string
	Order        string
	Page         uint32
	PageLimit    uint32
}

type getUserListRequest struct {
	Role          uint32
	HallID        uint32
	Page          uint32
	PageLimit     uint32
	Username      string
	Fuzzy         *bool
	Block         *bool
	Sort          string
	Order         string
	LastLoginDays *uint32
}

type SubAccountListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSubAccountListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubAccountListLogic {
	return &SubAccountListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubAccountListLogic) SubAccountList(req *types.SubAccountListRequest) (*types.SubAccountListResponse, error) {
	admin, err := operator.GetOperator(l.ctx)
	if err != nil {
		return nil, err
	}

	// 檢查開放部分管理公司
	checkErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), req.HallID)
	if checkErr != nil {
		return nil, errorx.BackstageOperatorNoDomainPerm
	}

	// 會員不可搜
	if req.Role < constants.Role2 {
		return nil, errorx.BackstageRoleIsError
	}

	// 可排序的欄位錯誤
	sortList := []string{"username", "created_at", "last_login"}
	if req.Sort != "" && !slices.Contains(sortList, req.Sort) {
		return nil, errorx.BackstageSortError
	}

	// 未登入天數僅能帶7 || 30
	days := []uint32{7, 30}
	if req.LastLoginDays != nil && !slices.Contains(days, *req.LastLoginDays) {
		return nil, errorx.BackstageLastLoginError
	}

	// 有單位職務時，不能過濾未登入天數
	if (req.DepartmentID != nil || req.PositionID != nil) && req.LastLoginDays != nil {
		return nil, errorx.BackstageLastLoginNotFilter
	}

	userSort := "username"
	if req.Sort != "" {
		userSort = req.Sort
	}

	order := constants.ASC
	if req.Order != "" {
		order = strings.ToLower(req.Order)
	}

	var userList []types.SubAccountList
	var total *uint32
	var userListErr error
	// 根據單位職務取得使用者資料
	if req.DepartmentID != nil || req.PositionID != nil {
		var fuzzy bool
		if req.Fuzzy != nil && *req.Fuzzy {
			fuzzy = *req.Fuzzy
		}

		request := getUserListByDepartRequest{
			DepartmentID: req.DepartmentID,
			PositionID:   req.PositionID,
			Block:        req.Block,
			Username:     req.Username,
			Fuzzy:        fuzzy,
			Sort:         userSort,
			Order:        order,
			Page:         req.Page,
			PageLimit:    req.PageLimit,
		}
		userList, total, userListErr = l.getUserListByDepart(request)
		if userListErr != nil {
			return nil, userListErr
		}
	} else {
		request := getUserListRequest{
			Role:          req.Role,
			HallID:        req.HallID,
			Page:          req.Page,
			PageLimit:     req.PageLimit,
			Username:      req.Username,
			Fuzzy:         req.Fuzzy,
			Block:         req.Block,
			Sort:          userSort,
			Order:         order,
			LastLoginDays: req.LastLoginDays,
		}
		// 沒有過濾單位職務時，取得使用者資料(user list)
		userList, total, userListErr = l.getUserList(request)
		if userListErr != nil {
			return nil, userListErr
		}
	}

	resp := &types.SubAccountListResponse{
		BaseResponse: types.BaseResponse{
			Data: userList,
		},
		Total: *total,
	}

	return resp, nil
}

func (l *SubAccountListLogic) getDepartment(request departmentRequest) (map[uint32]*agentclient.Department, error) {
	userIDs := []uint32{}
	for _, v := range request.Users {
		userIDs = append(userIDs, v.GetId())
	}

	req := &agentclient.DepartmentListRequest{}
	if len(request.Users) > 0 {
		req.UserId = userIDs
	}
	if request.DepartmentID != nil {
		req.DepartmentParameterId = *request.DepartmentID
	}
	if request.PositionID != nil {
		req.PositionParameterId = *request.PositionID
	}
	departmentList, err := l.svcCtx.AgentCtx.GetDepartmentList(l.ctx, req)
	if err != nil {
		return nil, err
	}

	// 將department轉換為map
	departmentMap := make(map[uint32]*agentclient.Department)
	for _, info := range departmentList.GetList() {
		departmentMap[info.GetUserId()] = info
	}

	return departmentMap, nil
}

// 取得子帳號列表
func (l *SubAccountListLogic) getUserList(request getUserListRequest) ([]types.SubAccountList, *uint32, error) {
	// 取得子帳號相關資訊
	depth := getDepth(request.Role)
	// 要撈取的條件
	conditions := userclient.GetUserListRequest{
		ParentId: request.HallID,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: depth,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: request.Sort,
		},
		Order: &userclient.StringValue{
			Value: request.Order,
		},
		Page: &userclient.Uint32Value{
			Value: request.Page,
		},
		Limit: &userclient.Uint32Value{
			Value: request.PageLimit,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}

	if request.Username != "" {
		conditions.Username = &userclient.StringValue{
			Value: request.Username,
		}
		if request.Fuzzy != nil && *request.Fuzzy {
			conditions.Username = &userclient.StringValue{
				Value: "%" + request.Username + "%",
			}
		}
	}

	if request.Block != nil {
		conditions.Block = &userclient.BoolValue{
			Value: *request.Block,
		}
	}

	userList, err := l.svcCtx.UserCtx.GetUserList(l.ctx, &conditions)
	if err != nil {
		return nil, nil, err
	}

	// 將parent轉換為map
	parentList := make(map[uint32]*userclient.GetResponse)
	for _, user := range userList.GetParents() {
		parentList[user.GetId()] = user
	}

	// 取得單位職務
	departmentRequest := departmentRequest{
		Users: userList.GetUsers(),
	}
	departmentList, err := l.getDepartment(departmentRequest)
	if err != nil {
		return nil, nil, err
	}

	var count uint32
	resp := []types.SubAccountList{}
	for _, v := range userList.GetUsers() {
		var parentName string
		if _, exists := parentList[v.GetParent()]; exists {
			parentName = parentList[v.GetParent()].GetUsername()
		}

		var departmentId uint32
		var positionId uint32
		if _, exists := departmentList[v.GetId()]; exists {
			departmentId = departmentList[v.GetId()].GetDepartmentParameterId()
			positionId = departmentList[v.GetId()].GetPositionParameterId()
		}

		// 取使用者的權限：帳號列表功能開關
		op := types.UserInfo{
			Id:           v.GetId(),
			Domain:       v.GetDomain(),
			ParentId:     v.GetParent(),
			AllParentsId: v.GetAllParents(),
			Role:         v.GetRole(),
			IsSub:        v.GetSub(),
		}
		checkPerm, err := repository.CheckMenuPermission(l.ctx, l.svcCtx, op, constants.UserPermID1392, "enable")
		if err != nil {
			return nil, nil, err
		}

		if request.LastLoginDays != nil {
			now := carbon.Now(constants.TimezoneGMT4)

			lastLoginParse := carbon.Parse(v.GetLastLogin(), constants.TimezoneGMT4)
			if now.DiffAbsInDays(lastLoginParse) >= int64(*request.LastLoginDays) {
				count++
			} else {
				continue
			}
		}

		resp = append(resp, types.SubAccountList{
			ID:           v.GetId(),
			ParentID:     v.GetParent(),
			Username:     v.GetUsername(),
			Alias:        v.GetAlias(),
			Block:        v.GetBlock(),
			CreatedAt:    carbon.Parse(v.GetCreatedAt(), constants.TimezoneGMT4).ToRfc3339String(),
			LastLoginAt:  carbon.Parse(v.GetLastLogin(), constants.TimezoneGMT4).ToRfc3339String(),
			Role:         v.GetRole(),
			DepartmentID: departmentId,
			PositionID:   positionId,
			ParentName:   parentName,
			ActionSwitch: checkPerm,
		})
	}

	total := &userList.Pagination.Total
	if request.LastLoginDays != nil {
		total = &count
	}

	return resp, total, nil
}

// 取得子帳號列表(單位職務)
func (l *SubAccountListLogic) getUserListByDepart(request getUserListByDepartRequest) ([]types.SubAccountList, *uint32, error) {
	// 搜尋單位職務
	departmentRequest := departmentRequest{
		DepartmentID: request.DepartmentID,
		PositionID:   request.PositionID,
	}
	departmentList, err := l.getDepartment(departmentRequest)
	if err != nil {
		return nil, nil, err
	}

	userIDs := []uint32{}
	for _, v := range departmentList {
		userIDs = append(userIDs, v.GetUserId())
	}

	// 取得帳號相關資訊
	extraInfo := true
	userList, err := l.svcCtx.UserCtx.GetUsers(l.ctx, userIDs, &extraInfo)
	if err != nil {
		return nil, nil, err
	}

	// 將parent轉換為map
	parentMap := make(map[uint32]*userclient.GetResponse)
	for _, user := range userList.GetParents() {
		parentMap[user.GetId()] = user
	}

	subAccountList := []types.SubAccountList{}
	for _, v := range userList.GetUsers() {
		var parentName string
		if _, exists := parentMap[v.GetParent()]; exists {
			parentName = parentMap[v.GetParent()].GetUsername()
		}

		var departmentId uint32
		var positionId uint32
		if _, exists := departmentList[v.GetId()]; exists {
			departmentId = departmentList[v.GetId()].GetDepartmentParameterId()
			positionId = departmentList[v.GetId()].GetPositionParameterId()
		}

		// 過濾凍結狀態
		if request.Block != nil {
			if *request.Block && !v.GetBlock() {
				continue
			}
		}

		// 過濾username
		if request.Username != "" {
			// 模糊
			if request.Fuzzy && !strings.Contains(v.GetUsername(), request.Username) {
				continue
			}
			// 精準
			if !request.Fuzzy && v.GetUsername() != request.Username {
				continue
			}
		}

		// 取使用者的權限：帳號列表功能開關
		op := types.UserInfo{
			Id:           v.GetId(),
			Domain:       v.GetDomain(),
			ParentId:     v.GetParent(),
			AllParentsId: v.GetAllParents(),
			Role:         v.GetRole(),
			IsSub:        v.GetSub(),
		}
		checkPerm, err := repository.CheckMenuPermission(l.ctx, l.svcCtx, op, constants.UserPermID1392, "enable")
		if err != nil {
			return nil, nil, err
		}

		subAccountList = append(subAccountList, types.SubAccountList{
			ID:           v.GetId(),
			ParentID:     v.GetParent(),
			Username:     v.GetUsername(),
			Alias:        v.GetAlias(),
			Block:        v.GetBlock(),
			CreatedAt:    carbon.Parse(v.GetCreatedAt(), constants.TimezoneGMT4).ToRfc3339String(),
			LastLoginAt:  carbon.Parse(v.GetLastLogin(), constants.TimezoneGMT4).ToRfc3339String(),
			Role:         v.GetRole(),
			DepartmentID: departmentId,
			PositionID:   positionId,
			ParentName:   parentName,
			ActionSwitch: checkPerm,
		})
	}

	// 排序
	switch request.Sort {
	case "username":
		sort.Slice(subAccountList, func(i, j int) bool {
			return subAccountList[i].Username > subAccountList[j].Username
		})
		if request.Order == constants.ASC {
			sort.Slice(subAccountList, func(i, j int) bool {
				return subAccountList[i].Username < subAccountList[j].Username
			})
		}
	case "created_at":
		sort.Slice(subAccountList, func(i, j int) bool {
			return subAccountList[i].CreatedAt > subAccountList[j].CreatedAt
		})
		if request.Order == constants.ASC {
			sort.Slice(subAccountList, func(i, j int) bool {
				return subAccountList[i].CreatedAt < subAccountList[j].CreatedAt
			})
		}
	case "last_login":
		sort.Slice(subAccountList, func(i, j int) bool {
			return subAccountList[i].LastLoginAt > subAccountList[j].LastLoginAt
		})
		if request.Order == constants.ASC {
			sort.Slice(subAccountList, func(i, j int) bool {
				return subAccountList[i].LastLoginAt < subAccountList[j].LastLoginAt
			})
		}
	}

	// 取特定筆數資料
	resp := forPage(subAccountList, int(request.Page), int(request.PageLimit))

	count := uint32(len(resp))
	total := &count

	return resp, total, nil
}

// 取特定筆數資料
func forPage(data []types.SubAccountList, page, perPage int) []types.SubAccountList {
	// 計算 offset，避免小於 0
	var offset int
	if (page-1)*perPage > 0 {
		offset = (page - 1) * perPage
	}

	// 如果 offset 超過資料長度，回傳空結果
	if offset >= len(data) {
		return []types.SubAccountList{}
	}

	// 判斷是否超過資料長度
	end := offset + perPage
	if end > len(data) {
		end = len(data)
	}

	return data[offset:end]
}
