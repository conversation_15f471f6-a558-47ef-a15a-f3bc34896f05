package user

import (
	"context"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/proto/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type PutParentPasswordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPutParentPasswordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PutParentPasswordLogic {
	return &PutParentPasswordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PutParentPasswordLogic) PutParentPassword(req *types.PutParentPasswordRequest) (*types.BaseResponse, error) {
	admin, adminErr := operator.GetOperator(l.ctx)
	if adminErr != nil {
		return nil, adminErr
	}

	parent, parentErr := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.ParentID)
	if parentErr != nil {
		return nil, parentErr
	}
	if req.Password == parent.GetUsername() {
		return nil, errorx.BackstagePasswordWithNameIdentical
	}

	// 檢查該user的權限：帳號列表功能開關
	userCheckPerm := types.UserInfo{
		Id:           parent.GetId(),
		Domain:       parent.GetDomain(),
		ParentId:     parent.GetParent(),
		AllParentsId: parent.GetAllParents(),
		Role:         parent.GetRole(),
		IsSub:        parent.GetSub(),
	}
	userPerm, checkUserPermErr := repository.CheckMenuPermission(l.ctx, l.svcCtx, userCheckPerm, constants.UserPermID1392, "enable")
	if checkUserPermErr != nil {
		return nil, checkUserPermErr
	}
	if !userPerm {
		return nil, errorx.BackstageNoPermission
	}

	// 檢查開放部分管理公司
	checkErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), parent.GetDomain())
	if checkErr != nil {
		return nil, errorx.BackstageOperatorNoDomainPerm
	}

	// 修改密碼的請求
	request := &user.SetPasswordRequest{
		UserId:      req.ParentID,
		NewPassword: req.Password,
		RequestUrl:  req.RequestURI,
		ClientIp:    admin.IP(),
		Entrance:    constants.EntranceControl,
		OperatorId:  admin.ID(),
		Operator:    admin.Username(),
	}

	// 登入後修改密碼
	if req.PasswordReset != nil && *req.PasswordReset {
		request.PasswordReset = &user.BoolValue{Value: true}
	}

	err := l.svcCtx.UserCtx.SetPassword(l.ctx, request)
	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: true,
	}, nil
}
