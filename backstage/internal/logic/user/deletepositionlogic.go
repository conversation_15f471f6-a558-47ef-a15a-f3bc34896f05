package user

import (
	"context"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/utils/iputil"
	"html"
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeletePositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewDeletePositionLogic(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *DeletePositionLogic {
	return &DeletePositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *DeletePositionLogic) DeletePosition(req *types.DeletePositionRequest) (*types.BaseResponse, error) {
	parameterSetResp, err := l.svcCtx.AgentCtx.GetParameterSet(l.ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{req.ParameterID},
		Type: "position",
	})

	if err != nil {
		return nil, err
	}

	positionInfoResp, err := l.svcCtx.AgentCtx.GetPositionInfo(l.ctx, agent.GetPositionInfoRequest{
		ParameterID: req.ParameterID,
	})

	if err != nil {
		return nil, err
	}

	positionInfo := positionInfoResp.GetPositionInfo()
	if len(positionInfo) == 0 {
		return nil, errorx.PositionNotFound
	}

	hallId := positionInfo[0].GetHallId()

	err = repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, req.OperatorID, hallId)

	if err != nil {
		return nil, err
	}

	hallResp, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, hallId)

	if err != nil {
		return nil, err
	}

	err = l.svcCtx.AgentCtx.DeletePosition(l.ctx, req.ParameterID)

	if err != nil {
		return nil, err
	}

	var name string
	var note string
	if len(parameterSetResp.GetParameterSet()) > 0 {
		name = parameterSetResp.GetParameterSet()[0].GetName()
		note = parameterSetResp.GetParameterSet()[0].GetNote()
	}

	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "parent",
			Value: hallResp.GetUsername(),
		},
		{
			Key:   "domain",
			Value: hallResp.GetName(),
		},
		{
			Key:   "name",
			Value: html.EscapeString(name),
		},
		{
			Key:   "note",
			Value: html.EscapeString(note),
		},
	}

	ip := iputil.GetIPWithoutPort(l.r.RemoteAddr)

	createAGRecordRequest := operationrecord.CreateAGRecordRequest{
		HallId:       hallId,
		OperatorId:   req.OperatorID,
		ActionId:     constants.ActionId76,
		TargetId:     hallResp.GetId(),
		TargetRoleId: hallResp.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][20],
		UserKey:      "",
		XMLDataMsg:   xmlDataMsg,
		URI:          l.r.RequestURI,
		IP:           ip,
	}

	err = l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, createAGRecordRequest)

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: true,
	}, nil
}
