package user

import (
	"context"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/user/userclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubUsersWithDepartment_GetOperatorError(t *testing.T) {
	l := NewSubUsersWithDepartmentLogic(context.Background(), svcCtx)
	resp, err := l.SubUsersWithDepartment(&types.GetSubUsersWithDepartmentRequest{
		ParentID: seeder.HallId,
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.BackstageOperatorNotFound, err)
}

func TestSubUsersWithDepartment_GetUserByUserIdError(t *testing.T) {
	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserCtx

	l := NewSubUsersWithDepartmentLogic(ctx, svcCtx)
	resp, err := l.SubUsersWithDepartment(&types.GetSubUsersWithDepartmentRequest{
		ParentID: seeder.HallId,
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestSubUsersWithDepartment_CheckAdminPermissionForHallIdError(t *testing.T) {
	mockUserCtx := mock.NewMockUserCtx()
	mockHallCtx := mock.NewMockHallCtx()

	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx

	l := NewSubUsersWithDepartmentLogic(ctx, svcCtx)
	resp, err := l.SubUsersWithDepartment(&types.GetSubUsersWithDepartmentRequest{
		ParentID: seeder.HallId,
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestSubUsersWithDepartment_GetUserListError(t *testing.T) {
	mockAdminCtx := mock.NewMockAdminCtx()
	mockUserCtx := mock.NewMockUserCtx()
	mockHallCtx := mock.NewMockHallCtx()

	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).
		Return(&seeder.GMHallPrivilege, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	getUserListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Enable: &userclient.BoolValue{
			Value: true,
		},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		FetchFields: []string{"id", "username"},
	}

	mockUserCtx.On("GetUserList", ctx, getUserListRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx

	l := NewSubUsersWithDepartmentLogic(ctx, svcCtx)
	resp, err := l.SubUsersWithDepartment(&types.GetSubUsersWithDepartmentRequest{
		ParentID: seeder.HallId,
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestSubUsersWithDepartment_GetDepartmentListError(t *testing.T) {
	mockAdminCtx := mock.NewMockAdminCtx()
	mockUserCtx := mock.NewMockUserCtx()
	mockHallCtx := mock.NewMockHallCtx()
	mockAgentCtx := mock.NewMockAgentCtx()

	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).
		Return(&seeder.GMHallPrivilege, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	getUserListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Enable: &userclient.BoolValue{
			Value: true,
		},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		FetchFields: []string{"id", "username"},
	}

	mockUserCtx.On("GetUserList", ctx, getUserListRequest).Return(&seeder.SubUsers, nil)

	mockAgentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		UserId: []uint32{455798902},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx

	l := NewSubUsersWithDepartmentLogic(ctx, svcCtx)
	resp, err := l.SubUsersWithDepartment(&types.GetSubUsersWithDepartmentRequest{
		ParentID: seeder.HallId,
	})

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestSubUsersWithDepartment(t *testing.T) {
	mockAdminCtx := mock.NewMockAdminCtx()
	mockUserCtx := mock.NewMockUserCtx()
	mockHallCtx := mock.NewMockHallCtx()
	mockAgentCtx := mock.NewMockAgentCtx()

	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).
		Return(&seeder.GMHallPrivilege, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	getUserListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Enable: &userclient.BoolValue{
			Value: true,
		},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		FetchFields: []string{"id", "username"},
	}

	mockUserCtx.On("GetUserList", ctx, getUserListRequest).Return(&seeder.SubUsers, nil)

	mockAgentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		UserId: []uint32{455798902},
	}).Return(&seeder.AgentDepartmentList, nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx

	l := NewSubUsersWithDepartmentLogic(ctx, svcCtx)
	resp, err := l.SubUsersWithDepartment(&types.GetSubUsersWithDepartmentRequest{
		ParentID: seeder.HallId,
	})

	expected := &types.BaseResponse{
		Code:    0,
		Message: "",
		Data: []SubUserWithDepartment{
			{
				UserID:                seeder.SubUserId,
				Username:              seeder.SubUsers.GetUsers()[0].GetUsername(),
				DepartmentParameterId: seeder.AgentDepartmentID5514,
				PositionParameterId:   seeder.AgentPositionID5164,
			},
		},
	}

	assert.Equal(t, expected, resp)
	assert.NoError(t, err)
}
