package user

import (
	"context"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/operationrecord/operationrecordclient"
	"gbh/user/userclient"
	"gbh/utils/strutil"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

var content = constants.LogChangeUsername + seeder.SubUsers.GetUsers()[0].GetUsername() + "<br />ID：" + strutil.Uint32ToString(seeder.UserId) + "<br />名稱：" + seeder.SubUsers.GetUsers()[0].GetAlias()

func TestDeleteSubAccountLogic_DeleteSubAccount(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", ctx, mockDeleteRPCRequest).Return(nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})
	mockPermissionRPC.On("DeleteUserPermissionByUserID", ctx, seeder.UserId).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     3,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      constants.AGRecordMainKey["boss"][24],
		UserKey:      constants.AGRecordMainKey["user"][24],
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "upperlevelid",
				Value: "R_hall",
			},
			{
				Key:   "upperloginname",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "levelid",
				Value: "R_hallsub",
			},
			{
				Key:   "loginname",
				Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
			},
		},
		URI: "",
		IP:  seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split("", "?")[0],
		Act:       "del",
		ActionSql: "",
		Content:   content,
		Bywho:     "admin",
		Byfile:    strings.Split("", "?")[0],
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", ctx, mockLogRPCRequest).Return(nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockAgentRPC.On("DeleteSubAccountDepartment", ctx, seeder.UserId).Return(nil)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	expected := &types.BaseResponse{
		Data: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestDeleteSubAccountLogic_DeleteSubAccount_BackstageOnlyActionToSub(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(&seeder.User, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.HallId,
		Domain:   seeder.HallId,
		ParentId: 0,
		Role:     constants.Role7,
		IsSub:    false,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.BackstageOnlyActionToSub, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_DeleteSubAccount_checkUserPermission_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_GetOperator_BackstageOperatorNotFound(t *testing.T) {
	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(context.Background(), svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_GetUserByUserId_ConnectionFailed(t *testing.T) {
	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_checkUserPermission(t *testing.T) {
	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	svcCtx.PermissionCtx = mockPermissionRPC

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	err := l.checkUserPermission(seeder.SubUsers.GetUsers()[0], "enable")

	assert.NoError(t, err)
}

func TestDeleteSubAccountLogic_checkUserPermission_BackstageNoPermAction(t *testing.T) {
	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	err := l.checkUserPermission(seeder.SubUsers.GetUsers()[0], "test")

	assert.Equal(t, errorx.BackstageNoPermAction, err)
}

func TestDeleteSubAccountLogic_checkUserPermission_CheckMenuPermission_ConnectionFailed(t *testing.T) {
	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = mockPermissionRPC

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	err := l.checkUserPermission(seeder.SubUsers.GetUsers()[0], "enable")

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestDeleteSubAccountLogic_checkUserPermission_BackstageNoAccountActionSwitchPermDelete(t *testing.T) {
	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(false, nil)

	svcCtx.PermissionCtx = mockPermissionRPC

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	err := l.checkUserPermission(seeder.SubUsers.GetUsers()[0], "enable")

	assert.Equal(t, errorx.BackstageNoAccountActionSwitchPermDelete, err)
}

func TestDeleteSubAccountLogic_GetGMHallPrivilege_BackstageOperatorNoDomainPerm(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(nil, errorx.ConnectionFailed)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_DeleteUser_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", ctx, mockDeleteRPCRequest).Return(errorx.ConnectionFailed)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_DeleteUserPermissionByUserID_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", ctx, mockDeleteRPCRequest).Return(nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})
	mockPermissionRPC.On("DeleteUserPermissionByUserID", ctx, seeder.UserId).Return(errorx.ConnectionFailed)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_GetUserByParentId_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", ctx, mockDeleteRPCRequest).Return(nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})
	mockPermissionRPC.On("DeleteUserPermissionByUserID", ctx, seeder.UserId).Return(nil)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_CreateAGRecord_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", ctx, mockDeleteRPCRequest).Return(nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})
	mockPermissionRPC.On("DeleteUserPermissionByUserID", ctx, seeder.UserId).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     3,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      constants.AGRecordMainKey["boss"][24],
		UserKey:      constants.AGRecordMainKey["user"][24],
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "upperlevelid",
				Value: "R_hall",
			},
			{
				Key:   "upperloginname",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "levelid",
				Value: "R_hallsub",
			},
			{
				Key:   "loginname",
				Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
			},
		},
		URI: "",
		IP:  seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(errorx.ConnectionFailed)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_AddLogChange_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", ctx, mockDeleteRPCRequest).Return(nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})
	mockPermissionRPC.On("DeleteUserPermissionByUserID", ctx, seeder.UserId).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     3,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      constants.AGRecordMainKey["boss"][24],
		UserKey:      constants.AGRecordMainKey["user"][24],
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "upperlevelid",
				Value: "R_hall",
			},
			{
				Key:   "upperloginname",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "levelid",
				Value: "R_hallsub",
			},
			{
				Key:   "loginname",
				Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
			},
		},
		URI: "",
		IP:  seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    "",
		Act:       "del",
		ActionSql: "",
		Content:   content,
		Bywho:     "admin",
		Byfile:    "",
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", ctx, mockLogRPCRequest).Return(errorx.ConnectionFailed)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteSubAccountLogic_DeleteSubAccountDepartment_ConnectionFailed(t *testing.T) {
	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", ctx, mockDeleteRPCRequest).Return(nil)
	mockUserRPC.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})
	mockPermissionRPC.On("DeleteUserPermissionByUserID", ctx, seeder.UserId).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()

	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     3,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      constants.AGRecordMainKey["boss"][24],
		UserKey:      constants.AGRecordMainKey["user"][24],
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "upperlevelid",
				Value: "R_hall",
			},
			{
				Key:   "upperloginname",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "levelid",
				Value: "R_hallsub",
			},
			{
				Key:   "loginname",
				Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
			},
		},
		URI: "",
		IP:  seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", ctx, mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    "",
		Act:       "del",
		ActionSql: "",
		Content:   content,
		Bywho:     "admin",
		Byfile:    "",
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", ctx, mockLogRPCRequest).Return(nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockAgentRPC.On("DeleteSubAccountDepartment", ctx, seeder.UserId).Return(errorx.ConnectionFailed)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallCtx

	request := &types.DeleteSubAccountRequest{
		UserID: seeder.UserId,
	}

	l := NewDeleteSubAccountLogic(ctx, svcCtx, &http.Request{})
	resp, err := l.DeleteSubAccount(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
