package user

import (
	"context"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/user/userclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPutParentPassword_Get(t *testing.T) {
	passwordReset := seeder.BoolTrue
	req := types.PutParentPasswordRequest{
		ParentID:      seeder.AgentID,
		PasswordReset: &passwordReset,
		Password:      seeder.ParentPassword,
		RequestURI:    "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	setPassword := &userclient.SetPasswordRequest{
		UserId:        seeder.AgentID,
		NewPassword:   req.Password,
		PasswordReset: &userclient.BoolValue{Value: true},
		ClientIp:      seeder.ClientIP,
		Entrance:      constants.EntranceControl,
		OperatorId:    seeder.GetAdminSession.GetUser().GetId(),
		Operator:      seeder.GetAdminSession.GetUser().GetUsername(),
		RequestUrl:    req.RequestURI,
	}

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(&seeder.Agent, nil)
	mockUserRPC.On("SetPassword", ctx, setPassword).Return(nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.Agent.GetId(),
		Domain:       seeder.Agent.GetDomain(),
		ParentId:     seeder.Agent.GetParent(),
		AllParentsId: seeder.Agent.GetAllParents(),
		Role:         seeder.Agent.GetRole(),
		IsSub:        seeder.Agent.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	l := NewPutParentPasswordLogic(ctx, svcCtx)
	resp, err := l.PutParentPassword(&req)

	expected := &types.BaseResponse{
		Code:    0,
		Message: "",
		Data:    true,
	}

	assert.Equal(t, expected, resp)
	assert.NoError(t, err)
}

func TestPutParentPassword_GetOperator_Error(t *testing.T) {
	req := types.PutParentPasswordRequest{
		ParentID:   seeder.AgentID,
		Password:   seeder.ParentPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	l := NewPutParentPasswordLogic(context.Background(), svcCtx)
	_, err := l.PutParentPassword(&req)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
}

func TestPutParentPassword_Parent_ConnectionFailed(t *testing.T) {
	req := types.PutParentPasswordRequest{
		ParentID:   seeder.AgentID,
		Password:   seeder.ParentPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC

	l := NewPutParentPasswordLogic(ctx, svcCtx)
	_, err := l.PutParentPassword(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestPutParentPassword_NewPasswordWithParentNameIsTheSame(t *testing.T) {
	req := types.PutParentPasswordRequest{
		ParentID:   seeder.AgentID,
		Password:   seeder.AgentNameWithPasswordIsTheSame.GetUsername(),
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(&seeder.AgentNameWithPasswordIsTheSame, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.Agent.GetId(),
		Domain:       seeder.Agent.GetDomain(),
		ParentId:     seeder.Agent.GetParent(),
		AllParentsId: seeder.Agent.GetAllParents(),
		Role:         seeder.Agent.GetRole(),
		IsSub:        seeder.Agent.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(nil, errorx.ConnectionFailed)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	l := NewPutParentPasswordLogic(ctx, svcCtx)
	_, err := l.PutParentPassword(&req)

	assert.Equal(t, errorx.BackstagePasswordWithNameIdentical, err)
}

func TestPutParentPassword_CheckAdminPermission_ConnectionFailed(t *testing.T) {
	req := types.PutParentPasswordRequest{
		ParentID:   seeder.AgentID,
		Password:   seeder.ParentPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(&seeder.Agent, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.Agent.GetId(),
		Domain:       seeder.Agent.GetDomain(),
		ParentId:     seeder.Agent.GetParent(),
		AllParentsId: seeder.Agent.GetAllParents(),
		Role:         seeder.Agent.GetRole(),
		IsSub:        seeder.Agent.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(nil, errorx.ConnectionFailed)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	l := NewPutParentPasswordLogic(ctx, svcCtx)
	_, err := l.PutParentPassword(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestPutParentPassword_CheckAdminPermission_NoPermission(t *testing.T) {
	req := types.PutParentPasswordRequest{
		ParentID:   seeder.AgentID,
		Password:   seeder.ParentPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(&seeder.Agent, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.Agent.GetId(),
		Domain:       seeder.Agent.GetDomain(),
		ParentId:     seeder.Agent.GetParent(),
		AllParentsId: seeder.Agent.GetAllParents(),
		Role:         seeder.Agent.GetRole(),
		IsSub:        seeder.Agent.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(false, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	l := NewPutParentPasswordLogic(ctx, svcCtx)
	_, err := l.PutParentPassword(&req)

	assert.Equal(t, errorx.BackstageNoPermission, err)
}

func TestPutParentPassword_CheckMenuPermission_ConnectionFailed(t *testing.T) {
	req := types.PutParentPasswordRequest{
		ParentID:   seeder.AgentID,
		Password:   seeder.ParentPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(&seeder.Agent, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.Agent.GetId(),
		Domain:       seeder.Agent.GetDomain(),
		ParentId:     seeder.Agent.GetParent(),
		AllParentsId: seeder.Agent.GetAllParents(),
		Role:         seeder.Agent.GetRole(),
		IsSub:        seeder.Agent.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	l := NewPutParentPasswordLogic(ctx, svcCtx)
	_, err := l.PutParentPassword(&req)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
}

func TestPutParentPassword_GeSetPassword_Failed(t *testing.T) {
	req := types.PutParentPasswordRequest{
		ParentID:   seeder.AgentID,
		Password:   seeder.ParentPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	setPassword := &userclient.SetPasswordRequest{
		UserId:      seeder.AgentID,
		NewPassword: seeder.ParentPassword,
		ClientIp:    seeder.ClientIP,
		Entrance:    constants.EntranceControl,
		OperatorId:  seeder.GetAdminSession.GetUser().GetId(),
		Operator:    seeder.GetAdminSession.GetUser().GetUsername(),
		RequestUrl:  req.RequestURI,
	}

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(&seeder.Agent, nil)
	mockUserRPC.On("SetPassword", ctx, setPassword).Return(errorx.ConnectionFailed)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.Agent.GetId(),
		Domain:       seeder.Agent.GetDomain(),
		ParentId:     seeder.Agent.GetParent(),
		AllParentsId: seeder.Agent.GetAllParents(),
		Role:         seeder.Agent.GetRole(),
		IsSub:        seeder.Agent.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	l := NewPutParentPasswordLogic(ctx, svcCtx)
	_, err := l.PutParentPassword(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
}
