package user

import (
	"context"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/user/userclient"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPutMemberPassword_Get(t *testing.T) {
	passwordReset := seeder.BoolTrue
	req := types.PutMemberPasswordRequest{
		UserID:        seeder.UserId,
		PasswordReset: &passwordReset,
		Password:      seeder.MemberPassword,
		RequestURI:    "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetSession", ctx, seeder.Token, seeder.ClientIP).Return(&seeder.GetAdminSession, nil)
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	setPassword := &userclient.SetPasswordRequest{
		UserId:        req.UserID,
		NewPassword:   req.Password,
		PasswordReset: &userclient.BoolValue{Value: true},
		ClientIp:      seeder.ClientIP,
		Entrance:      constants.EntranceControl,
		OperatorId:    seeder.GetAdminSession.GetUser().GetId(),
		Operator:      seeder.GetAdminSession.GetUser().GetUsername(),
		RequestUrl:    req.RequestURI,
	}

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(&seeder.GetUserByGtitest, nil)
	mockUserRPC.On("SetPassword", ctx, setPassword).Return(nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.GetUserByGtitest.GetId(),
		Domain:       seeder.GetUserByGtitest.GetDomain(),
		ParentId:     seeder.GetUserByGtitest.GetParent(),
		AllParentsId: seeder.GetUserByGtitest.GetAllParents(),
		Role:         seeder.GetUserByGtitest.GetRole(),
		IsSub:        seeder.GetUserByGtitest.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	r = &http.Request{RemoteAddr: seeder.RemoteAddr}
	l := NewPutMemberPasswordLogic(ctx, svcCtx, r)
	resp, err := l.PutMemberPassword(&req)

	expected := &types.BaseResponse{
		Code:    0,
		Message: "",
		Data:    true,
	}

	assert.Equal(t, expected, resp)
	assert.NoError(t, err)
}

func TestPutMemberPassword_GetOperator_ConnectionFailed(t *testing.T) {
	req := types.PutMemberPasswordRequest{
		UserID:     seeder.UserId,
		Password:   seeder.MemberPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	l := NewPutMemberPasswordLogic(context.Background(), svcCtx, r)
	_, err := l.PutMemberPassword(&req)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
}

func TestPutMemberPassword_Member_ConnectionFailed(t *testing.T) {
	req := types.PutMemberPasswordRequest{
		UserID:     seeder.UserId,
		Password:   seeder.MemberPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetSession", ctx, seeder.Token, seeder.ClientIP).Return(&seeder.GetAdminSession, nil)
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC

	r = &http.Request{RemoteAddr: seeder.RemoteAddr}
	l := NewPutMemberPasswordLogic(ctx, svcCtx, r)
	_, err := l.PutMemberPassword(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestPutMemberPassword_Member_BackstageRoleIsError(t *testing.T) {
	req := types.PutMemberPasswordRequest{
		UserID:     seeder.AgentID,
		Password:   seeder.MemberPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetSession", ctx, seeder.Token, seeder.ClientIP).Return(&seeder.GetAdminSession, nil)
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.AgentID).Return(&seeder.Agent, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetId(),
		Domain:       seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetDomain(),
		ParentId:     seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetParent(),
		AllParentsId: seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetAllParents(),
		Role:         seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetRole(),
		IsSub:        seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(false, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	r = &http.Request{RemoteAddr: seeder.RemoteAddr}
	l := NewPutMemberPasswordLogic(ctx, svcCtx, r)
	_, err := l.PutMemberPassword(&req)

	assert.Equal(t, errorx.BackstageRoleIsError, err)
}

func TestPutMemberPassword_Member_BackstagePasswordWithNameIdentical(t *testing.T) {
	req := types.PutMemberPasswordRequest{
		UserID:     seeder.UserId,
		Password:   seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetUsername(),
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetSession", ctx, seeder.Token, seeder.ClientIP).Return(&seeder.GetAdminSession, nil)
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(&seeder.GetUserByGtitestNameWithPasswordIsTheSame, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetId(),
		Domain:       seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetDomain(),
		ParentId:     seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetParent(),
		AllParentsId: seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetAllParents(),
		Role:         seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetRole(),
		IsSub:        seeder.GetUserByGtitestNameWithPasswordIsTheSame.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(false, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	r = &http.Request{RemoteAddr: seeder.RemoteAddr}
	l := NewPutMemberPasswordLogic(ctx, svcCtx, r)
	_, err := l.PutMemberPassword(&req)

	assert.Equal(t, errorx.BackstagePasswordWithNameIdentical, err)
}

func TestPutMemberPassword_CheckMenuPermission_ConnectionFailed(t *testing.T) {
	req := types.PutMemberPasswordRequest{
		UserID:     seeder.UserId,
		Password:   seeder.MemberPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetSession", ctx, seeder.Token, seeder.ClientIP).Return(&seeder.GetAdminSession, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(&seeder.GetUserByGtitest, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.GetUserByGtitest.GetId(),
		Domain:       seeder.GetUserByGtitest.GetDomain(),
		ParentId:     seeder.GetUserByGtitest.GetParent(),
		AllParentsId: seeder.GetUserByGtitest.GetAllParents(),
		Role:         seeder.GetUserByGtitest.GetRole(),
		IsSub:        seeder.GetUserByGtitest.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	r = &http.Request{RemoteAddr: seeder.RemoteAddr}
	l := NewPutMemberPasswordLogic(ctx, svcCtx, r)
	_, err := l.PutMemberPassword(&req)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
}

func TestPutMemberPassword_GeSetPassword_Failed(t *testing.T) {
	req := types.PutMemberPasswordRequest{
		UserID:     seeder.UserId,
		Password:   seeder.MemberPassword,
		RequestURI: "user.rpc.SetPassword",
	}

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetSession", ctx, seeder.Token, seeder.ClientIP).Return(&seeder.GetAdminSession, nil)
	mockAdminRPC.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	setPassword := &userclient.SetPasswordRequest{
		UserId:      req.UserID,
		NewPassword: req.Password,
		ClientIp:    seeder.ClientIP,
		Entrance:    constants.EntranceControl,
		OperatorId:  seeder.GetAdminSession.GetUser().GetId(),
		Operator:    seeder.GetAdminSession.GetUser().GetUsername(),
		RequestUrl:  req.RequestURI,
	}

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", ctx, seeder.UserId).Return(&seeder.GetUserByGtitest, nil)
	mockUserRPC.On("SetPassword", ctx, setPassword).Return(errorx.ConnectionFailed)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.GetUserByGtitest.GetId(),
		Domain:       seeder.GetUserByGtitest.GetDomain(),
		ParentId:     seeder.GetUserByGtitest.GetParent(),
		AllParentsId: seeder.GetUserByGtitest.GetAllParents(),
		Role:         seeder.GetUserByGtitest.GetRole(),
		IsSub:        seeder.GetUserByGtitest.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	r = &http.Request{RemoteAddr: seeder.RemoteAddr}
	l := NewPutMemberPasswordLogic(ctx, svcCtx, r)
	_, err := l.PutMemberPassword(&req)

	assert.Equal(t, errorx.ConnectionFailed, err)
}
