package user

import (
	"context"
	"net/http"
	"slices"
	"strings"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/operationrecord/operationrecordclient"
	"gbh/user/userclient"
	"gbh/utils/strutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteSubAccountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewDeleteSubAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *DeleteSubAccountLogic {
	return &DeleteSubAccountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *DeleteSubAccountLogic) DeleteSubAccount(req *types.DeleteSubAccountRequest) (*types.BaseResponse, error) {
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	// 先判斷是否有此user
	user, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	checkUserPermErr := l.checkUserPermission(user, "enable")
	if checkUserPermErr != nil {
		return nil, checkUserPermErr
	}

	// 檢查開放部分管理公司
	checkAdminPermErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), user.GetDomain())
	if checkAdminPermErr != nil {
		return nil, errorx.BackstageOperatorNoDomainPerm
	}

	// 僅能刪子帳號
	if !user.GetSub() {
		return nil, errorx.BackstageOnlyActionToSub
	}

	// 刪除
	deleteUserRequest := &userclient.DeleteUserRequest{
		Id:           req.UserID,
		Operator:     admin.Username(),
		OperatorRole: 1,
		OperatorId:   admin.ID(),
	}
	deleteUserErr := l.svcCtx.UserCtx.DeleteUser(l.ctx, deleteUserRequest)
	if deleteUserErr != nil {
		return nil, deleteUserErr
	}

	// 廳主時刪除相關設定
	if user.GetRole() == constants.Role7 {
		err := l.svcCtx.PermissionCtx.DeleteUserPermissionByUserID(l.ctx, req.UserID)
		if err != nil {
			return nil, err
		}
	}

	// 取parent資訊
	parent, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, user.GetParent())
	if err != nil {
		return nil, err
	}

	// 寫操作紀錄
	agRecordRequest := operationrecord.CreateAGRecordRequest{
		HallId:       user.GetDomain(),
		OperatorId:   admin.ID(),
		LevelId:      0,
		ActionId:     constants.AGRecordActionID3,
		TargetId:     user.GetDomain(),
		TargetRoleId: user.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][24],
		UserKey:      constants.AGRecordMainKey["user"][24],
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "upperlevelid",
				Value: coverRoleDict(parent.GetRole()),
			},
			{
				Key:   "upperloginname",
				Value: parent.GetUsername(),
			},
			{
				Key:   "levelid",
				Value: coverSubRoleDict(user.GetRole()),
			},
			{
				Key:   "loginname",
				Value: user.GetUsername(),
			},
		},
		URI: l.r.RequestURI,
		IP:  admin.IP(),
	}
	agRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, agRecordRequest)
	if agRecordErr != nil {
		return nil, agRecordErr
	}

	// 寫記錄檔查詢
	logChangeRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    user.GetDomain(),
		Applet:    strings.Split(l.r.RequestURI, "?")[0],
		Act:       "del",
		ActionSql: "",
		Content:   constants.LogChangeUsername + user.GetUsername() + "<br />ID：" + strutil.Uint32ToString(req.UserID) + "<br />名稱：" + user.GetAlias(),
		Bywho:     admin.Username(),
		Byfile:    strings.Split(l.r.RequestURI, "?")[0],
		Ip:        admin.IP(),
	}
	logChangeErr := l.svcCtx.OperationRecordCtx.AddLogChange(l.ctx, logChangeRequest)
	if logChangeErr != nil {
		return nil, logChangeErr
	}

	// 刪除職務單位設定
	deleteDepartmentErr := l.svcCtx.AgentCtx.DeleteSubAccountDepartment(l.ctx, req.UserID)
	if deleteDepartmentErr != nil {
		return nil, deleteDepartmentErr
	}

	resp := &types.BaseResponse{
		Data: true,
	}

	return resp, nil
}

// 檢查權限
func (l *DeleteSubAccountLogic) checkUserPermission(user *userclient.GetResponse, permAction string) error {
	if !slices.Contains([]string{"enable", "modify"}, permAction) {
		return errorx.BackstageNoPermAction
	}

	// 檢查該user"帳號列表功能開關"是否開啟
	op := types.UserInfo{
		Id:           user.GetId(),
		Domain:       user.GetDomain(),
		ParentId:     user.GetParent(),
		AllParentsId: user.GetAllParents(),
		Role:         user.GetRole(),
		IsSub:        user.GetSub(),
	}
	checkPerm, err := repository.CheckMenuPermission(l.ctx, l.svcCtx, op, constants.UserPermID1392, permAction)
	if err != nil {
		return err
	}

	if !checkPerm {
		return errorx.BackstageNoAccountActionSwitchPermDelete
	}

	return nil
}

// 體系對應字典檔code
func coverRoleDict(role uint32) string {
	roleDict := map[uint32]string{
		7: "R_hall", // 廳主
		5: "R_sc",   // 大股東
		4: "R_co",   // 股東
		3: "R_sa",   // 總代理
		2: "R_ag",   // 代理
		1: "R_mem",  // 會員
	}

	dict := roleDict[role]

	return dict
}

// 子帳號體系對應字典檔code
func coverSubRoleDict(role uint32) string {
	roleDict := map[uint32]string{
		7: "R_hallsub", // 廳主
		5: "R_scsub",   // 大股東
		4: "R_cosub",   // 股東
		3: "R_sasub",   // 總代理
		2: "R_agsub",   // 代理
		1: "R_memsub",  // 會員
	}

	dict := roleDict[role]

	return dict
}
