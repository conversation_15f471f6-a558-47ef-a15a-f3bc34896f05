package lottery

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/lotterygame/lotterygameclient"
	"gbh/proto/game"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubWagersURL_GetWagersByID_ConnectionError(t *testing.T) {
	req := &types.LotterySubWagersURLRequest{
		WagersID: 9215031295,
		Lang:     "zh-tw",
	}

	mockRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 9215031295,
	}

	mockLotteryGame := mock.NewMockLotteryGameCtx()
	mockLotteryGame.On("GetWagersByID", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.LotteryGameCtx = mockLotteryGame

	l := NewSubWagersURLLogic(ctx, svcCtx)
	resp, err := l.SubWagersURL(req)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestSubWagersURL_GetSubWagersSubURL_ConnectionError(t *testing.T) {
	req := &types.LotterySubWagersURLRequest{
		WagersID: 9215031295,
		Lang:     "zh-tw",
	}

	wagersByIDRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 9215031295,
	}

	wagersByIDResponse := &lotterygameclient.GetWagersByIDResponse{
		Id:             9215031295,
		WagersTime:     "2025-01-01 00:00:00",
		Hierarchy:      0,
		Portal:         0,
		WagersType:     0,
		Platform:       0,
		Client:         0,
		GameId:         "BQSQ",
		UserId:         456112001,
		RoundDate:      "2025-01-01",
		RoundTime:      "2025-01-01 00:00:00",
		Commissionable: 3000,
		Currency:       "CNY",
		ExchangeRate:   1.000000,
		Result:         4,
		Payoff:         2880.0000,
		HallId:         3820474,
		RoundSerial:    "20250220-0896",
		ReferenceId:    "9215031295",
		SettledTime:    "2025-01-01 00:00:00",
		Rule:           "L034",
		DbCategory:     122,
		ModifiedDate:   "2025-01-01 00:00:00",
	}

	subWagersURLRequest := &lotterygameclient.SubWagersURLRequest{
		GameId:   "BQSQ",
		HallId:   3820474,
		UserId:   456112001,
		WagersId: 9215031295,
		Lang:     "zh-tw",
	}

	mockLotteryGame := mock.NewMockLotteryGameCtx()

	mockLotteryGame.On("GetWagersByID", ctx, wagersByIDRequest).Return(wagersByIDResponse, nil)

	mockLotteryGame.On("GetSubWagersSubURL", ctx, subWagersURLRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.LotteryGameCtx = mockLotteryGame

	l := NewSubWagersURLLogic(ctx, svcCtx)
	resp, err := l.SubWagersURL(req)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestSubWagersURL_GetGameURLList_ConnectionError(t *testing.T) {
	req := &types.LotterySubWagersURLRequest{
		WagersID: 9215031295,
		Lang:     "zh-tw",
	}

	wagersByIDRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 9215031295,
	}

	wagersByIDResponse := &lotterygameclient.GetWagersByIDResponse{
		Id:             9215031295,
		WagersTime:     "2025-01-01 00:00:00",
		Hierarchy:      0,
		Portal:         0,
		WagersType:     0,
		Platform:       0,
		Client:         0,
		GameId:         "BQSQ",
		UserId:         456112001,
		RoundDate:      "2025-01-01",
		RoundTime:      "2025-01-01 00:00:00",
		Commissionable: 3000,
		Currency:       "CNY",
		ExchangeRate:   1.000000,
		Result:         4,
		Payoff:         2880.0000,
		HallId:         3820474,
		RoundSerial:    "20250220-0896",
		ReferenceId:    "9215031295",
		SettledTime:    "2025-01-01 00:00:00",
		Rule:           "L034",
		DbCategory:     122,
		ModifiedDate:   "2025-01-01 00:00:00",
	}

	subWagersURLRequest := &lotterygameclient.SubWagersURLRequest{
		GameId:   "BQSQ",
		HallId:   3820474,
		UserId:   456112001,
		WagersId: 9215031295,
		Lang:     "zh-tw",
	}

	subWagersURLResponse := "https://mock.bblottery/sub_wager_surl"

	gameURLRequest := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	mockLotteryGame := mock.NewMockLotteryGameCtx()

	mockLotteryGame.On("GetWagersByID", ctx, wagersByIDRequest).Return(wagersByIDResponse, nil)

	mockLotteryGame.On("GetSubWagersSubURL", ctx, subWagersURLRequest).Return(subWagersURLResponse, nil)

	mockGame := mock.NewMockGameCtx()

	mockGame.On("GetGameURLList", ctx, gameURLRequest).Return([]string{}, errorx.ConnectionFailed)

	svcCtx.LotteryGameCtx = mockLotteryGame
	svcCtx.GameCtx = mockGame

	l := NewSubWagersURLLogic(ctx, svcCtx)
	resp, err := l.SubWagersURL(req)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestSubWagersURL_replaceHost_ParseError(t *testing.T) {
	req := &types.LotterySubWagersURLRequest{
		WagersID: 9215031295,
		Lang:     "zh-tw",
	}

	wagersByIDRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 9215031295,
	}

	wagersByIDResponse := &lotterygameclient.GetWagersByIDResponse{
		Id:             9215031295,
		WagersTime:     "2025-01-01 00:00:00",
		Hierarchy:      0,
		Portal:         0,
		WagersType:     0,
		Platform:       0,
		Client:         0,
		GameId:         "BQSQ",
		UserId:         456112001,
		RoundDate:      "2025-01-01",
		RoundTime:      "2025-01-01 00:00:00",
		Commissionable: 3000,
		Currency:       "CNY",
		ExchangeRate:   1.000000,
		Result:         4,
		Payoff:         2880.0000,
		HallId:         3820474,
		RoundSerial:    "20250220-0896",
		ReferenceId:    "9215031295",
		SettledTime:    "2025-01-01 00:00:00",
		Rule:           "L034",
		DbCategory:     122,
		ModifiedDate:   "2025-01-01 00:00:00",
	}

	subWagersURLRequest := &lotterygameclient.SubWagersURLRequest{
		GameId:   "BQSQ",
		HallId:   3820474,
		UserId:   456112001,
		WagersId: 9215031295,
		Lang:     "zh-tw",
	}

	subWagersURLResponse := "invalid_url"

	gameURLRequest := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	gameURLResponse := []string{
		"bgp.bblotodemo.net",
		"bgp.bblotodemo1.net",
	}

	mockLotteryGame := mock.NewMockLotteryGameCtx()

	mockLotteryGame.On("GetWagersByID", ctx, wagersByIDRequest).Return(wagersByIDResponse, nil)

	mockLotteryGame.On("GetSubWagersSubURL", ctx, subWagersURLRequest).Return(subWagersURLResponse, nil)

	mockGame := mock.NewMockGameCtx()

	mockGame.On("GetGameURLList", ctx, gameURLRequest).Return(gameURLResponse, nil)

	svcCtx.LotteryGameCtx = mockLotteryGame
	svcCtx.GameCtx = mockGame

	l := NewSubWagersURLLogic(ctx, svcCtx)
	resp, err := l.SubWagersURL(req)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.InvalidResponse, err)
}

func TestSubWagersURL(t *testing.T) {
	req := &types.LotterySubWagersURLRequest{
		WagersID: 9215031295,
		Lang:     "zh-tw",
	}

	wagersByIDRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 9215031295,
	}

	wagersByIDResponse := &lotterygameclient.GetWagersByIDResponse{
		Id:             9215031295,
		WagersTime:     "2025-01-01 00:00:00",
		Hierarchy:      0,
		Portal:         0,
		WagersType:     0,
		Platform:       0,
		Client:         0,
		GameId:         "BQSQ",
		UserId:         456112001,
		RoundDate:      "2025-01-01",
		RoundTime:      "2025-01-01 00:00:00",
		Commissionable: 3000,
		Currency:       "CNY",
		ExchangeRate:   1.000000,
		Result:         4,
		Payoff:         2880.0000,
		HallId:         3820474,
		RoundSerial:    "20250220-0896",
		ReferenceId:    "9215031295",
		SettledTime:    "2025-01-01 00:00:00",
		Rule:           "L034",
		DbCategory:     122,
		ModifiedDate:   "2025-01-01 00:00:00",
	}

	subWagersURLRequest := &lotterygameclient.SubWagersURLRequest{
		GameId:   "BQSQ",
		HallId:   3820474,
		UserId:   456112001,
		WagersId: 9215031295,
		Lang:     "zh-tw",
	}

	subWagersURLResponse := "https://mock.bblottery/sub_wager_surl"

	gameURLRequest := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	gameURLResponse := []string{
		"bgp.bblotodemo.net",
		"bgp.bblotodemo1.net",
	}

	mockLotteryGame := mock.NewMockLotteryGameCtx()

	mockLotteryGame.On("GetWagersByID", ctx, wagersByIDRequest).Return(wagersByIDResponse, nil)

	mockLotteryGame.On("GetSubWagersSubURL", ctx, subWagersURLRequest).Return(subWagersURLResponse, nil)

	mockGame := mock.NewMockGameCtx()

	mockGame.On("GetGameURLList", ctx, gameURLRequest).Return(gameURLResponse, nil)

	svcCtx.LotteryGameCtx = mockLotteryGame
	svcCtx.GameCtx = mockGame

	l := NewSubWagersURLLogic(ctx, svcCtx)
	resp, err := l.SubWagersURL(req)

	assert.Equal(t, &types.BaseResponse{
		Data: "https://bgp.bblotodemo.net/sub_wager_surl",
	}, resp)
	assert.NoError(t, err)
}
