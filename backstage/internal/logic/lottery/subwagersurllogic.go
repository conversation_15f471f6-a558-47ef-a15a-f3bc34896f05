package lottery

import (
	"context"
	"net/url"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/lotterygame/lotterygameclient"
	"gbh/proto/game"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubWagersURLLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSubWagersURLLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubWagersURLLogic {
	return &SubWagersURLLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubWagersURLLogic) SubWagersURL(req *types.LotterySubWagersURLRequest) (*types.BaseResponse, error) {
	wagers, err := l.svcCtx.LotteryGameCtx.GetWagersByID(l.ctx, &lotterygameclient.GetWagersByIDRequest{
		Id: req.WagersID,
	})

	if err != nil {
		return nil, err
	}

	subWagersURL, err := l.svcCtx.LotteryGameCtx.GetSubWagersSubURL(l.ctx, &lotterygameclient.SubWagersURLRequest{
		WagersId: wagers.GetId(),
		GameId:   wagers.GetGameId(),
		HallId:   wagers.GetHallId(),
		UserId:   wagers.GetUserId(),
		Lang:     req.Lang,
	})

	if err != nil {
		return nil, err
	}

	gameURLs, err := l.svcCtx.GameCtx.GetGameURLList(l.ctx, &game.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: wagers.GetHallId(),
		},
	})

	if err != nil {
		return nil, err
	}

	subWagersURLWithHost, err := replaceHost(subWagersURL, gameURLs[0])

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: subWagersURLWithHost,
	}, nil
}

func replaceHost(u string, newHost string) (string, error) {
	parsedURL, err := url.ParseRequestURI(u)

	if err != nil {
		return "", errorx.InvalidResponse
	}

	parsedURL.Host = newHost

	return parsedURL.String(), nil
}
