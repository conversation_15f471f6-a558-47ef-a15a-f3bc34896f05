package permission

import (
	"context"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetAdminVisitedHistory_Get(t *testing.T) {
	request := types.GetAdminVisitedHistoryRequest{}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminVisitedHistory", ctx, seeder.GetAdminSession.GetUser().GetId()).Return(seeder.AdminVisitedHistoryResponse.GetVisitedHistoryDetail(), nil)

	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetWeblateLang", ctx, constants.ZhCn, "layout").Return(&seeder.GetWeblateLayoutZhCn, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.LangCtx = langCtx

	l := NewGetAdminVisitedHistoryLogic(ctx, svcCtx)
	resp, err := l.GetAdminVisitedHistory(&request)

	expectedResponse := &types.BaseResponse{
		Data: []adminVisitedHistoryData{
			{
				Id:          seeder.AdminPermID17,
				Dict:        "search_record",
				Translation: "纪录文件查询",
				IsAi:        true,
				IsNew:       false,
			},
			{
				Id:          seeder.AdminPermID22,
				Dict:        "temporary_password",
				Translation: "帳号临时密码",
				IsAi:        true,
				IsNew:       false,
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestGetAdminVisitedHistory_GetWithLang(t *testing.T) {
	request := types.GetAdminVisitedHistoryRequest{
		Lang: constants.ZhTw,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminVisitedHistory", ctx, seeder.GetAdminSession.GetUser().GetId()).Return(seeder.AdminVisitedHistoryResponse.GetVisitedHistoryDetail(), nil)

	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetWeblateLang", ctx, request.Lang, "layout").Return(&seeder.GetWeblateLayoutZhTw, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.LangCtx = langCtx

	l := NewGetAdminVisitedHistoryLogic(ctx, svcCtx)
	resp, err := l.GetAdminVisitedHistory(&request)

	expectedResponse := &types.BaseResponse{
		Data: []adminVisitedHistoryData{
			{
				Id:          seeder.AdminPermID17,
				Dict:        "search_record",
				Translation: "紀錄文件查詢",
				IsAi:        true,
				IsNew:       false,
			},
			{
				Id:          seeder.AdminPermID22,
				Dict:        "temporary_password",
				Translation: "帳號臨時密碼",
				IsAi:        true,
				IsNew:       false,
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestGetAdminVisitedHistory_GetOperatorError(t *testing.T) {
	request := types.GetAdminVisitedHistoryRequest{
		Lang: constants.ZhTw,
	}

	l := NewGetAdminVisitedHistoryLogic(context.Background(), svcCtx)
	resp, err := l.GetAdminVisitedHistory(&request)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestGetAdminVisitedHistory_GetAdminVisitedHistoryError(t *testing.T) {
	request := types.GetAdminVisitedHistoryRequest{
		Lang: constants.ZhTw,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminVisitedHistory", ctx, seeder.GetAdminSession.GetUser().GetId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewGetAdminVisitedHistoryLogic(ctx, svcCtx)
	resp, err := l.GetAdminVisitedHistory(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetAdminVisitedHistory_GetWeblateLangError(t *testing.T) {
	request := types.GetAdminVisitedHistoryRequest{
		Lang: constants.ZhTw,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminVisitedHistory", ctx, seeder.GetAdminSession.GetUser().GetId()).Return(seeder.AdminVisitedHistoryResponse.GetVisitedHistoryDetail(), nil)

	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetWeblateLang", ctx, request.Lang, "layout").Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.LangCtx = langCtx

	l := NewGetAdminVisitedHistoryLogic(ctx, svcCtx)
	resp, err := l.GetAdminVisitedHistory(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
