package permission

import (
	"context"
	"testing"

	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/permission/permissionclient"

	"github.com/stretchr/testify/assert"
)

func TestAdminControllerPermission_GetOperatorError(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	l := NewAdminControllerPermissionLogic(context.Background(), svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_AdminControllerPermissionIDsError(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return(nil, errorx.AdminControllerNotFound)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.AdminControllerNotFound, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_AdminControllerPermissionIDsIsEmpty(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return([]uint32{}, nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.BackstagePermissionDenied, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_AdminPermissionsError(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	controllerPermissionID := seeder.AdminControllerPermissionIDs.GetPermissionId()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return(controllerPermissionID, nil)

	enable := true
	adminPermissionsMockRequest := permission.AdminPermissionsRequest{
		AdminID:      []uint32{seeder.GetAdminSession.GetUser().GetId()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	}
	permissionCtx.On("AdminPermissions", ctx, adminPermissionsMockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_AdminPermissionsIsEmpty(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	controllerPermissionID := seeder.AdminControllerPermissionIDs.GetPermissionId()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return(controllerPermissionID, nil)

	enable := true
	adminPermissionsMockRequest := permission.AdminPermissionsRequest{
		AdminID:      []uint32{seeder.GetAdminSession.GetUser().GetId()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	}
	permissionCtx.On("AdminPermissions", ctx, adminPermissionsMockRequest).Return([]*permissionclient.AdminPermissionInfo{}, nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.BackstagePermissionDenied, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_AdminPermissionInfoListError(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	controllerPermissionID := seeder.AdminControllerPermissionIDs.GetPermissionId()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return(controllerPermissionID, nil)

	enable := true
	adminPermissionsMockRequest := permission.AdminPermissionsRequest{
		AdminID:      []uint32{seeder.GetAdminSession.GetUser().GetId()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	}
	adminPermissions := seeder.AdminPermissions.GetPermissionInfo()
	permissionCtx.On("AdminPermissions", ctx, adminPermissionsMockRequest).Return(adminPermissions, nil)

	permissionID := []uint32{}
	for _, value := range adminPermissions {
		permissionID = append(permissionID, value.GetPermissionId())
	}
	adminPermissionInfoListMockRequest := permission.AdminPermissionInfoListRequest{
		PermissionID: permissionID,
	}
	permissionCtx.On("AdminPermissionInfoList", ctx, adminPermissionInfoListMockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_AdminPermissionInfoListIsEmpty(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	controllerPermissionID := seeder.AdminControllerPermissionIDs.GetPermissionId()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return(controllerPermissionID, nil)

	enable := true
	adminPermissionsMockRequest := permission.AdminPermissionsRequest{
		AdminID:      []uint32{seeder.GetAdminSession.GetUser().GetId()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	}
	adminPermissions := seeder.AdminPermissions.GetPermissionInfo()
	permissionCtx.On("AdminPermissions", ctx, adminPermissionsMockRequest).Return(adminPermissions, nil)

	permissionID := []uint32{}
	for _, value := range adminPermissions {
		permissionID = append(permissionID, value.GetPermissionId())
	}
	adminPermissionInfoListMockRequest := permission.AdminPermissionInfoListRequest{
		PermissionID: permissionID,
	}
	permissionCtx.On("AdminPermissionInfoList", ctx, adminPermissionInfoListMockRequest).Return([]*permissionclient.PermissionListInfo{}, nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.BackstagePermissionDenied, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_PermissionDenied(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	controllerPermissionID := seeder.AdminControllerPermissionIDs.GetPermissionId()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return(controllerPermissionID, nil)

	enable := true
	adminPermissionsMockRequest := permission.AdminPermissionsRequest{
		AdminID:      []uint32{seeder.GetAdminSession.GetUser().GetId()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	}
	adminPermissions := seeder.AdminPermissions.GetPermissionInfo()
	permissionCtx.On("AdminPermissions", ctx, adminPermissionsMockRequest).Return(adminPermissions, nil)

	permissionID := []uint32{}
	for _, value := range adminPermissions {
		permissionID = append(permissionID, value.GetPermissionId())
	}
	adminPermissionInfoListMockRequest := permission.AdminPermissionInfoListRequest{
		PermissionID: permissionID,
	}
	AdminPermissionInfoList := []*permissionclient.PermissionListInfo{seeder.AdminPermissionInfoList.GetPermissionInfo()[1]}
	permissionCtx.On("AdminPermissionInfoList", ctx, adminPermissionInfoListMockRequest).Return(AdminPermissionInfoList, nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	assert.Equal(t, errorx.BackstagePermissionDisabled, err)
	assert.Nil(t, resp)
}

func TestAdminControllerPermission_PermissionOK(t *testing.T) {
	request := &types.AdminControllerPermissionRequest{
		ControllerName: seeder.AdminControllerName,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	controllerPermissionID := seeder.AdminControllerPermissionIDs.GetPermissionId()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, request.ControllerName).Return(controllerPermissionID, nil)

	enable := true
	adminPermissionsMockRequest := permission.AdminPermissionsRequest{
		AdminID:      []uint32{seeder.GetAdminSession.GetUser().GetId()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	}
	adminPermissions := seeder.AdminPermissions.GetPermissionInfo()
	permissionCtx.On("AdminPermissions", ctx, adminPermissionsMockRequest).Return(adminPermissions, nil)

	permissionID := []uint32{}
	for _, value := range adminPermissions {
		permissionID = append(permissionID, value.GetPermissionId())
	}
	adminPermissionInfoListMockRequest := permission.AdminPermissionInfoListRequest{
		PermissionID: permissionID,
	}
	permissionCtx.On("AdminPermissionInfoList", ctx, adminPermissionInfoListMockRequest).Return(seeder.AdminPermissionInfoList.GetPermissionInfo(), nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminControllerPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminControllerPermission(request)

	expectedResponse := &types.BaseResponse{}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
