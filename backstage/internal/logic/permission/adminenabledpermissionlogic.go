package permission

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminEnabledPermissionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminEnabledPermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminEnabledPermissionLogic {
	return &AdminEnabledPermissionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminEnabledPermissionLogic) AdminEnabledPermission() (*types.BaseResponse, error) {
	resp, err := l.svcCtx.PermissionCtx.GetAdminEnabledPermission(l.ctx)

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: resp,
	}, nil
}
