package permission

import (
	"context"
	"gbh/utils/maputil"
	"slices"
	"sort"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminSubAccountOpenablePermissionLogic struct {
	logx.Logger
	ctx              context.Context
	svcCtx           *svc.ServiceContext
	allPermConfigMap map[uint32]types.UserPermissionConfig // 所有權限配置的映射
}

func NewAdminSubAccountOpenablePermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminSubAccountOpenablePermissionLogic {
	return &AdminSubAccountOpenablePermissionLogic{
		Logger:           logx.WithContext(ctx),
		ctx:              ctx,
		svcCtx:           svcCtx,
		allPermConfigMap: make(map[uint32]types.UserPermissionConfig),
	}
}

type AllPermission struct {
	PermissionId uint32
	Name         string
	Enable       bool
	Modify       bool
}

func (l *AdminSubAccountOpenablePermissionLogic) AdminSubAccountOpenablePermission(req *types.AdminSubAccountOpenablePermissionRequest) (resp *types.BaseResponse, err error) {
	user, userErr := l.getUserInfo(req.UserId)
	if userErr != nil {
		return nil, userErr
	}

	privlegeErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, req.AdminId, user.Domain)
	if privlegeErr != nil {
		return nil, privlegeErr
	}

	allPermConfigMap, configErr := l.svcCtx.PermissionCtx.GetAllPermConfigMap(l.ctx)
	if configErr != nil {
		return nil, configErr
	}

	l.allPermConfigMap = allPermConfigMap

	permList, ppermListErr := l.getSubOpenablePermissions(*user)
	if ppermListErr != nil {
		return nil, ppermListErr
	}

	permCategory := []string{"menu"}
	if len(req.PermissionCategory) > 0 {
		permCategory = req.PermissionCategory
	}

	result := map[string][]types.UserPermissionConfig{}
	if slices.Contains(permCategory, "menu") {
		result["menu"] = manageMenu(permList)
	}

	if slices.Contains(permCategory, "special") {
		result["special"] = manageSpecial(permList)
	}

	if slices.Contains(permCategory, "general") {
		result["general"] = manageGeneral(permList)
	}

	return &types.BaseResponse{
		Data: result,
	}, nil
}

func (l *AdminSubAccountOpenablePermissionLogic) getUserInfo(userId uint32) (*types.UserInfo, error) {
	user, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, userId)

	if err != nil {
		return nil, err
	}

	return &types.UserInfo{
		Id:           user.GetId(),
		Domain:       user.GetDomain(),
		ParentId:     user.GetParent(),
		AllParentsId: user.GetAllParents(),
		Role:         user.GetRole(),
		IsSub:        user.GetSub(),
	}, nil
}

func (l *AdminSubAccountOpenablePermissionLogic) getRolePerms(role uint32) map[uint32]types.UserPermissionConfig {
	// 使用者子帳號的層級編號
	subRoleId := role + constants.SubRole

	rolePerms := make(map[uint32]types.UserPermissionConfig)
	for permId, perm := range l.allPermConfigMap {
		if perm.Enable && slices.Contains(perm.RoleSettable, subRoleId) {
			rolePerms[permId] = perm
		}
	}

	return rolePerms
}

func filterRolePerms(domainPerms []AllPermission, rolePerms map[uint32]types.UserPermissionConfig) ([]uint32, []types.UserPermissionConfig) {
	var roleResult []types.UserPermissionConfig

	// 取得指定層級的可開啟權限列表
	var roleOpenPermIDs []uint32

	for _, domainPerm := range domainPerms {
		if rolePerm, ok := rolePerms[domainPerm.PermissionId]; ok {
			// 判斷上層帳號是否有修改權限
			rolePerm.ParentModify = domainPerm.Modify

			roleResult = append(roleResult, rolePerm)
			roleOpenPermIDs = append(roleOpenPermIDs, rolePerm.PermissionId)
		}
	}

	return roleOpenPermIDs, roleResult
}

// 取得指定帳號的子帳號可開啟權限
func (l *AdminSubAccountOpenablePermissionLogic) getSubOpenablePermissions(user types.UserInfo) (map[string][]types.UserPermissionConfig, error) {
	domainPerms, domainPermsErr := l.getAllPermissionsModify(user)
	if domainPermsErr != nil {
		return nil, domainPermsErr
	}

	rolePerms := l.getRolePerms(user.Role)

	roleOpenPermIDs, roleResult := filterRolePerms(domainPerms, rolePerms)

	resultFilter := make([]types.UserPermissionConfig, 0)

	childPermConfigs := l.groupPermissionsByParentId(l.allPermConfigMap)

	for _, v := range roleResult {
		if v.Type == constants.Menu {
			childPerms := l.getChildPerms(childPermConfigs, v.PermissionId)

			/*
			   當此第二層權限有下層權限時:
			   如子帳號可被異動此權限，則判斷此第三層權限是否含在先前取出的子帳號可開啟權限陣列
			   當任一下層權限是可被異動的，此第二層便不需被移除
			*/
			if len(childPerms) > 0 && !permissionIsExist(childPerms, roleOpenPermIDs) {
				// 如沒有下層權限可被異動，則移除此第二層權限
				continue
			}
		}

		resultFilter = append(resultFilter, v)
	}

	return l.groupPermissionsByType(resultFilter), nil
}

func (l *AdminSubAccountOpenablePermissionLogic) getChildPerms(permConfigs map[uint32][]types.UserPermissionConfig, permissionId uint32) map[uint32]types.UserPermissionConfig {
	childConfigs := permConfigs[permissionId]
	childPerm := map[uint32]types.UserPermissionConfig{}
	for _, v := range childConfigs {
		if v.Enable {
			childPerm[v.PermissionId] = v
		}
	}

	return childPerm
}

func (l *AdminSubAccountOpenablePermissionLogic) groupPermissionsByParentId(permissions map[uint32]types.UserPermissionConfig) map[uint32][]types.UserPermissionConfig {
	permList := make(map[uint32][]types.UserPermissionConfig)
	for _, perm := range permissions {
		if perm.ParentId != 0 {
			permList[perm.ParentId] = append(permList[perm.ParentId], perm)
		}
	}
	return permList
}

func (l *AdminSubAccountOpenablePermissionLogic) groupPermissionsByType(permissions []types.UserPermissionConfig) map[string][]types.UserPermissionConfig {
	permList := make(map[string][]types.UserPermissionConfig)
	for _, perm := range permissions {
		permList[perm.Type] = append(permList[perm.Type], perm)
	}
	return permList
}

func permissionIsExist(perms map[uint32]types.UserPermissionConfig, openPermIDs []uint32) bool {
	for _, permId := range openPermIDs {
		if _, ok := perms[permId]; ok {
			return true
		}
	}

	return false
}

func sortPermList(permList []types.UserPermissionConfig) {
	sort.Slice(permList, func(i, j int) bool {
		if permList[i].Sort == permList[j].Sort {
			return permList[i].PermissionId < permList[j].PermissionId
		}
		return permList[i].Sort < permList[j].Sort
	})
}

func composeChildrenData(parentPerms []types.UserPermissionConfig, childPerms []types.UserPermissionConfig) []types.UserPermissionConfig {
	result := parentPerms
	for i := range parentPerms {
		for _, child := range childPerms {
			if child.ParentId == parentPerms[i].PermissionId {
				parentPerms[i].Children = append(parentPerms[i].Children, child)
			}
		}
	}

	return result
}

func manageMenu(permList map[string][]types.UserPermissionConfig) []types.UserPermissionConfig {
	factorList := permList["Factor"]
	sortPermList(factorList)

	privilegeList := permList["Privilege"]
	sortPermList(privilegeList)

	privilegeList = composeChildrenData(privilegeList, factorList)

	menuList := permList[constants.Menu]
	sortPermList(menuList)
	menuList = composeChildrenData(menuList, privilegeList)

	categoryList := permList["Category"]
	sortPermList(categoryList)
	categoryList = composeChildrenData(categoryList, menuList)

	permsFilter := make([]types.UserPermissionConfig, 0)
	for _, v := range categoryList {
		if len(v.Children) > 0 {
			permsFilter = append(permsFilter, v)
		}
	}
	return permsFilter
}

func manageSpecial(permList map[string][]types.UserPermissionConfig) []types.UserPermissionConfig {
	specialFactorList := permList["SpecialFactor"]
	sortPermList(specialFactorList)

	specialPrivilegeList := permList[constants.SpecialPrivilege]
	sortPermList(specialPrivilegeList)

	specialPrivilegeList = composeChildrenData(specialPrivilegeList, specialFactorList)

	specialCategoryList := permList["SpecialCategory"]
	sortPermList(specialCategoryList)
	specialCategoryList = composeChildrenData(specialCategoryList, specialPrivilegeList)

	permsFilter := make([]types.UserPermissionConfig, 0)
	for _, v := range specialCategoryList {
		if len(v.Children) > 0 {
			permsFilter = append(permsFilter, v)
		}
	}

	return permsFilter
}

func manageGeneral(permList map[string][]types.UserPermissionConfig) []types.UserPermissionConfig {
	generalFactorList := permList["GeneralFactor"]
	sortPermList(generalFactorList)

	generalList := permList["GeneralPrivilege"]
	sortPermList(generalList)
	return composeChildrenData(generalList, generalFactorList)
}

func (l *AdminSubAccountOpenablePermissionLogic) getAllPermissionsModify(user types.UserInfo) ([]AllPermission, error) {
	levelPermList, levelPermListErr := l.svcCtx.PermissionCtx.FilterUserPermission(l.ctx, l.allPermConfigMap, user, nil)
	if levelPermListErr != nil {
		return nil, levelPermListErr
	}

	if len(levelPermList.Enable) == 0 {
		return nil, nil
	}

	var validPermList []AllPermission
	for _, v := range l.allPermConfigMap {
		// 檢查上層跟自己的權限
		enableState, enableStateErr := l.checkLevelPermission(user, v, levelPermList.Enable[v.PermissionId], constants.Enable)
		if enableStateErr != nil {
			return nil, enableStateErr
		}

		if enableState {
			modifyState, modifyStateErr := l.checkLevelPermission(user, v, levelPermList.Modify[v.PermissionId], constants.Modify)
			if modifyStateErr != nil {
				return nil, modifyStateErr
			}

			validPermList = append(validPermList, AllPermission{
				PermissionId: v.PermissionId,
				Name:         v.Name,
				Enable:       v.Enable,
				Modify:       modifyState,
			})
		}
	}

	return validPermList, nil
}

func (l *AdminSubAccountOpenablePermissionLogic) checkLevelPermission(operator types.UserInfo, permConfig types.UserPermissionConfig, levelPerm map[uint32]bool, permissionAction string) (bool, error) {
	// 檢查上層權限
	state, err := l.checkParentPermission(operator, permConfig.ParentId, permissionAction)
	if err != nil {
		return false, err
	}

	if !state {
		return false, nil
	}

	parentCount := len(operator.AllParentsId)
	levelPermTrueCount := maputil.CountMapValues(levelPerm, true)

	// 筆數判斷-所有上層跟自己都要開
	if permConfig.Strict && levelPermTrueCount != parentCount+1 {
		return false, nil
	}

	// 其他: 自己要開, 子帳號: 上層也要開
	parentState, parentExist := levelPerm[uint32(parentCount)-1]
	userState, userExist := levelPerm[uint32(parentCount)]
	if (!userState || !userExist) || (operator.IsSub && (!parentState || !parentExist)) {
		return false, nil
	}

	return repository.CheckExtraConditions(l.ctx, l.svcCtx, operator, permConfig.PermissionId), nil
}

func (l *AdminSubAccountOpenablePermissionLogic) checkParentPermission(operator types.UserInfo, permissionId uint32, permissionAction string) (bool, error) {
	if permissionId == 0 {
		return true, nil
	}

	// 當該權限未開開關時回傳false
	parentConfig, exists := l.allPermConfigMap[permissionId]
	if !exists {
		return false, nil
	}

	if parentConfig.ParentId == 0 {
		return true, nil
	}

	// 當上層功能不為第一層時，判斷上層功能是否有權限
	state, err := repository.CheckMenuPermission(l.ctx, l.svcCtx, operator, parentConfig.PermissionId, permissionAction)
	if err != nil {
		return false, err
	}

	return state, nil
}
