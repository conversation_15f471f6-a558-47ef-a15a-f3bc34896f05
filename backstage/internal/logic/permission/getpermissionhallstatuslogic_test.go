package permission

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/livegame/livegameclient"
	"gbh/permission/permissionclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetPermissionHallStatusLogic(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1402)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1402).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	gameDetailResp := gameclient.GetGameDetailResponse{
		GameDetail: []*gameclient.GameDetail{
			{
				Id:       constants.GameDetailID1,
				GameKind: constants.BBLive,
				HallId:   seeder.HallId,
				Switch:   false,
			},
		},
	}
	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{seeder.HallId},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(gameDetailResp.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.UserCtx = userCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	expectedResp := make(map[uint32]GetPermissionHallStatusResponse)
	expectedResp[seeder.HallId] = GetPermissionHallStatusResponse{
		ID:     seeder.HallId,
		Enable: false,
		Modify: false,
	}

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: expectedResp,
	}, resp)
}

func TestGetPermissionHallStatusLogic_GameDetailDBEmpty(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1402)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1402).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	gameDetailResp := []*gameclient.GameDetail{}
	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{seeder.HallId},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(gameDetailResp, nil)

	hallTipSwitch := []*livegameclient.HallSwitchData{
		{
			HallId: seeder.HallId,
			Switch: false,
		},
	}
	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, seeder.HallId).Return(hallTipSwitch, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx
	svcCtx.UserCtx = userCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	expectedResp := make(map[uint32]GetPermissionHallStatusResponse)
	expectedResp[seeder.HallId] = GetPermissionHallStatusResponse{
		ID:     seeder.HallId,
		Enable: false,
		Modify: false,
	}

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: expectedResp,
	}, resp)
}

func TestGetPermissionHallStatusLogic_GetPermissionParentsErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1402).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_GetHallTipSwitchErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1402)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1402).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	gameDetailResp := []*gameclient.GameDetail{}
	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{seeder.HallId},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(gameDetailResp, nil)

	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx
	svcCtx.UserCtx = userCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_GetUserPermissionConfigListErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_BackstagePermissionNotFound(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.BackstagePermissionNotFound, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_GetHallListErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfigMap := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfigMap[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(nil, errorx.ConnectionFailed)

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		Enable: &enable,
	}).Return(allPermConfigMap, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_UserPermListErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1402)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1402).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_GetUserByUserIdErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1402)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1402).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_GetGameDetailErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1402,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1402)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1402).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		HallId: []uint32{seeder.HallId},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.UserCtx = userCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_GetUserLobby(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1471,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	allPermConfig[1].Extra.Conditions = constants.Lobby3
	defer func() {
		allPermConfig[1].Extra.Conditions = ""
	}()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[1])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1471)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1471).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1471},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	userInfo := &types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, *userInfo, uint32(constants.BBLive)).Return(true, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.UserCtx = userCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	expectedResp := make(map[uint32]GetPermissionHallStatusResponse)
	expectedResp[seeder.HallId] = GetPermissionHallStatusResponse{
		ID:     seeder.HallId,
		Enable: true,
		Modify: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: expectedResp,
	}, resp)
}

func TestGetPermissionHallStatusLogic_GetUserLobbyErr(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID1471,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	allPermConfig[1].Extra.Conditions = constants.Lobby3
	defer func() {
		allPermConfig[1].Extra.Conditions = ""
	}()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[1])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1471)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID1471).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1471},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	userInfo := &types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, *userInfo, uint32(constants.BBLive)).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.UserCtx = userCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPermissionHallStatusLogic_RoleIdIs7(t *testing.T) {
	request := types.GetPermissionHallStatusRequest{
		PermissionID: seeder.UserPermID400,
	}

	enable := true
	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	allPermConfig[0].Type = "Menu"
	defer func() {
		allPermConfig[0].Type = "Category"
	}()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[0])

	permissionCtx.On("GetUserPermissionConfigList", ctx, permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{request.PermissionID},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID400)

	permissionCtx.On("GetPermissionParents", ctx, seeder.UserPermID400).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", ctx, permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID400},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx

	l := NewGetPermissionHallStatusLogic(ctx, svcCtx)
	resp, err := l.GetPermissionHallStatus(&request)

	expectedResp := make(map[uint32]GetPermissionHallStatusResponse)
	expectedResp[seeder.HallId] = GetPermissionHallStatusResponse{
		ID:     seeder.HallId,
		Enable: true,
		Modify: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: expectedResp,
	}, resp)
}
