package permission

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminEnabledPermission_Get(t *testing.T) {
	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminEnabledPermission", ctx).Return(&seeder.AdminPermissionEnabled, nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminEnabledPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminEnabledPermission()

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: &seeder.AdminPermissionEnabled,
	}, resp)
}

func TestAdminEnabledPermission_GetError(t *testing.T) {
	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminEnabledPermission", ctx).Return("", errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminEnabledPermissionLogic(ctx, svcCtx)
	resp, err := l.AdminEnabledPermission()

	assert.Equal(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}
