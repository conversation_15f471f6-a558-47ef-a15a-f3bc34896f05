package permission

import (
	"context"

	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminControllerPermissionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminControllerPermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminControllerPermissionLogic {
	return &AdminControllerPermissionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminControllerPermissionLogic) AdminControllerPermission(req *types.AdminControllerPermissionRequest) (*types.BaseResponse, error) {
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	controllerPermissionID, controllerPermissionIDErr := l.svcCtx.PermissionCtx.AdminControllerPermissionIDs(l.ctx, req.ControllerName)
	if controllerPermissionIDErr != nil {
		return nil, controllerPermissionIDErr
	}

	if len(controllerPermissionID) == 0 {
		return nil, errorx.BackstagePermissionDenied
	}

	enable := true
	adminPermissions, adminPermissionsErr := l.svcCtx.PermissionCtx.AdminPermissions(l.ctx, permission.AdminPermissionsRequest{
		AdminID:      []uint32{admin.ID()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	})

	if adminPermissionsErr != nil {
		return nil, adminPermissionsErr
	}

	if len(adminPermissions) == 0 {
		return nil, errorx.BackstagePermissionDenied
	}

	permissionID := []uint32{}
	for _, value := range adminPermissions {
		permissionID = append(permissionID, value.GetPermissionId())
	}

	adminPermissionInfoList, adminPermissionInfoListErr := l.svcCtx.PermissionCtx.AdminPermissionInfoList(l.ctx, permission.AdminPermissionInfoListRequest{
		PermissionID: permissionID,
	})

	if adminPermissionInfoListErr != nil {
		return nil, adminPermissionInfoListErr
	}

	if len(adminPermissionInfoList) == 0 {
		return nil, errorx.BackstagePermissionDenied
	}

	for _, value := range adminPermissionInfoList {
		if value.GetEnable() {
			return &types.BaseResponse{}, nil
		}
	}

	return nil, errorx.BackstagePermissionDisabled
}
