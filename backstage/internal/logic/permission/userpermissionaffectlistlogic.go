package permission

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UserPermissionAffectListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUserPermissionAffectListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserPermissionAffectListLogic {
	return &UserPermissionAffectListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UserPermissionAffectListLogic) UserPermissionAffectList() (*types.BaseResponse, error) {
	resp, err := l.svcCtx.PermissionCtx.GetUserPermissionAffectList(l.ctx)
	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: resp,
	}, nil
}
