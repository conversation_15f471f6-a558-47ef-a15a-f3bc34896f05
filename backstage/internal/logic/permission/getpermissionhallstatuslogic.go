package permission

import (
	"context"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/permission/permissionclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPermissionHallStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type GetPermissionHallStatusResponse struct {
	ID     uint32 `json:"id"`
	Enable bool   `json:"enable"`
	Modify bool   `json:"modify"`
}

func NewGetPermissionHallStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPermissionHallStatusLogic {
	return &GetPermissionHallStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPermissionHallStatusLogic) GetPermissionHallStatus(req *types.GetPermissionHallStatusRequest) (resp *types.BaseResponse, err error) {
	enable := true
	permListResp, err := l.svcCtx.PermissionCtx.GetUserPermissionConfigList(l.ctx, permission.UserPermissionConfigListRequest{
		Enable:       &enable,
		PermissionId: []uint32{req.PermissionID},
	})

	if err != nil {
		return nil, err
	}

	if len(permListResp) == 0 {
		return nil, errorx.BackstagePermissionNotFound
	}

	hallListResp, err := l.svcCtx.HallCtx.GetHallList(l.ctx, enable)

	if err != nil {
		return nil, err
	}

	hallList := make([]uint32, 0, len(hallListResp.GetData()))
	for _, v := range hallListResp.GetData() {
		hallList = append(hallList, v.GetHallId())
	}

	permId := permListResp[0].GetPermissionId()
	parentPerm, err := l.svcCtx.PermissionCtx.GetPermissionParents(l.ctx, permId)

	if err != nil {
		return nil, err
	}

	filterParentPerm, parentPermissionIdList := l.filterParentPermissions(parentPerm)

	userPermListResp, err := l.svcCtx.PermissionCtx.UserPermList(l.ctx, permission.UserPermListRequest{
		PermissionId: parentPermissionIdList,
		RoleId:       []uint32{0},
		UserId:       hallList,
	})

	if err != nil {
		return nil, err
	}

	userPermMap := make(map[uint32]map[uint32]*permissionclient.UserPerm)
	for _, perm := range userPermListResp.GetData() {
		if _, exists := userPermMap[perm.GetUserId()]; !exists {
			userPermMap[perm.GetUserId()] = make(map[uint32]*permissionclient.UserPerm)
		}

		userPermMap[perm.GetUserId()][perm.GetPermissionId()] = perm
	}

	result, err := l.handlerPermissionResult(hallList, filterParentPerm, userPermMap)

	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: result,
	}, nil
}

// 第一層權限(大類)不用判斷
func (l *GetPermissionHallStatusLogic) filterParentPermissions(parentPerms []*types.UserPermissionConfig) ([]*types.UserPermissionConfig, []uint32) {
	parentPermissionIdList := []uint32{}
	filterParentPerm := []*types.UserPermissionConfig{}

	for _, v := range parentPerms {
		if v.Type != "SpecialCategory" && v.Type != "Category" {
			filterParentPerm = append(filterParentPerm, v)
			parentPermissionIdList = append(parentPermissionIdList, v.PermissionId)
		}
	}
	return filterParentPerm, parentPermissionIdList
}

// 判斷各個hall對應各permission，返回最終結果
func (l *GetPermissionHallStatusLogic) handlerPermissionResult(hallList []uint32, filterParentPerm []*types.UserPermissionConfig, userPermMap map[uint32]map[uint32]*permissionclient.UserPerm) (map[uint32]GetPermissionHallStatusResponse, error) {
	result := make(map[uint32]GetPermissionHallStatusResponse)
	for _, hallId := range hallList {
		// 預設開，當任一上層關及為關
		finalEnable := true
		finalModify := true

		for _, perm := range filterParentPerm {
			tempEnable := false
			tempModify := false

			// role perm
			for _, v := range perm.RolePerm {
				if v.RoleId == constants.Role7 {
					tempEnable = v.View
					tempModify = v.Modify
					break
				}
			}

			// user perm
			if userPerm, exists := userPermMap[hallId][perm.PermissionId]; exists {
				tempEnable = userPerm.GetEnable()
				tempModify = userPerm.GetModify()
			}

			// condition
			if tempEnable && perm.Extra.Conditions != "" {
				userResp, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, hallId)

				if err != nil {
					return nil, err
				}

				userInfo := &types.UserInfo{
					Id:           userResp.GetId(),
					Domain:       userResp.GetDomain(),
					ParentId:     userResp.GetParent(),
					AllParentsId: userResp.GetAllParents(),
					Role:         userResp.GetRole(),
					IsSub:        userResp.GetSub(),
				}

				// 處理permision config中extra condition有包含func名稱的狀況
				conditionResult, err := repository.HandleConditionSwitch(l.ctx, l.svcCtx, perm.Extra.Conditions, *userInfo)

				if err != nil {
					return nil, err
				}

				tempEnable = conditionResult
				tempModify = conditionResult
			}

			// 上層權限任一沒開此權限不開
			if !tempEnable {
				finalEnable = false
			}

			if !tempModify {
				finalModify = false
			}
		}

		result[hallId] = GetPermissionHallStatusResponse{
			ID:     hallId,
			Enable: finalEnable,
			Modify: finalModify,
		}
	}

	return result, nil
}
