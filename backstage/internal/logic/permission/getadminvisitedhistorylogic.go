package permission

import (
	"context"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAdminVisitedHistoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type adminVisitedHistoryData struct {
	Id          uint32 `json:"id"`
	Dict        string `json:"dict"`
	Translation string `json:"translation"`
	Is<PERSON>i        bool   `json:"is_ai"`
	IsNew       bool   `json:"is_new"`
}

func NewGetAdminVisitedHistoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAdminVisitedHistoryLogic {
	return &GetAdminVisitedHistoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAdminVisitedHistoryLogic) GetAdminVisitedHistory(req *types.GetAdminVisitedHistoryRequest) (*types.BaseResponse, error) {
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	visitedHistory, visitedHistoryErr := l.svcCtx.PermissionCtx.GetAdminVisitedHistory(l.ctx, admin.ID())
	if visitedHistoryErr != nil {
		return nil, visitedHistoryErr
	}

	lang := constants.ZhCn
	if req.Lang != "" {
		lang = req.Lang
	}

	layoutLang, layoutLangErr := l.svcCtx.LangCtx.GetWeblateLang(l.ctx, lang, "layout")
	if layoutLangErr != nil {
		return nil, layoutLangErr
	}

	// 最多顯示五筆
	maxLimit := constants.VisitedHistoryMaxLimit
	if len(visitedHistory) < maxLimit {
		maxLimit = len(visitedHistory)
	}

	resp := make([]adminVisitedHistoryData, 0, maxLimit)
	for i := 0; i < maxLimit; i++ {
		resp = append(resp, adminVisitedHistoryData{
			Id:          visitedHistory[i].GetId(),
			Dict:        visitedHistory[i].GetDict(),
			Translation: layoutLang.GetData()[visitedHistory[i].GetDict()],
			IsAi:        visitedHistory[i].GetIsAi(),
			IsNew:       visitedHistory[i].GetIsNew(),
		})
	}

	return &types.BaseResponse{
		Data: resp,
	}, nil
}
