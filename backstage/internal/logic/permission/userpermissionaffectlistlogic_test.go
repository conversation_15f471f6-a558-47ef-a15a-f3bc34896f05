package permission

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUserPermissionAffectList_Get(t *testing.T) {
	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetUserPermissionAffectList", ctx).Return(seeder.UserPermissionAffectList.GetList(), nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewUserPermissionAffectListLogic(ctx, svcCtx)
	resp, err := l.UserPermissionAffectList()

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: seeder.UserPermissionAffectList.GetList(),
	}, resp)
}

func TestUserPermissionAffectList_GetUserPermissionAffectListError(t *testing.T) {
	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetUserPermissionAffectList", ctx).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewUserPermissionAffectListLogic(ctx, svcCtx)
	resp, err := l.UserPermissionAffectList()

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
