package permission

import (
	"gbh/admin/adminclient"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminSubAccountOpenablePermissionLogic_AdminSubAccountOpenablePermission(t *testing.T) {
	request := &types.AdminSubAccountOpenablePermissionRequest{
		AdminId:            seeder.AdminID,
		UserId:             seeder.HallId,
		PermissionCategory: []string{"menu", "special", "general"},
	}

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(seeder.HallList.GetData()[0], nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{
		HallId: []uint32{},
	}, nil)

	permissionCtx := mock.NewMockPermissionCtx()

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	permissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	mockFilterUserPermission := permission.Result{
		Enable: seeder.HallEnablePermission,
		Modify: seeder.HallEnablePermission,
	}
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(mockFilterUserPermission, nil)
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Enable).Return(true, nil).Once()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Modify).Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1518).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1518].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1518].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID400].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID400].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID674).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID674].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID674].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1471).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1471].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1471].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID2038).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID2038].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID2038].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLottery)).Return(true, nil)
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.UserCtx = userCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp, err := l.AdminSubAccountOpenablePermission(request)

	perm2038 := userPermissionConfigMap[seeder.UserPermID2038]
	perm2038.ParentModify = true

	perm1471 := userPermissionConfigMap[seeder.UserPermID1471]
	perm1471.ParentModify = true
	perm1471.Children = []types.UserPermissionConfig{
		perm2038,
	}

	perm400 := userPermissionConfigMap[seeder.UserPermID400]
	perm400.ParentModify = true
	perm400.Children = []types.UserPermissionConfig{
		perm1471,
	}

	perm1402 := userPermissionConfigMap[seeder.UserPermID1402]
	perm1402.ParentModify = true

	perm405 := userPermissionConfigMap[seeder.UserPermID405]
	perm405.ParentModify = true
	perm405.Children = []types.UserPermissionConfig{
		perm1402,
	}

	perm674 := userPermissionConfigMap[seeder.UserPermID674]
	perm674.ParentModify = true

	perm1518 := userPermissionConfigMap[seeder.UserPermID1518]
	perm1518.ParentModify = true
	perm1518.Children = []types.UserPermissionConfig{
		perm674,
	}

	expectedResponse := &types.BaseResponse{
		Data: map[string][]types.UserPermissionConfig{
			"menu": {
				perm400,
				perm405,
			},
			"special": {
				perm1518,
			},
			"general": nil,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_AdminSubAccountOpenablePermission_getUserInfo_error(t *testing.T) {
	request := &types.AdminSubAccountOpenablePermissionRequest{
		AdminId:            seeder.AdminID,
		UserId:             seeder.HallId,
		PermissionCategory: []string{"menu"},
	}

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = userCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp, err := l.AdminSubAccountOpenablePermission(request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_AdminSubAccountOpenablePermission_CheckAdminPermissionForHallId_error(t *testing.T) {
	request := &types.AdminSubAccountOpenablePermissionRequest{
		AdminId:            seeder.AdminID,
		UserId:             seeder.HallId,
		PermissionCategory: []string{"menu"},
	}

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = userCtx
	svcCtx.HallCtx = hallCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp, err := l.AdminSubAccountOpenablePermission(request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_AdminSubAccountOpenablePermission_GetAllPermConfigMap_error(t *testing.T) {
	request := &types.AdminSubAccountOpenablePermissionRequest{
		AdminId:            seeder.AdminID,
		UserId:             seeder.HallId,
		PermissionCategory: []string{"menu"},
	}

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(seeder.HallList.GetData()[0], nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{
		HallId: []uint32{},
	}, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAllPermConfigMap", ctx).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = userCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp, err := l.AdminSubAccountOpenablePermission(request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_AdminSubAccountOpenablePermission_getSubOpenablePermissions_error(t *testing.T) {
	request := &types.AdminSubAccountOpenablePermissionRequest{
		AdminId:            seeder.AdminID,
		UserId:             seeder.HallId,
		PermissionCategory: []string{"menu"},
	}

	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(seeder.HallList.GetData()[0], nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{
		HallId: []uint32{},
	}, nil)

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp, err := l.AdminSubAccountOpenablePermission(request)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getUserInfo(t *testing.T) {
	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	svcCtx.UserCtx = userCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp, err := l.getUserInfo(seeder.User.GetId())

	expectedResponse := &types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getUserInfo_err(t *testing.T) {
	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = userCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp, err := l.getUserInfo(seeder.User.GetId())

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getRolePerms(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp := l.getRolePerms(constants.Role7)

	expectedResponse := make(map[uint32]types.UserPermissionConfig)
	expectedResponse[seeder.UserPermID400] = userPermissionConfigMap[seeder.UserPermID400]
	expectedResponse[seeder.UserPermID405] = userPermissionConfigMap[seeder.UserPermID405]
	expectedResponse[seeder.UserPermID674] = userPermissionConfigMap[seeder.UserPermID674]
	expectedResponse[seeder.UserPermID1402] = userPermissionConfigMap[seeder.UserPermID1402]
	expectedResponse[seeder.UserPermID1471] = userPermissionConfigMap[seeder.UserPermID1471]
	expectedResponse[seeder.UserPermID1518] = userPermissionConfigMap[seeder.UserPermID1518]
	expectedResponse[seeder.UserPermID2038] = userPermissionConfigMap[seeder.UserPermID2038]

	assert.Equal(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_filterRolePerms(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	perm400 := userPermissionConfigMap[seeder.UserPermID400]
	perm400.ParentModify = true

	perm1471 := userPermissionConfigMap[seeder.UserPermID1471]
	perm1471.ParentModify = true

	domainPerms := []AllPermission{
		{
			PermissionId: perm400.PermissionId,
			Name:         perm400.Name,
			Enable:       perm400.Enable,
			Modify:       perm400.Modifiable,
		},
		{
			PermissionId: perm1471.PermissionId,
			Name:         perm1471.Name,
			Enable:       perm1471.Enable,
			Modify:       perm1471.Modifiable,
		},
	}

	rolePerms := make(map[uint32]types.UserPermissionConfig)
	rolePerms[perm400.PermissionId] = perm400

	permIDs, result := filterRolePerms(domainPerms, rolePerms)

	assert.Equal(t, []uint32{perm400.PermissionId}, permIDs)
	assert.Equal(t, []types.UserPermissionConfig{perm400}, result)
}

func TestAdminSubAccountOpenablePermissionLogic_getSubOpenablePermissions(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	mockFilterUserPermission := permission.Result{
		Enable: seeder.HallEnablePermission,
		Modify: seeder.HallEnablePermission,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(mockFilterUserPermission, nil)
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Enable).Return(true, nil).Once()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Modify).Return(true, nil).Once()

	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1471).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1471].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1471].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1518).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1518].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1518].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID674).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID674].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID674].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID400].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID400].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID2038).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID2038].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID2038].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLottery)).Return(true, nil)
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp, err := l.getSubOpenablePermissions(operator)

	perm400 := userPermissionConfigMap[seeder.UserPermID400]
	perm400.ParentModify = true

	perm405 := userPermissionConfigMap[seeder.UserPermID405]
	perm405.ParentModify = true

	perm1471 := userPermissionConfigMap[seeder.UserPermID1471]
	perm1471.ParentModify = true

	perm1402 := userPermissionConfigMap[seeder.UserPermID1402]
	perm1402.ParentModify = true

	perm2038 := userPermissionConfigMap[seeder.UserPermID2038]
	perm2038.ParentModify = true

	perm674 := userPermissionConfigMap[seeder.UserPermID674]
	perm674.ParentModify = true

	perm1518 := userPermissionConfigMap[seeder.UserPermID1518]
	perm1518.ParentModify = true

	expectedResponse := make(map[string][]types.UserPermissionConfig)
	expectedResponse[constants.Category] = []types.UserPermissionConfig{
		perm400,
		perm405,
	}
	expectedResponse[constants.Menu] = []types.UserPermissionConfig{
		perm1471,
		perm1402,
	}
	expectedResponse[constants.Privilege] = []types.UserPermissionConfig{
		perm2038,
	}
	expectedResponse[constants.SpecialCategory] = []types.UserPermissionConfig{
		perm1518,
	}
	expectedResponse[constants.SpecialPrivilege] = []types.UserPermissionConfig{
		perm674,
	}

	assert.NoError(t, err)
	assert.ElementsMatch(t, expectedResponse[constants.Category], resp[constants.Category])
	assert.ElementsMatch(t, expectedResponse[constants.Menu], resp[constants.Menu])
	assert.ElementsMatch(t, expectedResponse[constants.Privilege], resp[constants.Privilege])
	assert.ElementsMatch(t, expectedResponse[constants.SpecialCategory], resp[constants.SpecialCategory])
	assert.ElementsMatch(t, expectedResponse[constants.SpecialPrivilege], resp[constants.SpecialPrivilege])
}

func TestAdminSubAccountOpenablePermissionLogic_getSubOpenablePermissions_getAllPermissionsModify_error(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp, err := l.getSubOpenablePermissions(operator)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getChildPerms(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	childPermConfigs := make(map[uint32][]types.UserPermissionConfig)
	childPermConfigs[seeder.UserPermID400] = append(childPermConfigs[seeder.UserPermID400], userPermissionConfigMap[seeder.UserPermID1471])
	childPermConfigs[seeder.UserPermID405] = append(childPermConfigs[seeder.UserPermID405], userPermissionConfigMap[seeder.UserPermID1402])

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp := l.getChildPerms(childPermConfigs, seeder.UserPermID400)

	expectedResponse := make(map[uint32]types.UserPermissionConfig)
	expectedResponse[seeder.UserPermID1471] = userPermissionConfigMap[seeder.UserPermID1471]

	assert.Equal(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_groupPermissionsByParentId(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp := l.groupPermissionsByParentId(userPermissionConfigMap)

	expectedResponse := make(map[uint32][]types.UserPermissionConfig)
	expectedResponse[seeder.UserPermID400] = append(expectedResponse[seeder.UserPermID400], userPermissionConfigMap[seeder.UserPermID1471])
	expectedResponse[seeder.UserPermID405] = append(expectedResponse[seeder.UserPermID405], userPermissionConfigMap[seeder.UserPermID1402])
	expectedResponse[seeder.UserPermID1471] = append(expectedResponse[seeder.UserPermID1471], userPermissionConfigMap[seeder.UserPermID2038])
	expectedResponse[seeder.UserPermID1518] = append(expectedResponse[seeder.UserPermID1518], userPermissionConfigMap[seeder.UserPermID674])

	assert.Equal(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_groupPermissionsByType(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permConfigs := []types.UserPermissionConfig{userPermissionConfigMap[seeder.UserPermID400], userPermissionConfigMap[seeder.UserPermID1471]}
	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	resp := l.groupPermissionsByType(permConfigs)

	expectedResponse := make(map[string][]types.UserPermissionConfig)
	expectedResponse[constants.Category] = append(expectedResponse[constants.Category], userPermissionConfigMap[seeder.UserPermID400])
	expectedResponse[constants.Menu] = append(expectedResponse[constants.Menu], userPermissionConfigMap[seeder.UserPermID1471])

	assert.Equal(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_permissionIsExist(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	existedResp := permissionIsExist(userPermissionConfigMap, []uint32{seeder.UserPermID400, seeder.UserPermID1518})
	assert.True(t, existedResp)

	notExistedResp := permissionIsExist(userPermissionConfigMap, []uint32{seeder.AdminDepartmentID2211, seeder.AdminDepartmentID2222})
	assert.False(t, notExistedResp)
}

func TestAdminSubAccountOpenablePermissionLogic_sortPermList(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permList := []types.UserPermissionConfig{
		userPermissionConfigMap[seeder.UserPermID405],
		userPermissionConfigMap[seeder.UserPermID400],
		userPermissionConfigMap[seeder.UserPermID1518],
	}

	sortPermList(permList)

	expectedResponse := []types.UserPermissionConfig{
		userPermissionConfigMap[seeder.UserPermID400],
		userPermissionConfigMap[seeder.UserPermID405],
		userPermissionConfigMap[seeder.UserPermID1518],
	}

	assert.ElementsMatch(t, expectedResponse, permList)
}

func TestAdminSubAccountOpenablePermissionLogic_composeChildrenData(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	perm400 := userPermissionConfigMap[seeder.UserPermID400]

	perm400.Children = []types.UserPermissionConfig{
		userPermissionConfigMap[seeder.UserPermID1471],
	}

	permList := []types.UserPermissionConfig{
		userPermissionConfigMap[seeder.UserPermID400],
	}

	childPermList := []types.UserPermissionConfig{
		userPermissionConfigMap[seeder.UserPermID1471],
	}

	resp := composeChildrenData(permList, childPermList)
	expectedResponse := []types.UserPermissionConfig{
		perm400,
	}

	assert.ElementsMatch(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_manageMenu(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permList := map[string][]types.UserPermissionConfig{}
	for _, v := range userPermissionConfigMap {
		permList[v.Type] = append(permList[v.Type], v)
	}

	perm400 := userPermissionConfigMap[seeder.UserPermID400]
	perm1471 := userPermissionConfigMap[seeder.UserPermID1471]
	perm405 := userPermissionConfigMap[seeder.UserPermID405]

	perm1471.Children = []types.UserPermissionConfig{
		userPermissionConfigMap[seeder.UserPermID2038],
	}
	perm405.Children = []types.UserPermissionConfig{
		userPermissionConfigMap[seeder.UserPermID1402],
	}
	perm400.Children = []types.UserPermissionConfig{
		perm1471,
	}

	resp := manageMenu(permList)
	expectedResponse := []types.UserPermissionConfig{
		perm400,
		perm405,
	}

	assert.ElementsMatch(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_manageSpecial(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permList := map[string][]types.UserPermissionConfig{}
	for _, v := range userPermissionConfigMap {
		permList[v.Type] = append(permList[v.Type], v)
	}

	perm1518 := userPermissionConfigMap[seeder.UserPermID1518]
	perm674 := userPermissionConfigMap[seeder.UserPermID674]

	perm1518.Children = []types.UserPermissionConfig{
		perm674,
	}

	resp := manageSpecial(permList)
	expectedResponse := []types.UserPermissionConfig{
		perm1518,
	}

	assert.ElementsMatch(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_manageGeneral(t *testing.T) {
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permList := map[string][]types.UserPermissionConfig{}
	for _, v := range userPermissionConfigMap {
		permList[v.Type] = append(permList[v.Type], v)
	}

	resp := manageGeneral(permList)
	expectedResponse := make([]types.UserPermissionConfig, 0)

	assert.ElementsMatch(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getAllPermissionsModify(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	mockFilterUserPermission := permission.Result{
		Enable: seeder.HallEnablePermission,
		Modify: seeder.HallEnablePermission,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(mockFilterUserPermission, nil)
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Enable).Return(true, nil).Once()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Modify).Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1471).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1471].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1471].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1518).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1518].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1518].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID674).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID674].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID674].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID400].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID400].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID2038).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID2038].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID2038].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLottery)).Return(true, nil)
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp, err := l.getAllPermissionsModify(operator)

	expectedResponse := []AllPermission{
		{
			PermissionId: seeder.UserPermID1471,
			Name:         userPermissionConfigMap[seeder.UserPermID1471].Name,
			Enable:       true,
			Modify:       true,
		},
		{
			PermissionId: seeder.UserPermID400,
			Name:         userPermissionConfigMap[seeder.UserPermID400].Name,
			Enable:       userPermissionConfigMap[seeder.UserPermID400].Enable,
			Modify:       userPermissionConfigMap[seeder.UserPermID400].Modifiable,
		},
		{
			PermissionId: seeder.UserPermID2038,
			Name:         userPermissionConfigMap[seeder.UserPermID2038].Name,
			Enable:       userPermissionConfigMap[seeder.UserPermID2038].Enable,
			Modify:       userPermissionConfigMap[seeder.UserPermID2038].Modifiable,
		},
		{
			PermissionId: seeder.UserPermID1518,
			Name:         userPermissionConfigMap[seeder.UserPermID1518].Name,
			Enable:       userPermissionConfigMap[seeder.UserPermID1518].Enable,
			Modify:       userPermissionConfigMap[seeder.UserPermID1518].Modifiable,
		},
		{
			PermissionId: seeder.UserPermID1402,
			Name:         userPermissionConfigMap[seeder.UserPermID1402].Name,
			Enable:       userPermissionConfigMap[seeder.UserPermID1402].Enable,
			Modify:       userPermissionConfigMap[seeder.UserPermID1402].Modifiable,
		},
		{
			PermissionId: seeder.UserPermID405,
			Name:         userPermissionConfigMap[seeder.UserPermID405].Name,
			Enable:       userPermissionConfigMap[seeder.UserPermID405].Enable,
			Modify:       userPermissionConfigMap[seeder.UserPermID405].Modifiable,
		},
		{
			PermissionId: seeder.UserPermID674,
			Name:         userPermissionConfigMap[seeder.UserPermID674].Name,
			Enable:       userPermissionConfigMap[seeder.UserPermID674].Enable,
			Modify:       userPermissionConfigMap[seeder.UserPermID674].Modifiable,
		},
	}

	assert.NoError(t, err)
	assert.ElementsMatch(t, expectedResponse, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getAllPermissionsModify_FilterUserPermission_error(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp, err := l.getAllPermissionsModify(operator)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getAllPermissionsModify_FilterUserPermission_empty_result(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	mockFilterUserPermission := permission.Result{}
	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(mockFilterUserPermission, nil)

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp, err := l.getAllPermissionsModify(operator)

	assert.NoError(t, err)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getAllPermissionsModify_checkLevelPermission_enable_error(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	mockFilterUserPermission := permission.Result{
		Enable: seeder.HallEnablePermission,
		Modify: seeder.HallEnablePermission,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(mockFilterUserPermission, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1471).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1471].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1471].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1518).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1518].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1518].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID674).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID674].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID674].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID400].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID400].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID2038).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID2038].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID2038].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, "enable").Return(nil, errorx.ConnectionFailed).Once()

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp, err := l.getAllPermissionsModify(operator)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_getAllPermissionsModify_checkLevelPermission_modify_error(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	mockFilterUserPermission := permission.Result{
		Enable: seeder.HallEnablePermission,
		Modify: seeder.HallEnablePermission,
	}

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("FilterUserPermission", ctx, userPermissionConfigMap, operator, []uint32(nil)).Return(mockFilterUserPermission, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1471).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1471].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1471].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1518).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1518].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1518].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID674).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID674].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID674].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID400].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID400].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID2038).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID2038].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID2038].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Enable).Return(true, nil).Once()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, "modify").Return(nil, errorx.ConnectionFailed).Once()

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap
	resp, err := l.getAllPermissionsModify(operator)

	assert.ErrorIs(t, err, errorx.ConnectionFailed)
	assert.Nil(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkLevelPermission(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.SubSupremeShareholderID,
		Domain:       seeder.HallId,
		ParentId:     seeder.SubSupremeShareholder.GetParent(),
		AllParentsId: seeder.SubSupremeShareholder.GetAllParents(),
		Role:         seeder.SubSupremeShareholder.GetRole(),
		IsSub:        seeder.SubSupremeShareholder.GetSub(),
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1402, "enable").Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLottery)).Return(true, nil)
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap

	levelParams := make(map[uint32]bool)
	levelParams[constants.Depth1] = true
	levelParams[constants.Depth2] = true
	levelParams[constants.Depth3] = true

	resp, err := l.checkLevelPermission(operator, userPermissionConfigMap[seeder.UserPermID1402], levelParams, "enable")

	assert.NoError(t, err)
	assert.True(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkLevelPermission_checkParentPermission_error(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.SubSupremeShareholderID,
		Domain:       seeder.HallId,
		ParentId:     seeder.SubSupremeShareholder.GetParent(),
		AllParentsId: seeder.SubSupremeShareholder.GetAllParents(),
		Role:         seeder.SubSupremeShareholder.GetRole(),
		IsSub:        seeder.SubSupremeShareholder.GetSub(),
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, "enable").Return(nil, errorx.DatabaseError).Once()

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap

	levelParams := make(map[uint32]bool)

	resp, err := l.checkLevelPermission(operator, userPermissionConfigMap[seeder.UserPermID2038], levelParams, "enable")

	assert.ErrorIs(t, err, errorx.DatabaseError)
	assert.False(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkLevelPermission_is_false(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.SubSupremeShareholderID,
		Domain:       seeder.HallId,
		ParentId:     seeder.SubSupremeShareholder.GetParent(),
		AllParentsId: seeder.SubSupremeShareholder.GetAllParents(),
		Role:         seeder.SubSupremeShareholder.GetRole(),
		IsSub:        seeder.SubSupremeShareholder.GetSub(),
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, constants.Enable).Return(false, nil).Once()

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap

	levelParams := make(map[uint32]bool)
	levelParams[constants.Depth1] = true
	levelParams[constants.Depth2] = true
	levelParams[constants.Depth3] = true

	resp, err := l.checkLevelPermission(operator, userPermissionConfigMap[seeder.UserPermID2038], levelParams, constants.Enable)

	assert.NoError(t, err)
	assert.False(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkLevelPermission_strict_parent_not_open(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.SubSupremeShareholderID,
		Domain:       seeder.HallId,
		ParentId:     seeder.SubSupremeShareholder.GetParent(),
		AllParentsId: seeder.SubSupremeShareholder.GetAllParents(),
		Role:         seeder.SubSupremeShareholder.GetRole(),
		IsSub:        seeder.SubSupremeShareholder.GetSub(),
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap

	levelParams := make(map[uint32]bool)
	levelParams[constants.Depth1] = true
	levelParams[constants.Depth2] = true
	levelParams[constants.Depth3] = false

	resp, err := l.checkLevelPermission(operator, userPermissionConfigMap[seeder.UserPermID1402], levelParams, "enable")

	assert.NoError(t, err)
	assert.False(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkLevelPermission_not_strict_parent_not_open(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.SubSupremeShareholderID,
		Domain:       seeder.HallId,
		ParentId:     seeder.SubSupremeShareholder.GetParent(),
		AllParentsId: seeder.SubSupremeShareholder.GetAllParents(),
		Role:         seeder.SubSupremeShareholder.GetRole(),
		IsSub:        seeder.SubSupremeShareholder.GetSub(),
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1402, "enable").Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLottery)).Return(true, nil)
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap

	levelParams := make(map[uint32]bool)
	levelParams[constants.Depth1] = true
	levelParams[constants.Depth2] = false
	levelParams[constants.Depth3] = false

	resp, err := l.checkLevelPermission(operator, userPermissionConfigMap[seeder.UserPermID674], levelParams, "enable")

	assert.NoError(t, err)
	assert.False(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkParentPermission(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1402, "enable").Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLottery)).Return(true, nil)
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap

	resp, err := l.checkParentPermission(operator, seeder.UserPermID1402, "enable")

	assert.NoError(t, err)
	assert.True(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkParentPermission_permissionId_is_zero(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)

	resp, err := l.checkParentPermission(operator, 0, "enable")

	assert.NoError(t, err)
	assert.True(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkParentPermission_allPermConfigMap_not_exist(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)

	resp, err := l.checkParentPermission(operator, seeder.UserPermID1402, "enable")

	assert.NoError(t, err)
	assert.False(t, resp)
}

func TestAdminSubAccountOpenablePermissionLogic_checkParentPermission_no_parent(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1518, "enable").Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1518).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1518].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1518].Extra,
	})

	svcCtx.PermissionCtx = permissionCtx

	l := NewAdminSubAccountOpenablePermissionLogic(ctx, svcCtx)
	l.allPermConfigMap = userPermissionConfigMap

	resp, err := l.checkParentPermission(operator, seeder.UserPermID1518, "enable")

	assert.NoError(t, err)
	assert.True(t, resp)
}
