package game

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"gbh/proto/maintain"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetMaintainStatus(t *testing.T) {
	enable := true
	LobbyCategoryRequest := game.LobbyCategoryRequest{
		Enable: &enable,
	}

	GameCtx := mock.NewMockGameCtx()
	GameCtx.On("LobbyCategory", ctx, LobbyCategoryRequest).Return(&seeder.LobbyCategoryEnableIsTrue, nil)

	MaintainCtx := mock.NewMockMaintainCtx()
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBLive,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy3, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBCasino,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy5, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBFish,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy38, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.NBBSport,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy31, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBLottery,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy12, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBBattle,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy66, nil)

	svcCtx.GameCtx = GameCtx
	svcCtx.MaintainCtx = MaintainCtx

	request := &types.EmptyRequest{}
	l := NewGetMaintainStatusLogic(ctx, svcCtx)
	resp, err := l.GetMaintainStatus(request)

	expected := &types.BaseResponse{
		Data: maintainResponse{
			Maintain: []maintainInfo{
				{
					GameKind:      constants.BBLive,
					Category:      constants.CategoryLive,
					StartTime:     seeder.GetMaintainByGameKindIsNoMaintainingBy3.GetBeginAt(),
					EndTime:       seeder.GetMaintainByGameKindIsNoMaintainingBy3.GetEndAt(),
					ModifyTime:    seeder.GetMaintainByGameKindIsNoMaintainingBy3.GetModifiedAt(),
					Message:       seeder.GetMaintainByGameKindIsNoMaintainingBy3.GetMsg(),
					IsMaintaining: seeder.GetMaintainByGameKindIsNoMaintainingBy3.GetIsMaintaining(),
				},
				{
					GameKind:      constants.BBCasino,
					Category:      constants.CategoryProb,
					StartTime:     seeder.GetMaintainByGameKindIsNoMaintainingBy5.GetBeginAt(),
					EndTime:       seeder.GetMaintainByGameKindIsNoMaintainingBy5.GetEndAt(),
					ModifyTime:    seeder.GetMaintainByGameKindIsNoMaintainingBy5.GetModifiedAt(),
					Message:       seeder.GetMaintainByGameKindIsNoMaintainingBy5.GetMsg(),
					IsMaintaining: seeder.GetMaintainByGameKindIsNoMaintainingBy5.GetIsMaintaining(),
				},
				{
					GameKind:      constants.BBFish,
					Category:      constants.CategoryProb,
					StartTime:     seeder.GetMaintainByGameKindIsNoMaintainingBy38.GetBeginAt(),
					EndTime:       seeder.GetMaintainByGameKindIsNoMaintainingBy38.GetEndAt(),
					ModifyTime:    seeder.GetMaintainByGameKindIsNoMaintainingBy38.GetModifiedAt(),
					Message:       seeder.GetMaintainByGameKindIsNoMaintainingBy38.GetMsg(),
					IsMaintaining: seeder.GetMaintainByGameKindIsNoMaintainingBy38.GetIsMaintaining(),
				},
				{
					GameKind:      constants.NBBSport,
					Category:      constants.CategorySport,
					StartTime:     seeder.GetMaintainByGameKindIsNoMaintainingBy31.GetBeginAt(),
					EndTime:       seeder.GetMaintainByGameKindIsNoMaintainingBy31.GetEndAt(),
					ModifyTime:    seeder.GetMaintainByGameKindIsNoMaintainingBy31.GetModifiedAt(),
					Message:       seeder.GetMaintainByGameKindIsNoMaintainingBy31.GetMsg(),
					IsMaintaining: seeder.GetMaintainByGameKindIsNoMaintainingBy31.GetIsMaintaining(),
				},
				{
					GameKind:      constants.BBLottery,
					Category:      constants.CategoryLottery,
					StartTime:     seeder.GetMaintainByGameKindIsNoMaintainingBy12.GetBeginAt(),
					EndTime:       seeder.GetMaintainByGameKindIsNoMaintainingBy12.GetEndAt(),
					ModifyTime:    seeder.GetMaintainByGameKindIsNoMaintainingBy12.GetModifiedAt(),
					Message:       seeder.GetMaintainByGameKindIsNoMaintainingBy12.GetMsg(),
					IsMaintaining: seeder.GetMaintainByGameKindIsNoMaintainingBy12.GetIsMaintaining(),
				},
				{
					GameKind:      constants.BBBattle,
					Category:      constants.CategoryCard,
					StartTime:     seeder.GetMaintainByGameKindIsNoMaintainingBy66.GetBeginAt(),
					EndTime:       seeder.GetMaintainByGameKindIsNoMaintainingBy66.GetEndAt(),
					ModifyTime:    seeder.GetMaintainByGameKindIsNoMaintainingBy66.GetModifiedAt(),
					Message:       seeder.GetMaintainByGameKindIsNoMaintainingBy66.GetMsg(),
					IsMaintaining: seeder.GetMaintainByGameKindIsNoMaintainingBy66.GetIsMaintaining(),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetMaintainStatus_GetLobbyCategory_ConnectionFailed(t *testing.T) {
	enable := true
	LobbyCategoryRequest := game.LobbyCategoryRequest{
		Enable: &enable,
	}
	GameCtx := mock.NewMockGameCtx()
	GameCtx.On("LobbyCategory", ctx, LobbyCategoryRequest).Return(nil, errorx.ConnectionFailed)
	svcCtx.GameCtx = GameCtx

	request := &types.EmptyRequest{}
	l := NewGetMaintainStatusLogic(ctx, svcCtx)
	resp, err := l.GetMaintainStatus(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetMaintainStatus_GetMaintainByGameKind_ConnectionFailed(t *testing.T) {
	enable := true
	LobbyCategoryRequest := game.LobbyCategoryRequest{
		Enable: &enable,
	}
	GameCtx := mock.NewMockGameCtx()
	GameCtx.On("LobbyCategory", ctx, LobbyCategoryRequest).Return(&seeder.LobbyCategory, nil)

	MaintainCtx := mock.NewMockMaintainCtx()
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBLive,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = GameCtx
	svcCtx.MaintainCtx = MaintainCtx

	request := &types.EmptyRequest{}
	l := NewGetMaintainStatusLogic(ctx, svcCtx)
	resp, err := l.GetMaintainStatus(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
