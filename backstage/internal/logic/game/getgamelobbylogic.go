package game

import (
	"context"

	"gbh/backstage/internal/game"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGameLobbyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGameLobbyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGameLobbyLogic {
	return &GetGameLobbyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGameLobbyLogic) GetGameLobby(req *types.GameLobbyRequest) (*types.BaseResponse, error) {
	bbTip := false
	if req.BBTip != nil {
		bbTip = *req.BBTip
	}

	gameLobby, err := l.svcCtx.GameCtx.FlattenLobbyCategory(l.ctx, game.LobbyCategoryRequest{
		Enable:         req.Enable,               // 遊戲開關
		Report:         req.ReportEnable,         // 報表開關
		JP:             req.JackpotEnable,        // 是否有彩金
		Commissionable: req.CommissionableEnable, // 是否有退水
		External:       req.External,             // 是否為外接遊戲
		BBTip:          bbTip,                    // 是否需包含BB小費
	})
	if err != nil {
		return nil, err
	}

	return &types.BaseResponse{
		Data: gameLobby,
	}, nil
}
