package game

import (
	"gbh/admin/adminclient"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/jackpot"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/proto/user"
	"gbh/user/userclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJackpotHistoryOuterLayerLogic(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:    constants.BBCasino,
		StartDate:  seeder.StartDate,
		EndDate:    seeder.EndDate,
		Role:       constants.Role5,
		OperatorID: seeder.AdminID,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{
		HallId: []uint32{seeder.HallId},
	}, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	jackpotCtx := mock.NewMockJackpotCtx()
	jackpotCtx.On("GetJackpotInfo", ctx, jackpot.GetJackpotInfoRequest{
		StartDate: request.StartDate,
		EndDate:   request.EndDate,
		Role:      request.Role,
		HallID:    []uint32{seeder.HallId},
	}).Return(&seeder.GetJackpotInfo, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.UserCtx = userCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	expectedResp := seeder.GetJackpotInfo.GetJackpotInfo()

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: expectedResp,
	}, resp)
}

func TestJackpotHistoryOuterLayerLogic_GetHallListErr(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:   constants.BBCasino,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role5,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestJackpotHistoryOuterLayerLogic_GetUsersErr(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:   constants.BBCasino,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role5,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestJackpotHistoryOuterLayerLogic_GetJackpotInfoErr(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:   constants.BBCasino,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role5,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	jackpotCtx := mock.NewMockJackpotCtx()
	jackpotCtx.On("GetJackpotInfo", ctx, jackpot.GetJackpotInfoRequest{
		StartDate: request.StartDate,
		EndDate:   request.EndDate,
		Role:      request.Role,
		HallID:    []uint32{seeder.HallId},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestJackpotHistoryOuterLayerLogic_WithoutRole(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:   constants.BBCasino,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	jackpotCtx := mock.NewMockJackpotCtx()
	jackpotCtx.On("GetJackpotInfo", ctx, jackpot.GetJackpotInfoRequest{
		StartDate: request.StartDate,
		EndDate:   request.EndDate,
		Role:      1,
		HallID:    []uint32{seeder.HallId},
	}).Return(&seeder.GetJackpotInfo, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	expectedResp := seeder.GetJackpotInfo.GetJackpotInfo()

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: expectedResp,
	}, resp)
}

func TestJackpotHistoryOuterLayerLogic_GetUserByUserIdError(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:   constants.BBCasino,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role3,
		ParentID:  seeder.SubSupremeShareholderID,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)
	userCtx.On("GetUserByUserId", ctx, seeder.SubSupremeShareholderID).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestJackpotHistoryOuterLayerLogic_WithParentID(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:   constants.BBCasino,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role3,
		ParentID:  seeder.SubSupremeShareholderID,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	subSupremeShareholderID := user.GetResponse{
		Id:   seeder.SubSupremeShareholderID,
		Role: constants.Role4,
	}
	userCtx.On("GetUserByUserId", ctx, seeder.SubSupremeShareholderID).Return(&subSupremeShareholderID, nil)

	jackpotCtx := mock.NewMockJackpotCtx()
	jackpotCtx.On("GetJackpotInfo", ctx, jackpot.GetJackpotInfoRequest{
		StartDate: request.StartDate,
		EndDate:   request.EndDate,
		Role:      constants.Role4,
		HallID:    []uint32{seeder.HallId},
		UserID:    seeder.SubSupremeShareholderID,
	}).Return(&seeder.GetJackpotInfo, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.JackpotCtx = jackpotCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	expectedResp := seeder.GetJackpotInfo.GetJackpotInfo()

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{
		Data: expectedResp,
	}, resp)
}

func TestJackpotHistoryOuterLayerLogic_BackstageSearchRoleError(t *testing.T) {
	request := types.JackpotHistoryOuterLayerRequest{
		LobbyID:   constants.BBCasino,
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role5,
		ParentID:  seeder.SubSupremeShareholderID,
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	subSupremeShareholderID := user.GetResponse{
		Id:   seeder.SubSupremeShareholderID,
		Role: constants.Role4,
	}
	userCtx.On("GetUserByUserId", ctx, seeder.SubSupremeShareholderID).Return(&subSupremeShareholderID, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewJackpotHistoryOuterLayerLogic(ctx, svcCtx)
	resp, err := l.JackpotHistoryOuterLayer(&request)

	assert.Equal(t, errorx.BackstageSearchRoleError, err)
	assert.Nil(t, resp)
}
