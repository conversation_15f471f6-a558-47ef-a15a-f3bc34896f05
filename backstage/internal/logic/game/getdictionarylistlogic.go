package game

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/lang/langclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictionaryListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDictionaryListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictionaryListLogic {
	return &GetDictionaryListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictionaryListLogic) GetDictionaryList(req *types.DictionaryListRequest) (*types.BaseResponse, error) {
	lang := "zh-cn"
	if req.Lang != nil {
		lang = *req.Lang
	}

	gameDictionary := &langclient.GameDictionaryResponse{}
	var err error
	switch req.Type {
	case "relate":
		gameDictionary, err = l.svcCtx.LangCtx.GetGameFeatures(l.ctx, lang, req.GameKind)
	case "playtype":
		gameDictionary, err = l.svcCtx.LangCtx.GetGamePlayType(l.ctx, lang, req.GameKind)
	case "round_result":
		gameDictionary, err = l.svcCtx.LangCtx.GetGameRoundResult(l.ctx, lang, req.GameKind)
	}

	if err != nil {
		return nil, err
	}

	resp := &types.BaseResponse{
		Data: gameDictionary.GetData(),
	}

	return resp, nil
}
