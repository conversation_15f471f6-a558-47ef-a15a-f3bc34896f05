package game

import (
	"context"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"

	"gbh/backstage/internal/game"

	"gbh/proto/maintain"
	"gbh/utils/strutil"
)

type maintainResponse struct {
	Maintain []maintainInfo `json:"maintains"`
}

type maintainInfo struct {
	GameKind      uint32 `json:"game_kind"`
	Category      string `json:"category"`
	StartTime     string `json:"start_time"`
	EndTime       string `json:"end_time"`
	ModifyTime    string `json:"modify_time"`
	Message       string `json:"message"`
	IsMaintaining bool   `json:"is_maintaining"`
}

type GetMaintainStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMaintainStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMaintainStatusLogic {
	return &GetMaintainStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMaintainStatusLogic) GetMaintainStatus(_ *types.EmptyRequest) (resp *types.BaseResponse, err error) {
	// 取得所有大廳
	enable := true
	lobbyCategory, err := l.svcCtx.GameCtx.LobbyCategory(l.ctx, game.LobbyCategoryRequest{
		Enable: &enable,
	})
	if err != nil {
		return nil, err
	}

	respList := make([]maintainInfo, 0)
	// 整理視訊分類的大廳
	for _, lobby := range lobbyCategory.GetLive() {
		respList = append(respList, maintainInfo{
			GameKind: strutil.StringToUint32(lobby),
			Category: constants.CategoryLive,
		})
	}

	// 整理機率分類的大廳
	for _, lobby := range lobbyCategory.GetProb() {
		respList = append(respList, maintainInfo{
			GameKind: strutil.StringToUint32(lobby),
			Category: constants.CategoryProb,
		})
	}

	// 整理體育分類的大廳
	for _, lobby := range lobbyCategory.GetSport() {
		respList = append(respList, maintainInfo{
			GameKind: strutil.StringToUint32(lobby),
			Category: constants.CategorySport,
		})
	}

	// 整理彩票分類的大廳
	for _, lobby := range lobbyCategory.GetLottery() {
		respList = append(respList, maintainInfo{
			GameKind: strutil.StringToUint32(lobby),
			Category: constants.CategoryLottery,
		})
	}

	// 整理棋牌分類的大廳
	for _, lobby := range lobbyCategory.GetCard() {
		respList = append(respList, maintainInfo{
			GameKind: strutil.StringToUint32(lobby),
			Category: constants.CategoryCard,
		})
	}

	// 取得維護資訊 & 整理回傳資料
	for k, v := range respList {
		maintainByGameKind, err := l.svcCtx.MaintainCtx.GetMaintainByGameKind(l.ctx, &maintain.GetMaintainByGameKindRequest{
			GameKind: v.GameKind,
		})
		if err != nil {
			return nil, err
		}

		respList[k].StartTime = maintainByGameKind.GetBeginAt()
		respList[k].EndTime = maintainByGameKind.GetEndAt()
		respList[k].ModifyTime = maintainByGameKind.GetModifiedAt()
		respList[k].Message = maintainByGameKind.GetMsg()
		respList[k].IsMaintaining = maintainByGameKind.GetIsMaintaining()
	}

	return &types.BaseResponse{
		Data: maintainResponse{
			Maintain: respList,
		},
	}, nil
}
