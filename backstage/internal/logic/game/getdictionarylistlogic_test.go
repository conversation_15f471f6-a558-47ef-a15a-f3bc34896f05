package game

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetDictionaryByType_GetGameFeatures(t *testing.T) {
	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGameFeatures", ctx, "zh-cn", uint32(3)).Return(&seeder.GetGameFeaturesZhCn, nil)

	svcCtx.LangCtx = langCtx

	request := &types.DictionaryListRequest{
		GameKind: 3,
		Type:     "relate",
	}
	l := NewGetDictionaryListLogic(ctx, svcCtx)
	resp, err := l.GetDictionaryList(request)

	expected := &types.BaseResponse{
		Data: seeder.GetGameFeaturesZhCn.GetData(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetDictionaryByType_GetGamePlayType(t *testing.T) {
	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGamePlayType", ctx, "zh-cn", uint32(3)).Return(&seeder.GetGamePlayTypeZhCn, nil)

	svcCtx.LangCtx = langCtx

	request := &types.DictionaryListRequest{
		GameKind: 3,
		Type:     "playtype",
	}
	l := NewGetDictionaryListLogic(ctx, svcCtx)
	resp, err := l.GetDictionaryList(request)

	expected := &types.BaseResponse{
		Data: seeder.GetGamePlayTypeZhCn.GetData(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetDictionaryByType_GetGameRoundResult(t *testing.T) {
	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGameRoundResult", ctx, "zh-cn", uint32(3)).Return(&seeder.GetGameRoundResultZhCn, nil)

	svcCtx.LangCtx = langCtx

	request := &types.DictionaryListRequest{
		GameKind: 3,
		Type:     "round_result",
	}
	l := NewGetDictionaryListLogic(ctx, svcCtx)
	resp, err := l.GetDictionaryList(request)

	expected := &types.BaseResponse{
		Data: seeder.GetGameRoundResultZhCn.GetData(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetDictionaryByType_GetGameFeatures_WithLang(t *testing.T) {
	lang := "zh-tw"
	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGameFeatures", ctx, lang, uint32(3)).Return(&seeder.GetGameFeaturesZhTw, nil)

	svcCtx.LangCtx = langCtx

	request := &types.DictionaryListRequest{
		GameKind: 3,
		Type:     "relate",
		Lang:     &lang,
	}
	l := NewGetDictionaryListLogic(ctx, svcCtx)
	resp, err := l.GetDictionaryList(request)

	expected := &types.BaseResponse{
		Data: seeder.GetGameFeaturesZhTw.GetData(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expected, resp)
}

func TestGetTables_GetDictionaryByType_GetGameFeatures_ConnectionFailed(t *testing.T) {
	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGameFeatures", ctx, "zh-cn", uint32(3)).Return(nil, errorx.ConnectionFailed)

	svcCtx.LangCtx = langCtx

	request := &types.DictionaryListRequest{
		GameKind: 3,
		Type:     "relate",
	}
	l := NewGetDictionaryListLogic(ctx, svcCtx)
	resp, err := l.GetDictionaryList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetTables_GetDictionaryByType_GetGamePlayType_ConnectionFailed(t *testing.T) {
	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGamePlayType", ctx, "zh-cn", uint32(3)).Return(nil, errorx.ConnectionFailed)

	svcCtx.LangCtx = langCtx

	request := &types.DictionaryListRequest{
		GameKind: 3,
		Type:     "playtype",
	}
	l := NewGetDictionaryListLogic(ctx, svcCtx)
	resp, err := l.GetDictionaryList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetTables_GetDictionaryByType_GetGameRoundResult_ConnectionFailed(t *testing.T) {
	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGameRoundResult", ctx, "zh-cn", uint32(3)).Return(nil, errorx.ConnectionFailed)

	svcCtx.LangCtx = langCtx

	request := &types.DictionaryListRequest{
		GameKind: 3,
		Type:     "round_result",
	}
	l := NewGetDictionaryListLogic(ctx, svcCtx)
	resp, err := l.GetDictionaryList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
