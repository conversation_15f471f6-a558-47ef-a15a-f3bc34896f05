package game

import (
	"context"

	"gbh/backstage/internal/jackpot"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/jackpot/jackpotclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type JackpotHistoryOuterLayerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewJackpotHistoryOuterLayerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *JackpotHistoryOuterLayerLogic {
	return &JackpotHistoryOuterLayerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *JackpotHistoryOuterLayerLogic) JackpotHistoryOuterLayer(req *types.JackpotHistoryOuterLayerRequest) (resp *types.BaseResponse, err error) {
	admin := uint32(0)
	if req.OperatorID > 0 {
		admin = req.OperatorID
	}
	hallListResp, err := repository.GetAdminPermissionHallList(l.ctx, l.svcCtx, admin, true)

	if err != nil {
		return nil, err
	}

	hallList := []uint32{}
	for _, v := range hallListResp {
		hallList = append(hallList, v.HallID)
	}

	role := req.Role
	if req.Role == 0 {
		role = 1
	}

	request := jackpot.GetJackpotInfoRequest{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		HallID:    hallList,
		JpTypeID:  req.JPType,
		GameID:    req.GameID,
		Role:      role,
	}

	// 有帶入搜尋層級，驗證是否大於或等於自身層級
	if req.ParentID > 0 {
		parentInfo, err := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.ParentID)

		if err != nil {
			return nil, err
		}

		if role >= parentInfo.GetRole() {
			return nil, errorx.BackstageSearchRoleError
		}

		request.UserID = req.ParentID
		request.Role = parentInfo.GetRole()
	}

	result := []*jackpotclient.JackpotInfo{}
	if len(hallList) > 0 {
		jackpotInfoResp, err := l.svcCtx.JackpotCtx.GetJackpotInfo(l.ctx, request)

		if err != nil {
			return nil, err
		}

		if len(jackpotInfoResp.GetJackpotInfo()) > 0 {
			result = jackpotInfoResp.GetJackpotInfo()
		}
	}

	return &types.BaseResponse{
		Data: result,
	}, nil
}
