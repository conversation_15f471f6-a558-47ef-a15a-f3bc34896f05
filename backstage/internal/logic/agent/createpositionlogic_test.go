package agent

import (
	"bytes"
	"context"
	"encoding/json"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

const apiCreatePosition = "/api/agent/position"

func TestCreatePositionLogic(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID674,
					Name:   "厅主讯息",
					Enable: true,
					Modify: false,
				},
			},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}
	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Menu[0].ID, constants.Enable).Return(true, nil)
	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Special[0].ID, constants.Enable).Return(true, nil)

	mockAgentCtx.On("CreatePosition", ctx, agent.CreatePositionRequest{
		HallID: request.HallID,
		Name:   request.Name,
		Note:   request.Note,
		Permissions: []agent.PositionPermission{
			{
				ID:     seeder.UserPermID2038,
				Modify: true,
			},
			{
				ID:     seeder.UserPermID674,
				Modify: false,
			},
		},
	}).Return(nil)

	mockOperationRecordCtx := mock.NewMockOperationRecordCtx()
	mockOperationRecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       request.HallID,
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][18],
		UserKey:      "",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "parent",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "domain",
				Value: seeder.User.GetName(),
			},
			{
				Key:   "name",
				Value: request.Name,
			},
			{
				Key:   "note",
				Value: request.Note,
			},
			{
				Key:   "menuperm",
				Value: "<b>- 一般权限 -</b><br>游戏管理->游戏平台管理->游戏维护资讯: <span style='color:blue'>可检视/修改</span><br>",
			},
			{
				Key:   "specialperm",
				Value: "<b>- 特殊权限 -</b><br>其他->厅主讯息: <span style='color:green'>可检视</span><br>",
			},
		},
		XMLCancelTag: false,
		URI:          apiCreatePosition,
		IP:           seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx
	svcCtx.OperationRecordCtx = mockOperationRecordCtx

	r = &http.Request{
		RemoteAddr: seeder.RemoteAddr,
		RequestURI: apiCreatePosition,
	}

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	expectedResponse := &types.BaseResponse{
		Data: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestCreatePositionLogic_GetSession_DuplicateUserPosition(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID674,
					Name:   "厅主讯息",
					Enable: true,
					Modify: true,
				},
			},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&seeder.AgentPositionList, nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.BackstageDuplicateUserPosition, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_checkPermissionTypeIsCategory_ReturnTrue(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
				{
					ID:     seeder.UserPermID400,
					Name:   "游戏管理",
					Enable: true,
					Modify: true,
				},
				{
					ID:     999999,
					Name:   "test",
					Enable: true,
					Modify: true,
				},
			},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}

	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Menu[0].ID, constants.Enable).Return(true, nil)

	mockAgentCtx.On("CreatePosition", ctx, agent.CreatePositionRequest{
		HallID: request.HallID,
		Name:   request.Name,
		Note:   request.Note,
		Permissions: []agent.PositionPermission{
			{
				ID:     seeder.UserPermID2038,
				Modify: true,
			},
		},
	}).Return(nil)

	mockOperationRecordCtx := mock.NewMockOperationRecordCtx()
	mockOperationRecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       request.HallID,
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][18],
		UserKey:      "",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "parent",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "domain",
				Value: seeder.User.GetName(),
			},
			{
				Key:   "name",
				Value: request.Name,
			},
			{
				Key:   "note",
				Value: request.Note,
			},
			{
				Key:   "menuperm",
				Value: "<b>- 一般权限 -</b><br>游戏管理->游戏平台管理->游戏维护资讯: <span style='color:blue'>可检视/修改</span><br>",
			},
			{
				Key:   "specialperm",
				Value: "",
			},
		},
		XMLCancelTag: false,
		URI:          apiCreatePosition,
		IP:           seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx
	svcCtx.OperationRecordCtx = mockOperationRecordCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	expectedResponse := &types.BaseResponse{
		Data: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestCreatePositionLogic_CheckUserPermission_ReturnFalse(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}

	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Menu[0].ID, constants.Enable).Return(false, nil)

	mockAgentCtx.On("CreatePosition", ctx, agent.CreatePositionRequest{
		HallID:      request.HallID,
		Name:        request.Name,
		Note:        request.Note,
		Permissions: []agent.PositionPermission{},
	}).Return(nil)

	mockOperationRecordCtx := mock.NewMockOperationRecordCtx()
	mockOperationRecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       request.HallID,
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][18],
		UserKey:      "",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "parent",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "domain",
				Value: seeder.User.GetName(),
			},
			{
				Key:   "name",
				Value: request.Name,
			},
			{
				Key:   "note",
				Value: request.Note,
			},
			{
				Key:   "menuperm",
				Value: "",
			},
			{
				Key:   "specialperm",
				Value: "",
			},
		},
		XMLCancelTag: false,
		URI:          apiCreatePosition,
		IP:           seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx
	svcCtx.OperationRecordCtx = mockOperationRecordCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	expectedResponse := &types.BaseResponse{
		Data: true,
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestCreatePositionLogic_OperatorNotFound(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	l := NewCreatePositionLogic(context.Background(), svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_GetHallById_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = mockHallCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_GetGMHallPrivilege_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_GetPositionList_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_GetUserByUserId_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_GetAllPermConfigMap_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_GetWeblateLang_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_CheckUserPermission_Menu_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}
	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Menu[0].ID, constants.Enable).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_CheckUserPermission_Special_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{},
			Special: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID674,
					Name:   "厅主讯息",
					Enable: true,
					Modify: true,
				},
			},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}
	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Special[0].ID, constants.Enable).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_CreatePosition_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}
	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Menu[0].ID, constants.Enable).Return(true, nil)

	mockAgentCtx.On("CreatePosition", ctx, agent.CreatePositionRequest{
		HallID: request.HallID,
		Name:   request.Name,
		Note:   request.Note,
		Permissions: []agent.PositionPermission{
			{
				ID:     seeder.UserPermID2038,
				Modify: true,
			},
		},
	}).Return(errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreatePositionLogic_CreateAGRecord_ConnectionFailed(t *testing.T) {
	request := &types.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: types.Permissions{
			Menu: []types.PermissionInfo{
				{
					ID:     seeder.UserPermID2038,
					Name:   "游戏维护资讯",
					Enable: true,
					Modify: true,
				},
			},
			Special: []types.PermissionInfo{},
		},
	}

	body, err := json.Marshal(request)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	r := httptest.NewRequest(http.MethodPost, apiCreatePosition, reader)
	r.RequestURI = apiCreatePosition
	r.RemoteAddr = seeder.ClientIP

	mockAdminCtx := mock.NewMockAdminCtx()

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(&seeder.GetHallById, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   request.HallID,
		Position: request.Name,
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}
	mockPermissionCtx.On("CheckUserPermission", ctx, operator, request.Permissions.Menu[0].ID, constants.Enable).Return(true, nil)

	mockAgentCtx.On("CreatePosition", ctx, agent.CreatePositionRequest{
		HallID: request.HallID,
		Name:   request.Name,
		Note:   request.Note,
		Permissions: []agent.PositionPermission{
			{
				ID:     seeder.UserPermID2038,
				Modify: true,
			},
		},
	}).Return(nil)

	mockOperationRecordCtx := mock.NewMockOperationRecordCtx()
	mockOperationRecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       request.HallID,
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][18],
		UserKey:      "",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "parent",
				Value: seeder.User.GetUsername(),
			},
			{
				Key:   "domain",
				Value: seeder.User.GetName(),
			},
			{
				Key:   "name",
				Value: request.Name,
			},
			{
				Key:   "note",
				Value: request.Note,
			},
			{
				Key:   "menuperm",
				Value: "<b>- 一般权限 -</b><br>游戏管理->游戏平台管理->游戏维护资讯: <span style='color:blue'>可检视/修改</span><br>",
			},
			{
				Key:   "specialperm",
				Value: "",
			},
		},
		XMLCancelTag: false,
		URI:          apiCreatePosition,
		IP:           seeder.ClientIP,
	}).Return(errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx
	svcCtx.OperationRecordCtx = mockOperationRecordCtx

	l := NewCreatePositionLogic(ctx, svcCtx)
	resp, err := l.CreatePosition(request, r)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
