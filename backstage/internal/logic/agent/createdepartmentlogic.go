package agent

import (
	"context"
	"html"

	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/proto/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDepartmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDepartmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDepartmentLogic {
	return &CreateDepartmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDepartmentLogic) CreateDepartment(req *types.CreateAgentDepartmentRequest, requestURI string) (resp *types.BaseResponse, err error) {
	// 取得管理員ID
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	// 檢查管理員權限
	checkPermissionErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), req.HallID)
	if checkPermissionErr != nil {
		return nil, checkPermissionErr
	}

	// 如果沒帶上層帳號ID，則使用上層帳號名稱去取得上層帳號ID
	var userData *user.GetResponse
	var getUserByUsernameErr error
	var getUserByUserIdErr error
	if req.ParentID == 0 {
		userData, getUserByUsernameErr = l.svcCtx.UserCtx.GetUserByUsername(l.ctx, req.HallID, req.ParentName)
		if getUserByUsernameErr != nil {
			return nil, getUserByUsernameErr
		}
	} else {
		userData, getUserByUserIdErr = l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.ParentID)
		if getUserByUserIdErr != nil {
			return nil, getUserByUserIdErr
		}
	}

	// 判斷上層是否有管理者帳號權限
	parent := types.UserInfo{
		Id:           userData.GetId(),
		ParentId:     userData.GetParent(),
		AllParentsId: userData.GetAllParents(),
		Role:         userData.GetRole(),
		IsSub:        userData.GetSub(),
	}

	checkMenuPermission, checkMenuPermissionErr := repository.CheckMenuPermission(l.ctx, l.svcCtx, parent, constants.UserPermID1605, "enable")
	if checkMenuPermissionErr != nil {
		return nil, checkMenuPermissionErr
	}

	if !checkMenuPermission {
		return nil, errorx.BackstageParentNoPermission
	}

	// 檢查有無重複單位
	hasDepaeartmentErr := l.svcCtx.AgentCtx.HasDepartment(l.ctx, agent.HasDepartmentRequest{
		HallID:  req.HallID,
		Role:    userData.GetRole(),
		OwnerID: userData.GetId(),
		Name:    req.DepartmentName,
	})
	if hasDepaeartmentErr != nil {
		return nil, hasDepaeartmentErr
	}

	// 新增單位
	createDepartmentErr := l.svcCtx.AgentCtx.CreateDepartment(l.ctx, agent.CreateDepartmentRequest{
		HallID:  req.HallID,
		Role:    userData.GetRole(),
		OwnerID: userData.GetId(),
		Name:    req.DepartmentName,
		Note:    req.Note,
	})
	if createDepartmentErr != nil {
		return nil, createDepartmentErr
	}

	// 組合XMLDataMsg
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "role",
			Value: constants.RoleDictionaryMapping[userData.GetRole()],
		},
		{
			Key:   "parent",
			Value: userData.GetUsername(),
		},
		{
			Key:   "name",
			Value: html.EscapeString(req.DepartmentName),
		},
		{
			Key:   "note",
			Value: html.EscapeString(req.Note),
		},
	}

	// 寫操作記錄
	createAGRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, operationrecord.CreateAGRecordRequest{
		HallId:       req.HallID,
		OperatorId:   admin.ID(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     userData.GetId(),
		TargetRoleId: userData.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][15],
		UserKey:      constants.AGRecordMainKey["user"][15],
		XMLDataMsg:   xmlDataMsg,
		URI:          requestURI,
		IP:           admin.IP(),
	})
	if createAGRecordErr != nil {
		return nil, createAGRecordErr
	}

	return &types.BaseResponse{}, nil
}
