package agent

import (
	"context"
	"html"
	"testing"

	"gbh/admin/adminclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/hall/hallclient"

	"github.com/stretchr/testify/assert"
)

func TestCreateDepartment_GetOperatorError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentName:     "bbingbgp",
	}

	l := NewCreateDepartmentLogic(context.Background(), svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_CheckAdminPermissionForHallIdError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentName:     "bbingbgp",
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&hallclient.GetHallByIdResponse{
		HallId: seeder.HallId,
		Enable: false,
	}, nil)

	svcCtx.HallCtx = hallCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_GetUserByUsernameError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentName:     "bbingbgp",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUsername", ctx, seeder.HallId, request.ParentName).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_GetUserByUserIdError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentID:       seeder.HallId,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, request.ParentID).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_CheckMenuPermissionError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentID:       seeder.HallId,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, request.ParentID).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_ParentNoPermission(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentID:       seeder.HallId,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, request.ParentID).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(false, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageParentNoPermission, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_HasDepartmentError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentID:       seeder.HallId,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, request.ParentID).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, constants.UserPermID1605).Return(&types.UserPermissionConfig{})

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("HasDepartment", ctx, agent.HasDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: request.ParentID,
		Name:    request.DepartmentName,
	}).Return(errorx.BackstageDuplicateUserDepartment)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.AgentCtx = agentCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageDuplicateUserDepartment, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_CreateDepartmentError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentID:       seeder.HallId,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, request.ParentID).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, constants.UserPermID1605).Return(&types.UserPermissionConfig{})

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("HasDepartment", ctx, agent.HasDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: request.ParentID,
		Name:    request.DepartmentName,
	}).Return(nil)
	agentCtx.On("CreateDepartment", ctx, agent.CreateDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: request.ParentID,
		Name:    request.DepartmentName,
		Note:    request.Note,
	}).Return(errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.AgentCtx = agentCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_CreateAGRecordError(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentID:       seeder.HallId,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, request.ParentID).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, constants.UserPermID1605).Return(&types.UserPermissionConfig{})

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("HasDepartment", ctx, agent.HasDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: request.ParentID,
		Name:    request.DepartmentName,
	}).Return(nil)
	agentCtx.On("CreateDepartment", ctx, agent.CreateDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: request.ParentID,
		Name:    request.DepartmentName,
		Note:    request.Note,
	}).Return(nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       request.HallID,
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Add_Department",
		UserKey:      "R_Add_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "name",
				Value: html.EscapeString(request.DepartmentName),
			},
			{
				Key:   "note",
				Value: html.EscapeString(request.Note),
			},
		},
		URI: seeder.CreateAgentDepartmentRequestURI,
		IP:  seeder.ClientIP,
	}).Return(errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCreateDepartment_WithParentID(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentID:       seeder.HallId,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, request.ParentID).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, constants.UserPermID1605).Return(&types.UserPermissionConfig{})

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("HasDepartment", ctx, agent.HasDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: request.ParentID,
		Name:    request.DepartmentName,
	}).Return(nil)
	agentCtx.On("CreateDepartment", ctx, agent.CreateDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: request.ParentID,
		Name:    request.DepartmentName,
		Note:    request.Note,
	}).Return(nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       request.HallID,
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Add_Department",
		UserKey:      "R_Add_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "name",
				Value: html.EscapeString(request.DepartmentName),
			},
			{
				Key:   "note",
				Value: html.EscapeString(request.Note),
			},
		},
		URI: seeder.CreateAgentDepartmentRequestURI,
		IP:  seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	expectedResponse := &types.BaseResponse{}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestCreateDepartment_WithParentName(t *testing.T) {
	request := &types.CreateAgentDepartmentRequest{
		HallID:         seeder.HallId,
		DepartmentName: "test",
		Note:           "test",
		ParentName:     "bbinbgp",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUsername", ctx, request.HallID, request.ParentName).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, constants.UserPermID1605).Return(&types.UserPermissionConfig{})

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("HasDepartment", ctx, agent.HasDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: seeder.User.GetId(),
		Name:    request.DepartmentName,
	}).Return(nil)
	agentCtx.On("CreateDepartment", ctx, agent.CreateDepartmentRequest{
		HallID:  request.HallID,
		Role:    seeder.User.GetRole(),
		OwnerID: seeder.User.GetId(),
		Name:    request.DepartmentName,
		Note:    request.Note,
	}).Return(nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       request.HallID,
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Add_Department",
		UserKey:      "R_Add_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "name",
				Value: html.EscapeString(request.DepartmentName),
			},
			{
				Key:   "note",
				Value: html.EscapeString(request.Note),
			},
		},
		URI: seeder.CreateAgentDepartmentRequestURI,
		IP:  seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewCreateDepartmentLogic(ctx, svcCtx)
	resp, err := l.CreateDepartment(request, seeder.CreateAgentDepartmentRequestURI)

	expectedResponse := &types.BaseResponse{}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
