package agent

import (
	"context"

	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPositionResponse struct {
	List     []positionData         `json:"list"`
	Paginate types.PaginateResponse `json:"pagination"`
}

type positionData struct {
	ID         uint32 `json:"id"`
	Name       string `json:"name"`
	Note       string `json:"note"`
	CreatedAt  string `json:"created_at"`
	Count      uint32 `json:"count"`
	OwnerID    uint32 `json:"owner_id"`
	ParentName string `json:"parent_name"`
}

type GetPositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPositionLogic {
	return &GetPositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPositionLogic) GetPosition(req *types.GetPositionRequest) (*types.BaseResponse, error) {
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	// 檢查操作者是否有權限
	checkPermissionErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), req.HallID)
	if checkPermissionErr != nil {
		return nil, checkPermissionErr
	}

	// 取得職位列表
	positionList, positionListErr := l.svcCtx.AgentCtx.GetPositionList(l.ctx, agent.GetPositionListRequest{
		HallID:    req.HallID,
		Position:  req.Position,
		Fuzzy:     req.Fuzzy,
		Order:     req.Order,
		Sort:      req.Sort,
		Page:      req.Page,
		PageLimit: req.PageLimit,
	})
	if positionListErr != nil {
		return nil, positionListErr
	}

	// 取得上層帳號名稱
	owner, ownerErr := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.HallID)
	if ownerErr != nil {
		return nil, ownerErr
	}

	list := make([]positionData, 0, len(positionList.GetList()))
	for _, position := range positionList.GetList() {
		list = append(list, positionData{
			ID:         position.GetId(),
			Name:       position.GetName(),
			Note:       position.GetNote(),
			CreatedAt:  position.GetCreatedAt(),
			Count:      position.GetCount(),
			OwnerID:    owner.GetId(),
			ParentName: owner.GetUsername(),
		})
	}

	return &types.BaseResponse{
		Data: GetPositionResponse{
			List: list,
			Paginate: types.PaginateResponse{
				Page:        positionList.GetPagination().GetCurrentPage(),
				PageLimit:   positionList.GetPagination().GetPageLimit(),
				TotalNumber: positionList.GetPagination().GetTotal(),
				TotalPage:   positionList.GetPagination().GetTotalPage(),
			},
		},
	}, nil
}
