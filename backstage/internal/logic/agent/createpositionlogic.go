package agent

import (
	"context"
	"html"
	"net/http"
	"strings"

	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreatePositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreatePositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePositionLogic {
	return &CreatePositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreatePositionLogic) CreatePosition(req *types.CreatePositionRequest, r *http.Request) (*types.BaseResponse, error) {
	admin, err := operator.GetOperator(l.ctx)
	if err != nil {
		return nil, err
	}

	// 檢查操作者是否有權限
	checkPermissionErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), req.HallID)
	if checkPermissionErr != nil {
		return nil, checkPermissionErr
	}

	// 檢查是否存在相同職務名稱
	checkDuplicateErr := l.checkDuplicatePosition(req.HallID, req.Name)
	if checkDuplicateErr != nil {
		return nil, checkDuplicateErr
	}

	hallInfo, hallInfoErr := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, req.HallID)
	if hallInfoErr != nil {
		return nil, hallInfoErr
	}

	hall := types.UserInfo{
		Id:           hallInfo.GetId(),
		Domain:       hallInfo.GetDomain(),
		ParentId:     hallInfo.GetParent(),
		AllParentsId: hallInfo.GetAllParents(),
		Role:         hallInfo.GetRole(),
		IsSub:        hallInfo.GetSub(),
	}

	// 取得使用者權限設定
	permConfigMap, permConfigMapErr := l.svcCtx.PermissionCtx.GetAllPermConfigMap(l.ctx)
	if permConfigMapErr != nil {
		return nil, permConfigMapErr
	}

	// 取得選單字典檔
	agentMenuLang, agentMenuLangErr := l.svcCtx.LangCtx.GetWeblateLang(l.ctx, constants.ZhCn, "agent_menu")
	if agentMenuLangErr != nil {
		return nil, agentMenuLangErr
	}

	// 取得該層級可開啟的權限
	menuPermissions, menuPermStr, menuPermissionsErr := l.getEnablePermissions(hall, req.Permissions.Menu, permConfigMap, agentMenuLang.GetData())
	if menuPermissionsErr != nil {
		return nil, menuPermissionsErr
	}
	if menuPermStr != "" {
		menuPermStr = "<b>- 一般权限 -</b><br>" + menuPermStr
	}

	specialPermissions, specialPermStr, specialPermissionsErr := l.getEnablePermissions(hall, req.Permissions.Special, permConfigMap, agentMenuLang.GetData())
	if specialPermissionsErr != nil {
		return nil, specialPermissionsErr
	}
	if specialPermStr != "" {
		specialPermStr = "<b>- 特殊权限 -</b><br>" + specialPermStr
	}

	// 整合權限資料
	permissions := []agent.PositionPermission{}
	permissions = append(permissions, menuPermissions...)
	permissions = append(permissions, specialPermissions...)

	// 新增職務
	createPositionErr := l.svcCtx.AgentCtx.CreatePosition(l.ctx, agent.CreatePositionRequest{
		HallID:      req.HallID,
		Name:        req.Name,
		Note:        req.Note,
		Permissions: permissions,
	})

	if createPositionErr != nil {
		return nil, createPositionErr
	}

	// 寫操作紀錄
	createAGRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, operationrecord.CreateAGRecordRequest{
		HallId:       req.HallID,
		OperatorId:   admin.ID(),
		ActionId:     constants.ActionId76,
		TargetId:     hallInfo.GetId(),
		TargetRoleId: hallInfo.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][18],
		UserKey:      "",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "parent",
				Value: hallInfo.GetUsername(),
			},
			{
				Key:   "domain",
				Value: hallInfo.GetName(),
			},
			{
				Key:   "name",
				Value: html.EscapeString(req.Name),
			},
			{
				Key:   "note",
				Value: html.EscapeString(req.Note),
			},
			{
				Key:   "menuperm",
				Value: menuPermStr,
			},
			{
				Key:   "specialperm",
				Value: specialPermStr,
			},
		},
		URI: r.RequestURI,
		IP:  admin.IP(),
	})

	if createAGRecordErr != nil {
		return nil, createAGRecordErr
	}

	return &types.BaseResponse{
		Data: true,
	}, nil
}

func (l *CreatePositionLogic) checkDuplicatePosition(hallId uint32, name string) error {
	positionList, err := l.svcCtx.AgentCtx.GetPositionList(l.ctx, agent.GetPositionListRequest{
		HallID:   hallId,
		Position: name,
		Fuzzy:    false,
	})
	if err != nil {
		return err
	}

	if len(positionList.GetList()) > 0 {
		return errorx.BackstageDuplicateUserPosition
	}

	return nil
}

func (l *CreatePositionLogic) getEnablePermissions(hall types.UserInfo, permissions []types.PermissionInfo, permConfigMap map[uint32]types.UserPermissionConfig, agentMenuLang map[string]string) ([]agent.PositionPermission, string, error) {
	permissionList := []agent.PositionPermission{}
	permissionStr := ""

	for _, v := range permissions {
		// Category 層權限不新增進職務
		if checkPermissionTypeIsCategory(v.ID, permConfigMap) {
			continue
		}

		enable, checkErr := l.svcCtx.PermissionCtx.CheckUserPermission(l.ctx, hall, v.ID, constants.Enable)
		if checkErr != nil {
			return nil, "", checkErr
		}

		if !enable {
			continue
		}

		permissionList = append(permissionList, agent.PositionPermission{
			ID:     v.ID,
			Modify: v.Modify,
		})

		tmpStr := l.buildPermissionsString(v, permConfigMap, agentMenuLang)
		permissionStr += tmpStr
	}

	return permissionList, permissionStr, nil
}

// 檢查權限是否為Category層
func checkPermissionTypeIsCategory(permId uint32, permConfigMap map[uint32]types.UserPermissionConfig) bool {
	// 權限不存在
	if _, exists := permConfigMap[permId]; !exists {
		return true
	}

	if strings.Contains(permConfigMap[permId].Type, "Category") {
		return true
	}

	return false
}

// 組職務權限字串
func (l *CreatePositionLogic) buildPermissionsString(permissionInfo types.PermissionInfo, permConfigMap map[uint32]types.UserPermissionConfig, agentMenuLang map[string]string) string {
	permissionStr := agentMenuLang[permConfigMap[permissionInfo.ID].Dict]
	parentId := permConfigMap[permissionInfo.ID].ParentId

	// 掃父層權限
	for parentId != 0 {
		permissionStr = agentMenuLang[permConfigMap[parentId].Dict] + "->" + permissionStr
		parentId = permConfigMap[parentId].ParentId

		if parentId == 0 {
			modifyStr := ": <span style='color:blue'>可检视/修改</span><br>"
			if !permissionInfo.Modify {
				modifyStr = ": <span style='color:green'>可检视</span><br>"
			}
			permissionStr += modifyStr
		}
	}

	return permissionStr
}
