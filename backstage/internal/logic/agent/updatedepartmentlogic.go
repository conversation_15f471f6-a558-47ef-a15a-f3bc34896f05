package agent

import (
	"context"
	"html"

	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDepartmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDepartmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDepartmentLogic {
	return &UpdateDepartmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDepartmentLogic) UpdateDepartment(req *types.UpdateAgentDepartmentRequest, requestURI string) (resp *types.BaseResponse, err error) {
	admin, err := operator.GetOperator(l.ctx)

	if err != nil {
		return nil, err
	}

	// 檢查單位是否存在
	parameterSetResponse, parameterSetResponseErr := l.svcCtx.AgentCtx.GetParameterSet(l.ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{req.DepartmentID},
		Type: "department",
	})
	if parameterSetResponseErr != nil {
		return nil, parameterSetResponseErr
	}

	if len(parameterSetResponse.GetParameterSet()) == 0 {
		return nil, errorx.BackstageUserDepartmentNotFound
	}

	// 取得單位的上層id, hall
	departmentInfoResponse, departmentInfoResponseErr := l.svcCtx.AgentCtx.DepartmentInfoList(l.ctx, agent.DepartmentInfoListRequest{
		ParameterID: req.DepartmentID,
	})
	if departmentInfoResponseErr != nil {
		return nil, departmentInfoResponseErr
	}

	if len(departmentInfoResponse.GetList()) == 0 {
		return nil, errorx.BackstageUserDepartmentNotFound
	}

	departmentInfo := departmentInfoResponse.GetList()[0]

	// 檢查管理員權限
	checkPermissionErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), departmentInfo.GetHallId())
	if checkPermissionErr != nil {
		return nil, checkPermissionErr
	}

	// 檢查有無重複單位
	departmentListResponse, departmentListResponseErr := l.svcCtx.AgentCtx.DepartmentInfoList(l.ctx, agent.DepartmentInfoListRequest{
		OwnerID: departmentInfo.GetOwnerId(),
		Name:    req.DepartmentName,
	})
	if departmentListResponseErr != nil {
		return nil, departmentListResponseErr
	}

	for _, v := range departmentListResponse.GetList() {
		if v.GetId() != departmentInfo.GetId() {
			return nil, errorx.BackstageDuplicateUserDepartment
		}
	}

	// 修改單位資訊
	updateDepartmentErr := l.svcCtx.AgentCtx.UpdateDepartment(l.ctx, agent.UpdateDepartmentRequest{
		ID:   req.DepartmentID,
		Name: req.DepartmentName,
		Note: req.Note,
	})
	if updateDepartmentErr != nil {
		return nil, updateDepartmentErr
	}

	// 取原先此單位的資訊
	originDepartmentName := parameterSetResponse.GetParameterSet()[0].GetName()
	originDepartmentNote := parameterSetResponse.GetParameterSet()[0].GetNote()

	// 判斷是否有異動單位資訊
	isModify := false
	newDepartmentName := ""
	newDepartmentNote := ""
	if originDepartmentName != req.DepartmentName {
		isModify = true
		newDepartmentName = constants.ArrowString + req.DepartmentName
	}

	if originDepartmentNote != req.Note {
		isModify = true
		newDepartmentNote = constants.ArrowString + req.Note
	}

	if !isModify {
		return &types.BaseResponse{}, nil
	}

	// 取得上層資訊
	userData, getUserByUserIdErr := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, departmentInfo.GetOwnerId())
	if getUserByUserIdErr != nil {
		return nil, getUserByUserIdErr
	}

	// 組合XMLDataMsg
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "role",
			Value: constants.RoleDictionaryMapping[userData.GetRole()],
		},
		{
			Key:   "parent",
			Value: userData.GetUsername(),
		},
		{
			Key:   "oldname",
			Value: html.EscapeString(originDepartmentName),
		},
		{
			Key:   "oldnote",
			Value: html.EscapeString(originDepartmentNote),
		},
		{
			Key:   "newname",
			Value: html.EscapeString(newDepartmentName),
		},
		{
			Key:   "newnote",
			Value: html.EscapeString(newDepartmentNote),
		},
	}

	// 寫操作記錄
	createAGRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, operationrecord.CreateAGRecordRequest{
		HallId:       userData.GetDomain(),
		OperatorId:   admin.ID(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     userData.GetId(),
		TargetRoleId: userData.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][16],
		UserKey:      constants.AGRecordMainKey["user"][16],
		XMLDataMsg:   xmlDataMsg,
		URI:          requestURI,
		IP:           admin.IP(),
	})
	if createAGRecordErr != nil {
		return nil, createAGRecordErr
	}

	return &types.BaseResponse{}, nil
}
