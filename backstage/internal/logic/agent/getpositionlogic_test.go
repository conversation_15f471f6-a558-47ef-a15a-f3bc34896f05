package agent

import (
	"context"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetPositionLogic_Get(t *testing.T) {
	request := &types.GetPositionRequest{
		HallID: seeder.HallId,
	}

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(seeder.HallList.GetData()[0], nil)

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID: request.HallID,
	}).Return(&seeder.AgentPositionList, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(&seeder.User, nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	expectedResponse := &types.BaseResponse{
		Data: GetPositionResponse{
			List: []positionData{
				{
					ID:         seeder.AgentPositionList.GetList()[0].GetId(),
					Name:       seeder.AgentPositionList.GetList()[0].GetName(),
					Note:       seeder.AgentPositionList.GetList()[0].GetNote(),
					CreatedAt:  seeder.AgentPositionList.GetList()[0].GetCreatedAt(),
					Count:      seeder.AgentPositionList.GetList()[0].GetCount(),
					OwnerID:    seeder.User.GetId(),
					ParentName: seeder.User.GetUsername(),
				},
			},
			Paginate: types.PaginateResponse{
				Page:        seeder.AgentPositionList.GetPagination().GetCurrentPage(),
				PageLimit:   seeder.AgentPositionList.GetPagination().GetPageLimit(),
				TotalNumber: seeder.AgentPositionList.GetPagination().GetTotal(),
				TotalPage:   seeder.AgentPositionList.GetPagination().GetTotalPage(),
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func TestGetPositionLogic_GetOperatorError(t *testing.T) {
	request := &types.GetPositionRequest{
		HallID: seeder.HallId,
	}

	l := NewGetPositionLogic(context.Background(), svcCtx)
	resp, err := l.GetPosition(request)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestGetPositionLogic_GetHallById_ConnectionFailed(t *testing.T) {
	request := &types.GetPositionRequest{
		HallID: seeder.HallId,
	}

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = mockHallCtx

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPositionLogic_GetGMHallPrivilege_ConnectionFailed(t *testing.T) {
	request := &types.GetPositionRequest{
		HallID: seeder.HallId,
	}

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(seeder.HallList.GetData()[0], nil)

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPositionLogic_GetPositionList_ConnectionFailed(t *testing.T) {
	request := &types.GetPositionRequest{
		HallID: seeder.HallId,
	}

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(seeder.HallList.GetData()[0], nil)

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID: request.HallID,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetPositionLogic_GetUserByUserId_ConnectionFailed(t *testing.T) {
	request := &types.GetPositionRequest{
		HallID: seeder.HallId,
	}

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, request.HallID).Return(seeder.HallList.GetData()[0], nil)

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID: request.HallID,
	}).Return(&seeder.AgentPositionList, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, request.HallID).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx

	l := NewGetPositionLogic(ctx, svcCtx)
	resp, err := l.GetPosition(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
