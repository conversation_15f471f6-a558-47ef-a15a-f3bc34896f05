package agent

import (
	"context"

	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type departmentsResponse struct {
	Departments []department           `json:"departments"`
	Pagination  types.PaginateResponse `json:"pagination"`
}

type department struct {
	ID             uint32 `json:"id"`
	DepartmentName string `json:"department_name"`
	Note           string `json:"note"`
	ParentID       uint32 `json:"parent_id"`
	ParentName     string `json:"parent_name"`
	TotalUser      uint32 `json:"total_user"`
	CreatedAt      string `json:"created_at"`
}

type DepartmentListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDepartmentListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DepartmentListLogic {
	return &DepartmentListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DepartmentListLogic) DepartmentList(req *types.AgentDepartmentListRequest) (resp *types.BaseResponse, err error) {
	// 取得管理員ID
	admin, err := operator.GetOperator(l.ctx)
	if err != nil {
		return nil, err
	}

	// 檢查管理員權限
	checkPermissionErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), req.HallID)
	if checkPermissionErr != nil {
		return nil, checkPermissionErr
	}

	// 取得單位資訊
	departmentListResponse, departmentListResponseErr := l.svcCtx.AgentCtx.DepartmentInfoList(l.ctx, agent.DepartmentInfoListRequest{
		HallID:    req.HallID,
		Role:      req.Role,
		OwnerID:   req.ParentID,
		Name:      req.DepartmentName,
		Fuzzy:     true,
		Order:     req.Order,
		Sort:      req.Sort,
		Page:      req.Page,
		PageLimit: req.PageLimit,
	})
	if departmentListResponseErr != nil {
		return nil, departmentListResponseErr
	}

	departmentList := departmentListResponse.GetList()
	parentIDs := make([]uint32, 0, len(departmentList))
	for _, v := range departmentList {
		parentIDs = append(parentIDs, v.GetOwnerId())
	}

	// 取得上層帳號名稱
	usernameMapping, usernameMappingErr := l.svcCtx.UserCtx.GetUsersUsername(l.ctx, parentIDs)
	if usernameMappingErr != nil {
		return nil, usernameMappingErr
	}

	// 整理資訊
	departments := make([]department, 0, len(departmentList))
	for _, v := range departmentList {
		departments = append(departments, department{
			ID:             v.GetId(),
			DepartmentName: v.GetName(),
			Note:           v.GetNote(),
			ParentID:       v.GetOwnerId(),
			ParentName:     usernameMapping[v.GetOwnerId()],
			TotalUser:      v.GetCount(),
			CreatedAt:      v.GetCreatedAt(),
		})
	}

	pagination := departmentListResponse.GetPagination()
	return &types.BaseResponse{
		Data: departmentsResponse{
			Departments: departments,
			Pagination: types.PaginateResponse{
				Page:        pagination.GetCurrentPage(),
				PageLimit:   pagination.GetPageLimit(),
				TotalNumber: pagination.GetTotal(),
				TotalPage:   pagination.GetTotalPage(),
			},
		},
	}, nil
}
