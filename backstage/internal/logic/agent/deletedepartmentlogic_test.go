package agent

import (
	"context"
	"html"
	"testing"

	"gbh/admin/adminclient"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/hall/hallclient"

	"github.com/stretchr/testify/assert"
)

func TestDeleteDepartment_GeOperatorError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{}

	l := NewDeleteDepartmentLogic(context.Background(), svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_GetParameterSetError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_ParameterSetIsEmpty(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&agentclient.GetParameterSetResponse{}, nil)

	svcCtx.AgentCtx = agentCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageUserDepartmentNotFound, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_DepartmentInfoListError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_DepartmentInfoListIsEmpty(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&agentclient.DepartmentInfoListResponse{}, nil)

	svcCtx.AgentCtx = agentCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageUserDepartmentNotFound, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_CheckAdminPermissionForHallIdError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&hallclient.GetHallByIdResponse{
		HallId: seeder.HallId,
		Enable: false,
	}, nil)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_GetDepartmentListError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		DepartmentParameterId: request.DepartmentID,
	}).Return(nil, errorx.ConnectionFailed)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_DuplicateDepartment(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		DepartmentParameterId: request.DepartmentID,
	}).Return(&seeder.AgentDepartmentList, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageUserDepartmentInUse, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_DeleteDepartmentError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		DepartmentParameterId: request.DepartmentID,
	}).Return(&agentclient.DepartmentListResponse{}, nil)

	agentCtx.On("DeleteDepartment", ctx, request.DepartmentID).Return(errorx.ConnectionFailed)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_GetUserByUserIdError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		DepartmentParameterId: request.DepartmentID,
	}).Return(&agentclient.DepartmentListResponse{}, nil)

	agentCtx.On("DeleteDepartment", ctx, request.DepartmentID).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment_CreateAGRecordError(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		DepartmentParameterId: request.DepartmentID,
	}).Return(&agentclient.DepartmentListResponse{}, nil)

	agentCtx.On("DeleteDepartment", ctx, request.DepartmentID).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId()).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       seeder.User.GetDomain(),
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Delete_Department",
		UserKey:      "R_Delete_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "name",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetName()),
			},
			{
				Key:   "note",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetNote()),
			},
		},
		URI: seeder.DeleteAgentDepartmentRequestURI,
		IP:  seeder.ClientIP,
	}).Return(errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDeleteDepartment(t *testing.T) {
	request := &types.DeleteAgentDepartmentRequest{
		DepartmentID: seeder.DepartmentParameterSetID5530,
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		DepartmentParameterId: request.DepartmentID,
	}).Return(&agentclient.DepartmentListResponse{}, nil)

	agentCtx.On("DeleteDepartment", ctx, request.DepartmentID).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId()).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       seeder.User.GetDomain(),
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Delete_Department",
		UserKey:      "R_Delete_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "name",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetName()),
			},
			{
				Key:   "note",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetNote()),
			},
		},
		URI: seeder.DeleteAgentDepartmentRequestURI,
		IP:  seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewDeleteDepartmentLogic(ctx, svcCtx)
	resp, err := l.DeleteDepartment(request, seeder.DeleteAgentDepartmentRequestURI)

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{}, resp)
}
