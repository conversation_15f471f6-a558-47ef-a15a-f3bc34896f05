package agent

import (
	"context"
	"html"

	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/repository"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDepartmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDepartmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDepartmentLogic {
	return &DeleteDepartmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDepartmentLogic) DeleteDepartment(req *types.DeleteAgentDepartmentRequest, requestURI string) (resp *types.BaseResponse, err error) {
	// 取得管理員ID
	admin, err := operator.GetOperator(l.ctx)
	if err != nil {
		return nil, err
	}

	// 檢查單位是否存在
	parameterSetResponse, parameterSetResponseErr := l.svcCtx.AgentCtx.GetParameterSet(l.ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{req.DepartmentID},
		Type: "department",
	})
	if parameterSetResponseErr != nil {
		return nil, parameterSetResponseErr
	}

	if len(parameterSetResponse.GetParameterSet()) == 0 {
		return nil, errorx.BackstageUserDepartmentNotFound
	}

	// 取得單位的上層id, hall
	departmentInfoResponse, departmentInfoResponseErr := l.svcCtx.AgentCtx.DepartmentInfoList(l.ctx, agent.DepartmentInfoListRequest{
		ParameterID: req.DepartmentID,
	})
	if departmentInfoResponseErr != nil {
		return nil, departmentInfoResponseErr
	}

	if len(departmentInfoResponse.GetList()) == 0 {
		return nil, errorx.BackstageUserDepartmentNotFound
	}

	departmentInfo := departmentInfoResponse.GetList()[0]

	// 檢查管理員權限
	checkPermissionErr := repository.CheckAdminPermissionForHallId(l.ctx, l.svcCtx, admin.ID(), departmentInfo.GetHallId())
	if checkPermissionErr != nil {
		return nil, checkPermissionErr
	}

	// 此單位底下是否有使用者
	departmentListResponse, departmentListResponseErr := l.svcCtx.AgentCtx.GetDepartmentList(l.ctx, &agentclient.DepartmentListRequest{
		DepartmentParameterId: req.DepartmentID,
	})
	if departmentListResponseErr != nil {
		return nil, departmentListResponseErr
	}

	if len(departmentListResponse.GetList()) != 0 {
		return nil, errorx.BackstageUserDepartmentInUse
	}

	// 刪除單位
	deleteDepartmentErr := l.svcCtx.AgentCtx.DeleteDepartment(l.ctx, req.DepartmentID)
	if deleteDepartmentErr != nil {
		return nil, deleteDepartmentErr
	}

	// 取得上層資訊
	userData, getUserByUserIdErr := l.svcCtx.UserCtx.GetUserByUserId(l.ctx, departmentInfo.GetOwnerId())
	if getUserByUserIdErr != nil {
		return nil, getUserByUserIdErr
	}

	// 組合XMLDataMsg
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "role",
			Value: constants.RoleDictionaryMapping[userData.GetRole()],
		},
		{
			Key:   "parent",
			Value: userData.GetUsername(),
		},
		{
			Key:   "name",
			Value: html.EscapeString(parameterSetResponse.GetParameterSet()[0].GetName()),
		},
		{
			Key:   "note",
			Value: html.EscapeString(parameterSetResponse.GetParameterSet()[0].GetNote()),
		},
	}

	// 寫操作記錄
	createAGRecordErr := l.svcCtx.OperationRecordCtx.CreateAGRecord(l.ctx, operationrecord.CreateAGRecordRequest{
		HallId:       userData.GetDomain(),
		OperatorId:   admin.ID(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     userData.GetId(),
		TargetRoleId: userData.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][17],
		UserKey:      constants.AGRecordMainKey["user"][17],
		XMLDataMsg:   xmlDataMsg,
		URI:          requestURI,
		IP:           admin.IP(),
	})
	if createAGRecordErr != nil {
		return nil, createAGRecordErr
	}

	return &types.BaseResponse{}, nil
}
