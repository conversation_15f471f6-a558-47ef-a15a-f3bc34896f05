package agent

import (
	"context"
	"html"
	"testing"

	"gbh/admin/adminclient"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/hall/hallclient"

	"github.com/stretchr/testify/assert"
)

func TestUpdateDepartment_GetOperatorError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	l := NewUpdateDepartmentLogic(context.Background(), svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_GetParameterSetError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_ParameterSetIsEmpty(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&agentclient.GetParameterSetResponse{}, nil)

	svcCtx.AgentCtx = agentCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageUserDepartmentNotFound, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_DepartmentInfoListError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_DepartmentInfoListIsEmpty(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&agentclient.DepartmentInfoListResponse{}, nil)

	svcCtx.AgentCtx = agentCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageUserDepartmentNotFound, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_CheckAdminPermissionForHallIdError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&hallclient.GetHallByIdResponse{
		HallId: seeder.HallId,
		Enable: false,
	}, nil)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_CheckDuplicate_DepartmentInfoListError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		OwnerID: seeder.HallId,
		Name:    request.DepartmentName,
	}).Return(nil, errorx.ConnectionFailed)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_DuplicateDepartment(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest032801",
		Note:           "test",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		OwnerID: seeder.HallId,
		Name:    request.DepartmentName,
	}).Return(&seeder.DepartmentInfoListID5529, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.BackstageDuplicateUserDepartment, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_UpdateDepartmentError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		OwnerID: seeder.HallId,
		Name:    request.DepartmentName,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("UpdateDepartment", ctx, agent.UpdateDepartmentRequest{
		ID:   request.DepartmentID,
		Name: request.DepartmentName,
		Note: request.Note,
	}).Return(errorx.ConnectionFailed)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_NoChange(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		OwnerID: seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId(),
		Name:    request.DepartmentName,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("UpdateDepartment", ctx, agent.UpdateDepartmentRequest{
		ID:   request.DepartmentID,
		Name: request.DepartmentName,
		Note: request.Note,
	}).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{}, resp)
}

func TestUpdateDepartment_GetUserByUserIdError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		OwnerID: seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId(),
		Name:    request.DepartmentName,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("UpdateDepartment", ctx, agent.UpdateDepartmentRequest{
		ID:   request.DepartmentID,
		Name: request.DepartmentName,
		Note: request.Note,
	}).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId()).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment_CreateAGRecordError(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest0328",
		Note:           "test",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		OwnerID: seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId(),
		Name:    request.DepartmentName,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("UpdateDepartment", ctx, agent.UpdateDepartmentRequest{
		ID:   request.DepartmentID,
		Name: request.DepartmentName,
		Note: request.Note,
	}).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId()).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       seeder.User.GetDomain(),
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Modify_Department",
		UserKey:      "R_Modify_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "oldname",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetName()),
			},
			{
				Key:   "oldnote",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetNote()),
			},
			{
				Key:   "newname",
				Value: "",
			},
			{
				Key:   "newnote",
				Value: html.EscapeString(constants.ArrowString + request.Note),
			},
		},
		URI: seeder.UpdateAgentDepartmentRequestURI,
		IP:  seeder.ClientIP,
	}).Return(errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestUpdateDepartment(t *testing.T) {
	request := &types.UpdateAgentDepartmentRequest{
		DepartmentID:   seeder.DepartmentParameterSetID5530,
		DepartmentName: "gtest032802",
		Note:           "",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", ctx, &agentclient.GetParameterSetRequest{
		Id:   []uint32{request.DepartmentID},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		ParameterID: request.DepartmentID,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		OwnerID: seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId(),
		Name:    request.DepartmentName,
	}).Return(&agentclient.DepartmentInfoListResponse{}, nil)

	agentCtx.On("UpdateDepartment", ctx, agent.UpdateDepartmentRequest{
		ID:   request.DepartmentID,
		Name: request.DepartmentName,
		Note: request.Note,
	}).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId()).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       seeder.User.GetDomain(),
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Modify_Department",
		UserKey:      "R_Modify_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "oldname",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetName()),
			},
			{
				Key:   "oldnote",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetNote()),
			},
			{
				Key:   "newname",
				Value: html.EscapeString(constants.ArrowString + request.DepartmentName),
			},
			{
				Key:   "newnote",
				Value: html.EscapeString(""),
			},
		},
		URI: seeder.UpdateAgentDepartmentRequestURI,
		IP:  seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	l := NewUpdateDepartmentLogic(ctx, svcCtx)
	resp, err := l.UpdateDepartment(request, seeder.UpdateAgentDepartmentRequestURI)

	assert.NoError(t, err)
	assert.Equal(t, &types.BaseResponse{}, resp)
}
