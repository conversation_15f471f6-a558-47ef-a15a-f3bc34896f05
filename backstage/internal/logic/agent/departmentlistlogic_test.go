package agent

import (
	"context"
	"testing"

	"gbh/admin/adminclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/hall/hallclient"

	"github.com/stretchr/testify/assert"
)

func TestDepartmentList_GetOperatorError(t *testing.T) {
	request := &types.AgentDepartmentListRequest{}

	l := NewDepartmentListLogic(context.Background(), svcCtx)
	resp, err := l.DepartmentList(request)

	assert.Equal(t, errorx.BackstageOperatorNotFound, err)
	assert.Nil(t, resp)
}

func TestDepartmentList_CheckAdminPermissionForHallIdError(t *testing.T) {
	request := &types.AgentDepartmentListRequest{
		HallID:         seeder.HallId,
		Role:           constants.Role7,
		ParentID:       456120811,
		DepartmentName: "test",
		Sort:           "created_at",
		Page:           1,
		PageLimit:      1,
		Order:          "desc",
	}

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&hallclient.GetHallByIdResponse{
		HallId: seeder.HallId,
		Enable: false,
	}, nil)

	svcCtx.HallCtx = hallCtx

	l := NewDepartmentListLogic(ctx, svcCtx)
	resp, err := l.DepartmentList(request)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
	assert.Nil(t, resp)
}

func TestDepartmentList_DepartmentInfoListErrorError(t *testing.T) {
	request := &types.AgentDepartmentListRequest{
		HallID:         seeder.HallId,
		Role:           constants.Role7,
		ParentID:       456120811,
		DepartmentName: "test",
		Sort:           "created_at",
		Page:           1,
		PageLimit:      1,
		Order:          "desc",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		HallID:    request.HallID,
		Role:      request.Role,
		OwnerID:   request.ParentID,
		Name:      request.DepartmentName,
		Fuzzy:     true,
		Order:     request.Order,
		Sort:      request.Sort,
		Page:      request.Page,
		PageLimit: request.PageLimit,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AgentCtx = agentCtx

	l := NewDepartmentListLogic(ctx, svcCtx)
	resp, err := l.DepartmentList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDepartmentList_GetUsersUsernameError(t *testing.T) {
	request := &types.AgentDepartmentListRequest{
		HallID:         seeder.HallId,
		Role:           constants.Role7,
		ParentID:       456120811,
		DepartmentName: "test",
		Sort:           "created_at",
		Page:           1,
		PageLimit:      1,
		Order:          "desc",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	parentIDs := make([]uint32, 0, len(seeder.DepartmentInfoList.GetList()))
	for _, v := range seeder.DepartmentInfoList.GetList() {
		parentIDs = append(parentIDs, v.GetOwnerId())
	}
	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUsersUsername", ctx, parentIDs).Return(nil, errorx.ConnectionFailed)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		HallID:    request.HallID,
		Role:      request.Role,
		OwnerID:   request.ParentID,
		Name:      request.DepartmentName,
		Fuzzy:     true,
		Order:     request.Order,
		Sort:      request.Sort,
		Page:      request.Page,
		PageLimit: request.PageLimit,
	}).Return(&seeder.DepartmentInfoList, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.AgentCtx = agentCtx

	l := NewDepartmentListLogic(ctx, svcCtx)
	resp, err := l.DepartmentList(request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestDepartmentList(t *testing.T) {
	request := &types.AgentDepartmentListRequest{
		HallID:         seeder.HallId,
		Role:           constants.Role7,
		ParentID:       456120811,
		DepartmentName: "test",
		Sort:           "created_at",
		Page:           1,
		PageLimit:      1,
		Order:          "desc",
	}

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	parentIDs := make([]uint32, 0, len(seeder.DepartmentInfoList.GetList()))
	for _, v := range seeder.DepartmentInfoList.GetList() {
		parentIDs = append(parentIDs, v.GetOwnerId())
	}
	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUsersUsername", ctx, parentIDs).Return(map[uint32]string{
		seeder.HallId: seeder.User.GetUsername(),
	}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		HallID:    request.HallID,
		Role:      request.Role,
		OwnerID:   request.ParentID,
		Name:      request.DepartmentName,
		Fuzzy:     true,
		Order:     request.Order,
		Sort:      request.Sort,
		Page:      request.Page,
		PageLimit: request.PageLimit,
	}).Return(&seeder.DepartmentInfoList, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.AgentCtx = agentCtx

	l := NewDepartmentListLogic(ctx, svcCtx)
	resp, err := l.DepartmentList(request)

	expectedResponse := &types.BaseResponse{
		Data: departmentsResponse{
			Departments: []department{
				{
					ID:             seeder.AgentDepartmentID5514,
					DepartmentName: "gtit",
					Note:           "gtit",
					ParentID:       seeder.HallId,
					ParentName:     seeder.User.GetUsername(),
					TotalUser:      0,
					CreatedAt:      "2024-12-17T04:14:50-04:00",
				},
				{
					ID:             seeder.AgentDepartmentID5168,
					DepartmentName: "test",
					Note:           "test",
					ParentID:       seeder.HallId,
					ParentName:     seeder.User.GetUsername(),
					TotalUser:      seeder.AgentDepartmentID5168Count,
					CreatedAt:      "2021-03-04T02:56:52-04:00",
				},
			},
			Pagination: types.PaginateResponse{
				Page:        seeder.DepartmentInfoList.GetPagination().GetCurrentPage(),
				PageLimit:   seeder.DepartmentInfoList.GetPagination().GetPageLimit(),
				TotalNumber: seeder.DepartmentInfoList.GetPagination().GetTotal(),
				TotalPage:   seeder.DepartmentInfoList.GetPagination().GetTotalPage(),
			},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}
