package slot

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/slotgame/slotgameclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCrushTimesRecord_Error(t *testing.T) {
	request := &types.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallID:         3820474,
		GameID:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
	}

	ctxRequest := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallId:         3820474,
		GameId:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
	}

	mockSlotGameCtx := mock.NewMockSlotGameCtx()

	mockSlotGameCtx.On("GetCrushTimesRecord", ctx, ctxRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.SlotGameCtx = mockSlotGameCtx

	l := NewCrushTimesRecordLogic(ctx, svcCtx)
	resp, err := l.CrushTimesRecord(request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestCrushTimesRecord_ErrorWithOptionalFields(t *testing.T) {
	request := &types.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallID:         3820474,
		GameID:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
		WagersID:       1,
		UserID:         1,
		CurrentPage:    1,
		PageLimit:      50,
	}

	ctxRequest := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallId:         3820474,
		GameId:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
		WagersId:       1,
		UserId:         1,
		CurrentPage:    1,
		PageLimit:      50,
	}

	mockSlotGameCtx := mock.NewMockSlotGameCtx()

	mockSlotGameCtx.On("GetCrushTimesRecord", ctx, ctxRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.SlotGameCtx = mockSlotGameCtx

	l := NewCrushTimesRecordLogic(ctx, svcCtx)
	resp, err := l.CrushTimesRecord(request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestCrushTimesRecord(t *testing.T) {
	request := &types.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallID:         3820474,
		GameID:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
	}

	ctxRequest := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallId:         3820474,
		GameId:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
	}

	mockSlotGameCtx := mock.NewMockSlotGameCtx()

	mockReturn := &slotgameclient.GetCrushTimesRecordResponse{
		CrushTimeRecord: []*slotgameclient.CrushTimesRecord{
			{
				WagersId:     5200007992458,
				GameId:       5247,
				HallId:       3820474,
				UserId:       456120535,
				Times:        5,
				RoundDate:    "2025-01-01",
				RoundTime:    "2025-01-01 00:00:00",
				ModifiedDate: "2025-01-01 00:00:00",
			},
		},
		Pagination: &slotgameclient.Pagination{
			CurrentPage: 1,
			PageLimit:   500,
			Total:       1,
			TotalPage:   1,
		},
	}

	mockSlotGameCtx.On("GetCrushTimesRecord", ctx, ctxRequest).Return(mockReturn, nil)

	svcCtx.SlotGameCtx = mockSlotGameCtx

	l := NewCrushTimesRecordLogic(ctx, svcCtx)
	resp, err := l.CrushTimesRecord(request)

	expected := &types.BaseResponse{
		Data: CrushTimesResponse{
			CrushTimesRecord: []CrushTimes{
				{
					WagersID:     5200007992458,
					GameID:       5247,
					HallID:       3820474,
					UserID:       456120535,
					Times:        5,
					RoundDate:    "2025-01-01",
					RoundTime:    "2025-01-01 00:00:00",
					ModifiedDate: "2025-01-01 00:00:00",
				},
			},
			Pagination: types.PaginateResponse{
				Page:        1,
				PageLimit:   500,
				TotalNumber: 1,
				TotalPage:   1,
			},
		}}

	assert.Equal(t, expected, resp)
	assert.NoError(t, err)
}
