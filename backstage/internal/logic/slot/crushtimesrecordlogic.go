package slot

import (
	"context"

	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/slotgame/slotgameclient"

	"github.com/zeromicro/go-zero/core/logx"
)

type CrushTimesRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type CrushTimes struct {
	WagersID     uint64 `json:"wagers_id"`
	GameID       uint32 `json:"game_id"`
	HallID       uint32 `json:"hall_id"`
	UserID       uint32 `json:"user_id"`
	Times        uint32 `json:"times"`
	RoundDate    string `json:"round_date"`
	RoundTime    string `json:"round_time"`
	ModifiedDate string `json:"modified_date"`
}

type CrushTimesResponse struct {
	CrushTimesRecord []CrushTimes           `json:"crush_times_record"`
	Pagination       types.PaginateResponse `json:"pagination"`
}

func NewCrushTimesRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CrushTimesRecordLogic {
	return &CrushTimesRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CrushTimesRecordLogic) CrushTimesRecord(req *types.GetCrushTimesRecordRequest) (*types.BaseResponse, error) {
	grpcRequest := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      req.StartDate,
		EndDate:        req.EndDate,
		HallId:         req.HallID,
		GameId:         req.GameID,
		Times:          req.Times,
		OperatorSymbol: req.OperatorSymbol,
	}

	if req.WagersID != 0 {
		grpcRequest.WagersId = req.WagersID
	}

	if req.UserID != 0 {
		grpcRequest.UserId = req.UserID
	}

	if req.CurrentPage != 0 {
		grpcRequest.CurrentPage = req.CurrentPage
	}

	if req.PageLimit != 0 {
		grpcRequest.PageLimit = req.PageLimit
	}

	resp, err := l.svcCtx.SlotGameCtx.GetCrushTimesRecord(l.ctx, grpcRequest)

	if err != nil {
		return nil, err
	}

	crushTimesRecord := CrushTimesResponse{
		CrushTimesRecord: make([]CrushTimes, 0, len(resp.GetCrushTimeRecord())),
	}

	for _, v := range resp.GetCrushTimeRecord() {
		crushTimesRecord.CrushTimesRecord = append(crushTimesRecord.CrushTimesRecord, CrushTimes{
			WagersID:     v.GetWagersId(),
			GameID:       v.GetGameId(),
			HallID:       v.GetHallId(),
			UserID:       v.GetUserId(),
			Times:        v.GetTimes(),
			RoundDate:    v.GetRoundDate(),
			RoundTime:    v.GetRoundTime(),
			ModifiedDate: v.GetModifiedDate(),
		})
	}

	crushTimesRecord.Pagination = types.PaginateResponse{
		Page:        resp.GetPagination().GetCurrentPage(),
		PageLimit:   resp.GetPagination().GetPageLimit(),
		TotalNumber: resp.GetPagination().GetTotal(),
		TotalPage:   resp.GetPagination().GetTotalPage(),
	}

	return &types.BaseResponse{
		Data: crushTimesRecord,
	}, nil
}
