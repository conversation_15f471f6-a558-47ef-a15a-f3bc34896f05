package slotgame

import (
	"context"
	"gbh/errorx"
	"gbh/slotgame/slotgameclient"
)

type Context interface {
	GetCrushTimesRecord(ctx context.Context, in *slotgameclient.GetCrushTimesRecordRequest) (*slotgameclient.GetCrushTimesRecordResponse, error)
}

type slotGameContext struct {
	SlotGameRPC slotgameclient.SlotGame
}

func New(slotGameRPC slotgameclient.SlotGame) Context {
	return &slotGameContext{
		SlotGameRPC: slotGameRPC,
	}
}

func (c *slotGameContext) GetCrushTimesRecord(ctx context.Context, in *slotgameclient.GetCrushTimesRecordRequest) (*slotgameclient.GetCrushTimesRecordResponse, error) {
	resp, err := c.SlotGameRPC.GetCrushTimesRecord(ctx, in)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
