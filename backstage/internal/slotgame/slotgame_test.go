package slotgame

import (
	"gbh/backstage/internal/constants"
	"gbh/errorx"
	"gbh/slotgame/slotgameclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	slotGameRPC := newMockSlotGameRPC()
	slotGameCtx := New(slotGameRPC)

	expectedResponse := &slotGameContext{
		SlotGameRPC: slotGameRPC,
	}

	assert.Equal(t, expectedResponse, slotGameCtx)
}

func TestGetCrushTimesRecord_GrpcError(t *testing.T) {
	in := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallId:         3820474,
		GameId:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
	}

	mockSlotGameRPC := newMockSlotGameRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)

	mockSlotGameRPC.On("GetCrushTimesRecord", ctx, in).Return(nil, mockError)

	slotGameCtx := New(mockSlotGameRPC)

	resp, err := slotGameCtx.GetCrushTimesRecord(ctx, in)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestGetCrushTimesRecord(t *testing.T) {
	in := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallId:         3820474,
		GameId:         5247,
		OperatorSymbol: constants.SymbolEqual,
		Times:          5,
	}

	mockSlotGameRPC := newMockSlotGameRPC()

	mockReturn := &slotgameclient.GetCrushTimesRecordResponse{
		CrushTimeRecord: []*slotgameclient.CrushTimesRecord{
			{
				WagersId:     5200007992458,
				GameId:       5247,
				HallId:       3820474,
				UserId:       456120535,
				Times:        5,
				RoundDate:    "2025-01-01",
				RoundTime:    "2025-01-01 00:00:00",
				ModifiedDate: "2025-01-01 00:00:00",
			},
		},
		Pagination: &slotgameclient.Pagination{
			CurrentPage: 1,
			PageLimit:   500,
			Total:       1,
			TotalPage:   1,
		},
	}

	mockSlotGameRPC.On("GetCrushTimesRecord", ctx, in).Return(mockReturn, nil)

	slotGameCtx := New(mockSlotGameRPC)

	resp, err := slotGameCtx.GetCrushTimesRecord(ctx, in)

	assert.Equal(t, mockReturn, resp)
	assert.NoError(t, err)
}
