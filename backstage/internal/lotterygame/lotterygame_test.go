package lotterygame

import (
	"gbh/errorx"
	"gbh/lotterygame/lotterygameclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	lotteryGameRPC := newMockLotteryGameRPC()
	lotteryGameCtx := New(lotteryGameRPC)

	expectedResponse := &lotteryGameContext{
		LotteryGameRPC: lotteryGameRPC,
	}

	assert.Equal(t, expectedResponse, lotteryGameCtx)
}

func TestGetWagersByID_GrpcError(t *testing.T) {
	grpcRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 1,
	}

	mockLotteryGameRPC := newMockLotteryGameRPC()
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockLotteryGameRPC.On("GetWagersByID", ctx, grpcRequest).Return(nil, mockError)

	lotteryGameCtx := New(mockLotteryGameRPC)

	resp, err := lotteryGameCtx.GetWagersByID(ctx, grpcRequest)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestGetWagersByID(t *testing.T) {
	grpcRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 1,
	}

	mockResponse := &lotterygameclient.GetWagersByIDResponse{
		Id:             9215031295,
		WagersTime:     "2025-01-01 00:00:00",
		Hierarchy:      0,
		Portal:         0,
		WagersType:     0,
		Platform:       0,
		Client:         0,
		GameId:         "BQSQ",
		UserId:         456112001,
		RoundDate:      "2025-01-01",
		RoundTime:      "2025-01-01 00:00:00",
		Commissionable: 3000,
		Currency:       "CNY",
		ExchangeRate:   1.000000,
		Result:         4,
		Payoff:         2880.0000,
		HallId:         3820474,
		RoundSerial:    "20250220-0896",
		ReferenceId:    "9215031295",
		SettledTime:    "2025-01-01 00:00:00",
		Rule:           "L034",
		DbCategory:     122,
		ModifiedDate:   "2025-01-01 00:00:00",
	}

	mockLotteryGameRPC := newMockLotteryGameRPC()
	mockLotteryGameRPC.On("GetWagersByID", ctx, grpcRequest).Return(mockResponse, nil)

	lotteryGameCtx := New(mockLotteryGameRPC)

	resp, err := lotteryGameCtx.GetWagersByID(ctx, grpcRequest)

	assert.Equal(t, mockResponse, resp)
	assert.NoError(t, err)
}

func TestGetSubWagersSubURL_GrpcError(t *testing.T) {
	grpcRequest := &lotterygameclient.SubWagersURLRequest{
		WagersId: 9215031295,
		UserId:   456112001,
		GameId:   "BQSQ",
		HallId:   3820474,
		Lang:     "zh-tw",
	}

	mockLotteryGameRPC := newMockLotteryGameRPC()
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockLotteryGameRPC.On("SubWagersURL", ctx, grpcRequest).Return(nil, mockError)

	lotteryGameCtx := New(mockLotteryGameRPC)

	resp, err := lotteryGameCtx.GetSubWagersSubURL(ctx, grpcRequest)

	assert.Empty(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func TestGetSubWagersSubURL(t *testing.T) {
	grpcRequest := &lotterygameclient.SubWagersURLRequest{
		WagersId: 9215031295,
		UserId:   456112001,
		GameId:   "BQSQ",
		HallId:   3820474,
		Lang:     "zh-tw",
	}

	mockLotteryGameRPC := newMockLotteryGameRPC()

	mockSubWagersURL := "https://mock.bblottery/sub_wager_surl"

	mockResponse := &lotterygameclient.SubWagersURLResponse{
		Url: mockSubWagersURL,
	}

	mockLotteryGameRPC.On("SubWagersURL", ctx, grpcRequest).Return(mockResponse, nil)

	lotteryGameCtx := New(mockLotteryGameRPC)

	resp, err := lotteryGameCtx.GetSubWagersSubURL(ctx, grpcRequest)

	assert.Equal(t, mockSubWagersURL, resp)
	assert.NoError(t, err)
}
