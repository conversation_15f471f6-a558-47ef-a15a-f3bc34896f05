package lotterygame

import (
	"context"
	"gbh/errorx"
	"gbh/lotterygame/lotterygameclient"
)

type Context interface {
	GetWagersByID(ctx context.Context, in *lotterygameclient.GetWagersByIDRequest) (*lotterygameclient.GetWagersByIDResponse, error)
	GetSubWagersSubURL(ctx context.Context, in *lotterygameclient.SubWagersURLRequest) (string, error)
}

type lotteryGameContext struct {
	LotteryGameRPC lotterygameclient.LotteryGame
}

func New(lotteryGameRPC lotterygameclient.LotteryGame) Context {
	return &lotteryGameContext{
		LotteryGameRPC: lotteryGameRPC,
	}
}

func (c *lotteryGameContext) GetWagersByID(ctx context.Context, in *lotterygameclient.GetWagersByIDRequest) (*lotterygameclient.GetWagersByIDResponse, error) {
	resp, err := c.LotteryGameRPC.GetWagersByID(ctx, in)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *lotteryGameContext) GetSubWagersSubURL(ctx context.Context, in *lotterygameclient.SubWagersURLRequest) (string, error) {
	resp, err := c.LotteryGameRPC.SubWagersURL(ctx, in)

	if err != nil {
		return "", errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetUrl(), nil
}
