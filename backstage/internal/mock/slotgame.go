package mock

import (
	"context"
	"gbh/slotgame/slotgameclient"

	"github.com/stretchr/testify/mock"
)

type SlotGameCtx struct{ mock.Mock }

func (m *SlotGameCtx) GetCrushTimesRecord(ctx context.Context, in *slotgameclient.GetCrushTimesRecordRequest) (*slotgameclient.GetCrushTimesRecordResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*slotgameclient.GetCrushTimesRecordResponse); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockSlotGameCtx() *SlotGameCtx {
	return &SlotGameCtx{}
}
