package mock

import (
	"context"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"

	"github.com/stretchr/testify/mock"
)

type AgentCtx struct{ mock.Mock }

func (c *AgentCtx) GetDepartmentList(ctx context.Context, in *agentclient.DepartmentListRequest) (*agentclient.DepartmentListResponse, error) {
	args := c.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*agentclient.DepartmentListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *AgentCtx) DepartmentInfoList(ctx context.Context, req agent.DepartmentInfoListRequest) (*agentclient.DepartmentInfoListResponse, error) {
	args := c.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*agentclient.DepartmentInfoListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *AgentCtx) HasDepartment(ctx context.Context, req agent.HasDepartmentRequest) error {
	args := c.Called(ctx, req)
	err := args.Error(0)

	if err != nil {
		return err
	}

	return nil
}

func (c *AgentCtx) CreateDepartment(ctx context.Context, req agent.CreateDepartmentRequest) error {
	args := c.Called(ctx, req)

	return args.Error(0)
}

func (c *AgentCtx) DeletePosition(ctx context.Context, positionId uint32) error {
	args := c.Called(ctx, positionId)

	return args.Error(0)
}

func (c *AgentCtx) GetParameterSet(ctx context.Context, in *agentclient.GetParameterSetRequest) (*agentclient.GetParameterSetResponse, error) {
	args := c.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*agentclient.GetParameterSetResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *AgentCtx) GetPositionInfo(ctx context.Context, in agent.GetPositionInfoRequest) (*agentclient.GetPositionInfoResponse, error) {
	args := c.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*agentclient.GetPositionInfoResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *AgentCtx) DeleteSubAccountDepartment(ctx context.Context, userId uint32) error {
	args := c.Called(ctx, userId)

	return args.Error(0)
}

func (c *AgentCtx) GetPositionList(ctx context.Context, req agent.GetPositionListRequest) (*agentclient.PositionListResponse, error) {
	args := c.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*agentclient.PositionListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *AgentCtx) UpdateDepartment(ctx context.Context, req agent.UpdateDepartmentRequest) error {
	args := c.Called(ctx, req)

	return args.Error(0)
}

func (c *AgentCtx) CreatePosition(ctx context.Context, req agent.CreatePositionRequest) error {
	args := c.Called(ctx, req)
	err := args.Error(0)

	if err != nil {
		return err
	}

	return nil
}

func (c *AgentCtx) DeleteDepartment(ctx context.Context, departmentId uint32) error {
	args := c.Called(ctx, departmentId)

	return args.Error(0)
}

func (c *AgentCtx) UpdateUserDepartment(ctx context.Context, req *agentclient.UpdateUserDepartmentRequest) error {
	args := c.Called(ctx, req)

	return args.Error(0)
}

func (c *AgentCtx) UpdateUserPosition(ctx context.Context, req *agentclient.UpdateUserPositionRequest) error {
	args := c.Called(ctx, req)

	return args.Error(0)
}

func NewMockAgentCtx() *AgentCtx {
	return &AgentCtx{}
}
