package mock

import (
	"context"
	"gbh/report/reportclient"

	"github.com/stretchr/testify/mock"
)

type ReportCtx struct{ mock.Mock }

func (m *ReportCtx) GetReportCloseDate(ctx context.Context) (string, error) {
	args := m.Called(ctx)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return "", err
	}

	if res, ok := resp.(string); ok {
		return res, nil
	}

	return "", nil
}

func (m *ReportCtx) GetGameAnalysisEvent(ctx context.Context, in *reportclient.GetGameAnalysisEventRequest) (*reportclient.GetGameAnalysisEventResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*reportclient.GetGameAnalysisEventResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *ReportCtx) DeleteGameAnalysisEvent(ctx context.Context, in *reportclient.DeleteGameAnalysisEventRequest) error {
	args := m.Called(ctx, in)
	err := args.Error(0)

	if err != nil {
		return err
	}

	return nil
}

func (m *ReportCtx) CreateGameAnalysisEvent(ctx context.Context, in *reportclient.CreateGameAnalysisEventRequest) error {
	args := m.Called(ctx, in)
	err := args.Error(0)

	if err != nil {
		return err
	}

	return nil
}

func NewMockReportCtx() *ReportCtx {
	return &ReportCtx{}
}
