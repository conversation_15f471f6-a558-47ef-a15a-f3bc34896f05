package mock

import (
	"context"
	"gbh/admin/adminclient"
	"gbh/backstage/internal/admin"

	"github.com/stretchr/testify/mock"
)

type AdminCtx struct{ mock.Mock }

func (s *AdminCtx) PositionList(ctx context.Context, req admin.PositionListRequest) (*adminclient.PositionListResponse, error) {
	args := s.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*adminclient.PositionListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (s *AdminCtx) GetSession(ctx context.Context, session, ip string) (*adminclient.GetSessionResponse, error) {
	args := s.Called(ctx, session, ip)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*adminclient.GetSessionResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (s *AdminCtx) GetGMUserIdleList(ctx context.Context, in admin.GetGMUserIdleListRequest) (*adminclient.GetGMUserListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*adminclient.GetGMUserListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (s *AdminCtx) GetGMUserList(ctx context.Context, in admin.GetGMUserListRequest) (*adminclient.GetGMUserListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*adminclient.GetGMUserListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (s *AdminCtx) GetUsersDepartment(ctx context.Context, in admin.GetUsersDepartmentRequest) (*adminclient.UsersDepartmentResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*adminclient.UsersDepartmentResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (s *AdminCtx) DepartmentList(ctx context.Context, req admin.DepartmentListRequest) (*adminclient.DepartmentListResponse, error) {
	args := s.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*adminclient.DepartmentListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (s *AdminCtx) GetGMHallPrivilege(ctx context.Context, adminId uint32) (*adminclient.GMHallPrivilegeResponse, error) {
	args := s.Called(ctx, adminId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*adminclient.GMHallPrivilegeResponse); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockAdminCtx() *AdminCtx {
	return &AdminCtx{}
}
