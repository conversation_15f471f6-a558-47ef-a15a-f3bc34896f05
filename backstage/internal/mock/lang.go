package mock

import (
	"context"
	"gbh/lang/langclient"

	"github.com/stretchr/testify/mock"
)

type LangCtx struct{ mock.Mock }

func (m *LangCtx) GetGameFeatures(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error) {
	args := m.Called(ctx, lang, gameKind)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*langclient.GameDictionaryResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LangCtx) GetGamePlayType(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error) {
	args := m.Called(ctx, lang, gameKind)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*langclient.GameDictionaryResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LangCtx) GetGameRoundResult(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error) {
	args := m.Called(ctx, lang, gameKind)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*langclient.GameDictionaryResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LangCtx) GetWeblateLang(ctx context.Context, lang string, category string) (*langclient.WeblateResponse, error) {
	args := m.Called(ctx, lang, category)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*langclient.WeblateResponse); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockLangCtx() *LangCtx {
	return &LangCtx{}
}
