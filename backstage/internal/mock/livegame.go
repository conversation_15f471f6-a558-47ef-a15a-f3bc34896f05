package mock

import (
	"context"
	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/types"
	"gbh/livegame/livegameclient"

	"github.com/stretchr/testify/mock"
)

type LiveGameCtx struct{ mock.Mock }

func (m *LiveGameCtx) RoundInfo(ctx context.Context, req livegame.RoundInfoRequest) (*livegameclient.RoundInfoResponse, error) {
	args := m.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*livegameclient.RoundInfoResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LiveGameCtx) CancelWagers(ctx context.Context, in *types.CancelWagersRequest) error {
	args := m.Called(ctx, in)

	return args.Error(0)
}

func (m *LiveGameCtx) GetDomainGameCodeSetting(ctx context.Context, req livegame.GameCodeSettingRequest) (*livegameclient.GetDomainGameCodeSettingResponse, error) {
	args := m.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*livegameclient.GetDomainGameCodeSettingResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LiveGameCtx) RoundInfoByRoundSerial(ctx context.Context, roundSerial uint32, stateId uint32, sort string) ([]*livegameclient.RoundInfo, error) {
	args := m.Called(ctx, roundSerial, stateId, sort)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*livegameclient.RoundInfo); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LiveGameCtx) RoundXMLInfo(ctx context.Context, roundSerials []uint32, startDate string, endDate string) ([]*livegameclient.RoundXMLInfo, error) {
	args := m.Called(ctx, roundSerials, startDate, endDate)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*livegameclient.RoundXMLInfo); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LiveGameCtx) GetHallTipSwitch(ctx context.Context, hallId uint32) ([]*livegameclient.HallSwitchData, error) {
	args := m.Called(ctx, hallId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*livegameclient.HallSwitchData); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockLiveGameCtx() *LiveGameCtx {
	return &LiveGameCtx{}
}
