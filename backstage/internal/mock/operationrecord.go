package mock

import (
	"context"
	"gbh/backstage/internal/operationrecord"
	"gbh/operationrecord/operationrecordclient"

	"github.com/stretchr/testify/mock"
)

type OperationRecordCtx struct{ mock.Mock }

func (m *OperationRecordCtx) CreateAGRecord(ctx context.Context, in operationrecord.CreateAGRecordRequest) error {
	args := m.Called(ctx, in)

	return args.Error(0)
}

func (m *OperationRecordCtx) AddLogChange(ctx context.Context, in *operationrecordclient.AddLogChangeRequest) error {
	args := m.Called(ctx, in)

	return args.Error(0)
}

func NewMockOperationRecordCtx() *OperationRecordCtx {
	return &OperationRecordCtx{}
}
