package mock

import (
	"context"
	"gbh/fishgame/fishgameclient"

	"github.com/stretchr/testify/mock"
)

type FishGameCtx struct{ mock.Mock }

func (m *FishGameCtx) GetMultiSubWagersURL(ctx context.Context, lang string, wagersId []uint64) (*fishgameclient.GetMultiSubWagersURLResponse, error) {
	args := m.Called(ctx, lang, wagersId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*fishgameclient.GetMultiSubWagersURLResponse); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockFishGameCtx() *FishGameCtx {
	return &FishGameCtx{}
}
