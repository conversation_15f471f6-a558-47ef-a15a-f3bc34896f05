package mock

import (
	"context"
	"gbh/backstage/internal/user"
	"gbh/user/userclient"

	"github.com/stretchr/testify/mock"
)

type UserCtx struct{ mock.Mock }

func (c *UserCtx) GetUserByUserId(ctx context.Context, userId uint32) (*userclient.GetResponse, error) {
	args := c.Called(ctx, userId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*userclient.GetResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) GetUserList(ctx context.Context, in *userclient.GetUserListRequest) (*userclient.GetUserListResponse, error) {
	args := c.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*userclient.GetUserListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) GetUserByUsername(ctx context.Context, parentId uint32, username string) (*userclient.GetResponse, error) {
	args := c.Called(ctx, parentId, username)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*userclient.GetResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) GetUsers(ctx context.Context, userIDs []uint32, extraInfo *bool) (*userclient.GetUsersResponse, error) {
	args := c.Called(ctx, userIDs, extraInfo)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*userclient.GetUsersResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) GetUsersUsername(ctx context.Context, userIDs []uint32) (map[uint32]string, error) {
	args := c.Called(ctx, userIDs)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(map[uint32]string); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) GetHierarchy(ctx context.Context, in *userclient.GetHierarchyRequest) (*userclient.GetHierarchyResponse, error) {
	args := c.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*userclient.GetHierarchyResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) DeleteUser(ctx context.Context, in *userclient.DeleteUserRequest) error {
	args := c.Called(ctx, in)

	return args.Error(0)
}

func (c *UserCtx) GetLoginRecord(ctx context.Context, in user.GetLoginRecordRequest) (*userclient.LoginRecordResponse, error) {
	args := c.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*userclient.LoginRecordResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) CheckUsernameUnique(ctx context.Context, in *userclient.CheckUsernameUniqueRequest) (*userclient.CheckUsernameUniqueResponse, error) {
	args := c.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*userclient.CheckUsernameUniqueResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (c *UserCtx) EditUser(ctx context.Context, in *userclient.EditUserRequest) error {
	args := c.Called(ctx, in)

	return args.Error(0)
}

func (c *UserCtx) SetPassword(ctx context.Context, in *userclient.SetPasswordRequest) error {
	args := c.Called(ctx, in)

	return args.Error(0)
}

func NewMockUserCtx() *UserCtx {
	return &UserCtx{}
}
