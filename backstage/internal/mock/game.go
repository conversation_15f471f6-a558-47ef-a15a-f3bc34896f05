package mock

import (
	"context"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/types"
	"gbh/game/gameclient"

	"github.com/stretchr/testify/mock"
)

type GameCtx struct{ mock.Mock }

func (m *GameCtx) GetGameURLList(ctx context.Context, in *gameclient.GameURLListRequest) ([]string, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]string); ok {
		return res, nil
	}

	return nil, nil
}

func (m *GameCtx) GetGameDetail(ctx context.Context, in game.GetGameDetailRequest) ([]*gameclient.GameDetail, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*gameclient.GameDetail); ok {
		return res, nil
	}

	return nil, nil
}

func (m *GameCtx) GetUserLobbySwitch(ctx context.Context, in game.GetUserLobbySwitchRequest) ([]*gameclient.LobbySwitch, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*gameclient.LobbySwitch); ok {
		return res, nil
	}

	return nil, nil
}

func (m *GameCtx) LobbyCategory(ctx context.Context, in game.LobbyCategoryRequest) (*gameclient.LobbyCategoryResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*gameclient.LobbyCategoryResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *GameCtx) CreateGameDetail(ctx context.Context, in game.CreateGameDetailRequest) (*gameclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	return nil, args.Error(1)
}

func (m *GameCtx) FlattenLobbyCategory(ctx context.Context, in game.LobbyCategoryRequest) ([]string, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]string); ok {
		return res, nil
	}

	return nil, nil
}

func (m *GameCtx) GetGameListWithSwitch(ctx context.Context, in *gameclient.GetGameListWithSwitchRequest) (*gameclient.GetGameListWithSwitchResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*gameclient.GetGameListWithSwitchResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *GameCtx) GetUserLobby(ctx context.Context, user types.UserInfo, gameKind uint32) (bool, error) {
	args := m.Called(ctx, user, gameKind)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return false, err
	}

	if res, ok := resp.(bool); ok {
		return res, nil
	}

	return false, nil
}

func NewMockGameCtx() *GameCtx {
	return &GameCtx{}
}
