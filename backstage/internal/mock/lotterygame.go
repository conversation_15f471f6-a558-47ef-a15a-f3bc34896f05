package mock

import (
	"context"
	"gbh/lotterygame/lotterygameclient"

	"github.com/stretchr/testify/mock"
)

type LotteryGameCtx struct{ mock.Mock }

func (m *LotteryGameCtx) GetWagersByID(ctx context.Context, in *lotterygameclient.GetWagersByIDRequest) (*lotterygameclient.GetWagersByIDResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*lotterygameclient.GetWagersByIDResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *LotteryGameCtx) GetSubWagersSubURL(ctx context.Context, in *lotterygameclient.SubWagersURLRequest) (string, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return "", err
	}

	if res, ok := resp.(string); ok {
		return res, nil
	}

	return "", nil
}

func NewMockLotteryGameCtx() *LotteryGameCtx {
	return &LotteryGameCtx{}
}
