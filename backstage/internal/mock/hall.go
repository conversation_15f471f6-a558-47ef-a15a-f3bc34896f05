package mock

import (
	"context"
	"gbh/hall/hallclient"

	"github.com/stretchr/testify/mock"
)

type HallCtx struct{ mock.Mock }

func (s *HallCtx) GetHallList(ctx context.Context, enable bool) (*hallclient.HallListResponse, error) {
	args := s.Called(ctx, enable)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*hallclient.HallListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (s *HallCtx) GetHallById(ctx context.Context, hallId uint32) (*hallclient.GetHallByIdResponse, error) {
	args := s.Called(ctx, hallId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*hallclient.GetHallByIdResponse); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockHallCtx() *HallCtx {
	return &HallCtx{}
}
