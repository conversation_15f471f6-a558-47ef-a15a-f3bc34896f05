package mock

import (
	"context"
	"gbh/maintain/maintainclient"

	"github.com/stretchr/testify/mock"
)

type MaintainCtx struct{ mock.Mock }

func (m *MaintainCtx) GetMaintainByGameKind(ctx context.Context, in *maintainclient.GetMaintainByGameKindRequest) (*maintainclient.GetMaintainByGameKindResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*maintainclient.GetMaintainByGameKindResponse); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockMaintainCtx() *MaintainCtx {
	return &MaintainCtx{}
}
