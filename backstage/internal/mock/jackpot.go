package mock

import (
	"context"
	"gbh/backstage/internal/jackpot"
	"gbh/jackpot/jackpotclient"

	"github.com/stretchr/testify/mock"
)

type JackpotCtx struct{ mock.Mock }

func (m *JackpotCtx) GetJackpotInfo(ctx context.Context, req jackpot.GetJackpotInfoRequest) (*jackpotclient.GetJackpotInfoResponse, error) {
	args := m.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*jackpotclient.GetJackpotInfoResponse); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockJackpotCtx() *JackpotCtx {
	return &JackpotCtx{}
}
