package mock

import (
	"context"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/types"
	"gbh/permission/permissionclient"

	"github.com/stretchr/testify/mock"
)

type PermissionCtx struct{ mock.Mock }

func (m *PermissionCtx) AdminControllerPermissionIDs(ctx context.Context, controllerName string) ([]uint32, error) {
	args := m.Called(ctx, controllerName)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]uint32); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) AdminPermissions(ctx context.Context, req permission.AdminPermissionsRequest) ([]*permissionclient.AdminPermissionInfo, error) {
	args := m.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*permissionclient.AdminPermissionInfo); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) AdminPermissionInfoList(ctx context.Context, req permission.AdminPermissionInfoListRequest) ([]*permissionclient.PermissionListInfo, error) {
	args := m.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*permissionclient.PermissionListInfo); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) GetAdminEnabledPermission(ctx context.Context) (*permissionclient.GetAdminPermissionEnabledResponse, error) {
	args := m.Called(ctx)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*permissionclient.GetAdminPermissionEnabledResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) GetUserPermissionAffectList(ctx context.Context) (map[uint32]*permissionclient.PermissionAffectList, error) {
	args := m.Called(ctx)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(map[uint32]*permissionclient.PermissionAffectList); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) GetUserAllPermission(ctx context.Context, user types.UserInfo, permissionId []uint32, enable *bool) ([]*permissionclient.UserPermission, error) {
	args := m.Called(ctx, user, permissionId, enable)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*permissionclient.UserPermission); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) GetUserPermEnabled(ctx context.Context, permissionId []uint32) ([]uint32, error) {
	args := m.Called(ctx, permissionId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]uint32); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) GetUserPermissionConfigList(ctx context.Context, req permission.UserPermissionConfigListRequest) ([]*permissionclient.UserPermissionConfig, error) {
	args := m.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*permissionclient.UserPermissionConfig); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) GetAllPermConfigMap(ctx context.Context) (map[uint32]types.UserPermissionConfig, error) {
	args := m.Called(ctx)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(map[uint32]types.UserPermissionConfig); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) FilterUserPermission(ctx context.Context, allPermConfigMap map[uint32]types.UserPermissionConfig, operator types.UserInfo, permissionId []uint32) (permission.Result, error) {
	args := m.Called(ctx, allPermConfigMap, operator, permissionId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return permission.Result{}, err
	}

	if res, ok := resp.(permission.Result); ok {
		return res, nil
	}

	return permission.Result{}, nil
}

func (m *PermissionCtx) GetAdminVisitedHistory(ctx context.Context, adminId uint32) ([]*permissionclient.VisitedHistoryDetail, error) {
	args := m.Called(ctx, adminId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*permissionclient.VisitedHistoryDetail); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) DeleteUserPermissionByUserID(ctx context.Context, userId uint32) error {
	args := m.Called(ctx, userId)

	return args.Error(0)
}

func (m *PermissionCtx) CheckUserPermission(ctx context.Context, operator types.UserInfo, permissionId uint32, permissionAction string) (bool, error) {
	args := m.Called(ctx, operator, permissionId, permissionAction)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return false, err
	}

	if res, ok := resp.(bool); ok {
		return res, nil
	}

	return false, nil
}

func (m *PermissionCtx) GetPermissionConfigById(ctx context.Context, permissionId uint32) *types.UserPermissionConfig {
	args := m.Called(ctx, permissionId)
	resp := args.Get(0)

	if res, ok := resp.(*types.UserPermissionConfig); ok {
		return res
	}

	return nil
}

func (m *PermissionCtx) UserPermList(ctx context.Context, req permission.UserPermListRequest) (*permissionclient.UserPermListResponse, error) {
	args := m.Called(ctx, req)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.(*permissionclient.UserPermListResponse); ok {
		return res, nil
	}

	return nil, nil
}

func (m *PermissionCtx) GetPermissionParents(ctx context.Context, permissionId uint32) ([]*types.UserPermissionConfig, error) {
	args := m.Called(ctx, permissionId)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	if res, ok := resp.([]*types.UserPermissionConfig); ok {
		return res, nil
	}

	return nil, nil
}

func NewMockPermissionCtx() *PermissionCtx {
	return &PermissionCtx{}
}
