package jackpot

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/jackpot/jackpotclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	jackpotRPC := newMockJackpotRPC()
	jackpotCtx := New(jackpotRPC)

	expectedResponse := &jackpotContext{
		JackpotRPC: jackpotRPC,
	}

	assert.Equal(t, expectedResponse, jackpotCtx)
}

func Test_JackpotContext_GetJackpotInfo(t *testing.T) {
	jackpotRPC := newMockJackpotRPC()

	request := &jackpotclient.GetJackpotInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role7,
	}
	jackpotRPC.On("GetJackpotInfo", ctx, request).Return(&seeder.GetJackpotInfo, nil)
	jackpotCtx := New(jackpotRPC)

	resp, err := jackpotCtx.GetJackpotInfo(ctx, GetJackpotInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role7,
	})

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetJackpotInfo, resp)
}

func Test_JackpotContext_GetJackpotInfo_GRPCError(t *testing.T) {
	jackpotRPC := newMockJackpotRPC()

	request := &jackpotclient.GetJackpotInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role7,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	jackpotRPC.On("GetJackpotInfo", ctx, request).Return(nil, mockError)
	jackpotCtx := New(jackpotRPC)

	resp, err := jackpotCtx.GetJackpotInfo(ctx, GetJackpotInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role7,
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
