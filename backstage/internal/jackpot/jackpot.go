package jackpot

import (
	"context"
	"gbh/errorx"
	"gbh/jackpot/jackpotclient"
)

type Context interface {
	GetJackpotInfo(ctx context.Context, req GetJackpotInfoRequest) (*jackpotclient.GetJackpotInfoResponse, error)
}

type GetJackpotInfoRequest struct {
	StartDate string
	EndDate   string
	HallID    []uint32
	JpTypeID  []uint32
	GameID    []uint32
	Role      uint32
	UserID    uint32
}

type jackpotContext struct {
	JackpotRPC jackpotclient.Jackpot
}

func New(jackpotRPC jackpotclient.Jackpot) Context {
	return &jackpotContext{
		JackpotRPC: jackpotRPC,
	}
}

func (c *jackpotContext) GetJackpotInfo(ctx context.Context, req GetJackpotInfoRequest) (*jackpotclient.GetJackpotInfoResponse, error) {
	request := jackpotclient.GetJackpotInfoRequest{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		HallId:    req.<PERSON>,
		JpTypeId:  req.JpTypeID,
		GameType:  req.GameID,
		Role:      req.Role,
		UserId:    req.UserID,
	}

	resp, err := c.JackpotRPC.GetJackpotInfo(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
