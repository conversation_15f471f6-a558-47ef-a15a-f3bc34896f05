package game

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/redis"
	"gbh/utils/maputil"
	"gbh/utils/strutil"

	"github.com/zeromicro/go-zero/core/logx"
)

const (
	CacheGameURLListKey = "hall:%d:game_kind:%d:game_url"
)

type Context interface {
	GetGameURLList(ctx context.Context, in *gameclient.GameURLListRequest) ([]string, error)
	GetGameDetail(ctx context.Context, in GetGameDetailRequest) ([]*gameclient.GameDetail, error)
	GetUserLobbySwitch(ctx context.Context, in GetUserLobbySwitchRequest) ([]*gameclient.LobbySwitch, error)
	LobbyCategory(ctx context.Context, in LobbyCategoryRequest) (*gameclient.LobbyCategoryResponse, error)
	CreateGameDetail(ctx context.Context, in CreateGameDetailRequest) (*gameclient.EmptyResponse, error)
	FlattenLobbyCategory(ctx context.Context, in LobbyCategoryRequest) ([]string, error)
	GetGameListWithSwitch(ctx context.Context, in *gameclient.GetGameListWithSwitchRequest) (*gameclient.GetGameListWithSwitchResponse, error)
	GetUserLobby(ctx context.Context, user types.UserInfo, gameKind uint32) (bool, error)
}

type gameContext struct {
	GameRPC    gameclient.Game
	RedisCache redis.Redis
	logx.Logger
}

type GetGameDetailRequest struct {
	Id     []uint32
	HallId []uint32
}

type GetUserLobbySwitchRequest struct {
	UserId       uint32
	GameKind     uint32
	AllParentsId []uint32
	Role         uint32
	IsSub        *bool
}

type LobbyCategoryRequest struct {
	BBTip              bool
	TransferWagersType bool
	Enable             *bool
	Category           string
	Report             *bool
	Commissionable     *bool
	JP                 *bool
	External           *bool
	LastBet            string
	Series             string
}

type CreateGameDetailRequest struct {
	HallID   uint32
	GameKind uint32
	ID       uint32
	Enable   bool
}

type LobbySwitch struct {
	Mine   *bool
	Parent map[uint32]bool
}

func New(gameRPC gameclient.Game, redisCache redis.Redis) Context {
	return &gameContext{
		GameRPC:    gameRPC,
		RedisCache: redisCache,
		Logger:     logx.WithContext(context.Background()),
	}
}

func (c *gameContext) GetGameURLList(ctx context.Context, in *gameclient.GameURLListRequest) ([]string, error) {
	key := fmt.Sprintf(CacheGameURLListKey, in.GetHallId().GetValue(), in.GetGameKind())

	cached, err := c.GetGameURLListFromRedis(ctx, key)

	if err != nil {
		return []string{}, err
	}

	if len(cached) != 0 {
		return cached, nil
	}

	urls, err := c.GetGameURLListFromGrpc(ctx, in)

	if err != nil {
		return []string{}, err
	}

	c.CacheGameURLList(ctx, key, urls)

	return urls, nil
}

func (c *gameContext) GetGameURLListFromGrpc(ctx context.Context, in *gameclient.GameURLListRequest) ([]string, error) {
	resp, err := c.GameRPC.GetGameURLList(ctx, in)

	if err != nil {
		return []string{}, errorx.GRPCErrorToErrorx(err)
	}

	urls := make([]string, 0, len(resp.GetData()))

	for _, v := range resp.GetData() {
		urls = append(urls, v.GetUrl())
	}

	return urls, nil
}

func (c *gameContext) GetGameURLListFromRedis(ctx context.Context, key string) ([]string, error) {
	result, err := c.RedisCache.Get(ctx, key).Result()

	if err != nil {
		if !errors.Is(err, redis.ErrNil) {
			c.Error("redis get gameurl list error: ", err)
		}
		return []string{}, nil
	}

	var urls []string

	jsonErr := json.Unmarshal([]byte(result), &urls)

	if jsonErr != nil {
		return []string{}, errorx.JSONParseFailed
	}

	return urls, nil
}

func (c *gameContext) CacheGameURLList(ctx context.Context, key string, urls []string) {
	data, jsonErr := json.Marshal(urls)

	if jsonErr != nil {
		c.Error("marshal gameurl list error: ", jsonErr)
	}

	_, err := c.RedisCache.Set(ctx, key, string(data), 0).Result()

	if err != nil {
		c.Error("marshal gameurl list error: ", jsonErr)
	}
}

func (c *gameContext) GetGameDetail(ctx context.Context, in GetGameDetailRequest) ([]*gameclient.GameDetail, error) {
	resp, err := c.GameRPC.GetGameDetail(ctx, &gameclient.GetGameDetailRequest{
		Id:     in.Id,
		HallId: in.HallId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetGameDetail(), nil
}

func (c *gameContext) GetUserLobbySwitch(ctx context.Context, in GetUserLobbySwitchRequest) ([]*gameclient.LobbySwitch, error) {
	req := &gameclient.GetUserLobbySwitchRequest{
		UserId:     in.UserId,
		GameKind:   in.GameKind,
		AllParents: in.AllParentsId,
		Role:       in.Role,
	}

	if in.IsSub != nil {
		req.Sub = &gameclient.BoolValue{
			Value: *in.IsSub,
		}
	}

	resp, err := c.GameRPC.GetUserLobbySwitch(ctx, req)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetLobbySwitch(), nil
}

func (c *gameContext) LobbyCategory(ctx context.Context, in LobbyCategoryRequest) (*gameclient.LobbyCategoryResponse, error) {
	req := &gameclient.LobbyCategoryRequest{
		BbTip:              in.BBTip,
		TransferWagersType: in.TransferWagersType,
		Category:           in.Category,
		LastBet:            in.LastBet,
		Series:             in.Series,
	}

	if in.Enable != nil {
		req.Enable = &gameclient.BoolValue{
			Value: *in.Enable,
		}
	}

	if in.Report != nil {
		req.Report = &gameclient.BoolValue{
			Value: *in.Report,
		}
	}

	if in.Commissionable != nil {
		req.Commissionable = &gameclient.BoolValue{
			Value: *in.Commissionable,
		}
	}

	if in.JP != nil {
		req.Jp = &gameclient.BoolValue{
			Value: *in.JP,
		}
	}

	if in.External != nil {
		req.External = &gameclient.BoolValue{
			Value: *in.External,
		}
	}

	resp, err := c.GameRPC.LobbyCategory(ctx, req)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *gameContext) CreateGameDetail(ctx context.Context, in CreateGameDetailRequest) (*gameclient.EmptyResponse, error) {
	resp, err := c.GameRPC.CreateGameDetail(ctx, &gameclient.CreateGameDetailRequest{
		HallId:   in.HallID,
		GameKind: in.GameKind,
		Id:       in.ID,
		Enable:   in.Enable,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *gameContext) FlattenLobbyCategory(ctx context.Context, in LobbyCategoryRequest) ([]string, error) {
	resp, err := c.LobbyCategory(ctx, in)

	if err != nil {
		return nil, err
	}

	flattenLobbyCategor := []string{}
	flattenLobbyCategor = append(flattenLobbyCategor, resp.GetLive()...)
	flattenLobbyCategor = append(flattenLobbyCategor, resp.GetProb()...)
	flattenLobbyCategor = append(flattenLobbyCategor, resp.GetSport()...)
	flattenLobbyCategor = append(flattenLobbyCategor, resp.GetLottery()...)
	flattenLobbyCategor = append(flattenLobbyCategor, resp.GetCard()...)

	return flattenLobbyCategor, nil
}

func (c *gameContext) GetGameListWithSwitch(ctx context.Context, in *gameclient.GetGameListWithSwitchRequest) (*gameclient.GetGameListWithSwitchResponse, error) {
	resp, err := c.GameRPC.GetGameListWithSwitch(ctx, in)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *gameContext) GetUserLobby(ctx context.Context, user types.UserInfo, gameKind uint32) (bool, error) {
	lobbyCategorySwitch, lobbyCategorySwitchErr := c.lobbyCategorySwitchState(ctx, gameKind)
	if lobbyCategorySwitchErr != nil {
		return false, lobbyCategorySwitchErr
	}

	if !lobbyCategorySwitch {
		return false, nil
	}

	lobbySwitch, lobbySwitchErr := c.getLevelLobbySwitchState(ctx, user, gameKind)
	if lobbySwitchErr != nil {
		return false, lobbySwitchErr
	}

	// 廳主取自身資料
	if user.Role == constants.Role7 {
		if lobbySwitch.Mine != nil {
			return *lobbySwitch.Mine, nil
		}
	}

	var lobbySet = false
	if len(lobbySwitch.Parent) > 0 && len(lobbySwitch.Parent) == maputil.CountMapValues(lobbySwitch.Parent, true) {
		// 先取自身開關 ,沒有則取上層
		if lobbySwitch.Mine != nil {
			lobbySet = *lobbySwitch.Mine
		} else {
			lobbySet = true
		}
	}

	return lobbySet, nil
}

func (c *gameContext) lobbyCategorySwitchState(ctx context.Context, gameKind uint32) (bool, error) {
	enable := true
	allLobby, allLobbyErr := c.LobbyCategory(ctx, LobbyCategoryRequest{
		Enable: &enable,
	})
	if allLobbyErr != nil {
		return false, allLobbyErr
	}

	lobbyCategorySwitch := make([]string, 0)
	switch gameKind {
	case constants.BBLive:
		lobbyCategorySwitch = allLobby.GetLive()
	case constants.BBLottery:
		lobbyCategorySwitch = allLobby.GetLottery()
	}

	isExist := false
	for _, v := range lobbyCategorySwitch {
		if v == strutil.Uint32ToString(gameKind) {
			isExist = true
			break
		}
	}

	return isExist, nil
}

func (c *gameContext) getLevelLobbySwitchState(ctx context.Context, user types.UserInfo, gameKind uint32) (LobbySwitch, error) {
	userId := user.Id
	if user.IsSub {
		userId = user.ParentId
	}

	// 取自身及上層的開關
	userLobby, userLobbyErr := c.GetUserLobbySwitch(ctx, GetUserLobbySwitchRequest{
		UserId:       userId,
		GameKind:     gameKind,
		AllParentsId: user.AllParentsId,
		Role:         user.Role,
		IsSub:        &user.IsSub,
	})
	if userLobbyErr != nil {
		return LobbySwitch{}, userLobbyErr
	}

	var mineSwitch *bool                  // 自身的開關
	parentSwitch := make(map[uint32]bool) // 上層的開關

	for _, v := range userLobby {
		if v.GetUserId() == userId {
			tmp := v.GetSwitch()
			mineSwitch = &tmp
		} else {
			if _, exist := parentSwitch[v.GetGameKind()]; !exist {
				parentSwitch = make(map[uint32]bool)
			}
			parentSwitch[v.GetUserId()] = v.GetSwitch()
		}
	}

	lobbySwitch := LobbySwitch{
		Mine:   mineSwitch,
		Parent: parentSwitch,
	}

	return lobbySwitch, nil
}
