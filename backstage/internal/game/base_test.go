package game

import (
	"context"
	"gbh/game/gameclient"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx        context.Context
	redisCache *redis.Client
	redisMock  redismock.ClientMock
)

type mockGameRPC struct{ mock.Mock }

func (s *mockGameRPC) Lobby(ctx context.Context, in *gameclient.EmptyRequest, _ ...grpc.CallOption) (*gameclient.LobbyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.LobbyResponse), nil
}

func (s *mockGameRPC) EnableGameList(ctx context.Context, in *gameclient.EnableGameListRequest, _ ...grpc.CallOption) (*gameclient.EnableGameListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.EnableGameListResponse), nil
}

func (s *mockGameRPC) DemoLink(ctx context.Context, in *gameclient.DemoLinkRequest, _ ...grpc.CallOption) (*gameclient.DemoLinkResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.DemoLinkResponse), nil
}

func (s *mockGameRPC) GameDomain(ctx context.Context, in *gameclient.GameDomainRequest, _ ...grpc.CallOption) (*gameclient.GameDomainResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GameDomainResponse), nil
}

func (s *mockGameRPC) LobbyLink(ctx context.Context, in *gameclient.LobbyLinkRequest, _ ...grpc.CallOption) (*gameclient.LobbyLinkResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.LobbyLinkResponse), nil
}

func (s *mockGameRPC) GetLobbySwitch(ctx context.Context, in *gameclient.GetLobbySwitchRequest, _ ...grpc.CallOption) (*gameclient.GetLobbySwitchResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetLobbySwitchResponse), nil
}

func (s *mockGameRPC) GetLobbySwitchByHallID(ctx context.Context, in *gameclient.GetLobbySwitchByHallIDRequest, _ ...grpc.CallOption) (*gameclient.GetLobbySwitchByHallIDResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*gameclient.GetLobbySwitchByHallIDResponse), nil
}

func (s *mockGameRPC) GetCategory(ctx context.Context, in *gameclient.GetCategoryRequest, _ ...grpc.CallOption) (*gameclient.GetCategoryResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetCategoryResponse), nil
}

func (s *mockGameRPC) GetMenuSort(ctx context.Context, in *gameclient.GetMenuSortRequest, _ ...grpc.CallOption) (*gameclient.GetMenuSortResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetMenuSortResponse), nil
}

func (s *mockGameRPC) GetGameInfo(ctx context.Context, in *gameclient.GetGameInfoRequest, _ ...grpc.CallOption) (*gameclient.GetGameInfoResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetGameInfoResponse), nil
}

func (s *mockGameRPC) GetLobbyGameEntranceSwitch(ctx context.Context, in *gameclient.GetLobbyGameEntranceSwitchRequest, _ ...grpc.CallOption) (*gameclient.GetLobbyGameEntranceSwitchResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetLobbyGameEntranceSwitchResponse), nil
}

func (s *mockGameRPC) BulletinList(ctx context.Context, in *gameclient.BulletinRequest, _ ...grpc.CallOption) (*gameclient.BulletinResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.BulletinResponse), nil
}

func (s *mockGameRPC) GameKindList(ctx context.Context, in *gameclient.EmptyRequest, _ ...grpc.CallOption) (*gameclient.GameKindListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GameKindListResponse), nil
}

func (s *mockGameRPC) GameIconKind(ctx context.Context, in *gameclient.EmptyRequest, _ ...grpc.CallOption) (*gameclient.GameIconKindResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GameIconKindResponse), nil
}

func (s *mockGameRPC) AddUserFavorite(ctx context.Context, in *gameclient.AddUserFavoriteRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	return nil, args.Error(1)
}

func (s *mockGameRPC) GetMenuName(ctx context.Context, in *gameclient.GetMenuNameRequest, _ ...grpc.CallOption) (*gameclient.GetMenuNameResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetMenuNameResponse), nil
}

func (s *mockGameRPC) GetUserFavorite(ctx context.Context, in *gameclient.GetUserFavoriteRequest, _ ...grpc.CallOption) (*gameclient.GetUserFavoriteResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetUserFavoriteResponse), nil
}

func (s *mockGameRPC) DeleteUserFavorite(ctx context.Context, in *gameclient.DeleteUserFavoriteRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	return nil, args.Error(1)
}

func (s *mockGameRPC) GameList(ctx context.Context, in *gameclient.GameListRequest, _ ...grpc.CallOption) (*gameclient.GameListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GameListResponse), nil
}

func (s *mockGameRPC) GetGameMaintainLabel(ctx context.Context, in *gameclient.GameMaintainLabelRequest, _ ...grpc.CallOption) (*gameclient.GameMaintainLabelResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GameMaintainLabelResponse), nil
}

func (s *mockGameRPC) GetGameURLList(ctx context.Context, in *gameclient.GameURLListRequest, _ ...grpc.CallOption) (*gameclient.GameURLListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GameURLListResponse), nil
}

func (s *mockGameRPC) LobbyCategory(ctx context.Context, in *gameclient.LobbyCategoryRequest, _ ...grpc.CallOption) (*gameclient.LobbyCategoryResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.LobbyCategoryResponse), nil
}

func (s *mockGameRPC) GetGameListWithSwitch(ctx context.Context, in *gameclient.GetGameListWithSwitchRequest, _ ...grpc.CallOption) (*gameclient.GetGameListWithSwitchResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetGameListWithSwitchResponse), nil
}

func (s *mockGameRPC) GetUserLobbySwitch(ctx context.Context, in *gameclient.GetUserLobbySwitchRequest, _ ...grpc.CallOption) (*gameclient.GetUserLobbySwitchResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetUserLobbySwitchResponse), nil
}

func (s *mockGameRPC) GetGameDetail(ctx context.Context, in *gameclient.GetGameDetailRequest, _ ...grpc.CallOption) (*gameclient.GetGameDetailResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetGameDetailResponse), nil
}

func (s *mockGameRPC) CreateGameDetail(ctx context.Context, in *gameclient.CreateGameDetailRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	return nil, args.Error(1)
}

func (s *mockGameRPC) GetOnlineMemberMin(ctx context.Context, in *gameclient.GetOnlineMemberMinRequest, _ ...grpc.CallOption) (*gameclient.GetOnlineMemberMinResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetOnlineMemberMinResponse), nil
}

func (s *mockGameRPC) DeleteAPISynchronize(ctx context.Context, in *gameclient.DeleteAPISynchronizeRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	return nil, args.Error(1)
}

func (s *mockGameRPC) ModifyGameTypeSwitch(ctx context.Context, in *gameclient.ModifyGameTypeSwitchRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	return nil, args.Error(1)
}

func (s *mockGameRPC) DeleteGameTypeSynchronize(ctx context.Context, in *gameclient.DeleteGameTypeSynchronizeRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	return nil, args.Error(1)
}

func (s *mockGameRPC) ManageGameHallSwitch(ctx context.Context, in *gameclient.ManageGameHallSwitchRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.EmptyResponse), nil
}

func (s *mockGameRPC) DeleteUserLobby(ctx context.Context, in *gameclient.DeleteUserLobbyRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.EmptyResponse), nil
}

func (s *mockGameRPC) UpdateGameInfoHallList(ctx context.Context, in *gameclient.UpdateGameInfoHallListRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	return nil, args.Error(1)
}

func (s *mockGameRPC) GetOnlineMemberHour(ctx context.Context, in *gameclient.GetOnlineMemberHourRequest, _ ...grpc.CallOption) (*gameclient.GetOnlineMemberHourResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetOnlineMemberHourResponse), nil
}

func (s *mockGameRPC) UpdateHallLobbySwitch(ctx context.Context, in *gameclient.UpdateHallLobbySwitchRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockGameRPC) DeleteHallLowerAccountLobbySwitch(ctx context.Context, in *gameclient.DeleteHallLowerAccountLobbySwitchRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockGameRPC) CreateUserLobbySwitch(ctx context.Context, in *gameclient.CreateUserLobbySwitchRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockGameRPC) GetOnlineMemberDay(ctx context.Context, in *gameclient.GetOnlineMemberDayRequest, _ ...grpc.CallOption) (*gameclient.GetOnlineMemberDayResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*gameclient.GetOnlineMemberDayResponse), nil
}

func (s *mockGameRPC) DeleteHallLobbyCloseTime(ctx context.Context, in *gameclient.DeleteHallLobbyCloseTimeRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockGameRPC) SetHallLobbyCloseTime(ctx context.Context, in *gameclient.SetHallLobbyCloseTimeRequest, _ ...grpc.CallOption) (*gameclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func newMockGameRPC() *mockGameRPC {
	return &mockGameRPC{}
}

func init() {
	ctx = context.Background()
	redisCache, redisMock = redismock.NewClientMock()
}
