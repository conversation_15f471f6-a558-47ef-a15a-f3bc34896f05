package game

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/proto/game"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	gameRPC := newMockGameRPC()
	gameCtx := New(gameRPC, redisCache)

	expectedResponse := &gameContext{
		GameRPC:    gameRPC,
		RedisCache: redisCache,
		Logger:     logx.WithContext(ctx),
	}

	assert.Equal(t, expectedResponse, gameCtx)
}

const lotteryGameURLListKey = "hall:3820474:game_kind:12:game_url"

func TestGetGameURLListFromGrpc_GrpcError(t *testing.T) {
	grpcRequest := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	redisMock.ExpectGet(lotteryGameURLListKey).RedisNil()

	mockGameRPC := newMockGameRPC()
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("GetGameURLList", ctx, grpcRequest).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetGameURLList(ctx, grpcRequest)

	assert.Equal(t, []string{}, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func TestGetGameURLListFromGrpc(t *testing.T) {
	grpcRequest := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	redisMock.ExpectGet(lotteryGameURLListKey).RedisNil()

	redisMock.ExpectSet(lotteryGameURLListKey, "[\"bgp.bblotodemo.net\",\"bgp.bblotodemo1.net\"]", 0)

	mockGameRPC := newMockGameRPC()

	mockResponse := &gameclient.GameURLListResponse{
		Data: []*game.GameURL{
			{
				Id:  1,
				Url: "bgp.bblotodemo.net",
			},
			{
				Id:  2,
				Url: "bgp.bblotodemo1.net",
			},
		},
	}

	mockResponse.GetData()

	mockGameRPC.On("GetGameURLList", ctx, grpcRequest).Return(mockResponse, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetGameURLList(ctx, grpcRequest)

	assert.Equal(t, []string{
		"bgp.bblotodemo.net",
		"bgp.bblotodemo1.net",
	}, resp)
	assert.NoError(t, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func TestGetGameURLListFromRedis(t *testing.T) {
	redisMock.ExpectGet(lotteryGameURLListKey).SetVal("[\"bgp.bblotodemo.net\",\"bgp.bblotodemo1.net\"]")

	mockGameRPC := newMockGameRPC()

	gameCtx := New(mockGameRPC, redisCache)

	req := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	resp, err := gameCtx.GetGameURLList(ctx, req)

	assert.Equal(t, []string{
		"bgp.bblotodemo.net",
		"bgp.bblotodemo1.net",
	}, resp)
	assert.NoError(t, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func TestGetGameURLListFromRedis_JSONParseFailed(t *testing.T) {
	redisMock.ExpectGet(lotteryGameURLListKey).SetVal("i am not json")

	mockGameRPC := newMockGameRPC()

	gameCtx := New(mockGameRPC, redisCache)

	req := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	resp, err := gameCtx.GetGameURLList(ctx, req)

	assert.Equal(t, []string{}, resp)
	assert.ErrorIs(t, errorx.JSONParseFailed, err)
	assert.NoError(t, redisMock.ExpectationsWereMet())
	redisMock.ClearExpect()
}

func Test_GameContext_GetGameDetail(t *testing.T) {
	mockGameRPC := newMockGameRPC()
	request := GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}

	mockRequest := &gameclient.GetGameDetailRequest{
		Id:     request.Id,
		HallId: request.HallId,
	}

	mockGameRPC.On("GetGameDetail", ctx, mockRequest).Return(&seeder.GameDetail, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetGameDetail(ctx, request)

	expectedResponse := seeder.GameDetail.GetGameDetail()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GameContext_GetGameDetail_GRPCError(t *testing.T) {
	mockGameRPC := newMockGameRPC()
	request := GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}

	mockRequest := &gameclient.GetGameDetailRequest{
		Id:     request.Id,
		HallId: request.HallId,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("GetGameDetail", ctx, mockRequest).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetGameDetail(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_GetUserLobbySwitch(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	isSub := true
	request := GetUserLobbySwitchRequest{
		UserId:       seeder.UserId,
		GameKind:     constants.BBLive,
		AllParentsId: []uint32{456121695, 3820474},
		Role:         constants.Role4,
		IsSub:        &isSub,
	}

	mockRequest := &gameclient.GetUserLobbySwitchRequest{
		UserId:     request.UserId,
		GameKind:   request.GameKind,
		AllParents: request.AllParentsId,
		Role:       request.Role,
		Sub: &gameclient.BoolValue{
			Value: isSub,
		},
	}

	mockGameRPC.On("GetUserLobbySwitch", ctx, mockRequest).Return(&seeder.UserLobbySwitch, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobbySwitch(ctx, request)

	expectedResponse := seeder.UserLobbySwitch.GetLobbySwitch()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GameContext_GetUserLobbySwitch_GRPCError(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	isSub := true
	request := GetUserLobbySwitchRequest{
		UserId:       seeder.UserId,
		GameKind:     constants.BBLive,
		AllParentsId: []uint32{456121695, 3820474},
		Role:         constants.Role4,
		IsSub:        &isSub,
	}

	mockRequest := &gameclient.GetUserLobbySwitchRequest{
		UserId:     request.UserId,
		GameKind:   request.GameKind,
		AllParents: request.AllParentsId,
		Role:       request.Role,
		Sub: &gameclient.BoolValue{
			Value: isSub,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("GetUserLobbySwitch", ctx, mockRequest).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobbySwitch(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_LobbyCategory(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	report := true
	commissionable := true
	JP := true
	external := true

	request := LobbyCategoryRequest{
		BBTip:              true,
		TransferWagersType: false,
		Enable:             &enable,
		Category:           "",
		Report:             &report,
		Commissionable:     &commissionable,
		JP:                 &JP,
		External:           &external,
		LastBet:            "",
		Series:             "",
	}

	mockRequest := &gameclient.LobbyCategoryRequest{
		BbTip:              request.BBTip,
		TransferWagersType: request.TransferWagersType,
		Category:           request.Category,
		LastBet:            request.LastBet,
		Series:             request.Series,
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
		Report: &gameclient.BoolValue{
			Value: report,
		},
		Commissionable: &gameclient.BoolValue{
			Value: commissionable,
		},
		Jp: &gameclient.BoolValue{
			Value: JP,
		},
		External: &gameclient.BoolValue{
			Value: external,
		},
	}

	mockGameRPC.On("LobbyCategory", ctx, mockRequest).Return(&seeder.LobbyCategory, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.LobbyCategory(ctx, request)

	expectedResponse := &seeder.LobbyCategory

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_GameContext_LobbyCategory_GRPCError(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	report := true
	commissionable := true
	JP := true
	external := true

	request := LobbyCategoryRequest{
		BBTip:              true,
		TransferWagersType: false,
		Enable:             &enable,
		Category:           "",
		Report:             &report,
		Commissionable:     &commissionable,
		JP:                 &JP,
		External:           &external,
		LastBet:            "",
		Series:             "",
	}

	mockRequest := &gameclient.LobbyCategoryRequest{
		BbTip:              request.BBTip,
		TransferWagersType: request.TransferWagersType,
		Category:           request.Category,
		LastBet:            request.LastBet,
		Series:             request.Series,
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
		Report: &gameclient.BoolValue{
			Value: report,
		},
		Commissionable: &gameclient.BoolValue{
			Value: commissionable,
		},
		Jp: &gameclient.BoolValue{
			Value: JP,
		},
		External: &gameclient.BoolValue{
			Value: external,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("LobbyCategory", ctx, mockRequest).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.LobbyCategory(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_CreateGameDetail(t *testing.T) {
	mockGameRPC := newMockGameRPC()
	request := CreateGameDetailRequest{
		HallID:   seeder.HallId,
		GameKind: constants.BBLive,
		ID:       constants.GameDetailID1,
		Enable:   true,
	}

	mockRequest := &gameclient.CreateGameDetailRequest{
		HallId:   request.HallID,
		GameKind: request.GameKind,
		Id:       request.ID,
		Enable:   request.Enable,
	}

	mockGameRPC.On("CreateGameDetail", ctx, mockRequest).Return(&gameclient.EmptyResponse{}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.CreateGameDetail(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, (*gameclient.EmptyResponse)(nil), resp)
}

func Test_GameContext_CreateGameDetail_GRPCError(t *testing.T) {
	mockGameRPC := newMockGameRPC()
	request := CreateGameDetailRequest{
		HallID:   seeder.HallId,
		GameKind: constants.BBLive,
		ID:       constants.GameDetailID1,
		Enable:   true,
	}

	mockRequest := &gameclient.CreateGameDetailRequest{
		HallId:   request.HallID,
		GameKind: request.GameKind,
		Id:       request.ID,
		Enable:   request.Enable,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("CreateGameDetail", ctx, mockRequest).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.CreateGameDetail(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_FlattenLobbyCategory_GRPCError(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	report := true
	commissionable := true
	JP := true
	external := true

	request := LobbyCategoryRequest{
		BBTip:              true,
		TransferWagersType: false,
		Enable:             &enable,
		Category:           "",
		Report:             &report,
		Commissionable:     &commissionable,
		JP:                 &JP,
		External:           &external,
		LastBet:            "",
		Series:             "",
	}

	mockRequest := &gameclient.LobbyCategoryRequest{
		BbTip:              request.BBTip,
		TransferWagersType: request.TransferWagersType,
		Category:           request.Category,
		LastBet:            request.LastBet,
		Series:             request.Series,
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
		Report: &gameclient.BoolValue{
			Value: report,
		},
		Commissionable: &gameclient.BoolValue{
			Value: commissionable,
		},
		Jp: &gameclient.BoolValue{
			Value: JP,
		},
		External: &gameclient.BoolValue{
			Value: external,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("LobbyCategory", ctx, mockRequest).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.FlattenLobbyCategory(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_GameContext_FlattenLobbyCategory(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	report := true
	commissionable := true
	JP := true
	external := true

	request := LobbyCategoryRequest{
		BBTip:              true,
		TransferWagersType: false,
		Enable:             &enable,
		Category:           "",
		Report:             &report,
		Commissionable:     &commissionable,
		JP:                 &JP,
		External:           &external,
		LastBet:            "",
		Series:             "",
	}

	mockRequest := &gameclient.LobbyCategoryRequest{
		BbTip:              request.BBTip,
		TransferWagersType: request.TransferWagersType,
		Category:           request.Category,
		LastBet:            request.LastBet,
		Series:             request.Series,
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
		Report: &gameclient.BoolValue{
			Value: report,
		},
		Commissionable: &gameclient.BoolValue{
			Value: commissionable,
		},
		Jp: &gameclient.BoolValue{
			Value: JP,
		},
		External: &gameclient.BoolValue{
			Value: external,
		},
	}

	mockGameRPC.On("LobbyCategory", ctx, mockRequest).Return(&seeder.LobbyCategory, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.FlattenLobbyCategory(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, seeder.FlattenLobbyCategory, resp)
}

func Test_GameContext_GetGameListWithSwitch(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	in := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLive},
	}

	mockReturn := &gameclient.GetGameListWithSwitchResponse{
		Data: []*gameclient.GameListWithSwitch{
			{
				GameKind:            3,
				GameId:              3001,
				Name:                "百家樂",
				Device:              0,
				PlatformEnable:      true,
				PcEnable:            true,
				MobileEnable:        true,
				DemoEnable:          false,
				IsJackpot:           false,
				CommissionableGroup: "1",
				OpenDate:            "2014-01-01",
				UpdatedAt:           "2014-01-01 00:00:00",
				ExternalId:          "",
				IconKind:            "",
				WhiteList:           []uint32{3820325},
			},
		},
	}

	mockGameRPC.On("GetGameListWithSwitch", ctx, in).Return(mockReturn, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetGameListWithSwitch(ctx, in)

	assert.Equal(t, mockReturn, resp)
	assert.NoError(t, err)
}

func Test_GameContext_GetGameListWithSwitch_GRPCError(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	in := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLive},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("GetGameListWithSwitch", ctx, in).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetGameListWithSwitch(ctx, in)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_GameContext_GetUserLobbyError(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockRequest := &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("LobbyCategory", ctx, mockRequest).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.UserInfo, constants.BBLive)

	assert.False(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_GameContext_GetUserLobby_BBLive_LobbyCategorySwitchStateIsFalse(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockRequest := &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}

	mockGameRPC.On("LobbyCategory", ctx, mockRequest).Return(&gameclient.LobbyCategoryResponse{}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.UserInfo, constants.BBLive)

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_GameContext_GetUserLobby_BBLottery_LobbyCategorySwitchStateIsFalse(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockRequest := &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}

	mockGameRPC.On("LobbyCategory", ctx, mockRequest).Return(&gameclient.LobbyCategoryResponse{}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.UserInfo, constants.BBLottery)

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_GameContext_GetUserLobby_GetUserLobbySwitchError(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockGameRPC.On("LobbyCategory", ctx, &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}).Return(&seeder.LobbyCategory, nil)

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockGameRPC.On("GetUserLobbySwitch", ctx, &gameclient.GetUserLobbySwitchRequest{
		UserId:     seeder.UserInfo.Id,
		GameKind:   constants.BBLive,
		AllParents: seeder.UserInfo.AllParentsId,
		Role:       seeder.UserInfo.Role,
		Sub: &gameclient.BoolValue{
			Value: seeder.UserInfo.IsSub,
		},
	}).Return(nil, mockError)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.UserInfo, constants.BBLive)

	assert.False(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_GameContext_GetUserLobby_MaxUser(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockGameRPC.On("LobbyCategory", ctx, &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}).Return(&seeder.LobbyCategory, nil)

	mockGameRPC.On("GetUserLobbySwitch", ctx, &gameclient.GetUserLobbySwitchRequest{
		UserId:     seeder.MaxUserInfo.Id,
		GameKind:   constants.BBLive,
		AllParents: seeder.MaxUserInfo.AllParentsId,
		Role:       seeder.MaxUserInfo.Role,
		Sub: &gameclient.BoolValue{
			Value: seeder.MaxUserInfo.IsSub,
		},
	}).Return(&gameclient.GetUserLobbySwitchResponse{
		LobbySwitch: []*gameclient.LobbySwitch{
			{GameKind: constants.BBLive, Switch: true, UserId: seeder.MaxUserInfo.Id},
		},
	}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.MaxUserInfo, constants.BBLive)

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_GameContext_GetUserLobby_SubUser(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockGameRPC.On("LobbyCategory", ctx, &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}).Return(&seeder.LobbyCategory, nil)

	mockGameRPC.On("GetUserLobbySwitch", ctx, &gameclient.GetUserLobbySwitchRequest{
		UserId:     seeder.SubUserInfo.ParentId,
		GameKind:   constants.BBLive,
		AllParents: seeder.SubUserInfo.AllParentsId,
		Role:       seeder.SubUserInfo.Role,
		Sub: &gameclient.BoolValue{
			Value: seeder.SubUserInfo.IsSub,
		},
	}).Return(&gameclient.GetUserLobbySwitchResponse{
		LobbySwitch: []*gameclient.LobbySwitch{
			{GameKind: constants.BBLive, Switch: false, UserId: seeder.SubUserInfo.ParentId},
		},
	}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.SubUserInfo, constants.BBLive)

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_GameContext_GetUserLobby_User(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockGameRPC.On("LobbyCategory", ctx, &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}).Return(&seeder.LobbyCategory, nil)

	mockGameRPC.On("GetUserLobbySwitch", ctx, &gameclient.GetUserLobbySwitchRequest{
		UserId:     seeder.UserInfo.Id,
		GameKind:   constants.BBLive,
		AllParents: seeder.UserInfo.AllParentsId,
		Role:       seeder.UserInfo.Role,
		Sub: &gameclient.BoolValue{
			Value: seeder.UserInfo.IsSub,
		},
	}).Return(&gameclient.GetUserLobbySwitchResponse{
		LobbySwitch: []*gameclient.LobbySwitch{
			{GameKind: constants.BBLive, Switch: true, UserId: seeder.UserInfo.Id},
			{GameKind: constants.BBLive, Switch: true, UserId: seeder.UserInfo.ParentId},
		},
	}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.UserInfo, constants.BBLive)

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_GameContext_GetUserLobby_User_UpperIsFalse(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockGameRPC.On("LobbyCategory", ctx, &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}).Return(&seeder.LobbyCategory, nil)

	mockGameRPC.On("GetUserLobbySwitch", ctx, &gameclient.GetUserLobbySwitchRequest{
		UserId:     seeder.UserInfo.Id,
		GameKind:   constants.BBLive,
		AllParents: seeder.UserInfo.AllParentsId,
		Role:       seeder.UserInfo.Role,
		Sub: &gameclient.BoolValue{
			Value: seeder.UserInfo.IsSub,
		},
	}).Return(&gameclient.GetUserLobbySwitchResponse{
		LobbySwitch: []*gameclient.LobbySwitch{
			{GameKind: constants.BBLive, Switch: true, UserId: seeder.UserInfo.Id},
			{GameKind: constants.BBLive, Switch: false, UserId: seeder.UserInfo.ParentId},
		},
	}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.UserInfo, constants.BBLive)

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_GameContext_GetUserLobby_EmpryUserLobby(t *testing.T) {
	mockGameRPC := newMockGameRPC()

	enable := true
	mockGameRPC.On("LobbyCategory", ctx, &gameclient.LobbyCategoryRequest{
		Enable: &gameclient.BoolValue{
			Value: enable,
		},
	}).Return(&seeder.LobbyCategory, nil)

	mockGameRPC.On("GetUserLobbySwitch", ctx, &gameclient.GetUserLobbySwitchRequest{
		UserId:     seeder.UserInfo.Id,
		GameKind:   constants.BBLive,
		AllParents: seeder.UserInfo.AllParentsId,
		Role:       seeder.UserInfo.Role,
		Sub: &gameclient.BoolValue{
			Value: seeder.UserInfo.IsSub,
		},
	}).Return(&gameclient.GetUserLobbySwitchResponse{
		LobbySwitch: []*gameclient.LobbySwitch{
			{GameKind: constants.BBLive, Switch: true, UserId: seeder.UserInfo.ParentId},
		},
	}, nil)

	gameCtx := New(mockGameRPC, redisCache)

	resp, err := gameCtx.GetUserLobby(ctx, seeder.UserInfo, constants.BBLive)

	assert.True(t, resp)
	assert.NoError(t, err)
}
