package maintain

import (
	"gbh/backstage/internal/constants"
	"gbh/errorx"
	"gbh/maintain/maintainclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	maintainRPC := newMockMaintainRPC()
	maintainCtx := New(maintainRPC)

	expectedResponse := &maintainContext{
		MaintainRPC: maintainRPC,
	}

	assert.Equal(t, expectedResponse, maintainCtx)
}

func Test_GetMaintainByGameKind(t *testing.T) {
	grpcRequest := &maintainclient.GetMaintainByGameKindRequest{
		GameKind: constants.BBLive,
		ClientIp: &maintainclient.StringValue{
			Value: "127.0.0.1",
		},
	}

	mockResponse := &maintainclient.GetMaintainByGameKindResponse{
		BeginAt:       "2025-03-11T13:30:00+0800",
		EndAt:         "2025-03-11T13:30:00+0800",
		ModifiedAt:    "2025-03-11T13:30:00+0800",
		Code:          constants.BBLive,
		IsMaintaining: false,
		InWhitelist:   false,
		Msg:           "",
	}

	mockMaintainRPC := newMockMaintainRPC()
	mockMaintainRPC.On("GetMaintainByGameKind", ctx, grpcRequest).Return(mockResponse, nil)

	maintainCtx := New(mockMaintainRPC)
	resp, err := maintainCtx.GetMaintainByGameKind(ctx, grpcRequest)

	assert.NoError(t, err)
	assert.Equal(t, mockResponse, resp)
}

func Test_MaintainContext_GetMaintainByGameKind_GRPCError(t *testing.T) {
	grpcRequest := &maintainclient.GetMaintainByGameKindRequest{
		GameKind: constants.BBLive,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockMaintainRPC := newMockMaintainRPC()
	mockMaintainRPC.On("GetMaintainByGameKind", ctx, grpcRequest).Return(nil, mockError)

	maintainCtx := New(mockMaintainRPC)
	resp, err := maintainCtx.GetMaintainByGameKind(ctx, grpcRequest)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}
