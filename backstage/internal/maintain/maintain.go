package maintain

import (
	"context"
	"gbh/errorx"
	"gbh/maintain/maintainclient"
)

type Context interface {
	GetMaintainByGameKind(ctx context.Context, in *maintainclient.GetMaintainByGameKindRequest) (*maintainclient.GetMaintainByGameKindResponse, error)
}

type maintainContext struct {
	MaintainRPC maintainclient.Maintain
}

func New(maintainRPC maintainclient.Maintain) Context {
	return &maintainContext{
		MaintainRPC: maintainRPC,
	}
}

func (c *maintainContext) GetMaintainByGameKind(ctx context.Context, in *maintainclient.GetMaintainByGameKindRequest) (*maintainclient.GetMaintainByGameKindResponse, error) {
	request := &maintainclient.GetMaintainByGameKindRequest{
		GameKind: in.GetGameKind(),
	}

	if in.GetClientIp().GetValue() != "" {
		request.ClientIp = &maintainclient.StringValue{Value: in.GetClientIp().GetValue()}
	}

	resp, err := c.MaintainRPC.GetMaintainByGameKind(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
