package maintain

import (
	"context"
	"gbh/maintain/maintainclient"

	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx context.Context
)

type mockMaintainRPC struct{ mock.Mock }

func (m *mockMaintainRPC) GetMaintainByGameKind(ctx context.Context, in *maintainclient.GetMaintainByGameKindRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainByGameKindResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainByGameKindResponse), nil
}

func (m *mockMaintainRPC) Get(ctx context.Context, in *maintainclient.MaintainRequest, _ ...grpc.CallOption) (*maintainclient.MaintainResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.MaintainResponse), nil
}

func (m *mockMaintainRPC) GetMaintainByGameKindFromRedis(ctx context.Context, in *maintainclient.GetMaintainByGameKindFromRedisRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainByGameKindFromRedisResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainByGameKindFromRedisResponse), nil
}

func (m *mockMaintainRPC) GetMaintainByHallID(ctx context.Context, in *maintainclient.GetMaintainByHallIDRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainByHallIDResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainByHallIDResponse), nil
}

func (m *mockMaintainRPC) FeatureEntranceMaintenance(ctx context.Context, in *maintainclient.FeatureEntranceMaintenanceRequest, _ ...grpc.CallOption) (*maintainclient.FeatureEntranceMaintenanceResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.FeatureEntranceMaintenanceResponse), nil
}

func (m *mockMaintainRPC) GetMaintainGameKind(ctx context.Context, in *maintainclient.EmptyRequest, _ ...grpc.CallOption) (*maintainclient.GetMaintainGameKindResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetMaintainGameKindResponse), nil
}

func (m *mockMaintainRPC) CreateLogoutSchedule(ctx context.Context, in *maintainclient.CreateLogoutScheduleRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (m *mockMaintainRPC) DeleteLogoutSchedule(ctx context.Context, in *maintainclient.DeleteLogoutScheduleRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (m *mockMaintainRPC) UpdateDomainMaintenance(ctx context.Context, in *maintainclient.UpdateDomainMaintenanceRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (m *mockMaintainRPC) UpdateSystemDBMaintenanceNoteTable(ctx context.Context, in *maintainclient.UpdateSystemDBMaintenanceNoteTableRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (m *mockMaintainRPC) DeleteDomainMessage(ctx context.Context, in *maintainclient.DeleteDomainMessageRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func (m *mockMaintainRPC) GetSystemDBMaintenanceTable(ctx context.Context, in *maintainclient.EmptyRequest, _ ...grpc.CallOption) (*maintainclient.GetSystemDBMaintenanceTableResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.GetSystemDBMaintenanceTableResponse), nil
}

func (m *mockMaintainRPC) CreateDomainMessage(ctx context.Context, in *maintainclient.CreateDomainMessageRequest, _ ...grpc.CallOption) (*maintainclient.EmptyResponse, error) {
	args := m.Called(ctx, in)
	resp := args.Get(0)

	if resp == nil {
		return nil, args.Error(1)
	}

	return resp.(*maintainclient.EmptyResponse), nil
}

func newMockMaintainRPC() *mockMaintainRPC {
	return &mockMaintainRPC{}
}

func init() {
	ctx = context.Background()
}
