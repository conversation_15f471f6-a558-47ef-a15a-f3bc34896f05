package admin

import (
	"testing"

	"gbh/admin/adminclient"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/proto/admin"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/stretchr/testify/assert"
)

func TestNew(t *testing.T) {
	adminRPC := newMockAdminRPC()
	adminCtx := New(adminRPC)

	expectedResponse := &adminContext{
		AdminRPC: adminRPC,
	}
	assert.Equal(t, expectedResponse, adminCtx)
}

func Test_adminContext_PositionListFromRPC(t *testing.T) {
	adminRPC := newMockAdminRPC()

	request := PositionListRequest{}
	mockRequest := &adminclient.PositionListRequest{}

	adminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.PositionList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetPositionList, resp)
}

func Test_adminContext_PositionListFromRPC_WithPage(t *testing.T) {
	adminRPC := newMockAdminRPC()

	page := uint32(1)
	request := PositionListRequest{
		Page: &page,
	}
	mockRequest := &adminclient.PositionListRequest{
		Page: page,
	}

	adminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.PositionList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetPositionList, resp)
}

func Test_adminContext_PositionListFromRPC_WithLimit(t *testing.T) {
	adminRPC := newMockAdminRPC()

	limit := uint32(1)
	request := PositionListRequest{
		Limit: &limit,
	}
	mockRequest := &adminclient.PositionListRequest{
		PageLimit: limit,
	}

	adminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.PositionList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetPositionList, resp)
}

func Test_adminContext_PositionListFromRPC_WithSort(t *testing.T) {
	adminRPC := newMockAdminRPC()

	sort := "id"
	request := PositionListRequest{
		Sort: &sort,
	}
	mockRequest := &adminclient.PositionListRequest{
		Sort: &admin.StringValue{
			Value: sort,
		},
	}

	adminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.PositionList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetPositionList, resp)
}

func Test_adminContext_PositionListFromRPC_WithOrder(t *testing.T) {
	adminRPC := newMockAdminRPC()

	order := "ASC"
	request := PositionListRequest{
		Order: &order,
	}
	mockRequest := &adminclient.PositionListRequest{
		Order: &admin.StringValue{
			Value: order,
		},
	}

	adminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.PositionList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetPositionList, resp)
}

func Test_adminContext_PositionListFromRPC_WithPosition(t *testing.T) {
	adminRPC := newMockAdminRPC()

	position := "GM"
	request := PositionListRequest{
		Position: &position,
	}
	mockRequest := &adminclient.PositionListRequest{
		Position: &admin.StringValue{
			Value: position,
		},
	}

	adminRPC.On("PositionList", ctx, mockRequest).Return(&seeder.GetPositionList, nil)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.PositionList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetPositionList, resp)
}

func Test_adminContext_PositionList_GRPCError(t *testing.T) {
	adminRPC := newMockAdminRPC()

	request := PositionListRequest{}
	mockRequest := &adminclient.PositionListRequest{}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	adminRPC.On("PositionList", ctx, mockRequest).Return(nil, mockError)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.PositionList(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_adminContext_GetSessionFromRPC(t *testing.T) {
	adminRPC := newMockAdminRPC()

	mockRequest := &adminclient.GetSessionRequest{
		Session: seeder.Token,
		Ip:      "*************",
	}

	adminRPC.On("GetSession", ctx, mockRequest).Return(&seeder.GetAdminSession, nil)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetSession(ctx, mockRequest.GetSession(), mockRequest.GetIp())

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetAdminSession, resp)
}

func Test_adminContext_GetSession_GRPCError(t *testing.T) {
	adminRPC := newMockAdminRPC()

	mockRequest := &adminclient.GetSessionRequest{
		Session: seeder.Token,
		Ip:      "*************",
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	adminRPC.On("GetSession", ctx, mockRequest).Return(nil, mockError)
	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetSession(ctx, mockRequest.GetSession(), mockRequest.GetIp())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AdminContext_GetGMUserIdleList(t *testing.T) {
	adminRPC := newMockAdminRPC()

	enable := true
	request := GetGMUserIdleListRequest{
		Period:   7,
		Username: "test",
		Alias:    "",
		Sort:     "username",
		Order:    "asc",
		Enable:   &enable,
	}

	mockRequest := &adminclient.GetGMUserIdleListRequest{
		Period:   request.Period,
		Username: request.Username,
		Alias:    request.Alias,
		Sort:     request.Sort,
		Order:    request.Order,
		Enable: &adminclient.BoolValue{
			Value: *request.Enable,
		},
	}

	adminRPC.On("GetGMUserIdleList", ctx, mockRequest).Return(&seeder.GMUserList, nil)

	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetGMUserIdleList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GMUserList, resp)
}

func Test_AdminContext_GetGMUserIdleList_GRPCError(t *testing.T) {
	adminRPC := newMockAdminRPC()

	enable := true
	request := GetGMUserIdleListRequest{
		Period:   7,
		Username: "test",
		Alias:    "",
		Sort:     "username",
		Order:    "asc",
		Enable:   &enable,
	}

	mockRequest := &adminclient.GetGMUserIdleListRequest{
		Period:   request.Period,
		Username: request.Username,
		Alias:    request.Alias,
		Sort:     request.Sort,
		Order:    request.Order,
		Enable: &adminclient.BoolValue{
			Value: *request.Enable,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	adminRPC.On("GetGMUserIdleList", ctx, mockRequest).Return(nil, mockError)

	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetGMUserIdleList(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AdminContext_GetGMUserList(t *testing.T) {
	adminRPC := newMockAdminRPC()

	enable := true
	request := GetGMUserListRequest{
		Username: "test",
		Alias:    "",
		Sort:     "username",
		Order:    "asc",
		Enable:   &enable,
	}

	mockRequest := &adminclient.GetGMUserListRequest{
		Username: request.Username,
		Alias:    request.Alias,
		Sort:     request.Sort,
		Order:    request.Order,
		Enable: &adminclient.BoolValue{
			Value: *request.Enable,
		},
	}

	adminRPC.On("GetGMUserList", ctx, mockRequest).Return(&seeder.GMUserList, nil)

	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetGMUserList(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GMUserList, resp)
}

func Test_AdminContext_GetGMUserList_GRPCError(t *testing.T) {
	adminRPC := newMockAdminRPC()

	enable := true
	request := GetGMUserListRequest{
		Username: "test",
		Alias:    "",
		Sort:     "username",
		Order:    "asc",
		Enable:   &enable,
	}

	mockRequest := &adminclient.GetGMUserListRequest{
		Username: request.Username,
		Alias:    request.Alias,
		Sort:     request.Sort,
		Order:    request.Order,
		Enable: &adminclient.BoolValue{
			Value: *request.Enable,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	adminRPC.On("GetGMUserList", ctx, mockRequest).Return(nil, mockError)

	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetGMUserList(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AdminContext_GetUsersDepartment(t *testing.T) {
	adminRPC := newMockAdminRPC()

	request := GetUsersDepartmentRequest{
		Users:        seeder.GMUserList.GetUserList(),
		DepartmentId: 1,
		PositionId:   1,
		Page:         1,
		PageLimit:    1,
	}

	mockRequest := &adminclient.UsersDepartmentRequest{
		Users: request.Users,
		DepartmentId: &adminclient.Uint32Value{
			Value: request.DepartmentId,
		},
		PositionId: &adminclient.Uint32Value{
			Value: request.PositionId,
		},
		Page: &adminclient.Uint32Value{
			Value: request.Page,
		},
		PageLimit: &adminclient.Uint32Value{
			Value: request.PageLimit,
		},
	}

	adminRPC.On("UsersDepartment", ctx, mockRequest).Return(&seeder.UsersDepartment, nil)

	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetUsersDepartment(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.UsersDepartment, resp)
}

func Test_AdminContext_GetUsersDepartment_GRPCError(t *testing.T) {
	adminRPC := newMockAdminRPC()

	request := GetUsersDepartmentRequest{
		Users:        seeder.GMUserList.GetUserList(),
		DepartmentId: 1,
		PositionId:   1,
		Page:         1,
		PageLimit:    1,
	}

	mockRequest := &adminclient.UsersDepartmentRequest{
		Users: request.Users,
		DepartmentId: &adminclient.Uint32Value{
			Value: request.DepartmentId,
		},
		PositionId: &adminclient.Uint32Value{
			Value: request.PositionId,
		},
		Page: &adminclient.Uint32Value{
			Value: request.Page,
		},
		PageLimit: &adminclient.Uint32Value{
			Value: request.PageLimit,
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	adminRPC.On("UsersDepartment", ctx, mockRequest).Return(nil, mockError)

	adminCtx := New(adminRPC)
	resp, err := adminCtx.GetUsersDepartment(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AdminContext_DepartmentList(t *testing.T) {
	adminRPC := newMockAdminRPC()

	mockRequest := &adminclient.DepartmentListRequest{
		Sort:       "id",
		Order:      "asc",
		Department: "test",
		Page:       1,
		PageLimit:  1,
	}

	adminRPC.On("DepartmentList", ctx, mockRequest).Return(&seeder.DepartmentList, nil)

	adminCtx := New(adminRPC)

	resp, err := adminCtx.DepartmentList(ctx, DepartmentListRequest{
		Department: "test",
		Sort:       "id",
		Order:      "asc",
		Page:       1,
		PageLimit:  1,
	})

	expectedResponse := &seeder.DepartmentList

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_AdminContext_DepartmentList_GRPCError(t *testing.T) {
	adminRPC := newMockAdminRPC()

	mockRequest := &adminclient.DepartmentListRequest{
		Sort:       "id",
		Order:      "asc",
		Department: "test",
		Page:       1,
		PageLimit:  1,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	adminRPC.On("DepartmentList", ctx, mockRequest).Return(nil, mockError)

	adminCtx := New(adminRPC)

	resp, err := adminCtx.DepartmentList(ctx, DepartmentListRequest{
		Department: "test",
		Sort:       "id",
		Order:      "asc",
		Page:       1,
		PageLimit:  1,
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AdminContext_GetGMHallPrivilege(t *testing.T) {
	adminRPC := newMockAdminRPC()

	mockRequest := &adminclient.GMHallPrivilegeRequest{
		AdminId: seeder.AdminID,
	}

	adminRPC.On("GetGMHallPrivilege", ctx, mockRequest).Return(&seeder.GMHallPrivilege, nil)

	adminCtx := New(adminRPC)

	resp, err := adminCtx.GetGMHallPrivilege(ctx, seeder.AdminID)

	expectedResponse := &seeder.GMHallPrivilege

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_AdminContext_GetGMHallPrivilege_GRPCError(t *testing.T) {
	adminRPC := newMockAdminRPC()

	mockRequest := &adminclient.GMHallPrivilegeRequest{
		AdminId: seeder.AdminID,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	adminRPC.On("GetGMHallPrivilege", ctx, mockRequest).Return(nil, mockError)

	adminCtx := New(adminRPC)

	resp, err := adminCtx.GetGMHallPrivilege(ctx, seeder.AdminID)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
