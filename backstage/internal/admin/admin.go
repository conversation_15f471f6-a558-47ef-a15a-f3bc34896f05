package admin

import (
	"context"
	"gbh/admin/adminclient"
	"gbh/errorx"
	"gbh/proto/admin"
)

type Context interface {
	PositionList(ctx context.Context, request PositionListRequest) (*adminclient.PositionListResponse, error)
	GetSession(ctx context.Context, session, ip string) (*adminclient.GetSessionResponse, error)
	GetGMUserIdleList(ctx context.Context, req GetGMUserIdleListRequest) (*adminclient.GetGMUserListResponse, error)
	GetGMUserList(ctx context.Context, req GetGMUserListRequest) (*adminclient.GetGMUserListResponse, error)
	GetUsersDepartment(ctx context.Context, req GetUsersDepartmentRequest) (*adminclient.UsersDepartmentResponse, error)
	DepartmentList(ctx context.Context, request DepartmentListRequest) (*adminclient.DepartmentListResponse, error)
	GetGMHallPrivilege(ctx context.Context, adminId uint32) (*adminclient.GMHallPrivilegeResponse, error)
}

type PositionListRequest struct {
	Page     *uint32
	Limit    *uint32
	Sort     *string
	Order    *string
	Position *string
}

type GetGMUserIdleListRequest struct {
	Period   uint32
	Username string
	Alias    string
	Enable   *bool
	Sort     string
	Order    string
}

type GetGMUserListRequest struct {
	Username string
	Alias    string
	Enable   *bool
	Sort     string
	Order    string
}

type GetUsersDepartmentRequest struct {
	Users        []*adminclient.GMUser
	DepartmentId uint32
	PositionId   uint32
	PageLimit    uint32
	Page         uint32
}

type DepartmentListRequest struct {
	Department string
	Sort       string
	Order      string
	Page       uint32
	PageLimit  uint32
}

type adminContext struct {
	AdminRPC adminclient.Admin
}

func New(adminRPC adminclient.Admin) Context {
	return &adminContext{
		AdminRPC: adminRPC,
	}
}

func (c *adminContext) PositionList(ctx context.Context, request PositionListRequest) (*adminclient.PositionListResponse, error) {
	positionRequest := &admin.PositionListRequest{}
	if request.Page != nil {
		positionRequest.Page = *request.Page
	}
	if request.Limit != nil {
		positionRequest.PageLimit = *request.Limit
	}
	if request.Sort != nil {
		positionRequest.Sort = &admin.StringValue{
			Value: *request.Sort,
		}
	}
	if request.Order != nil {
		positionRequest.Order = &admin.StringValue{
			Value: *request.Order,
		}
	}
	if request.Position != nil {
		positionRequest.Position = &admin.StringValue{
			Value: *request.Position,
		}
	}

	resp, err := c.AdminRPC.PositionList(ctx, positionRequest)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *adminContext) GetSession(ctx context.Context, session, ip string) (*adminclient.GetSessionResponse, error) {
	resp, err := c.AdminRPC.GetSession(ctx, &adminclient.GetSessionRequest{
		Session: session,
		Ip:      ip,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *adminContext) GetGMUserIdleList(ctx context.Context, req GetGMUserIdleListRequest) (*adminclient.GetGMUserListResponse, error) {
	request := &adminclient.GetGMUserIdleListRequest{
		Period:   req.Period,
		Username: req.Username,
		Alias:    req.Alias,
		Sort:     req.Sort,
		Order:    req.Order,
	}

	if req.Enable != nil {
		request.Enable = &adminclient.BoolValue{
			Value: *req.Enable,
		}
	}

	resp, err := c.AdminRPC.GetGMUserIdleList(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *adminContext) GetGMUserList(ctx context.Context, req GetGMUserListRequest) (*adminclient.GetGMUserListResponse, error) {
	request := &adminclient.GetGMUserListRequest{
		Username: req.Username,
		Alias:    req.Alias,
		Sort:     req.Sort,
		Order:    req.Order,
	}

	if req.Enable != nil {
		request.Enable = &adminclient.BoolValue{
			Value: *req.Enable,
		}
	}

	resp, err := c.AdminRPC.GetGMUserList(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *adminContext) GetUsersDepartment(ctx context.Context, req GetUsersDepartmentRequest) (*adminclient.UsersDepartmentResponse, error) {
	request := &adminclient.UsersDepartmentRequest{
		Users: req.Users,
	}

	if req.DepartmentId > 0 {
		request.DepartmentId = &adminclient.Uint32Value{
			Value: req.DepartmentId,
		}
	}

	if req.PositionId > 0 {
		request.PositionId = &adminclient.Uint32Value{
			Value: req.PositionId,
		}
	}

	if req.Page > 0 {
		request.Page = &adminclient.Uint32Value{
			Value: req.Page,
		}
	}

	if req.PageLimit > 0 {
		request.PageLimit = &adminclient.Uint32Value{
			Value: req.PageLimit,
		}
	}

	resp, err := c.AdminRPC.UsersDepartment(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *adminContext) DepartmentList(ctx context.Context, req DepartmentListRequest) (*adminclient.DepartmentListResponse, error) {
	request := adminclient.DepartmentListRequest{
		Sort:       req.Sort,
		Order:      req.Order,
		Department: req.Department,
		Page:       req.Page,
		PageLimit:  req.PageLimit,
	}

	resp, err := c.AdminRPC.DepartmentList(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *adminContext) GetGMHallPrivilege(ctx context.Context, adminId uint32) (*adminclient.GMHallPrivilegeResponse, error) {
	resp, err := c.AdminRPC.GetGMHallPrivilege(ctx, &adminclient.GMHallPrivilegeRequest{
		AdminId: adminId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
