package operationrecord

import (
	"context"
	"gbh/operationrecord/operationrecordclient"

	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

var (
	ctx context.Context
)

type mockOperationRecordRPC struct{ mock.Mock }

func (s *mockOperationRecordRPC) CreateAGRecord(ctx context.Context, in *operationrecordclient.CreateAGRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockOperationRecordRPC) CreateTransferRecord(ctx context.Context, in *operationrecordclient.CreateTransferRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockOperationRecordRPC) CreateUserRecord(ctx context.Context, in *operationrecordclient.CreateUserRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockOperationRecordRPC) AddLogChange(ctx context.Context, in *operationrecordclient.AddLogChangeRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockOperationRecordRPC) AddDownloadRecord(ctx context.Context, in *operationrecordclient.AddDownloadRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockOperationRecordRPC) GetWhitelistRecord(ctx context.Context, in *operationrecordclient.GetWhitelistRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.GetWhitelistRecordResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*operationrecordclient.GetWhitelistRecordResponse), nil
}

func (s *mockOperationRecordRPC) GetMonthlyReconciliationRecord(ctx context.Context, in *operationrecordclient.GetMonthlyReconciliationRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.GetMonthlyReconciliationRecordResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*operationrecordclient.GetMonthlyReconciliationRecordResponse), nil
}

func (s *mockOperationRecordRPC) GetDownloadRecordList(ctx context.Context, in *operationrecordclient.GetDownloadRecordListRequest, _ ...grpc.CallOption) (*operationrecordclient.GetDownloadRecordListResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*operationrecordclient.GetDownloadRecordListResponse), nil
}

func (s *mockOperationRecordRPC) CreateGameTypeSwitchRecord(ctx context.Context, in *operationrecordclient.CreateGameTypeSwitchRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockOperationRecordRPC) GameTypeSwitchRecord(ctx context.Context, in *operationrecordclient.GameTypeSwitchRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.GameTypeSwitchRecordResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*operationrecordclient.GameTypeSwitchRecordResponse), nil
}

func (s *mockOperationRecordRPC) GetSystemMonitor(ctx context.Context, in *operationrecordclient.SystemMonitorRequest, _ ...grpc.CallOption) (*operationrecordclient.SystemMonitorResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*operationrecordclient.SystemMonitorResponse), nil
}

func (s *mockOperationRecordRPC) CreateSystemMonitorRecord(ctx context.Context, in *operationrecordclient.CreateSystemMonitorRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)

	return nil, args.Error(1)
}

func (s *mockOperationRecordRPC) GetSystemMaintenanceRecord(ctx context.Context, in *operationrecordclient.GetSystemMaintenanceRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.GetSystemMaintenanceRecordResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*operationrecordclient.GetSystemMaintenanceRecordResponse), nil
}

func (s *mockOperationRecordRPC) CreateSystemMaintenanceRecord(ctx context.Context, in *operationrecordclient.CreateSystemMaintenanceRecordRequest, _ ...grpc.CallOption) (*operationrecordclient.EmptyResponse, error) {
	args := s.Called(ctx, in)
	resp := args.Get(0)
	err := args.Error(1)

	if err != nil {
		return nil, err
	}

	return resp.(*operationrecordclient.EmptyResponse), nil
}

func newMockOperationRecordRPC() *mockOperationRecordRPC {
	return &mockOperationRecordRPC{}
}

func init() {
	ctx = context.Background()
}
