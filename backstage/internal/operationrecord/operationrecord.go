package operationrecord

import (
	"context"
	"gbh/errorx"
	"gbh/operationrecord/operationrecordclient"
	"gbh/proto/operationrecord"
)

type Context interface {
	CreateAGRecord(ctx context.Context, req CreateAGRecordRequest) error
	AddLogChange(ctx context.Context, in *operationrecordclient.AddLogChangeRequest) error
}

type CreateAGRecordRequest struct {
	HallId       uint32
	OperatorId   uint32
	LevelId      uint32
	ActionId     uint32
	SubActionId  uint32
	TargetId     uint32
	TargetRoleId uint32
	BossKey      string
	UserKey      string
	XMLDataMsg   []*KeyValue
	XMLCancelTag bool
	URI          string
	IP           string
}

type KeyValue struct {
	Key   string
	Value string
}

type operationRecordContext struct {
	OperationRecordRPC operationrecordclient.OperationRecord
}

func New(operationRecordRPC operationrecordclient.OperationRecord) Context {
	return &operationRecordContext{
		OperationRecordRPC: operationRecordRPC,
	}
}

func (c *operationRecordContext) CreateAGRecord(ctx context.Context, req CreateAGRecordRequest) error {
	xmlDataMsg := make([]*operationrecord.KeyValue, 0, len(req.XMLDataMsg))
	for _, v := range req.XMLDataMsg {
		xmlDataMsg = append(xmlDataMsg, &operationrecord.KeyValue{
			Key:   v.Key,
			Value: v.Value,
		})
	}

	request := operationrecordclient.CreateAGRecordRequest{
		HallId:       req.HallId,
		OperatorId:   req.OperatorId,
		LevelId:      req.LevelId,
		ActionId:     req.ActionId,
		SubActionId:  req.SubActionId,
		TargetId:     req.TargetId,
		TargetRoleId: req.TargetRoleId,
		BossKey:      req.BossKey,
		UserKey:      req.UserKey,
		XmlDataMsg:   xmlDataMsg,
		XmlCancelTag: req.XMLCancelTag,
		Uri:          req.URI,
		Ip:           req.IP,
	}

	_, err := c.OperationRecordRPC.CreateAGRecord(ctx, &request)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *operationRecordContext) AddLogChange(ctx context.Context, in *operationrecordclient.AddLogChangeRequest) error {
	_, err := c.OperationRecordRPC.AddLogChange(ctx, in)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}
