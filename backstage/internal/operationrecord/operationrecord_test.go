package operationrecord

import (
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/operationrecord/operationrecordclient"
	"gbh/utils/strutil"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	operationRecordRPC := newMockOperationRecordRPC()
	operationRecordCtx := New(operationRecordRPC)

	expectedResponse := &operationRecordContext{
		OperationRecordRPC: operationRecordRPC,
	}

	assert.Equal(t, expectedResponse, operationRecordCtx)
}

var content = "帳號：gtptest" + "<br />ID：" + strutil.Uint32ToString(seeder.UserId) + "<br />名稱：gtptest"

func Test_OperationRecordContext_CreateAGRecord(t *testing.T) {
	operationRecordRPC := newMockOperationRecordRPC()

	mockRequest := &operationrecordclient.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     seeder.AGRecordActionId,
		TargetId:     seeder.HallId,
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      seeder.AGRecordBossKey,
		UserKey:      "",
		XmlDataMsg:   seeder.XMLDataMsg,
		XmlCancelTag: false,
		Uri:          seeder.RequestURI,
		Ip:           seeder.ClientIP,
	}

	operationRecordRPC.On("CreateAGRecord", ctx, mockRequest).Return(nil, nil)
	operationRecordCtx := New(operationRecordRPC)

	xmlDataMsg := []*KeyValue{
		{
			Key:   "domain",
			Value: strutil.Uint32ToString(seeder.HallId),
		},
	}

	err := operationRecordCtx.CreateAGRecord(ctx, CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     seeder.AGRecordActionId,
		TargetId:     seeder.HallId,
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      seeder.AGRecordBossKey,
		UserKey:      "",
		XMLDataMsg:   xmlDataMsg,
		XMLCancelTag: false,
		URI:          seeder.RequestURI,
		IP:           seeder.ClientIP,
	})

	assert.NoError(t, err)
}

func Test_OperationRecordContext_CreateAGRecord_GRPCError(t *testing.T) {
	operationRecordRPC := newMockOperationRecordRPC()

	mockRequest := &operationrecordclient.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     seeder.AGRecordActionId,
		TargetId:     seeder.HallId,
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      seeder.AGRecordBossKey,
		UserKey:      "",
		XmlDataMsg:   seeder.XMLDataMsg,
		XmlCancelTag: false,
		Uri:          seeder.RequestURI,
		Ip:           seeder.ClientIP,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	operationRecordRPC.On("CreateAGRecord", ctx, mockRequest).Return(nil, mockError)
	operationRecordCtx := New(operationRecordRPC)

	xmlDataMsg := []*KeyValue{
		{
			Key:   "domain",
			Value: strutil.Uint32ToString(seeder.HallId),
		},
	}

	err := operationRecordCtx.CreateAGRecord(ctx, CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     seeder.AGRecordActionId,
		TargetId:     seeder.HallId,
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      seeder.AGRecordBossKey,
		UserKey:      "",
		XMLDataMsg:   xmlDataMsg,
		XMLCancelTag: false,
		URI:          seeder.RequestURI,
		IP:           seeder.ClientIP,
	})

	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_OperationRecordContext_AddLogChange(t *testing.T) {
	operationRecordRPC := newMockOperationRecordRPC()

	mockRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split("", "?")[0],
		Act:       "del",
		ActionSql: "",
		Content:   content,
		Bywho:     "admin",
		Byfile:    strings.Split("", "?")[0],
		Ip:        seeder.ClientIP,
	}

	operationRecordRPC.On("AddLogChange", ctx, mockRequest).Return(nil, nil)
	operationRecordCtx := New(operationRecordRPC)

	err := operationRecordCtx.AddLogChange(ctx, mockRequest)

	assert.NoError(t, err)
}

func Test_OperationRecordContext_AddLogChange_GRPCError(t *testing.T) {
	operationRecordRPC := newMockOperationRecordRPC()

	mockRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split("", "?")[0],
		Act:       "del",
		ActionSql: "",
		Content:   content,
		Bywho:     "admin",
		Byfile:    strings.Split("", "?")[0],
		Ip:        seeder.ClientIP,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	operationRecordRPC.On("AddLogChange", ctx, mockRequest).Return(nil, mockError)
	operationRecordCtx := New(operationRecordRPC)

	err := operationRecordCtx.AddLogChange(ctx, mockRequest)

	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}
