package repository

import (
	"context"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/types"

	"gbh/backstage/internal/svc"
)

func CheckMenuPermission(ctx context.Context, serviceCtx *svc.ServiceContext, operator types.UserInfo, permissionId uint32, permissionAction string) (bool, error) {
	result, err := serviceCtx.PermissionCtx.CheckUserPermission(ctx, operator, permissionId, permissionAction)
	if err != nil {
		return false, err
	}

	if !result {
		return result, nil
	}

	return CheckExtraConditions(ctx, serviceCtx, operator, permissionId), nil
}

func CheckExtraConditions(ctx context.Context, serviceCtx *svc.ServiceContext, operator types.UserInfo, permissionId uint32) bool {
	permConfig := serviceCtx.PermissionCtx.GetPermissionConfigById(ctx, permissionId)
	if permConfig == nil {
		return false
	}

	var enableState bool

	if permConfig.Extra.Conditions == "" && permConfig.ParentId == 0 {
		return true
	}

	if permConfig.Extra.Conditions == "" && permConfig.ParentId != 0 {
		enableState = true
	}

	if permConfig.Extra.Conditions != "" {
		var err error
		enableState, err = HandleConditionSwitch(ctx, serviceCtx, permConfig.Extra.Conditions, operator)

		if err != nil {
			return false
		}
	}

	if !enableState || permConfig.ParentId == 0 {
		return enableState
	}

	return CheckExtraConditions(ctx, serviceCtx, operator, permConfig.ParentId)
}

func HandleConditionSwitch(ctx context.Context, serviceCtx *svc.ServiceContext, condition string, operator types.UserInfo) (bool, error) {
	var err error
	var enableState bool

	switch condition {
	case constants.Lobby3:
		enableState, err = serviceCtx.GameCtx.GetUserLobby(ctx, operator, constants.BBLive)
	case constants.Lobby12:
		enableState, err = serviceCtx.GameCtx.GetUserLobby(ctx, operator, constants.BBLottery)
	case constants.BBTip:
		enableState, err = getUserLobbyBBTip(ctx, serviceCtx, operator.Domain)
	}

	if err != nil {
		return false, err
	}

	return enableState, nil
}

func getUserLobbyBBTip(ctx context.Context, serviceCtx *svc.ServiceContext, hallId uint32) (bool, error) {
	gameDetail, gameDetailErr := serviceCtx.GameCtx.GetGameDetail(ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{hallId},
	})
	if gameDetailErr != nil {
		return false, gameDetailErr
	}

	gameSwitch := false
	isExist := false
	for _, v := range gameDetail {
		if v.GetGameKind() == constants.BBLive {
			gameSwitch = v.GetSwitch()
			isExist = true
			break
		}
	}
	if !isExist {
		switchData, switchDataErr := serviceCtx.LiveGameCtx.GetHallTipSwitch(ctx, hallId)
		if switchDataErr != nil {
			return false, switchDataErr
		}

		gameSwitch = switchData[0].GetSwitch()
	}

	return gameSwitch, nil
}
