package repository

import (
	"context"
	"gbh/backstage/internal/svc"
	"gbh/errorx"
	"gbh/proto/user"
	"slices"
)

type GetDomainResp struct {
	HallID    uint32
	UserName  string
	Name      string
	Alias     string
	LoginCode string
	Enable    bool
	Role      uint32
}

// 檢查該 admin 是否有權限管理指定的 hallId
func CheckAdminPermissionForHallId(ctx context.Context, serviceCtx *svc.ServiceContext, adminId uint32, hallId uint32) error {
	hallListResp, err := serviceCtx.HallCtx.GetHallById(ctx, hallId)

	if err != nil {
		return err
	}

	if !hallListResp.GetEnable() {
		return errorx.BackstageOperatorNoDomainPerm
	}

	resp, err := serviceCtx.AdminCtx.GetGMHallPrivilege(ctx, adminId)

	if err != nil {
		return err
	}

	// 控端GM只能管理自己有權限的廳
	if len(resp.GetHallId()) > 0 {
		if !slices.Contains(resp.GetHallId(), hallId) {
			return errorx.BackstageOperatorNoDomainPerm
		}
	}

	return nil
}

func GetAdminPermissionHallList(ctx context.Context, serviceCtx *svc.ServiceContext, adminId uint32, enable bool) ([]*GetDomainResp, error) {
	enableHallListResp, err := serviceCtx.HallCtx.GetHallList(ctx, enable)

	if err != nil {
		return nil, err
	}

	if len(enableHallListResp.GetData()) == 0 {
		return nil, nil
	}

	enableHallList := make([]uint32, 0, len(enableHallListResp.GetData()))
	for _, domain := range enableHallListResp.GetData() {
		enableHallList = append(enableHallList, domain.GetHallId())
	}

	userResp, err := serviceCtx.UserCtx.GetUsers(ctx, enableHallList, nil)

	if err != nil {
		return nil, err
	}

	userList := make(map[uint32]*user.GetResponse, len(userResp.GetUsers()))
	for _, user := range userResp.GetUsers() {
		userList[user.GetId()] = user
	}

	gmHallSet := make(map[uint32]bool)
	if adminId > 0 {
		resp, err := serviceCtx.AdminCtx.GetGMHallPrivilege(ctx, adminId)

		if err != nil {
			return nil, err
		}

		for _, hallID := range resp.GetHallId() {
			gmHallSet[hallID] = true
		}
	}

	hallList := []*GetDomainResp{}
	for _, v := range enableHallListResp.GetData() {
		hallID := v.GetHallId()

		// 控端GM只能管理自己有權限的廳
		if adminId > 0 && !gmHallSet[v.GetHallId()] {
			continue
		}

		userInfo, exists := userList[v.GetHallId()]

		if !exists {
			return nil, errorx.SystemError
		}

		hallList = append(hallList, &GetDomainResp{
			HallID:    hallID,
			Name:      v.GetName(),
			Enable:    v.GetEnable(),
			LoginCode: v.GetLoginCode(),
			UserName:  userInfo.GetUsername(),
			Alias:     userInfo.GetAlias(),
			Role:      userInfo.GetRole(),
		})
	}

	return hallList, nil
}
