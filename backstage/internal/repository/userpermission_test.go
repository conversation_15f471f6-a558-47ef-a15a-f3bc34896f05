package repository

import (
	"testing"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/livegame/livegameclient"

	"github.com/stretchr/testify/assert"
)

func Test_CheckMenuPermission_NotEnable(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, uint32(1605), "enable").Return(false, nil)

	svcCtx.PermissionCtx = permissionCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, uint32(1605), "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_CheckUserPermissionError(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, uint32(1605), "enable").Return(false, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, uint32(1605), "enable")

	assert.False(t, resp)
	assert.ErrorIs(t, errorx.ConnectionFailed, err)
}

func Test_CheckMenuPermission_NotFindPermission(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, uint32(1605), "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, uint32(1605)).Return(nil)

	svcCtx.PermissionCtx = permissionCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, uint32(1605), "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_EmptyCondition(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, uint32(1605), "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, uint32(1605)).Return(&types.UserPermissionConfig{
		ParentId: 0,
	})

	svcCtx.PermissionCtx = permissionCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, uint32(1605), "enable")

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_GetUserLobbyError(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "Lobby3",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckPermission)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_ConditionIsBBLive(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "Lobby3",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckPermission)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID400, "enable")

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_ConditionIsBBLottery(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "Lobby12",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckPermission)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLottery)).Return(false, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_ConditionIsBBTip_GetGameDetailError(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "BBTip",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckPermission)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_ConditionIsBBTip_GetHallTipSwitchError(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "BBTip",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckPermission)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return([]*gameclient.GameDetail{}, nil)

	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID400, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_ConditionIsBBTip_GetHallTipSwitch(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "BBTip",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckPermission)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return([]*gameclient.GameDetail{}, nil)

	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, seeder.HallId).Return(seeder.HallTipSwitch.GetSwitchList(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID400, "enable")

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_ConditionIsBBTip_GetGameDetail(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "BBTip",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckPermission)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID400, "enable")

	assert.True(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_HasUpperMenu_UpperMenuHasCondition(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	upperConfigInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckUpperPermission := types.UserPermissionConfig{
		ParentId: upperConfigInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "Lobby3",
		},
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[1]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, "enable").Return(true, nil).Once()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1471).Return(&userCheckPermission).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckUpperPermission).Once()

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(false, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID1471, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}

func Test_CheckMenuPermission_HasUpperMenu_BothHasCondition(t *testing.T) {
	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: []uint32{},
		Role:         constants.Role7,
		IsSub:        false,
	}

	upperConfigInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[0]
	userCheckUpperPermission := types.UserPermissionConfig{
		ParentId: upperConfigInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "Lobby3",
		},
	}

	configInfo := seeder.UserPermissionConfigList.GetPermissionConfig()[1]
	userCheckPermission := types.UserPermissionConfig{
		ParentId: configInfo.GetParentId(),
		Extra: types.Extra{
			Conditions: "BBTip",
		},
	}

	permissionCtx := mock.NewMockPermissionCtx()

	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID1471, "enable").Return(true, nil).Once()
	permissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID400, "enable").Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID1471).Return(&userCheckPermission).Once()
	permissionCtx.On("GetPermissionConfigById", ctx, seeder.UserPermID400).Return(&userCheckUpperPermission).Once()

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetUserLobby", ctx, operator, uint32(constants.BBLive)).Return(true, nil)
	gameCtx.On("GetGameDetail", ctx, game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}).Return([]*gameclient.GameDetail{}, nil)

	liveGameCtx := mock.NewMockLiveGameCtx()
	liveGameCtx.On("GetHallTipSwitch", ctx, seeder.HallId).Return([]*livegameclient.HallSwitchData{
		{
			HallId: seeder.HallId,
			Switch: false,
		},
	}, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.LiveGameCtx = liveGameCtx

	resp, err := CheckMenuPermission(ctx, svcCtx, operator, seeder.UserPermID1471, "enable")

	assert.False(t, resp)
	assert.NoError(t, err)
}
