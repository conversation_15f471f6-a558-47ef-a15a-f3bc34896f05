package repository

import (
	"gbh/admin/adminclient"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/hall/hallclient"
	"gbh/proto/admin"
	"gbh/proto/hall"
	"gbh/user/userclient"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminPermission_CheckAdminPermissionForHallId(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()

	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx

	err := CheckAdminPermissionForHallId(ctx, svcCtx, seeder.AdminID, seeder.HallId)

	assert.NoError(t, err)
}

func TestAdminPermission_CheckAdminPermissionForHallId_GetHallByIdErr(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()

	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx

	err := CheckAdminPermissionForHallId(ctx, svcCtx, seeder.AdminID, seeder.HallId)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestAdminPermission_CheckAdminPermissionForHallId_HallDisable(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()

	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&hallclient.GetHallByIdResponse{
		HallId: seeder.HallId,
		Enable: false,
	}, nil)

	svcCtx.HallCtx = hallCtx

	err := CheckAdminPermissionForHallId(ctx, svcCtx, seeder.AdminID, seeder.HallId)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
}

func TestAdminPermission_CheckAdminPermissionForHallId_GetGMHallPrivilegeErr(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()

	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx

	err := CheckAdminPermissionForHallId(ctx, svcCtx, seeder.AdminID, seeder.HallId)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func TestAdminPermission_CheckAdminPermissionForHallId_GMNoDomainPerm(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()

	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{
		HallId: []uint32{1},
	}, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx

	err := CheckAdminPermissionForHallId(ctx, svcCtx, seeder.AdminID, seeder.HallId)

	assert.Equal(t, errorx.BackstageOperatorNoDomainPerm, err)
}

func TestAdminPermission_GetAdminPermissionHallList(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	resp, err := GetAdminPermissionHallList(ctx, svcCtx, 0, true)

	expectedResp := []*GetDomainResp{
		{
			HallID:    seeder.HallId,
			UserName:  mockResponse.GetParents()[0].GetUsername(),
			Name:      seeder.Users.GetUsers()[0].GetName(),
			Alias:     mockResponse.GetParents()[0].GetAlias(),
			LoginCode: seeder.Users.GetUsers()[0].GetLoginCode(),
			Enable:    seeder.Users.GetUsers()[0].GetEnable(),
			Role:      seeder.Users.GetUsers()[0].GetRole(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
}

func TestAdminPermission_GetAdminPermissionHallList_GetHallListErr(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx

	resp, err := GetAdminPermissionHallList(ctx, svcCtx, 0, true)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestAdminPermission_GetAdminPermissionHallList_GetUsersErr(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	resp, err := GetAdminPermissionHallList(ctx, svcCtx, seeder.AdminID, true)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestAdminPermission_GetAdminPermissionHallList_GetGMHallPrivilegeErr(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.AdminCtx = adminCtx

	resp, err := GetAdminPermissionHallList(ctx, svcCtx, seeder.AdminID, true)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestAdminPermission_GetAdminPermissionHallList_GetHallListEmpty(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&hall.HallListResponse{}, nil)

	svcCtx.HallCtx = hallCtx

	resp, err := GetAdminPermissionHallList(ctx, svcCtx, seeder.AdminID, true)

	assert.NoError(t, err)
	assert.Nil(t, resp)
}

func TestAdminPermission_GetAdminPermissionHallList_WithAdminID(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()
	hallList := &hall.HallListResponse{
		Data: []*hall.GetHallByIdResponse{
			{
				HallId:    seeder.HallId,
				Enable:    true,
				Name:      "BGP API 測試廳",
				LoginCode: "bgp",
			},
			{
				HallId:    2,
				Enable:    true,
				Name:      "testname",
				LoginCode: "test",
			},
		},
	}
	hallCtx.On("GetHallList", ctx, true).Return(hallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId, 2}, extraInfo).Return(mockResponse, nil)

	adminCtx := mock.NewMockAdminCtx()

	gmHallSet := &admin.GMHallPrivilegeResponse{
		HallId: []uint32{seeder.HallId, 1},
	}
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(gmHallSet, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.AdminCtx = adminCtx

	resp, err := GetAdminPermissionHallList(ctx, svcCtx, seeder.AdminID, true)

	expectedResp := []*GetDomainResp{
		{
			HallID:    seeder.HallId,
			UserName:  mockResponse.GetParents()[0].GetUsername(),
			Name:      seeder.Users.GetUsers()[0].GetName(),
			Alias:     mockResponse.GetParents()[0].GetAlias(),
			LoginCode: seeder.Users.GetUsers()[0].GetLoginCode(),
			Enable:    seeder.Users.GetUsers()[0].GetEnable(),
			Role:      seeder.Users.GetUsers()[0].GetRole(),
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
}

func TestAdminPermission_GetAdminPermissionHallList_UserNotExist(t *testing.T) {
	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", ctx, true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		{
			Id: 1,
		},
	}
	userCtx.On("GetUsers", ctx, []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx

	resp, err := GetAdminPermissionHallList(ctx, svcCtx, 0, true)

	assert.Equal(t, errorx.SystemError, err)
	assert.Nil(t, resp)
}
