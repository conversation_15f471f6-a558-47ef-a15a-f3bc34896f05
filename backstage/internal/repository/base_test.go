package repository

import (
	"context"
	"gbh/backstage/internal/config"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/svc"
)

var (
	ctx    context.Context
	svcCtx *svc.ServiceContext
)

func init() {
	ctx = context.Background()
	conf := config.Config{}
	svcCtx = &svc.ServiceContext{
		Config:   conf,
		AdminCtx: mock.NewMockAdminCtx(),
		HallCtx:  mock.NewMockHallCtx(),
		UserCtx:  mock.NewMockUserCtx(),
	}
}
