package livegame

import (
	"testing"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/livegame/livegameclient"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()
	liveGameCtx := New(liveGameRPC)

	expectedResponse := &liveGameContext{
		LiveGameRPC: liveGameRPC,
	}

	assert.Equal(t, expectedResponse, liveGameCtx)
}

func Test_LiveGameContext_RoundInfo(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	roundNO := "51-23"
	wagersType := uint32(0)
	gameCode := uint32(93)
	page := uint32(1)
	pageLimit := uint32(1)
	sort := "asc"
	mockRequest := &livegameclient.RoundInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		GameId:    constants.GameID3001,
		RoundNo:   roundNO,
		WagersType: &livegameclient.Uint32Value{
			Value: wagersType,
		},
		GameCode:  gameCode,
		Page:      page,
		PageLimit: pageLimit,
		Sort:      sort,
	}
	liveGameRPC.On("RoundInfo", ctx, mockRequest).Return(&seeder.RoundInfo, nil)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.RoundInfo(ctx, RoundInfoRequest{
		StartDate:  seeder.StartDate,
		EndDate:    seeder.EndDate,
		GameID:     constants.GameID3001,
		RoundNo:    roundNO,
		WagersType: &wagersType,
		GameCode:   gameCode,
		Page:       page,
		PageLimit:  pageLimit,
		Sort:       sort,
	})

	expectedResponse := &seeder.RoundInfo

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_RoundInfo_GRPCError(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	roundNO := "51-23"
	wagersType := uint32(0)
	gameCode := uint32(93)
	page := uint32(1)
	pageLimit := uint32(1)
	sort := "asc"
	mockRequest := &livegameclient.RoundInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		GameId:    constants.GameID3001,
		RoundNo:   roundNO,
		WagersType: &livegameclient.Uint32Value{
			Value: wagersType,
		},
		GameCode:  gameCode,
		Page:      page,
		PageLimit: pageLimit,
		Sort:      sort,
	}
	liveGameRPC.On("RoundInfo", ctx, mockRequest).Return(nil, mockError)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.RoundInfo(ctx, RoundInfoRequest{
		StartDate:  seeder.StartDate,
		EndDate:    seeder.EndDate,
		GameID:     constants.GameID3001,
		RoundNo:    roundNO,
		WagersType: &wagersType,
		GameCode:   gameCode,
		Page:       page,
		PageLimit:  pageLimit,
		Sort:       sort,
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestCancelWagers_InvalidResponse(t *testing.T) {
	mockGrpcRequest := &livegameclient.CancelWagersReuest{
		RoundSerial: 1,
	}

	mockLiveGameRPC := newMockLiveGameRPC()

	mockError := status.Error(codes.Code(errorx.InvalidResponse.Code), errorx.InvalidResponse.Message)
	mockLiveGameRPC.On("CancelWagers", ctx, mockGrpcRequest).Return(nil, mockError)

	liveGameCtx := New(mockLiveGameRPC)

	err := liveGameCtx.CancelWagers(ctx, &types.CancelWagersRequest{

		RoundSerial: 1,
	})

	assert.ErrorIs(t, errorx.InvalidResponse, err)
}

func TestCancelWagers(t *testing.T) {
	mockGrpcRequest := &livegameclient.CancelWagersReuest{
		RoundSerial: 1,
	}

	mockLiveGameRPC := newMockLiveGameRPC()

	mockLiveGameRPC.On("CancelWagers", ctx, mockGrpcRequest).Return(&livegameclient.EmptyResponse{}, nil)

	liveGameCtx := New(mockLiveGameRPC)

	err := liveGameCtx.CancelWagers(ctx, &types.CancelWagersRequest{
		RoundSerial: 1,
	})

	assert.NoError(t, err)
}

func TestCancelWagers_WithWagersID(t *testing.T) {
	mockGrpcRequest := &livegameclient.CancelWagersReuest{
		RoundSerial: 111111,
		WagersId:    1,
	}

	mockLiveGameRPC := newMockLiveGameRPC()

	mockLiveGameRPC.On("CancelWagers", ctx, mockGrpcRequest).Return(&livegameclient.EmptyResponse{}, nil)

	liveGameCtx := New(mockLiveGameRPC)

	wagersId := uint64(1)
	err := liveGameCtx.CancelWagers(ctx, &types.CancelWagersRequest{
		RoundSerial: 111111,
		WagersID:    &wagersId,
	})

	assert.NoError(t, err)
}

func Test_LiveGameContext_GetDomainGameCodeSetting(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := &livegameclient.GetDomainGameCodeSettingRequest{}
	liveGameRPC.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	liveGameCtx := New(liveGameRPC)

	request := GameCodeSettingRequest{}
	resp, err := liveGameCtx.GetDomainGameCodeSetting(ctx, request)

	expectedResponse := &seeder.DomainGameCodeSetting

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_GetDomainGameCodeSetting_WithEnable(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	enable := true
	mockRequest := &livegameclient.GetDomainGameCodeSettingRequest{
		Enable: &livegameclient.BoolValue{
			Value: enable,
		},
	}
	liveGameRPC.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	liveGameCtx := New(liveGameRPC)

	request := GameCodeSettingRequest{
		Enable: &enable,
	}
	resp, err := liveGameCtx.GetDomainGameCodeSetting(ctx, request)

	expectedResponse := &seeder.DomainGameCodeSetting

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_GetDomainGameCodeSetting_WithGameId(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	gameId := uint32(3001)
	mockRequest := &livegameclient.GetDomainGameCodeSettingRequest{
		GameId: gameId,
	}
	liveGameRPC.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	liveGameCtx := New(liveGameRPC)

	request := GameCodeSettingRequest{
		GameId: &gameId,
	}
	resp, err := liveGameCtx.GetDomainGameCodeSetting(ctx, request)

	expectedResponse := &seeder.DomainGameCodeSetting

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_GetDomainGameCodeSetting_WithState(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	state := uint32(0)
	mockRequest := &livegameclient.GetDomainGameCodeSettingRequest{
		State: &livegameclient.Uint32Value{
			Value: state,
		},
	}
	liveGameRPC.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	liveGameCtx := New(liveGameRPC)

	request := GameCodeSettingRequest{
		State: &state,
	}
	resp, err := liveGameCtx.GetDomainGameCodeSetting(ctx, request)

	expectedResponse := &seeder.DomainGameCodeSetting

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_GetDomainGameCodeSetting_WithHallID(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	hallId := uint32(3820474)
	mockRequest := &livegameclient.GetDomainGameCodeSettingRequest{
		HallId: hallId,
	}
	liveGameRPC.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	liveGameCtx := New(liveGameRPC)

	request := GameCodeSettingRequest{
		HallID: &hallId,
	}
	resp, err := liveGameCtx.GetDomainGameCodeSetting(ctx, request)

	expectedResponse := &seeder.DomainGameCodeSetting

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_GetDomainGameCodeSetting_GRPCError(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &livegameclient.GetDomainGameCodeSettingRequest{}
	liveGameRPC.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(nil, mockError)

	liveGameCtx := New(liveGameRPC)

	request := GameCodeSettingRequest{}
	resp, err := liveGameCtx.GetDomainGameCodeSetting(ctx, request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_LiveGameContext_RoundInfoByRoundSerial(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	roundSerial := uint32(123)
	stateId := uint32(0)
	sort := "desc"
	mockRequest := &livegameclient.RoundInfoByRoundserialRequest{
		RoundSerial: roundSerial,
		StateId:     stateId,
		Sort:        sort,
	}
	liveGameRPC.On("RoundInfoByRoundserial", ctx, mockRequest).Return(&seeder.RoundInfoByRoundSerial, nil)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.RoundInfoByRoundSerial(ctx, roundSerial, stateId, sort)

	expectedResponse := seeder.RoundInfoByRoundSerial.GetRoundInfo()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_RoundInfoByRoundSerial_GRPCError(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	roundSerial := uint32(123)
	stateId := uint32(0)
	sort := "desc"
	mockRequest := &livegameclient.RoundInfoByRoundserialRequest{
		RoundSerial: roundSerial,
		StateId:     stateId,
		Sort:        sort,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	liveGameRPC.On("RoundInfoByRoundserial", ctx, mockRequest).Return(nil, mockError)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.RoundInfoByRoundSerial(ctx, roundSerial, stateId, sort)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_LiveGameContext_RoundXMLInfo(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := &livegameclient.RoundXMLInfoRequest{
		RoundSerials: []uint32{seeder.RoundInfoRoundSerial},
		StartDate:    seeder.StartDate,
		EndDate:      seeder.EndDate,
	}
	liveGameRPC.On("RoundXMLInfo", ctx, mockRequest).Return(&seeder.RoundXMLInfo, nil)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.RoundXMLInfo(ctx, []uint32{seeder.RoundInfoRoundSerial}, seeder.StartDate, seeder.EndDate)

	expectedResponse := seeder.RoundXMLInfo.GetRoundXmlInfo()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_RoundXMLInfo_GRPCError(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := &livegameclient.RoundXMLInfoRequest{
		RoundSerials: []uint32{seeder.RoundInfoRoundSerial},
		StartDate:    seeder.StartDate,
		EndDate:      seeder.EndDate,
	}
	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	liveGameRPC.On("RoundXMLInfo", ctx, mockRequest).Return(nil, mockError)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.RoundXMLInfo(ctx, []uint32{seeder.RoundInfoRoundSerial}, seeder.StartDate, seeder.EndDate)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_LiveGameContext_GetHallTipSwitch(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := &livegameclient.GetHallTipSwitchReuest{
		HallId: seeder.HallId,
	}

	liveGameRPC.On("GetHallTipSwitch", ctx, mockRequest).Return(&seeder.HallTipSwitch, nil)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.GetHallTipSwitch(ctx, seeder.HallId)

	expectedResponse := seeder.HallTipSwitch.GetSwitchList()

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LiveGameContext_GetHallTipSwitch_GRPCError(t *testing.T) {
	liveGameRPC := newMockLiveGameRPC()

	mockRequest := &livegameclient.GetHallTipSwitchReuest{
		HallId: seeder.HallId,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	liveGameRPC.On("GetHallTipSwitch", ctx, mockRequest).Return(nil, mockError)

	liveGameCtx := New(liveGameRPC)

	resp, err := liveGameCtx.GetHallTipSwitch(ctx, seeder.HallId)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
