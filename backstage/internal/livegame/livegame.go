package livegame

import (
	"context"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/livegame/livegameclient"
)

type Context interface {
	RoundInfo(ctx context.Context, req RoundInfoRequest) (*livegameclient.RoundInfoResponse, error)
	CancelWagers(ctx context.Context, in *types.CancelWagersRequest) error
	GetDomainGameCodeSetting(ctx context.Context, request GameCodeSettingRequest) (*livegameclient.GetDomainGameCodeSettingResponse, error)
	RoundInfoByRoundSerial(ctx context.Context, roundSerial uint32, stateId uint32, sort string) ([]*livegameclient.RoundInfo, error)
	RoundXMLInfo(ctx context.Context, roundSerials []uint32, startDate string, endDate string) ([]*livegameclient.RoundXMLInfo, error)
	GetHallTipSwitch(ctx context.Context, hallId uint32) ([]*livegameclient.HallSwitchData, error)
}

type RoundInfoRequest struct {
	StartDate  string
	EndDate    string
	GameID     uint32
	RoundNo    string
	WagersType *uint32
	GameCode   uint32
	Page       uint32
	PageLimit  uint32
	Sort       string
}

type GameCodeSettingRequest struct {
	Enable *bool
	GameId *uint32
	State  *uint32
	HallID *uint32
}

type liveGameContext struct {
	LiveGameRPC livegameclient.LiveGame
}

func New(liveGameRPC livegameclient.LiveGame) Context {
	return &liveGameContext{
		LiveGameRPC: liveGameRPC,
	}
}

func (c *liveGameContext) RoundInfo(ctx context.Context, req RoundInfoRequest) (*livegameclient.RoundInfoResponse, error) {
	request := livegameclient.RoundInfoRequest{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		GameId:    req.GameID,
	}

	if req.RoundNo != "" {
		request.RoundNo = req.RoundNo
	}

	if req.WagersType != nil {
		request.WagersType = &livegameclient.Uint32Value{Value: *req.WagersType}
	}

	if req.GameCode != 0 {
		request.GameCode = req.GameCode
	}

	if req.Page != 0 {
		request.Page = req.Page
	}

	if req.PageLimit != 0 {
		request.PageLimit = req.PageLimit
	}

	if req.Sort != "" {
		request.Sort = req.Sort
	}

	resp, err := c.LiveGameRPC.RoundInfo(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) CancelWagers(ctx context.Context, in *types.CancelWagersRequest) error {
	grpcRequest := &livegameclient.CancelWagersReuest{
		RoundSerial: in.RoundSerial,
	}

	if in.WagersID != nil {
		grpcRequest.WagersId = *in.WagersID
	}

	_, err := c.LiveGameRPC.CancelWagers(ctx, grpcRequest)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *liveGameContext) GetDomainGameCodeSetting(ctx context.Context, request GameCodeSettingRequest) (*livegameclient.GetDomainGameCodeSettingResponse, error) {
	liveGameRequest := &livegameclient.GetDomainGameCodeSettingRequest{}
	if request.Enable != nil {
		liveGameRequest.Enable = &livegameclient.BoolValue{
			Value: *request.Enable,
		}
	}
	if request.GameId != nil {
		liveGameRequest.GameId = *request.GameId
	}
	if request.State != nil {
		liveGameRequest.State = &livegameclient.Uint32Value{
			Value: *request.State,
		}
	}
	if request.HallID != nil {
		liveGameRequest.HallId = *request.HallID
	}

	resp, err := c.LiveGameRPC.GetDomainGameCodeSetting(ctx, liveGameRequest)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *liveGameContext) RoundInfoByRoundSerial(ctx context.Context, roundSerial uint32, stateId uint32, sort string) ([]*livegameclient.RoundInfo, error) {
	request := livegameclient.RoundInfoByRoundserialRequest{
		RoundSerial: roundSerial,
		StateId:     stateId,
	}

	if sort != "" {
		request.Sort = sort
	}

	resp, err := c.LiveGameRPC.RoundInfoByRoundserial(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetRoundInfo(), nil
}

func (c *liveGameContext) RoundXMLInfo(ctx context.Context, roundSerials []uint32, startDate string, endDate string) ([]*livegameclient.RoundXMLInfo, error) {
	request := livegameclient.RoundXMLInfoRequest{
		RoundSerials: roundSerials,
		StartDate:    startDate,
		EndDate:      endDate,
	}

	resp, err := c.LiveGameRPC.RoundXMLInfo(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetRoundXmlInfo(), nil
}

func (c *liveGameContext) GetHallTipSwitch(ctx context.Context, hallId uint32) ([]*livegameclient.HallSwitchData, error) {
	resp, err := c.LiveGameRPC.GetHallTipSwitch(ctx, &livegameclient.GetHallTipSwitchReuest{
		HallId: hallId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp.GetSwitchList(), nil
}
