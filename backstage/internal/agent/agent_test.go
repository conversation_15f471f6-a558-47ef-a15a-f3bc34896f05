package agent

import (
	"gbh/agent/agentclient"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	agentRPC := newMockAgentRPC()
	agentCtx := New(agentRPC)

	expectedResponse := &agentContext{
		AgentRPC: agentRPC,
	}

	assert.Equal(t, expectedResponse, agentCtx)
}

func Test_AgentContext_DepartmentInfoList(t *testing.T) {
	userRPC := newMockAgentRPC()

	userRPC.On("DepartmentInfoList", ctx, &agentclient.DepartmentInfoListRequest{}).Return(&seeder.DepartmentInfoList, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.DepartmentInfoList(ctx, DepartmentInfoListRequest{})

	assert.NoError(t, err)
	assert.Equal(t, &seeder.DepartmentInfoList, resp)
}

func Test_AgentContext_DepartmentInfoList_GRPCError(t *testing.T) {
	userRPC := newMockAgentRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("DepartmentInfoList", ctx, &agentclient.DepartmentInfoListRequest{}).Return(nil, mockError)
	userCtx := New(userRPC)

	resp, err := userCtx.DepartmentInfoList(ctx, DepartmentInfoListRequest{})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AgentContext_HasDepartment_NoDuplicate(t *testing.T) {
	userRPC := newMockAgentRPC()

	userRPC.On("DepartmentInfoList", ctx, &agentclient.DepartmentInfoListRequest{
		HallId:  3820474,
		Role:    7,
		OwnerId: 3820474,
		Name:    "gtit",
	}).Return(&agentclient.DepartmentInfoListResponse{}, nil)
	userCtx := New(userRPC)

	err := userCtx.HasDepartment(ctx, HasDepartmentRequest{
		HallID:  3820474,
		Role:    7,
		OwnerID: 3820474,
		Name:    "gtit",
	})

	assert.NoError(t, err)
}

func Test_AgentContext_HasDepartment_Duplicate(t *testing.T) {
	userRPC := newMockAgentRPC()

	userRPC.On("DepartmentInfoList", ctx, &agentclient.DepartmentInfoListRequest{
		HallId:  3820474,
		Role:    7,
		OwnerId: 3820474,
		Name:    "gtit",
	}).Return(&seeder.DepartmentInfoList, nil)
	userCtx := New(userRPC)

	err := userCtx.HasDepartment(ctx, HasDepartmentRequest{
		HallID:  3820474,
		Role:    7,
		OwnerID: 3820474,
		Name:    "gtit",
	})

	assert.Equal(t, errorx.BackstageDuplicateUserDepartment, err)
}

func Test_AgentContext_HasDepartment_GRPCError(t *testing.T) {
	userRPC := newMockAgentRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("DepartmentInfoList", ctx, &agentclient.DepartmentInfoListRequest{
		HallId:  3820474,
		Role:    7,
		OwnerId: 3820474,
		Name:    "gtit",
	}).Return(nil, mockError)
	userCtx := New(userRPC)

	err := userCtx.HasDepartment(ctx, HasDepartmentRequest{
		HallID:  3820474,
		Role:    7,
		OwnerID: 3820474,
		Name:    "gtit",
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_CreateDepartment(t *testing.T) {
	userRPC := newMockAgentRPC()

	userRPC.On("CreateDepartment", ctx, &agentclient.CreateDepartmentRequest{
		HallId:  3820474,
		Role:    7,
		OwnerId: 3820474,
		Name:    "gtit",
		Note:    "gtit",
	}).Return(&agentclient.EmptyResponse{}, nil)
	userCtx := New(userRPC)

	err := userCtx.CreateDepartment(ctx, CreateDepartmentRequest{
		HallID:  3820474,
		Role:    7,
		OwnerID: 3820474,
		Name:    "gtit",
		Note:    "gtit",
	})

	assert.NoError(t, err)
}

func Test_AgentContext_CreateDepartment_GRPCError(t *testing.T) {
	userRPC := newMockAgentRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("CreateDepartment", ctx, &agentclient.CreateDepartmentRequest{
		HallId:  3820474,
		Role:    7,
		OwnerId: 3820474,
		Name:    "gtit",
		Note:    "gtit",
	}).Return(nil, mockError)
	userCtx := New(userRPC)

	err := userCtx.CreateDepartment(ctx, CreateDepartmentRequest{
		HallID:  3820474,
		Role:    7,
		OwnerID: 3820474,
		Name:    "gtit",
		Note:    "gtit",
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_GetParameterSet(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}

	agentRPC.On("GetParameterSet", ctx, mockRequest).Return(&seeder.GetParameterSet, nil)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetParameterSet(ctx, mockRequest)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetParameterSet, resp)
}

func Test_AgentContext_GetParameterSet_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("GetParameterSet", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetParameterSet(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AgentContext_GetPositionInfo(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.GetPositionInfoRequest{
		ParameterId: seeder.PositionParameterID,
	}

	agentRPC.On("GetPositionInfo", ctx, mockRequest).Return(&seeder.GetPositionInfo, nil)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetPositionInfo(ctx, GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	})

	assert.NoError(t, err)
	assert.Equal(t, &seeder.GetPositionInfo, resp)
}

func Test_AgentContext_GetPositionInfo_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.GetPositionInfoRequest{
		ParameterId: seeder.PositionParameterID,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("GetPositionInfo", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetPositionInfo(ctx, GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AgentContext_DeletePosition(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DeletePositionRequest{
		PositionId: seeder.PositionParameterID,
	}

	agentRPC.On("DeletePosition", ctx, mockRequest).Return(nil, nil)
	agentCtx := New(agentRPC)

	err := agentCtx.DeletePosition(ctx, seeder.PositionParameterID)

	assert.NoError(t, err)
}

func Test_AgentContext_DeletePosition_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DeletePositionRequest{
		PositionId: seeder.PositionParameterID,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("DeletePosition", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	err := agentCtx.DeletePosition(ctx, seeder.PositionParameterID)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_GetDepartmentList(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.UserId},
	}

	agentRPC.On("DepartmentList", ctx, mockRequest).Return(&seeder.AgentDepartmentList, nil)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetDepartmentList(ctx, mockRequest)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.AgentDepartmentList, resp)
}

func Test_AgentContext_GetDepartmentList_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.UserId},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("DepartmentList", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetDepartmentList(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AgentContext_DeleteSubAccountDepartment(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DeleteSubAccountDepartmentRequest{
		UserId: seeder.UserId,
	}

	agentRPC.On("DeleteSubAccountDepartment", ctx, mockRequest).Return(nil, nil)
	agentCtx := New(agentRPC)

	err := agentCtx.DeleteSubAccountDepartment(ctx, seeder.UserId)

	assert.NoError(t, err)
}

func Test_AgentContext_DeleteSubAccountDepartment_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DeleteSubAccountDepartmentRequest{
		UserId: seeder.UserId,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("DeleteSubAccountDepartment", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	err := agentCtx.DeleteSubAccountDepartment(ctx, seeder.UserId)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_GetPositionList(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.PositionListRequest{
		HallId: seeder.HallId,
	}

	agentRPC.On("PositionList", ctx, mockRequest).Return(&seeder.AgentPositionList, nil)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetPositionList(ctx, GetPositionListRequest{
		HallID: seeder.HallId,
	})

	assert.NoError(t, err)
	assert.Equal(t, &seeder.AgentPositionList, resp)
}

func Test_AgentContext_GetPositionList_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("PositionList", ctx, &agentclient.PositionListRequest{}).Return(nil, mockError)
	agentCtx := New(agentRPC)

	resp, err := agentCtx.GetPositionList(ctx, GetPositionListRequest{})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_AgentContext_UpdateDepartment(t *testing.T) {
	userRPC := newMockAgentRPC()

	userRPC.On("UpdateDepartment", ctx, &agentclient.UpdateDepartmentRequest{
		Id:   1,
		Name: "gtit",
		Note: "gtit",
	}).Return(&agentclient.EmptyResponse{}, nil)
	userCtx := New(userRPC)

	err := userCtx.UpdateDepartment(ctx, UpdateDepartmentRequest{
		ID:   1,
		Name: "gtit",
		Note: "gtit",
	})

	assert.NoError(t, err)
}

func Test_AgentContext_UpdateDepartment_GRPCError(t *testing.T) {
	userRPC := newMockAgentRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("UpdateDepartment", ctx, &agentclient.UpdateDepartmentRequest{
		Id:   1,
		Name: "gtit",
		Note: "gtit",
	}).Return(nil, mockError)
	userCtx := New(userRPC)

	err := userCtx.UpdateDepartment(ctx, UpdateDepartmentRequest{
		ID:   1,
		Name: "gtit",
		Note: "gtit",
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_CreatePosition(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.CreatePositionRequest{
		HallId: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: []*agentclient.PositionPermission{
			{
				PermId: seeder.UserPermID1,
				Modify: 1,
			},
		},
	}

	agentRPC.On("CreatePosition", ctx, mockRequest).Return(&agentclient.EmptyResponse{}, nil)
	agentCtx := New(agentRPC)

	err := agentCtx.CreatePosition(ctx, CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: []PositionPermission{
			{
				ID:     seeder.UserPermID1,
				Modify: true,
			},
		},
	})

	assert.NoError(t, err)
}

func Test_AgentContext_CreatePosition_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.CreatePositionRequest{
		HallId: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: []*agentclient.PositionPermission{
			{
				PermId: seeder.UserPermID1,
				Modify: 1,
			},
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("CreatePosition", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	err := agentCtx.CreatePosition(ctx, CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: []PositionPermission{
			{
				ID:     seeder.UserPermID1,
				Modify: true,
			},
		},
	})

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_DeleteDepartment(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DeleteDepartmentRequest{
		DepartmentId: seeder.DepartmentParameterSetID5530,
	}

	agentRPC.On("DeleteDepartment", ctx, mockRequest).Return(&agentclient.EmptyResponse{}, nil)
	agentCtx := New(agentRPC)

	err := agentCtx.DeleteDepartment(ctx, seeder.DepartmentParameterSetID5530)

	assert.NoError(t, err)
}

func Test_AgentContext_DeleteDepartment_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.DeleteDepartmentRequest{
		DepartmentId: seeder.DepartmentParameterSetID5530,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("DeleteDepartment", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	err := agentCtx.DeleteDepartment(ctx, seeder.DepartmentParameterSetID5530)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_UpdateUserDepartment(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.UpdateUserDepartmentRequest{
		DepartmentId: seeder.DepartmentParameterSetID5530,
	}

	agentRPC.On("UpdateUserDepartment", ctx, mockRequest).Return(&agentclient.EmptyResponse{}, nil)
	agentCtx := New(agentRPC)

	err := agentCtx.UpdateUserDepartment(ctx, mockRequest)

	assert.NoError(t, err)
}

func Test_AgentContext_UpdateUserDepartment_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.UpdateUserDepartmentRequest{
		DepartmentId: seeder.DepartmentParameterSetID5530,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("UpdateUserDepartment", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	err := agentCtx.UpdateUserDepartment(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_AgentContext_UpdateUserPosition(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.UpdateUserPositionRequest{
		PositionId: seeder.DepartmentParameterSetID5530,
	}

	agentRPC.On("UpdateUserPosition", ctx, mockRequest).Return(&agentclient.EmptyResponse{}, nil)
	agentCtx := New(agentRPC)

	err := agentCtx.UpdateUserPosition(ctx, mockRequest)

	assert.NoError(t, err)
}

func Test_AgentContext_UpdateUserPosition_GRPCError(t *testing.T) {
	agentRPC := newMockAgentRPC()

	mockRequest := &agentclient.UpdateUserPositionRequest{
		PositionId: seeder.PositionParameterID,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	agentRPC.On("UpdateUserPosition", ctx, mockRequest).Return(nil, mockError)
	agentCtx := New(agentRPC)

	err := agentCtx.UpdateUserPosition(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
}
