package agent

import (
	"context"
	"gbh/agent/agentclient"
	"gbh/errorx"
)

type Context interface {
	DepartmentInfoList(ctx context.Context, request DepartmentInfoListRequest) (*agentclient.DepartmentInfoListResponse, error)
	HasDepartment(ctx context.Context, request HasDepartmentRequest) error
	CreateDepartment(ctx context.Context, request CreateDepartmentRequest) error
	GetDepartmentList(ctx context.Context, in *agentclient.DepartmentListRequest) (*agentclient.DepartmentListResponse, error)
	GetParameterSet(ctx context.Context, req *agentclient.GetParameterSetRequest) (*agentclient.GetParameterSetResponse, error)
	GetPositionInfo(ctx context.Context, req GetPositionInfoRequest) (*agentclient.GetPositionInfoResponse, error)
	DeletePosition(ctx context.Context, positionId uint32) error
	DeleteSubAccountDepartment(ctx context.Context, userId uint32) error
	GetPositionList(ctx context.Context, req GetPositionListRequest) (*agentclient.PositionListResponse, error)
	UpdateDepartment(ctx context.Context, req UpdateDepartmentRequest) error
	CreatePosition(ctx context.Context, req CreatePositionRequest) error
	DeleteDepartment(ctx context.Context, departmentId uint32) error
	UpdateUserDepartment(ctx context.Context, req *agentclient.UpdateUserDepartmentRequest) error
	UpdateUserPosition(ctx context.Context, req *agentclient.UpdateUserPositionRequest) error
}

type DepartmentInfoListRequest struct {
	HallID      uint32
	Role        uint32
	ParameterID uint32
	OwnerID     uint32
	Name        string
	Fuzzy       bool
	Order       string
	Sort        string
	Page        uint32
	PageLimit   uint32
}

type HasDepartmentRequest struct {
	HallID  uint32
	Role    uint32
	OwnerID uint32
	Name    string
}

type CreateDepartmentRequest struct {
	HallID  uint32
	Role    uint32
	OwnerID uint32
	Name    string
	Note    string
}

type GetPositionInfoRequest struct {
	HallID      uint32
	ParameterID uint32
}

type GetPositionListRequest struct {
	HallID    uint32
	Position  string
	Fuzzy     bool
	Order     string
	Sort      string
	Page      uint32
	PageLimit uint32
}

type UpdateDepartmentRequest struct {
	ID   uint32
	Name string
	Note string
}

type CreatePositionRequest struct {
	HallID      uint32
	Name        string
	Note        string
	Permissions []PositionPermission
}

type PositionPermission struct {
	ID     uint32
	Modify bool
}

type agentContext struct {
	AgentRPC agentclient.Agent
}

func New(agentRPC agentclient.Agent) Context {
	return &agentContext{
		AgentRPC: agentRPC,
	}
}

func (c *agentContext) GetParameterSet(ctx context.Context, req *agentclient.GetParameterSetRequest) (*agentclient.GetParameterSetResponse, error) {
	resp, err := c.AgentRPC.GetParameterSet(ctx, req)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *agentContext) GetPositionInfo(ctx context.Context, req GetPositionInfoRequest) (*agentclient.GetPositionInfoResponse, error) {
	request := agentclient.GetPositionInfoRequest{
		HallId:      req.HallID,
		ParameterId: req.ParameterID,
	}

	resp, err := c.AgentRPC.GetPositionInfo(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *agentContext) DeletePosition(ctx context.Context, positionId uint32) error {
	_, err := c.AgentRPC.DeletePosition(ctx, &agentclient.DeletePositionRequest{
		PositionId: positionId,
	})

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *agentContext) DepartmentInfoList(ctx context.Context, req DepartmentInfoListRequest) (*agentclient.DepartmentInfoListResponse, error) {
	request := agentclient.DepartmentInfoListRequest{
		HallId:      req.HallID,
		Role:        req.Role,
		ParameterId: req.ParameterID,
		OwnerId:     req.OwnerID,
		Name:        req.Name,
		Fuzzy:       req.Fuzzy,
		Order:       req.Order,
		Sort:        req.Sort,
		Page:        req.Page,
		PageLimit:   req.PageLimit,
	}

	resp, err := c.AgentRPC.DepartmentInfoList(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *agentContext) HasDepartment(ctx context.Context, req HasDepartmentRequest) error {
	departmentInfoList, departmentInfoListErr := c.DepartmentInfoList(ctx, DepartmentInfoListRequest{
		HallID:  req.HallID,
		Role:    req.Role,
		OwnerID: req.OwnerID,
		Name:    req.Name,
	})

	if departmentInfoListErr != nil {
		return departmentInfoListErr
	}

	if len(departmentInfoList.GetList()) == 0 {
		return nil
	}

	return errorx.BackstageDuplicateUserDepartment
}

func (c *agentContext) CreateDepartment(ctx context.Context, req CreateDepartmentRequest) error {
	request := agentclient.CreateDepartmentRequest{
		HallId:  req.HallID,
		Role:    req.Role,
		OwnerId: req.OwnerID,
		Name:    req.Name,
		Note:    req.Note,
	}

	_, err := c.AgentRPC.CreateDepartment(ctx, &request)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *agentContext) GetDepartmentList(ctx context.Context, in *agentclient.DepartmentListRequest) (*agentclient.DepartmentListResponse, error) {
	resp, err := c.AgentRPC.DepartmentList(ctx, in)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *agentContext) DeleteSubAccountDepartment(ctx context.Context, userId uint32) error {
	_, err := c.AgentRPC.DeleteSubAccountDepartment(ctx, &agentclient.DeleteSubAccountDepartmentRequest{
		UserId: userId,
	})

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *agentContext) GetPositionList(ctx context.Context, req GetPositionListRequest) (*agentclient.PositionListResponse, error) {
	request := agentclient.PositionListRequest{
		HallId:    req.HallID,
		Position:  req.Position,
		Fuzzy:     req.Fuzzy,
		Order:     req.Order,
		Sort:      req.Sort,
		Page:      req.Page,
		PageLimit: req.PageLimit,
	}

	resp, err := c.AgentRPC.PositionList(ctx, &request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *agentContext) UpdateDepartment(ctx context.Context, req UpdateDepartmentRequest) error {
	request := agentclient.UpdateDepartmentRequest{
		Id:   req.ID,
		Name: req.Name,
		Note: req.Note,
	}

	_, err := c.AgentRPC.UpdateDepartment(ctx, &request)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *agentContext) CreatePosition(ctx context.Context, req CreatePositionRequest) error {
	permissions := []*agentclient.PositionPermission{}
	for _, v := range req.Permissions {
		modify := uint32(0)
		if v.Modify {
			modify = 1
		}

		permissions = append(permissions, &agentclient.PositionPermission{
			PermId: v.ID,
			Modify: modify,
		})
	}

	request := agentclient.CreatePositionRequest{
		HallId:      req.HallID,
		Name:        req.Name,
		Note:        req.Note,
		Permissions: permissions,
	}

	_, err := c.AgentRPC.CreatePosition(ctx, &request)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *agentContext) DeleteDepartment(ctx context.Context, departmentId uint32) error {
	_, err := c.AgentRPC.DeleteDepartment(ctx, &agentclient.DeleteDepartmentRequest{
		DepartmentId: departmentId,
	})

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *agentContext) UpdateUserDepartment(ctx context.Context, req *agentclient.UpdateUserDepartmentRequest) error {
	_, err := c.AgentRPC.UpdateUserDepartment(ctx, req)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *agentContext) UpdateUserPosition(ctx context.Context, req *agentclient.UpdateUserPositionRequest) error {
	_, err := c.AgentRPC.UpdateUserPosition(ctx, req)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}
