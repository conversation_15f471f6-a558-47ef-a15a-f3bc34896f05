package user

import (
	"fmt"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/user/userclient"
	"gbh/utils/strutil"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestSubAccountListHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page", "1")
	query.AddString("page_limit", "10")
	query.AddString("role", "7")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiSubAccountList, seeder.HallId), query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(seeder.HallId)
	req = pathvar.WithVars(req, params)

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockListRPCRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "parent", "username", "alias", "block", "created_at", "last_login", "role"},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		Page: &userclient.Uint32Value{
			Value: 1,
		},
		Limit: &userclient.Uint32Value{
			Value: 10,
		},
		ExtraInfo: &userclient.BoolValue{
			Value: true,
		},
	}
	mockUserRPC.On("GetUserList", req.Context(), mockListRPCRequest).Return(&seeder.SubUsers, nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockDepartmentRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockAgentRPC.On("GetDepartmentList", req.Context(), mockDepartmentRPCRequest).Return(&seeder.AgentDepartmentList, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", req.Context(), userInfo, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", req.Context(), uint32(1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", req.Context(), seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	w := httptest.NewRecorder()
	SubAccountListHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"id":*********,"parent_id":3820474,"username":"0318test1","alias":"QA-3C","block":false,"created_at":"2021-03-17T22:53:01-04:00","last_login_at":"2024-10-30T04:07:14-04:00","role":7,"department_id":5514,"position_id":5164,"parent_name":"bbinbgp","action_switch":true}],"Total":1}`, w.Body.String())
}

func TestSubAccountListHandler_InvalidParameter(t *testing.T) {
	uri := fmt.Sprintf(apiSubAccountList, seeder.HallId)
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(seeder.HallId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	SubAccountListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubAccountListHandler_WithoutParameterPage(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page_limit", "10")
	query.AddString("role", "3")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiSubAccountList, seeder.HallId), query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(seeder.HallId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	SubAccountListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubAccountListHandler_WithoutParameterPageLimit(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page", "1")
	query.AddString("role", "3")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiSubAccountList, seeder.HallId), query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(seeder.HallId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	SubAccountListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubAccountListHandler_WithoutParameterRole(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page", "1")
	query.AddString("page_limit", "10")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiSubAccountList, seeder.HallId), query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(seeder.HallId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	SubAccountListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubAccountListHandler_InvalidRole(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page", "1")
	query.AddString("page_limit", "10")
	query.AddString("role", "6")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiSubAccountList, seeder.HallId), query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(seeder.HallId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	SubAccountListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Role is invalid"}`, w.Body.String())
}

func TestSubAccountListHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page", "1")
	query.AddString("page_limit", "10")
	query.AddString("role", "3")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiSubAccountList, seeder.HallId), query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(seeder.HallId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	SubAccountListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Operator not found"}`, w.Body.String())
}
