package user

import (
	"fmt"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestDeletePositionHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)

	uri := fmt.Sprintf(apiDeletePostion+"?%s", seeder.PositionParameterID, query.Encode())

	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)
	req.RequestURI = uri
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	params := make(map[string]string)
	params["parameterID"] = "5523"
	req = pathvar.WithVars(req, params)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", req.Context(), &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(&seeder.GetParameterSet, nil)

	agentCtx.On("GetPositionInfo", req.Context(), agent.GetPositionInfoRequest{
		ParameterID: seeder.PositionParameterID,
	}).Return(&seeder.GetPositionInfo, nil)

	agentCtx.On("DeletePosition", req.Context(), seeder.PositionParameterID).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", req.Context(), seeder.HallId).Return(&seeder.GetHallById, nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", req.Context(), seeder.HallId).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "parent",
			Value: "bbinbgp",
		},
		{
			Key:   "domain",
			Value: seeder.User.GetName(),
		},
		{
			Key:   "name",
			Value: "test",
		},
		{
			Key:   "note",
			Value: "test note",
		},
	}
	operationrecordCtx.On("CreateAGRecord", req.Context(), operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     seeder.AGRecordActionId,
		TargetId:     seeder.HallId,
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      seeder.AGRecordBossKey,
		UserKey:      "",
		XMLDataMsg:   xmlDataMsg,
		XMLCancelTag: false,
		URI:          seeder.RequestURI,
		IP:           seeder.ClientIP,
	}).Return(nil)

	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	w := httptest.NewRecorder()
	DeletePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":"","data":true}`, w.Body.String())
}

func TestDeletePositionHandler_WithoutParameter(t *testing.T) {
	uri := fmt.Sprintf(apiDeletePostion, seeder.PositionParameterID)

	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	DeletePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestDeletePositionHandler_LogicErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("operator_id", seeder.AdminID)

	uri := fmt.Sprintf(apiDeletePostion+"?%s", seeder.PositionParameterID, query.Encode())

	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["parameterID"] = "5523"
	req = pathvar.WithVars(req, params)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", req.Context(), &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.PositionParameterID},
		Type: "position",
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AgentCtx = agentCtx

	w := httptest.NewRecorder()
	DeletePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
