package user

import (
	"fmt"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/operationrecord/operationrecordclient"
	"gbh/proto/user"
	"gbh/user/userclient"
	"gbh/utils/strutil"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestUpdateSubAccountHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("username", "gtptest")
	query.AddString("alias", "gtptest")
	query.AddString("department_id", "5174")
	query.AddString("position_id", "5164")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiPutSubAccount, seeder.SubUserId), query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.SubUserId)
	req = pathvar.WithVars(req, params)

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetSession", req.Context(), seeder.Token, "").Return(&seeder.GetAdminSession, nil)
	mockAdminRPC.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", req.Context(), seeder.SubUserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockUniqueRPCRequest := &userclient.CheckUsernameUniqueRequest{
		HallId:   seeder.HallId,
		Username: "gtptest",
	}
	mockUniqueResponse := &user.CheckUsernameUniqueResponse{
		Unique: true,
	}
	mockUserRPC.On("CheckUsernameUnique", req.Context(), mockUniqueRPCRequest).Return(mockUniqueResponse, nil)
	mockUserRPC.On("GetUserByUserId", req.Context(), seeder.HallId).Return(&seeder.User, nil)

	mockEditRPCRequest := &userclient.EditUserRequest{
		Id: seeder.SubUserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
		Alias: &userclient.StringValue{
			Value: "gtptest",
		},
	}
	mockUserRPC.On("EditUser", req.Context(), mockEditRPCRequest).Return(nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockParameterSetRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5164},
	}
	mockParameterSetResponse := &seeder.GetParameterSet
	mockParameterSetResponse.GetParameterSet()[0].Id = 5164
	mockParameterSetResponse = &agentclient.GetParameterSetResponse{
		ParameterSet: []*agentclient.ParameterSet{
			mockParameterSetResponse.GetParameterSet()[0],
			{
				Id:           5174,
				Type:         "department",
				Name:         "test",
				Note:         "test note",
				LastOperator: 0,
				CreatedAt:    "2025-02-14 04:03:07",
				UpdatedAt:    "2025-02-14 04:03:07",
			},
		},
	}
	mockAgentRPC.On("GetParameterSet", req.Context(), mockParameterSetRPCRequest).Return(mockParameterSetResponse, nil)

	mockDepartmentInfoRPCRequest := agent.DepartmentInfoListRequest{
		ParameterID: 5174,
	}
	mockDepartmentInfoResponse := &agentclient.DepartmentInfoListResponse{
		List: []*agentclient.DepartmentInfo{
			{
				Id:        5174,
				HallId:    seeder.HallId,
				Role:      constants.Role7,
				Name:      "gtest0328",
				Note:      "",
				OwnerId:   seeder.HallId,
				CreatedAt: "2025-03-28T00:02:43-04:00",
				Count:     0,
			},
		},
		Pagination: &agentclient.Pagination{
			Total:       1,
			TotalPage:   1,
			CurrentPage: 1,
			PageLimit:   1,
		},
	}
	mockAgentRPC.On("DepartmentInfoList", req.Context(), mockDepartmentInfoRPCRequest).Return(mockDepartmentInfoResponse, nil)

	mockPositionInfoRPCRequest := agent.GetPositionInfoRequest{
		ParameterID: 5164,
	}
	mockAgentRPC.On("GetPositionInfo", req.Context(), mockPositionInfoRPCRequest).Return(&seeder.GetPositionInfo, nil)

	mockDepartmentListRPCRequest := &agentclient.DepartmentListRequest{
		UserId: []uint32{seeder.SubUserId},
	}
	mockDepartmentListResponse := &seeder.AgentDepartmentList
	mockDepartmentListResponse.GetList()[0].DepartmentParameterId = 5174
	mockAgentRPC.On("GetDepartmentList", req.Context(), mockDepartmentListRPCRequest).Return(mockDepartmentListResponse, nil)

	mockParameterSetDepartmentRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5174, 5174},
	}
	mockParameterSetDepartmentResponse := &seeder.GetParameterSet
	mockParameterSetDepartmentResponse.GetParameterSet()[0].Id = 5174
	mockAgentRPC.On("GetParameterSet", req.Context(), mockParameterSetDepartmentRPCRequest).Return(mockParameterSetDepartmentResponse, nil)

	mockParameterSetPositionRPCRequest := &agentclient.GetParameterSetRequest{
		Id: []uint32{5164, 5164},
	}
	mockParameterSetPositionResponse := &seeder.GetParameterSet
	mockParameterSetPositionResponse.GetParameterSet()[0].Id = 5164
	mockAgentRPC.On("GetParameterSet", req.Context(), mockParameterSetPositionRPCRequest).Return(mockParameterSetPositionResponse, nil)

	mockUpdateDepartmentRPCRequest := &agentclient.UpdateUserDepartmentRequest{
		UserId:       seeder.SubUserId,
		DepartmentId: 5174,
	}
	mockAgentRPC.On("UpdateUserDepartment", req.Context(), mockUpdateDepartmentRPCRequest).Return(nil)

	mockUpdatePositionRPCRequest := &agentclient.UpdateUserPositionRequest{
		UserId:     seeder.SubUserId,
		PositionId: 5164,
	}
	mockAgentRPC.On("UpdateUserPosition", req.Context(), mockUpdatePositionRPCRequest).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "upperlevelid",
			Value: "R_hall",
		},
		{
			Key:   "upperloginname",
			Value: seeder.User.GetUsername(),
		},
		{
			Key:   "levelid",
			Value: "R_hallsub",
		},
		{
			Key:   "loginname",
			Value: "gtptest",
		},
		{
			Key:   "typeloginname",
			Value: "R_type_loginname",
		},
		{
			Key:   "chg_loginname",
			Value: "R_loginname",
		},
		{
			Key:   "old_loginname",
			Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
		},
		{
			Key:   "new_loginname",
			Value: "gtptest",
		},
		{
			Key:   "typenickname",
			Value: "R_type_nickname",
		},
		{
			Key:   "chg_nickname",
			Value: "R_nickname",
		},
		{
			Key:   "old_nickname",
			Value: seeder.SubUsers.GetUsers()[0].GetAlias(),
		},
		{
			Key:   "new_nickname",
			Value: "gtptest",
		},
		{
			Key:   "typepassword",
			Value: "",
		},
		{
			Key:   "chg_password",
			Value: "",
		},
		{
			Key:   "old_password",
			Value: "",
		},
		{
			Key:   "new_password",
			Value: "",
		},
	}
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     2,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      "R_all_usersubedit",
		UserKey:      "R_all_usersubedit",
		XMLDataMsg:   xmlDataMsg,
		URI:          req.RequestURI,
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", req.Context(), mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split(req.RequestURI, "?")[0],
		Act:       "change",
		ActionSql: "",
		Content:   "帳號：0318test1→gtptest<br />名稱：QA-3C→gtptest<br />",
		Bywho:     "admin",
		Byfile:    strings.Split(req.RequestURI, "?")[0],
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", req.Context(), mockLogRPCRequest).Return(nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", req.Context(), mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", req.Context(), uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", req.Context(), seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	w := httptest.NewRecorder()
	UpdateSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":true}`, w.Body.String())
}

func TestUpdateSubAccountHandler_InvalidParameter(t *testing.T) {
	uri := fmt.Sprintf(apiPutSubAccount, seeder.SubUserId)
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.SubUserId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	UpdateSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestUpdateSubAccountHandler_WithoutParameterUsername(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("alias", "gtptest")
	query.AddString("department_id", "5174")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiPutSubAccount, seeder.SubUserId), query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.SubUserId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	UpdateSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestUpdateSubAccountHandler_WithoutParameterAlias(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("username", "gtptest")
	query.AddString("department_id", "5174")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiPutSubAccount, seeder.SubUserId), query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.SubUserId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	UpdateSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestUpdateSubAccountHandler_WithoutParameterDepartmentID(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("username", "gtptest")
	query.AddString("alias", "gtptest")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiPutSubAccount, seeder.SubUserId), query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.SubUserId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	UpdateSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestUpdateSubAccountHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("username", "gtptest")
	query.AddString("alias", "gtptest")
	query.AddString("department_id", "5174")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiPutSubAccount, seeder.SubUserId), query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.SubUserId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	UpdateSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Operator not found"}`, w.Body.String())
}
