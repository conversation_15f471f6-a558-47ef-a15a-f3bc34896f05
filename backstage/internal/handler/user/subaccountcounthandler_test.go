package user

import (
	"fmt"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/user/userclient"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubAccountCountHandler(t *testing.T) {
	params := urlutil.NewBuilder()
	params.AddUint32("hall_id", seeder.HallId)
	params.AddString("role", "7")

	uri := fmt.Sprintf("%s?%s", apiSubAccountCount, params.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	mockUserRPC := mock.NewMockUserCtx()
	mockListRPCRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "last_login"},
	}
	mockUserRPC.On("GetUserList", req.Context(), mockListRPCRequest).Return(&seeder.SubUsers, nil)

	svcCtx.UserCtx = mockUserRPC

	w := httptest.NewRecorder()
	SubAccountCountHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"all":1,"seven":1,"thirty":1}}`, w.Body.String())
}

func TestSubAccountCountHandler_InvalidParameter(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiSubAccountCount, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubAccountCountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubAccountCountHandler_WithoutParameterHallID(t *testing.T) {
	params := urlutil.NewBuilder()
	params.AddString("role", "7")

	uri := fmt.Sprintf("%s?%s", apiSubAccountCount, params.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubAccountCountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubAccountCountHandler_WithoutParameterRole(t *testing.T) {
	params := urlutil.NewBuilder()
	params.AddUint32("hall_id", seeder.HallId)

	uri := fmt.Sprintf("%s?%s", apiSubAccountCount, params.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubAccountCountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubAccountCountHandler_InvalidRole(t *testing.T) {
	params := urlutil.NewBuilder()
	params.AddString("token", seeder.Token)
	params.AddUint32("hall_id", seeder.HallId)
	params.AddString("role", "6")

	uri := fmt.Sprintf("%s?%s", apiSubAccountCount, params.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubAccountCountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Role is invalid"}`, w.Body.String())
}

func TestSubAccountCountHandler_LogicError(t *testing.T) {
	params := urlutil.NewBuilder()
	params.AddUint32("hall_id", seeder.HallId)
	params.AddString("role", "7")

	uri := fmt.Sprintf("%s?%s", apiSubAccountCount, params.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserList", req.Context(), &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		FetchFields: []string{"id", "last_login"},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = mockUserCtx

	w := httptest.NewRecorder()
	SubAccountCountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Connection failed"}`, w.Body.String())
}
