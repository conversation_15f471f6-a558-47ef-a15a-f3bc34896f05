package user

import (
	"net/http/httptest"
	"testing"

	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/user"
	"gbh/utils/urlutil"
	"net/http"

	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
)

func Test_LoginRecordHandler(t *testing.T) {
	now := carbon.Now().ToDateString()
	query := urlutil.NewBuilder()
	query.AddUint32("user_id", seeder.GTIHallId)
	query.AddString("start_date", now)
	query.AddString("end_date", now)
	query.AddString("ip", seeder.ClientIP)
	query.AddString("page", "0")
	query.AddString("page_limit", "0")

	uri := apiGetLoginRecord + "?" + query.Encode()

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req = req.WithContext(ctx)
	req.RequestURI = uri
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.GTIHallId).Return(&seeder.User, nil)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil).Once()

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockRequest := user.GetLoginRecordRequest{
		StartTime: now + "T00:00:00-04:00",
		EndTime:   now + "T23:59:59-04:00",
		UserID:    seeder.GTIHallId,
		HallID:    seeder.HallId,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}

	mockUserCtx.On("GetLoginRecord", ctx, mockRequest).Return(&seeder.LoginRecord, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx

	w := httptest.NewRecorder()
	LoginRecordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":"","data":{"record":[{"role":7,"domain":3820474,"name":"BGP API 測試廳","login_time":"2025-01-01T04:32:32-04:00","username":"gtihall","ip":"*************","result":3,"host":"bgp-hall.vir111.com","ingress":1,"client_os":"OS X","country":"台湾","city":"台北市","regin":"台湾","client_browser":"Chrome","is_slide":false}],"pagination":{"page":1,"page_limit":10,"total_number":1,"total_page":1}}}`, w.Body.String())
}

func Test_LoginRecordHandler_WithoutParameterUserID(t *testing.T) {
	now := carbon.Now().ToDateString()
	query := urlutil.NewBuilder()
	query.AddString("start_date", now)
	query.AddString("end_date", now)
	query.AddString("ip", seeder.ClientIP)
	query.AddString("page", "0")
	query.AddString("page_limit", "0")

	uri := apiGetLoginRecord + "?" + query.Encode()

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RequestURI = uri
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	LoginRecordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func Test_LoginRecordHandler_WithoutParameterStartDate(t *testing.T) {
	now := carbon.Now().ToDateString()
	query := urlutil.NewBuilder()
	query.AddUint32("user_id", seeder.GTIHallId)
	query.AddString("end_date", now)
	query.AddString("ip", seeder.ClientIP)
	query.AddString("page", "0")
	query.AddString("page_limit", "0")

	uri := apiGetLoginRecord + "?" + query.Encode()

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RequestURI = uri
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	LoginRecordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func Test_LoginRecordHandler_WithoutParameterEndDate(t *testing.T) {
	now := carbon.Now().ToDateString()
	query := urlutil.NewBuilder()
	query.AddUint32("user_id", seeder.GTIHallId)
	query.AddString("start_date", now)
	query.AddString("ip", seeder.ClientIP)
	query.AddString("page", "0")
	query.AddString("page_limit", "0")

	uri := apiGetLoginRecord + "?" + query.Encode()

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RequestURI = uri
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	LoginRecordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func Test_LoginRecordHandler_LogicErr(t *testing.T) {
	now := carbon.Now().ToDateString()
	query := urlutil.NewBuilder()
	query.AddUint32("user_id", seeder.GTIHallId)
	query.AddString("start_date", now)
	query.AddString("end_date", now)

	uri := apiGetLoginRecord + "?" + query.Encode()

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	LoginRecordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}

func Test_LoginRecordHandler_ValidError(t *testing.T) {
	lastyear := carbon.Now().SubYear().ToDateString()
	query := urlutil.NewBuilder()
	query.AddUint32("user_id", seeder.GTIHallId)
	query.AddString("start_date", lastyear)
	query.AddString("end_date", lastyear)
	query.AddString("ip", seeder.ClientIP)
	query.AddString("page", "0")
	query.AddString("page_limit", "0")

	uri := apiGetLoginRecord + "?" + query.Encode()

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	LoginRecordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000021,"message":"Search Time Period can not be greater than 60 days"}`, w.Body.String())
}
