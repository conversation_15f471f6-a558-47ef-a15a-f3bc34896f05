package user

import (
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/operationrecord/operationrecordclient"
	"gbh/user/userclient"
	"gbh/utils/strutil"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestDeleteSubAccountHandler(t *testing.T) {
	uri := fmt.Sprintf(apiDeleteSubAccount, seeder.UserId)
	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.UserId)
	req = pathvar.WithVars(req, params)

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", req.Context(), seeder.UserId).Return(seeder.SubUsers.GetUsers()[0], nil)

	mockDeleteRPCRequest := &userclient.DeleteUserRequest{
		Id:           seeder.UserId,
		Operator:     "admin",
		OperatorRole: 1,
		OperatorId:   seeder.AdminID,
	}
	mockUserRPC.On("DeleteUser", req.Context(), mockDeleteRPCRequest).Return(nil)
	mockUserRPC.On("GetUserByUserId", req.Context(), seeder.HallId).Return(&seeder.User, nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	mockOperator := types.UserInfo{
		Id:       seeder.SubUserId,
		Domain:   seeder.HallId,
		ParentId: seeder.HallId,
		Role:     constants.Role7,
		IsSub:    true,
	}
	mockPermissionRPC.On("CheckUserPermission", req.Context(), mockOperator, uint32(1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", req.Context(), uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{})
	mockPermissionRPC.On("DeleteUserPermissionByUserID", req.Context(), seeder.UserId).Return(nil)

	mockOperationRecordRPC := mock.NewMockOperationRecordCtx()
	xmlDataMsg := []*operationrecord.KeyValue{
		{
			Key:   "upperlevelid",
			Value: "R_hall",
		},
		{
			Key:   "upperloginname",
			Value: seeder.User.GetUsername(),
		},
		{
			Key:   "levelid",
			Value: "R_hallsub",
		},
		{
			Key:   "loginname",
			Value: seeder.SubUsers.GetUsers()[0].GetUsername(),
		},
	}
	mockAGRecordRPCRequest := operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     3,
		TargetId:     seeder.HallId,
		TargetRoleId: constants.Role7,
		BossKey:      constants.AGRecordMainKey["boss"][24],
		UserKey:      constants.AGRecordMainKey["user"][24],
		XMLDataMsg:   xmlDataMsg,
		URI:          req.RequestURI,
		IP:           seeder.ClientIP,
	}
	mockOperationRecordRPC.On("CreateAGRecord", req.Context(), mockAGRecordRPCRequest).Return(nil)

	mockLogRPCRequest := &operationrecordclient.AddLogChangeRequest{
		Balance:   0,
		Area:      "C",
		HallId:    seeder.HallId,
		Applet:    strings.Split(req.RequestURI, "?")[0],
		Act:       "del",
		ActionSql: "",
		Content:   constants.LogChangeUsername + seeder.SubUsers.GetUsers()[0].GetUsername() + "<br />ID：" + strutil.Uint32ToString(seeder.UserId) + "<br />名稱：" + seeder.SubUsers.GetUsers()[0].GetAlias(),
		Bywho:     "admin",
		Byfile:    strings.Split(req.RequestURI, "?")[0],
		Ip:        seeder.ClientIP,
	}
	mockOperationRecordRPC.On("AddLogChange", req.Context(), mockLogRPCRequest).Return(nil)

	mockAgentRPC := mock.NewMockAgentCtx()
	mockAgentRPC.On("DeleteSubAccountDepartment", req.Context(), seeder.UserId).Return(nil)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", req.Context(), seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.OperationRecordCtx = mockOperationRecordRPC
	svcCtx.AgentCtx = mockAgentRPC
	svcCtx.HallCtx = mockHallCtx

	w := httptest.NewRecorder()
	DeleteSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":true}`, w.Body.String())
}

func TestDeleteSubAccountHandler_InvalidParameter(t *testing.T) {
	uri := fmt.Sprintf(apiDeleteSubAccount, seeder.UserId)
	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	DeleteSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestDeleteSubAccountHandler_LogicError(t *testing.T) {
	uri := fmt.Sprintf(apiDeleteSubAccount, seeder.UserId)
	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["user_id"] = strutil.Uint32ToString(seeder.UserId)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	DeleteSubAccountHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Operator not found"}`, w.Body.String())
}
