package user

import (
	"encoding/json"
	"fmt"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/logic/user"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/user/userclient"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubUsersWithDepartmentHandler_TheParametersAreNotComplete(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiSubUsersWithDepartment, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubUsersWithDepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubUsersWithDepartmentHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("parent_id", seeder.HallId)
	uri := fmt.Sprintf("%s?%s", apiSubUsersWithDepartment, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	req.RemoteAddr = seeder.ClientIP

	w := httptest.NewRecorder()
	SubUsersWithDepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}

func TestSubUsersWithDepartmentHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("parent_id", seeder.HallId)

	uri := fmt.Sprintf("%s?%s", apiSubUsersWithDepartment, query.Encode())

	mockAdminCtx := mock.NewMockAdminCtx()
	mockUserCtx := mock.NewMockUserCtx()
	mockHallCtx := mock.NewMockHallCtx()
	mockAgentCtx := mock.NewMockAgentCtx()

	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).
		Return(&seeder.GMHallPrivilege, nil)

	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	getUserListRequest := &userclient.GetUserListRequest{
		ParentId: seeder.HallId,
		Depth: &userclient.Uint32Value{
			Value: 1,
		},
		Sub: &userclient.BoolValue{
			Value: true,
		},
		Enable: &userclient.BoolValue{
			Value: true,
		},
		Sort: &userclient.StringValue{
			Value: "username",
		},
		Order: &userclient.StringValue{
			Value: "asc",
		},
		FetchFields: []string{"id", "username"},
	}

	mockUserCtx.On("GetUserList", ctx, getUserListRequest).Return(&seeder.SubUsers, nil)

	mockAgentCtx.On("GetDepartmentList", ctx, &agentclient.DepartmentListRequest{
		UserId: []uint32{455798902},
	}).Return(&seeder.AgentDepartmentList, nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	w := httptest.NewRecorder()
	SubUsersWithDepartmentHandler(svcCtx).ServeHTTP(w, req)

	expected, err := json.Marshal(&types.BaseResponse{
		Code:    0,
		Message: "",
		Data: []user.SubUserWithDepartment{
			{
				UserID:                seeder.SubUserId,
				Username:              seeder.SubUsers.GetUsers()[0].GetUsername(),
				DepartmentParameterId: seeder.AgentDepartmentID5514,
				PositionParameterId:   seeder.AgentPositionID5164,
			},
		},
	})

	assert.NoError(t, err)
	assert.Equal(t, string(expected), w.Body.String())
}
