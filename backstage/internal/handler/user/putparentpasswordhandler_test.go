package user

import (
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/user/userclient"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPutParentPasswordHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("parent_id", seeder.AgentID)
	query.AddString("password", seeder.ParentPassword)
	query.AddString("request_url", "user.rpc.SetPassword")

	uri := fmt.Sprintf("%s?%s", apiPutParentPassword, query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	mockAdminRPC := mock.NewMockAdminCtx()
	mockAdminRPC.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	setPassword := &userclient.SetPasswordRequest{
		UserId:      seeder.AgentID,
		NewPassword: query.Values().Get("password"),
		ClientIp:    seeder.ClientIP,
		Entrance:    constants.EntranceControl,
		OperatorId:  seeder.GetAdminSession.GetUser().GetId(),
		Operator:    seeder.GetAdminSession.GetUser().GetUsername(),
		RequestUrl:  query.Values().Get("request_url"),
	}
	mockUserRPC := mock.NewMockUserCtx()
	mockUserRPC.On("GetUserByUserId", req.Context(), seeder.AgentID).Return(&seeder.Agent, nil)
	mockUserRPC.On("SetPassword", req.Context(), setPassword).Return(nil)

	mockPermissionRPC := mock.NewMockPermissionCtx()
	userInfo := types.UserInfo{
		Id:           seeder.Agent.GetId(),
		Domain:       seeder.Agent.GetDomain(),
		ParentId:     seeder.Agent.GetParent(),
		AllParentsId: seeder.Agent.GetAllParents(),
		Role:         seeder.Agent.GetRole(),
		IsSub:        seeder.Agent.GetSub(),
	}
	mockPermissionRPC.On("CheckUserPermission", ctx, userInfo, uint32(constants.UserPermID1392), "enable").Return(true, nil)
	mockPermissionRPC.On("GetPermissionConfigById", ctx, uint32(constants.UserPermID1392)).Return(&types.UserPermissionConfig{}, nil)

	mockHallRPC := mock.NewMockHallCtx()
	mockHallRPC.On("GetHallById", req.Context(), seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = mockAdminRPC
	svcCtx.UserCtx = mockUserRPC
	svcCtx.PermissionCtx = mockPermissionRPC
	svcCtx.HallCtx = mockHallRPC

	w := httptest.NewRecorder()
	PutParentPasswordHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":true}`, w.Body.String())
}

func TestPutParentPasswordHandler_InvalidParameter(t *testing.T) {
	req, err := http.NewRequest(http.MethodPut, apiPutParentPassword, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	PutParentPasswordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestPutParentPasswordHandler_WithoutParameter_ParentID(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("password", seeder.ParentPassword)
	query.AddString("request_url", "user.rpc.SetPassword")

	uri := fmt.Sprintf("%s?%s", apiPutParentPassword, query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	PutParentPasswordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestPutParentPasswordHandler_WithoutParameter_Password(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("parent_id", seeder.AgentID)
	query.AddString("request_url", "user.rpc.SetPassword")

	uri := fmt.Sprintf("%s?%s", apiPutParentPassword, query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	PutParentPasswordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestPutParentPasswordHandler_WithoutParameter_RequestURI(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("parent_id", seeder.AgentID)
	query.AddString("password", seeder.ParentPassword)

	uri := fmt.Sprintf("%s?%s", apiPutParentPassword, query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	PutParentPasswordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestPutParentPasswordHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("parent_id", seeder.AgentID)
	query.AddString("password", "Asd123")
	query.AddString("request_url", "user.rpc.SetPassword")

	uri := fmt.Sprintf("%s?%s", apiPutParentPassword, query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	PutParentPasswordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000043,"message":"Password is invalid"}`, w.Body.String())
}

func TestPutParentPasswordHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("parent_id", seeder.AgentID)
	query.AddString("password", seeder.ParentPassword)
	query.AddString("request_url", "user.rpc.SetPassword")

	uri := fmt.Sprintf("%s?%s", apiPutParentPassword, query.Encode())
	req, err := http.NewRequest(http.MethodPut, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	PutParentPasswordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}
