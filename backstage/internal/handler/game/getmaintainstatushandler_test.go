package game

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/proto/maintain"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetMaintainStatusHandler(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiMaintainStatus, http.NoBody)
	assert.NoError(t, err)

	enable := true
	LobbyCategoryRequest := game.LobbyCategoryRequest{
		Enable: &enable,
	}
	GameCtx := mock.NewMockGameCtx()
	GameCtx.On("LobbyCategory", ctx, LobbyCategoryRequest).Return(&seeder.LobbyCategoryEnableIsTrue, nil)

	MaintainCtx := mock.NewMockMaintainCtx()
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBLive,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy3, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBCasino,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy5, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBFish,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy38, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.NBBSport,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy31, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBLottery,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy12, nil)
	MaintainCtx.On("GetMaintainByGameKind", ctx, &maintain.GetMaintainByGameKindRequest{
		GameKind: constants.BBBattle,
	}).Return(&seeder.GetMaintainByGameKindIsNoMaintainingBy66, nil)

	svcCtx.GameCtx = GameCtx
	svcCtx.MaintainCtx = MaintainCtx

	w := httptest.NewRecorder()
	GetMaintainStatusHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"maintains":[{"game_kind":3,"category":"live","start_time":"2024-03-07T00:00:00+0800","end_time":"2024-03-07T11:30:00+0800","modify_time":"2024-03-07T11:23:15+0800","message":"","is_maintaining":false},{"game_kind":5,"category":"prob","start_time":"2024-03-07T00:00:00+0800","end_time":"2024-03-07T11:30:00+0800","modify_time":"2024-03-07T11:23:15+0800","message":"","is_maintaining":false},{"game_kind":38,"category":"prob","start_time":"2024-03-07T00:00:00+0800","end_time":"2024-03-07T11:30:00+0800","modify_time":"2024-03-07T11:23:15+0800","message":"","is_maintaining":false},{"game_kind":31,"category":"sport","start_time":"2024-03-07T00:00:00+0800","end_time":"2024-03-07T11:30:00+0800","modify_time":"2024-03-07T11:23:15+0800","message":"","is_maintaining":false},{"game_kind":12,"category":"lottery","start_time":"2024-03-07T00:00:00+0800","end_time":"2024-03-07T11:30:00+0800","modify_time":"2024-03-07T11:23:15+0800","message":"","is_maintaining":false},{"game_kind":66,"category":"card","start_time":"2024-03-07T00:00:00+0800","end_time":"2024-03-07T11:30:00+0800","modify_time":"2024-03-07T11:23:15+0800","message":"","is_maintaining":false}]}}`, w.Body.String())
}

func TestGetMaintainStatusHandler_LogicError(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiMaintainStatus, http.NoBody)
	assert.NoError(t, err)

	enable := true
	LobbyCategoryRequest := game.LobbyCategoryRequest{
		Enable: &enable,
	}
	GameCtx := mock.NewMockGameCtx()
	GameCtx.On("LobbyCategory", ctx, LobbyCategoryRequest).Return(nil, errorx.ConnectionFailed)
	svcCtx.GameCtx = GameCtx

	w := httptest.NewRecorder()
	GetMaintainStatusHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
