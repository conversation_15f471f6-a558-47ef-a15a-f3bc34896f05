package game

import (
	"context"
	"gbh/backstage/internal/config"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

var (
	ctx    context.Context
	svcCtx *svc.ServiceContext
)

const (
	apiDictionaryByType         = "/api/game/dictionary"
	apiMaintainStatus           = "/api/game/maintain_status"
	apiGetGameLobby             = "/api/game/lobby"
	apiJackpotHistoryOuterLayer = "/api/game/jackpot_history/outer_layer/lobby/%d"
)

func init() {
	ctx = context.Background()
	conf := config.Config{}
	svcCtx = &svc.ServiceContext{
		Config:     conf,
		LangCtx:    mock.NewMockLangCtx(),
		GameCtx:    mock.NewMockGameCtx(),
		JackpotCtx: mock.NewMockJackpotCtx(),
	}

	// 設定統一錯誤處理
	httpx.SetErrorHandler(func(err error) (int, any) {
		errx := errorx.ErrorToErrorx(err)
		return http.StatusOK, &types.BaseResponse{
			Code:    errx.Code,
			Message: errx.Message,
		}
	})
}
