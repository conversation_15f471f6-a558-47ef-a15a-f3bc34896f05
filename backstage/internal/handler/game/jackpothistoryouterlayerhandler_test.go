package game

import (
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/jackpot"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/user/userclient"
	"gbh/utils/strutil"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestJackpotHistoryOuterLayerHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)
	query.AddUint32("role", constants.Role7)

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiJackpotHistoryOuterLayer, constants.BBCasino), query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["lobbyID"] = strutil.Uint32ToString(constants.BBCasino)
	req = pathvar.WithVars(req, params)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", req.Context(), true).Return(&seeder.HallList, nil)

	userCtx := mock.NewMockUserCtx()
	var extraInfo *bool
	mockResponse := &seeder.Users
	mockResponse.Users = []*userclient.GetResponse{
		mockResponse.GetUsers()[0],
		mockResponse.GetParents()[0],
	}
	userCtx.On("GetUsers", req.Context(), []uint32{seeder.HallId}, extraInfo).Return(mockResponse, nil)

	jackpotCtx := mock.NewMockJackpotCtx()
	jackpotCtx.On("GetJackpotInfo", req.Context(), jackpot.GetJackpotInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		Role:      constants.Role7,
		HallID:    []uint32{seeder.HallId},
	}).Return(&seeder.GetJackpotInfo, nil)

	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.JackpotCtx = jackpotCtx

	w := httptest.NewRecorder()
	JackpotHistoryOuterLayerHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"hall_id":3820474,"prize":"100","total":1}]}`, w.Body.String())
}

func TestJackpotHistoryOuterLayerHandler_WithoutParameter(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)
	query.AddUint32("role", constants.Role7)

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiJackpotHistoryOuterLayer, constants.BBCasino), query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	JackpotHistoryOuterLayerHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestJackpotHistoryOuterLayerHandler_ValidateErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)
	query.AddString("role", "6")

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiJackpotHistoryOuterLayer, constants.BBCasino), query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["lobbyID"] = strutil.Uint32ToString(constants.BBCasino)
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	JackpotHistoryOuterLayerHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.JSONEq(t, `{"code":562000017,"message":"Role is invalid"}`, w.Body.String())
}

func TestJackpotHistoryOuterLayerHandler_LogicErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)
	query.AddUint32("role", constants.Role7)

	uri := fmt.Sprintf("%s?%s", fmt.Sprintf(apiJackpotHistoryOuterLayer, constants.BBCasino), query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["lobbyID"] = strutil.Uint32ToString(constants.BBCasino)
	req = pathvar.WithVars(req, params)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", req.Context(), true).Return(nil, errorx.ConnectionFailed)

	svcCtx.HallCtx = hallCtx

	w := httptest.NewRecorder()
	JackpotHistoryOuterLayerHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
