package game

import (
	"fmt"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetGameLobbyHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("bb_tip", "true")
	uri := fmt.Sprintf("%s?%s", apiGetGameLobby, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("FlattenLobbyCategory", ctx, game.LobbyCategoryRequest{
		BBTip: true,
	}).Return(seeder.FlattenLobbyCategory, nil)

	svcCtx.GameCtx = gameCtx

	w := httptest.NewRecorder()
	GetGameLobbyHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":["3","99","5","38","31","12","66"]}`, w.Body.String())
}

func TestGetGameLobbyHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("bb_tip", "test")
	uri := fmt.Sprintf("%s?%s", apiGetGameLobby, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetGameLobbyHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetGameLobbyHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	uri := fmt.Sprintf("%s?%s", apiGetGameLobby, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("FlattenLobbyCategory", ctx, game.LobbyCategoryRequest{
		BBTip: false,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	w := httptest.NewRecorder()
	GetGameLobbyHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
