package game

import (
	"fmt"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetDictionaryByTypeHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("game_kind", "3")
	query.AddString("type", "relate")
	uri := fmt.Sprintf("%s?%s", apiDictionaryByType, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	langCtx := mock.NewMockLangCtx()

	langCtx.On("GetGameFeatures", ctx, "zh-cn", uint32(3)).Return(&seeder.GetGameFeaturesZhCn, nil)

	svcCtx.LangCtx = langCtx

	w := httptest.NewRecorder()
	GetDictionaryListHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"dealer_type_1":"BB 现场","dealer_type_3":"BC 现场"}}`, w.Body.String())
}

func TestGetDictionaryByTypeHandler_WithoutParameterGameKind(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("type", "relate")
	uri := fmt.Sprintf("%s?%s", apiDictionaryByType, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetDictionaryListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetDictionaryByTypeHandler_WithoutParameterType(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("game_kind", "3")
	uri := fmt.Sprintf("%s?%s", apiDictionaryByType, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetDictionaryListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetDictionaryByTypeHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("game_kind", "3")
	query.AddString("type", "test")

	uri := fmt.Sprintf("%s?%s", apiDictionaryByType, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetDictionaryListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562060013,"message":"Game dictionary type not found"}`, w.Body.String())
}

func TestGetDictionaryByTypeHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("game_kind", "3")
	query.AddString("type", "relate")
	uri := fmt.Sprintf("%s?%s", apiDictionaryByType, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetGameFeatures", ctx, "zh-cn", uint32(3)).Return(nil, errorx.ConnectionFailed)

	svcCtx.LangCtx = langCtx

	w := httptest.NewRecorder()
	GetDictionaryListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
