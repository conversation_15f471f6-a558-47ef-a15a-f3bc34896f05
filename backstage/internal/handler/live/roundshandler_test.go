package live

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"

	"github.com/stretchr/testify/assert"
)

func TestRoundsHandler_WithoutParameterStartDate(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("end_date", seeder.EndDate)
	query.AddUint32("game_id", constants.GameID3001)

	uri := fmt.Sprintf("%s?%s", apiRounds, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestRoundsHandler_WithoutParameterEndDate(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddUint32("game_id", constants.GameID3001)

	uri := fmt.Sprintf("%s?%s", apiRounds, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestRoundsHandler_WithoutParameterGameId(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)

	uri := fmt.Sprintf("%s?%s", apiRounds, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestRoundsHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", "test")
	query.AddUint32("game_id", constants.GameID3001)

	uri := fmt.Sprintf("%s?%s", apiRounds, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000006,"message":"End date error"}`, w.Body.String())
}

func TestRoundsHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)
	query.AddUint32("game_id", constants.GameID3001)

	uri := fmt.Sprintf("%s?%s", apiRounds, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	liveGameCtx := mock.NewMockLiveGameCtx()

	roundInfoMockRequest := livegame.RoundInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		GameID:    constants.GameID3001,
	}
	liveGameCtx.On("RoundInfo", ctx, roundInfoMockRequest).Return(&seeder.RoundInfo, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	RoundsHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"rounds":[{"datetime":"2025-01-01 00:00:08","round_serial":*********,"foreign_round_serial":"****************","round_no":"51-23","game_id":3001,"game_code":93,"round_xml":"bankerCast=C.2,C.6,\u0026playerCast=S.1,D.5,\u0026bankerPoint=8\u0026playerPoint=6","bet_amount":0,"payoff":0,"result_status":85,"parsed_xml":{"banker_card":["C.2","C.6"],"banker_point":"8","player_card":["S.1","D.5"],"player_point":"6"}}],"pagination":{"page":1,"page_limit":5,"total_number":1,"total_page":1}}}`, w.Body.String())
}

func TestRoundsHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)
	query.AddUint32("game_id", constants.GameID3001)

	uri := fmt.Sprintf("%s?%s", apiRounds, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	liveGameCtx := mock.NewMockLiveGameCtx()

	roundInfoMockRequest := livegame.RoundInfoRequest{
		StartDate: seeder.StartDate,
		EndDate:   seeder.EndDate,
		GameID:    constants.GameID3001,
	}
	liveGameCtx.On("RoundInfo", ctx, roundInfoMockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	RoundsHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Connection failed"}`, w.Body.String())
}
