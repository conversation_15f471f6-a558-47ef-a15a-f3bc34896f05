package live

import (
	"context"
	"gbh/backstage/internal/config"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

var (
	ctx    context.Context
	svcCtx *svc.ServiceContext
)

const (
	apiRounds               = "/api/live/rounds"
	apiLiveCancelWagers     = "/api/live/cancel_wagers"
	apiLiveTables           = "/api/live/tables"
	apiRoundsByRoundSerial  = "/api/live/rounds/123"
	apiRoundsParsedXML      = "/api/live/rounds/parsed_xml"
	apiGetTipSwitchByHallId = "/api/live/tip_switch/%d"
)

func init() {
	ctx = context.Background()
	conf := config.Config{}
	svcCtx = &svc.ServiceContext{
		Config:      conf,
		LiveGameCtx: mock.NewMockLiveGameCtx(),
	}

	// 設定統一錯誤處理
	httpx.SetErrorHandler(func(err error) (int, any) {
		errx := errorx.ErrorToErrorx(err)
		return http.StatusOK, &types.BaseResponse{
			Code:    errx.Code,
			Message: errx.Message,
		}
	})
}
