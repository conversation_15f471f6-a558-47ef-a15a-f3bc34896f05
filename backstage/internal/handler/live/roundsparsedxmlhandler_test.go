package live

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"

	"github.com/stretchr/testify/assert"
)

func TestRoundsParsedXMLHandler_WithoutParameterRoundSerials(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)

	uri := fmt.Sprintf("%s?%s", apiRoundsParsedXML, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsParsedXMLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestRoundsParsedXMLHandler_WithoutParameterStartDate(t *testing.T) {
	roundSerials := []uint32{seeder.RoundInfoRoundSerial}
	roundSerialsJSON, jsonErr := json.Marshal(roundSerials)
	assert.NoError(t, jsonErr)

	query := urlutil.NewBuilder()
	query.AddString("round_serials", string(roundSerialsJSON))
	query.AddString("end_date", seeder.EndDate)

	uri := fmt.Sprintf("%s?%s", apiRoundsParsedXML, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsParsedXMLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestRoundsParsedXMLHandler_WithoutParameterEndDate(t *testing.T) {
	roundSerials := []uint32{seeder.RoundInfoRoundSerial}
	roundSerialsJSON, jsonErr := json.Marshal(roundSerials)
	assert.NoError(t, jsonErr)

	query := urlutil.NewBuilder()
	query.AddString("round_serials", string(roundSerialsJSON))
	query.AddString("start_date", seeder.EndDate)

	uri := fmt.Sprintf("%s?%s", apiRoundsParsedXML, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsParsedXMLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestRoundsParsedXMLHandler_ValidError(t *testing.T) {
	roundSerials := []uint32{seeder.RoundInfoRoundSerial}
	roundSerialsJSON, jsonErr := json.Marshal(roundSerials)
	assert.NoError(t, jsonErr)

	query := urlutil.NewBuilder()
	query.AddString("round_serials", string(roundSerialsJSON))
	query.AddString("start_date", "test")
	query.AddString("end_date", seeder.EndDate)

	uri := fmt.Sprintf("%s?%s", apiRoundsParsedXML, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	RoundsParsedXMLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000005,"message":"Start date error"}`, w.Body.String())
}

func TestRoundsParsedXMLHandler(t *testing.T) {
	roundSerials := []uint32{seeder.RoundInfoRoundSerial}
	roundSerialsJSON, jsonErr := json.Marshal(roundSerials)
	assert.NoError(t, jsonErr)

	query := urlutil.NewBuilder()
	query.AddString("round_serials", string(roundSerialsJSON))
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)

	uri := fmt.Sprintf("%s?%s", apiRoundsParsedXML, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundXMLInfo", ctx, roundSerials, seeder.StartDate, seeder.EndDate).Return(seeder.RoundXMLInfo.GetRoundXmlInfo(), nil)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	RoundsParsedXMLHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"*********":{"banker_card":["C.2","C.6"],"banker_point":"8","player_card":["S.1","D.5"],"player_point":"6"}}}`, w.Body.String())
}

func TestRoundsParsedXMLHandler_LogicError(t *testing.T) {
	roundSerials := []uint32{seeder.RoundInfoRoundSerial}
	roundSerialsJSON, jsonErr := json.Marshal(roundSerials)
	assert.NoError(t, jsonErr)

	query := urlutil.NewBuilder()
	query.AddString("round_serials", string(roundSerialsJSON))
	query.AddString("start_date", seeder.StartDate)
	query.AddString("end_date", seeder.EndDate)

	uri := fmt.Sprintf("%s?%s", apiRoundsParsedXML, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundXMLInfo", ctx, roundSerials, seeder.StartDate, seeder.EndDate).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	RoundsParsedXMLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Connection failed"}`, w.Body.String())
}
