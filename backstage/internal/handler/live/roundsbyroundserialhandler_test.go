package live

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/strutil"
	"gbh/utils/urlutil"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestRoundsByRoundSerialHandler_WithoutParameterStateId(t *testing.T) {
	query := urlutil.NewBuilder()

	uri := fmt.Sprintf("%s?%s", apiRoundsByRoundSerial, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["roundSerial"] = "123"
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	RoundsByRoundSerialHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestRoundsByRoundSerialHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("state_id", "0")
	query.AddString("sort", "test")

	uri := fmt.Sprintf("%s?%s", apiRoundsByRoundSerial, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["roundSerial"] = "123"
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	RoundsByRoundSerialHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000009,"message":"Sort error"}`, w.Body.String())
}

func TestRoundsByRoundSerialHandler(t *testing.T) {
	stateId := uint32(0)
	roundSerial := uint32(123)
	query := urlutil.NewBuilder()
	query.AddUint32("state_id", stateId)

	uri := fmt.Sprintf("%s?%s", apiRoundsByRoundSerial, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["roundSerial"] = strutil.Uint32ToString(roundSerial)
	req = pathvar.WithVars(req, params)

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundInfoByRoundSerial", req.Context(), roundSerial, stateId, "").Return(seeder.RoundInfoByRoundSerial.GetRoundInfo(), nil)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	RoundsByRoundSerialHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"rounds":[{"datetime":"2025-01-01 00:00:08","round_serial":*********,"foreign_round_serial":"****************","round_no":"51-23","game_id":3001,"game_code":93,"round_xml":"bankerCast=C.2,C.6,\u0026playerCast=S.1,D.5,\u0026bankerPoint=8\u0026playerPoint=6","bet_amount":0,"payoff":0,"result_status":85,"parsed_xml":{"banker_card":["C.2","C.6"],"banker_point":"8","player_card":["S.1","D.5"],"player_point":"6"}}]}}`, w.Body.String())
}

func TestRoundsByRoundSerialHandler_LogicError(t *testing.T) {
	stateId := uint32(0)
	roundSerial := uint32(123)
	query := urlutil.NewBuilder()
	query.AddUint32("state_id", stateId)

	uri := fmt.Sprintf("%s?%s", apiRoundsByRoundSerial, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["roundSerial"] = strutil.Uint32ToString(roundSerial)
	req = pathvar.WithVars(req, params)

	liveGameCtx := mock.NewMockLiveGameCtx()

	liveGameCtx.On("RoundInfoByRoundSerial", req.Context(), roundSerial, stateId, "").Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	RoundsByRoundSerialHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":*********,"message":"Connection failed"}`, w.Body.String())
}
