package live

import (
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/strutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestGetTipSwitchByHallIdHandler(t *testing.T) {
	hallId := uint32(123)
	uri := fmt.Sprintf(apiGetTipSwitchByHallId, hallId)

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(hallId)
	req = pathvar.WithVars(req, params)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", req.Context(), game.GetGameDetailRequest{
		HallId: []uint32{hallId},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.GameCtx = gameCtx

	w := httptest.NewRecorder()
	GetTipSwitchByHallIdHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":true}`, w.Body.String())
}

func TestGetTipSwitchByHallIdHandler_LogicError(t *testing.T) {
	hallId := uint32(123)
	uri := fmt.Sprintf(apiGetTipSwitchByHallId, hallId)

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = strutil.Uint32ToString(hallId)
	req = pathvar.WithVars(req, params)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", req.Context(), game.GetGameDetailRequest{
		HallId: []uint32{hallId},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = gameCtx

	w := httptest.NewRecorder()
	GetTipSwitchByHallIdHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}

func TestGetTipSwitchByHallIdHandler_InvalidParameters(t *testing.T) {
	hallId := uint32(123)
	uri := fmt.Sprintf(apiGetTipSwitchByHallId, hallId)

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["hall_id"] = "test"
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	GetTipSwitchByHallIdHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}
