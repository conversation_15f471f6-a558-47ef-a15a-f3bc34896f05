package live

import (
	"fmt"
	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetTablesHandler(t *testing.T) {
	query := url.Values{}
	uri := fmt.Sprintf("%s?%s", apiLiveTables, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	liveGameCtx := mock.NewMockLiveGameCtx()

	mockRequest := livegame.GameCodeSettingRequest{}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(&seeder.DomainGameCodeSetting, nil)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	GetTablesHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"relation_id":1,"wagers_type":0,"state":0,"game_id":3001,"game_code":1,"dealer_type":1,"enable":true,"update_time":"2025-01-13T21:33:18-04:00"}]}`, w.Body.String())
}

func TestGetTablesHandler_LogicError(t *testing.T) {
	query := url.Values{}
	uri := fmt.Sprintf("%s?%s", apiLiveTables, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	liveGameCtx := mock.NewMockLiveGameCtx()
	mockRequest := livegame.GameCodeSettingRequest{}
	liveGameCtx.On("GetDomainGameCodeSetting", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.LiveGameCtx = liveGameCtx

	w := httptest.NewRecorder()
	GetTablesHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
