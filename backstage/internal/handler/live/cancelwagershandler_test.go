package live

import (
	"fmt"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCancelWagersHandler_WithoutRoundSerial(t *testing.T) {
	query := urlutil.NewBuilder()

	uri := fmt.Sprintf("%s?%s", apiLiveCancelWagers, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	CancelWagersHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestCancelWagersHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("round_serial", "1")

	uri := fmt.Sprintf("%s?%s", apiLiveCancelWagers, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	ctxRequest := &types.CancelWagersRequest{
		RoundSerial: 1,
	}

	mockLiveGameCtx := mock.NewMockLiveGameCtx()

	mockLiveGameCtx.On("CancelWagers", ctx, ctxRequest).Return(errorx.InvalidResponse)

	svcCtx.LiveGameCtx = mockLiveGameCtx

	w := httptest.NewRecorder()
	CancelWagersHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000004,"message":"Invalid response"}`, w.Body.String())
}

func TestCancelWagersHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("round_serial", "1")

	uri := fmt.Sprintf("%s?%s", apiLiveCancelWagers, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	ctxRequest := &types.CancelWagersRequest{
		RoundSerial: 1,
	}

	mockLiveGameCtx := mock.NewMockLiveGameCtx()

	mockLiveGameCtx.On("CancelWagers", ctx, ctxRequest).Return(nil)

	svcCtx.LiveGameCtx = mockLiveGameCtx

	w := httptest.NewRecorder()
	CancelWagersHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}

func TestCancelWagersHandler_WithWagersID(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("round_serial", "1")
	query.AddString("wagers_id", "1")

	uri := fmt.Sprintf("%s?%s", apiLiveCancelWagers, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	wagersId := uint64(1)
	ctxRequest := &types.CancelWagersRequest{
		RoundSerial: 1,
		WagersID:    &wagersId,
	}

	mockLiveGameCtx := mock.NewMockLiveGameCtx()

	mockLiveGameCtx.On("CancelWagers", ctx, ctxRequest).Return(nil)

	svcCtx.LiveGameCtx = mockLiveGameCtx

	w := httptest.NewRecorder()
	CancelWagersHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}
