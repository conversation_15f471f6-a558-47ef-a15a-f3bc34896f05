package slot

import (
	"encoding/json"
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/logic/slot"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/slotgame/slotgameclient"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCrushTimesRecordHandler_TheParametersAreNotComplete(t *testing.T) {
	withoutStartDateQuery := urlutil.NewBuilder()
	withoutStartDateQuery.AddString("end_date", "2025-01-01")
	withoutStartDateQuery.AddString("hall_id", "3820474")
	withoutStartDateQuery.AddString("game_id", "5247")
	withoutStartDateQuery.AddString("times", "5")
	withoutStartDateQuery.AddString("operator_symbol", "1")

	withoutEndDateQuery := urlutil.NewBuilder()
	withoutEndDateQuery.AddString("start_date", "2025-01-01")
	withoutEndDateQuery.AddString("hall_id", "3820474")
	withoutEndDateQuery.AddString("game_id", "5247")
	withoutEndDateQuery.AddString("times", "5")
	withoutEndDateQuery.AddString("operator_symbol", "1")

	withoutHallIDQuery := urlutil.NewBuilder()
	withoutHallIDQuery.AddString("start_date", "2025-01-01")
	withoutHallIDQuery.AddString("end_date", "2025-01-01")
	withoutHallIDQuery.AddString("game_id", "5247")
	withoutHallIDQuery.AddString("times", "5")
	withoutHallIDQuery.AddString("operator_symbol", "1")

	withoutGameIDQuery := urlutil.NewBuilder()
	withoutGameIDQuery.AddString("start_date", "2025-01-01")
	withoutGameIDQuery.AddString("end_date", "2025-01-01")
	withoutGameIDQuery.AddString("hall_id", "3820474")
	withoutGameIDQuery.AddString("times", "5")
	withoutGameIDQuery.AddString("operator_symbol", "1")

	withoutTimesQuery := urlutil.NewBuilder()
	withoutTimesQuery.AddString("start_date", "2025-01-01")
	withoutTimesQuery.AddString("end_date", "2025-01-01")
	withoutTimesQuery.AddString("hall_id", "3820474")
	withoutTimesQuery.AddString("game_id", "5247")
	withoutTimesQuery.AddString("operator_symbol", "1")

	withoutOperatorSymbolQuery := urlutil.NewBuilder()
	withoutOperatorSymbolQuery.AddString("start_date", "2025-01-01")
	withoutOperatorSymbolQuery.AddString("end_date", "2025-01-01")
	withoutOperatorSymbolQuery.AddString("hall_id", "3820474")
	withoutOperatorSymbolQuery.AddString("game_id", "5247")
	withoutOperatorSymbolQuery.AddString("times", "5")

	tests := []string{
		withoutStartDateQuery.Encode(),
		withoutEndDateQuery.Encode(),
		withoutHallIDQuery.Encode(),
		withoutGameIDQuery.Encode(),
		withoutTimesQuery.Encode(),
		withoutOperatorSymbolQuery.Encode(),
	}

	for _, tt := range tests {
		t.Run("InvalidParameters", func(t *testing.T) {
			uri := fmt.Sprintf("%s?%s", apiCrushTimesRecord, tt)

			req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			CrushTimesRecordHandler(svcCtx).ServeHTTP(w, req)

			assert.NoError(t, err)
			assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
		})
	}
}

func TestCrushTimesRecordHandler_InvalidParameters(t *testing.T) {
	invalidStartDateQuery := urlutil.NewBuilder()
	invalidStartDateQuery.AddString("start_date", "yo")
	invalidStartDateQuery.AddString("end_date", "2025-01-01")
	invalidStartDateQuery.AddString("hall_id", "3820474")
	invalidStartDateQuery.AddString("game_id", "5247")
	invalidStartDateQuery.AddString("times", "5")
	invalidStartDateQuery.AddString("operator_symbol", "1")

	invalidEndDateQuery := urlutil.NewBuilder()
	invalidEndDateQuery.AddString("start_date", "2025-01-01")
	invalidEndDateQuery.AddString("end_date", "yo")
	invalidEndDateQuery.AddString("hall_id", "3820474")
	invalidEndDateQuery.AddString("game_id", "5247")
	invalidEndDateQuery.AddString("times", "5")
	invalidEndDateQuery.AddString("operator_symbol", "1")

	startDateGraterThanEndQuery := urlutil.NewBuilder()
	startDateGraterThanEndQuery.AddString("start_date", "2025-01-02")
	startDateGraterThanEndQuery.AddString("end_date", "2025-01-01")
	startDateGraterThanEndQuery.AddString("hall_id", "3820474")
	startDateGraterThanEndQuery.AddString("game_id", "5247")
	startDateGraterThanEndQuery.AddString("times", "5")
	startDateGraterThanEndQuery.AddString("operator_symbol", "1")

	timesLessThanFiveQuery := urlutil.NewBuilder()
	timesLessThanFiveQuery.AddString("start_date", "2025-01-01")
	timesLessThanFiveQuery.AddString("end_date", "2025-01-01")
	timesLessThanFiveQuery.AddString("hall_id", "3820474")
	timesLessThanFiveQuery.AddString("game_id", "5247")
	timesLessThanFiveQuery.AddString("times", "4")
	timesLessThanFiveQuery.AddString("operator_symbol", "1")

	timesGreaterThanTwentyQuery := urlutil.NewBuilder()
	timesGreaterThanTwentyQuery.AddString("start_date", "2025-01-01")
	timesGreaterThanTwentyQuery.AddString("end_date", "2025-01-01")
	timesGreaterThanTwentyQuery.AddString("hall_id", "3820474")
	timesGreaterThanTwentyQuery.AddString("game_id", "5247")
	timesGreaterThanTwentyQuery.AddString("times", "21")
	timesGreaterThanTwentyQuery.AddString("operator_symbol", "1")

	invalidOperatorSymbolQuery := urlutil.NewBuilder()
	invalidOperatorSymbolQuery.AddString("start_date", "2025-01-01")
	invalidOperatorSymbolQuery.AddString("end_date", "2025-01-01")
	invalidOperatorSymbolQuery.AddString("hall_id", "3820474")
	invalidOperatorSymbolQuery.AddString("game_id", "5247")
	invalidOperatorSymbolQuery.AddString("times", "5")
	invalidOperatorSymbolQuery.AddString("operator_symbol", "3")

	invalidPageLimitQuery := urlutil.NewBuilder()
	invalidPageLimitQuery.AddString("start_date", "2025-01-01")
	invalidPageLimitQuery.AddString("end_date", "2025-01-01")
	invalidPageLimitQuery.AddString("hall_id", "3820474")
	invalidPageLimitQuery.AddString("game_id", "5247")
	invalidPageLimitQuery.AddString("times", "5")
	invalidPageLimitQuery.AddString("operator_symbol", "3")
	invalidPageLimitQuery.AddString("page_limit", "1")

	tests := []struct {
		name     string
		query    string
		expected string
	}{
		{
			name:     "invalidStartDate",
			query:    invalidStartDateQuery.Encode(),
			expected: `{"code":562000005,"message":"Start date error"}`,
		},
		{
			name:     "invalidEndDate",
			query:    invalidEndDateQuery.Encode(),
			expected: `{"code":562000006,"message":"End date error"}`,
		},
		{
			name:     "startDateGraterThanEnd",
			query:    startDateGraterThanEndQuery.Encode(),
			expected: `{"code":562000008,"message":"Start date exceeds end date"}`,
		},
		{
			name:     "timesLessThanFive",
			query:    timesLessThanFiveQuery.Encode(),
			expected: `{"code":562000004,"message":"Parameter validation error"}`,
		},
		{
			name:     "timesGreaterThanTwenty",
			query:    timesGreaterThanTwentyQuery.Encode(),
			expected: `{"code":562000004,"message":"Parameter validation error"}`,
		},
		{
			name:     "invalidOperatorSymbol",
			query:    invalidOperatorSymbolQuery.Encode(),
			expected: `{"code":562000004,"message":"Parameter validation error"}`,
		},
		{
			name:     "invalidPageLimit",
			query:    invalidPageLimitQuery.Encode(),
			expected: `{"code":562000004,"message":"Parameter validation error"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			uri := fmt.Sprintf("%s?%s", apiCrushTimesRecord, tt.query)

			req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
			assert.NoError(t, err)

			w := httptest.NewRecorder()
			CrushTimesRecordHandler(svcCtx).ServeHTTP(w, req)

			assert.NoError(t, err)
			assert.Equal(t, tt.expected, w.Body.String())
		})
	}
}

func TestCrushTimesRecordHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", "2025-01-01")
	query.AddString("end_date", "2025-01-01")
	query.AddString("hall_id", "3820474")
	query.AddString("game_id", "5247")
	query.AddString("times", "5")
	query.AddString("operator_symbol", "1")

	mockRequest := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallId:         3820474,
		GameId:         5247,
		Times:          5,
		OperatorSymbol: constants.SymbolEqual,
	}

	uri := fmt.Sprintf("%s?%s", apiCrushTimesRecord, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	mockSlotGameCtx := mock.NewMockSlotGameCtx()

	mockSlotGameCtx.On("GetCrushTimesRecord", ctx, mockRequest).Return(nil, errorx.ConnectionFailed)

	svcCtx.SlotGameCtx = mockSlotGameCtx

	w := httptest.NewRecorder()
	CrushTimesRecordHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}

func TestCrushTimesRecordHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("start_date", "2025-01-01")
	query.AddString("end_date", "2025-01-01")
	query.AddString("hall_id", "3820474")
	query.AddString("game_id", "5247")
	query.AddString("times", "5")
	query.AddString("operator_symbol", "1")

	mockRequest := &slotgameclient.GetCrushTimesRecordRequest{
		StartDate:      "2025-01-01",
		EndDate:        "2025-01-01",
		HallId:         3820474,
		GameId:         5247,
		Times:          5,
		OperatorSymbol: constants.SymbolEqual,
	}

	uri := fmt.Sprintf("%s?%s", apiCrushTimesRecord, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	mockSlotGameCtx := mock.NewMockSlotGameCtx()

	mockReturn := &slotgameclient.GetCrushTimesRecordResponse{
		CrushTimeRecord: []*slotgameclient.CrushTimesRecord{
			{
				WagersId:     5200007992458,
				GameId:       5247,
				HallId:       3820474,
				UserId:       456120535,
				Times:        5,
				RoundDate:    "2025-01-01",
				RoundTime:    "2025-01-01 00:00:00",
				ModifiedDate: "2025-01-01 00:00:00",
			},
		},
		Pagination: &slotgameclient.Pagination{
			CurrentPage: 1,
			PageLimit:   500,
			Total:       1,
			TotalPage:   1,
		},
	}

	mockSlotGameCtx.On("GetCrushTimesRecord", ctx, mockRequest).Return(mockReturn, nil)

	svcCtx.SlotGameCtx = mockSlotGameCtx

	w := httptest.NewRecorder()
	CrushTimesRecordHandler(svcCtx).ServeHTTP(w, req)

	expected := &types.BaseResponse{
		Data: slot.CrushTimesResponse{
			CrushTimesRecord: []slot.CrushTimes{
				{
					WagersID:     5200007992458,
					GameID:       5247,
					HallID:       3820474,
					UserID:       456120535,
					Times:        5,
					RoundDate:    "2025-01-01",
					RoundTime:    "2025-01-01 00:00:00",
					ModifiedDate: "2025-01-01 00:00:00",
				},
			},
			Pagination: types.PaginateResponse{
				Page:        1,
				PageLimit:   500,
				TotalNumber: 1,
				TotalPage:   1,
			},
		},
	}

	expectedBody, err := json.Marshal(expected)

	assert.NoError(t, err)
	assert.Equal(t, string(expectedBody), w.Body.String())
}
