package admin

import (
	"fmt"
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetPositionHandler(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiGetPosition, http.NoBody)
	assert.NoError(t, err)

	mockAdminRPC := mock.NewMockAdminCtx()

	mockAdminRPC.On("PositionList", ctx, admin.PositionListRequest{}).Return(&seeder.GetPositionList, nil)
	svcCtx.AdminCtx = mockAdminRPC

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"id":111,"name":"GM測試1","note":" ","created_at":"2019-06-17T11:34:08-04:00","count":1},{"id":192,"name":"GMMP新人專用","note":" ","created_at":"2020-10-18T07:51:01-04:00","count":10}],"Total":2}`, w.Body.String())
}

func TestGetPositionHandler_InvalidParameters(t *testing.T) {
	params := urlutil.NewBuilder()
	params.AddString("limit", "w")

	uri := fmt.Sprintf("%s?%s", apiGetPosition, params.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	mockAdminRPC := mock.NewMockAdminCtx()

	mockAdminRPC.On("PositionList", ctx, admin.PositionListRequest{}).Return(nil, errorx.ConnectionFailed)
	svcCtx.AdminCtx = mockAdminRPC

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetPositionHandler_LogicError(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiGetPosition, http.NoBody)
	assert.NoError(t, err)

	mockAdminRPC := mock.NewMockAdminCtx()

	mockAdminRPC.On("PositionList", ctx, admin.PositionListRequest{}).Return(nil, errorx.ConnectionFailed)
	svcCtx.AdminCtx = mockAdminRPC

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
