package admin

import (
	"fmt"
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminListHandler(t *testing.T) {
	query := url.Values{}

	uri := fmt.Sprintf("%s?%s", apiAdminList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	mockAdminRPC := mock.NewMockAdminCtx()

	mockGMUserListRequest := admin.GetGMUserListRequest{
		Username: "",
		Alias:    "",
		Enable:   nil,
		Sort:     "username",
		Order:    "asc",
	}
	mockAdminRPC.On("GetGMUserList", ctx, mockGMUserListRequest).Return(&seeder.GMUserList, nil)

	mockUsersDepartmentRequest := admin.GetUsersDepartmentRequest{
		Users:        seeder.GMUserList.GetUserList(),
		PositionId:   0,
		DepartmentId: 0,
		Page:         1,
		PageLimit:    0,
	}
	mockAdminRPC.On("GetUsersDepartment", ctx, mockUsersDepartmentRequest).Return(&seeder.UsersDepartment, nil)
	svcCtx.AdminCtx = mockAdminRPC

	w := httptest.NewRecorder()
	ListHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"users":[{"id":10169,"username":"admin","alias":"admin","block":false,"enable":true,"enable_ub_auth":false,"bind":false,"created_at":"2017-05-23T21:46:31-04:00","department_id":1,"department":"Department","position_id":1,"position":"Position"}],"total":1}}`, w.Body.String())
}

func TestAdminListHandler_InvalidParameter(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("department_id", "test")

	uri := fmt.Sprintf("%s?%s", apiAdminList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	ListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestAdminListHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page_limit", "200")

	uri := fmt.Sprintf("%s?%s", apiAdminList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	ListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000011,"message":"PageLimit error"}`, w.Body.String())
}

func TestAdminListHandler_LogicError(t *testing.T) {
	query := url.Values{}

	uri := fmt.Sprintf("%s?%s", apiAdminList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	mockAdminRPC := mock.NewMockAdminCtx()

	mockGMUserListRequest := admin.GetGMUserListRequest{
		Username: "",
		Alias:    "",
		Enable:   nil,
		Sort:     "username",
		Order:    "asc",
	}
	mockAdminRPC.On("GetGMUserList", ctx, mockGMUserListRequest).Return(nil, errorx.ConnectionFailed)
	svcCtx.AdminCtx = mockAdminRPC

	w := httptest.NewRecorder()
	ListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
