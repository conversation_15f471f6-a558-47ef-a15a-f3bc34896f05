package admin

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"

	"github.com/stretchr/testify/assert"
)

func TestDepartmentHandler_InvalidParameterType(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("page", "test")

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	DepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestDepartmentHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("sort", "test")

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	DepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000009,"message":"Sort error"}`, w.Body.String())
}

func TestDepartmentHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("sort", "id")
	query.AddString("order", "asc")
	query.AddString("department", "test")
	query.AddString("page", "1")
	query.AddString("page_limit", "1")

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	adminCtx := mock.NewMockAdminCtx()

	departmentListMockRequest := admin.DepartmentListRequest{
		Department: "test",
		Sort:       "id",
		Order:      "asc",
		Page:       1,
		PageLimit:  1,
	}

	adminCtx.On("DepartmentList", ctx, departmentListMockRequest).Return(&seeder.DepartmentList, nil)

	svcCtx.AdminCtx = adminCtx

	w := httptest.NewRecorder()
	DepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"departments":[{"id":2222,"name":"PD-BM","note":"控管端組","created_at":"2024-09-09T23:39:05-04:00","total_user":31},{"id":2211,"name":"PIDunitTEST","note":"","created_at":"2021-10-25T15:10:43-04:00","total_user":1}],"pagination":{"page":0,"page_limit":0,"total_number":2,"total_page":0}}}`, w.Body.String())
}

func TestDepartmentHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	adminCtx := mock.NewMockAdminCtx()

	adminCtx.On("DepartmentList", ctx, admin.DepartmentListRequest{}).Return(nil, errorx.ConnectionFailed)

	svcCtx.AdminCtx = adminCtx

	w := httptest.NewRecorder()
	DepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
