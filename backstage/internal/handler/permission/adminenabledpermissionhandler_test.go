package permission

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminenabledPermissionHandler(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiAdminEnabledPermission, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminEnabledPermission", req.Context()).Return(&seeder.AdminPermissionEnabled, nil)
	svcCtx.PermissionCtx = permissionCtx
	AdminEnabledPermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"menu":[{"id":2,"type":"Category","name":"Category_15","note":"客服系統","dict":"service_system","rd3_dict":"_SERVICE_SYSTEM","modify":true,"old_perm":true,"child":true,"enable":true}],"special":[{"id":378,"type":"SpecialCategory","name":"Other_Information","weight":255,"note":"其他設定","dict":"group_other","child":true,"enable":true}],"general":[{"id":172,"type":"Privilege","name":"FReportNoDateLimit","weight":17,"note":"快速報表日期不受關帳日限制","rd3_dict":"快速報表日期不受關帳日限制","modify":true,"old_perm":true,"child":true,"enable":true}]}}`, w.Body.String())
}

func TestAdminenabledPermissionHandler_LogicError(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiAdminEnabledPermission, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminEnabledPermission", req.Context()).Return(nil, errorx.DatabaseError)
	svcCtx.PermissionCtx = permissionCtx
	AdminEnabledPermissionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000014,"message":"Database error"}`, w.Body.String())
}
