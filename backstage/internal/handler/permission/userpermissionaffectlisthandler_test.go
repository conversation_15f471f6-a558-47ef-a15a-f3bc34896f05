package permission

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUserPermissionAffectListHandler(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiUserPermissionAffectList, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetUserPermissionAffectList", req.Context()).Return(seeder.UserPermissionAffectList.GetList(), nil)

	svcCtx.PermissionCtx = permissionCtx

	UserPermissionAffectListHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"11":{"data":{"menu":{"401":{"id":401,"type":"Category","name":"Menu_401","dict":"account_management","child":[{"id":6,"parent":401,"type":"Menu","name":"Menu_6","dict":"account_list","child":[{"id":11,"dict":"perm_affect_note_90","affect_id":6,"note":"帳號-會員詳細資料-BB現金系統","sort":1},{"id":11,"dict":"perm_affect_note_70","affect_id":6,"note":"i-BB現金系統","sort":2},{"id":11,"dict":"perm_affect_note_69","affect_id":6,"note":"i-會員詳細資料-BB現金系統","sort":3}]}]},"402":{"id":402,"type":"Category","name":"Menu_402","dict":"cash_system","child":[{"id":11,"parent":402,"type":"Menu","name":"CashSystem","dict":"bb_cash_system"}]}}},"count":8}}}`, w.Body.String())
}

func TestUserPermissionAffectList_LogicError(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiUserPermissionAffectList, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetUserPermissionAffectList", req.Context()).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	UserPermissionAffectListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
