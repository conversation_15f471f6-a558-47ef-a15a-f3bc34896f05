package permission

import (
	"fmt"
	"gbh/admin/adminclient"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminSubAccountOpenablePermissionHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("admin_id", seeder.AdminID)
	query.AddUint32("user_id", seeder.HallId)
	query.AddString("permission_category", `["menu"]`)

	uri := fmt.Sprintf("%s?%s", apiAdminEnabledPermission, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", req.Context(), seeder.HallId).Return(&seeder.User, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", req.Context(), seeder.HallId).Return(seeder.HallList.GetData()[0], nil)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{
		HallId: []uint32{},
	}, nil)

	permissionCtx := mock.NewMockPermissionCtx()

	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	permissionCtx.On("GetAllPermConfigMap", req.Context()).Return(userPermissionConfigMap, nil)

	operator := types.UserInfo{
		Id:           seeder.HallId,
		Domain:       seeder.HallId,
		ParentId:     0,
		AllParentsId: nil,
		Role:         constants.Role7,
		IsSub:        false,
	}

	mockFilterUserPermission := permission.Result{
		Enable: seeder.HallEnablePermission,
		Modify: seeder.HallEnablePermission,
	}
	permissionCtx.On("FilterUserPermission", req.Context(), userPermissionConfigMap, operator, []uint32(nil)).Return(mockFilterUserPermission, nil)
	permissionCtx.On("FilterUserPermission", req.Context(), userPermissionConfigMap, operator, []uint32{seeder.UserPermID1471}).Return(mockFilterUserPermission, nil)
	permissionCtx.On("CheckUserPermission", req.Context(), operator, seeder.UserPermID1471, "enable").Return(true, nil).Once()
	permissionCtx.On("CheckUserPermission", req.Context(), operator, seeder.UserPermID1471, "modify").Return(true, nil).Once()
	permissionCtx.On("GetPermissionConfigById", req.Context(), seeder.UserPermID1518).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1518].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1518].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", req.Context(), seeder.UserPermID400).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID400].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID400].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", req.Context(), seeder.UserPermID674).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID674].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID674].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", req.Context(), seeder.UserPermID1402).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1402].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1402].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", req.Context(), seeder.UserPermID1471).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID1471].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID1471].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", req.Context(), seeder.UserPermID2038).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID2038].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID2038].Extra,
	})
	permissionCtx.On("GetPermissionConfigById", req.Context(), seeder.UserPermID405).Return(&types.UserPermissionConfig{
		ParentId: userPermissionConfigMap[seeder.UserPermID405].ParentId,
		Extra:    userPermissionConfigMap[seeder.UserPermID405].Extra,
	})

	gameCtx := mock.NewMockGameCtx()

	mockGetGameDetailRequest := game.GetGameDetailRequest{
		Id:     []uint32{constants.GameDetailID1},
		HallId: []uint32{seeder.HallId},
	}
	gameCtx.On("GetGameDetail", req.Context(), mockGetGameDetailRequest).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.UserCtx = userCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.AdminCtx = adminCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.GameCtx = gameCtx

	AdminSubAccountOpenablePermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	//nolint:misspell // game_managment 原邏輯拼字錯誤，暫不修正
	assert.JSONEq(t, `{"code":0,"message":"","data":{"menu":[{"permission_id":400,"parent_id":0,"type":"Category","name":"Menu_400","note":"遊戲管理","dict":"game_managment","rd3_dict":"_GAME_MANAGEMENT","enable":true,"sort":0,"domain_only":false,"top_sub_domain":false,"open_new":false,"strict":true,"modifiable":true,"old_perm":true,"hierarchy_perm":false,"maintenance":false,"extra":{"icon":"mdi-gamepad-square","file":"","host":"","qstr":"","route":"","conditions":"","api_name":"","api_type":""},"role_perm":[{"role_id":2,"view":true,"modify":true},{"role_id":3,"view":true,"modify":true},{"role_id":4,"view":true,"modify":true},{"role_id":5,"view":true,"modify":true},{"role_id":7,"view":true,"modify":true},{"role_id":12,"view":true,"modify":true},{"role_id":13,"view":true,"modify":true},{"role_id":14,"view":true,"modify":true},{"role_id":15,"view":true,"modify":true},{"role_id":17,"view":true,"modify":true}],"role_settable":[7,17],"parent_modify":true,"children":[{"permission_id":1471,"parent_id":400,"type":"Menu","name":"GamePlatform","note":"遊戲平台管理(PID)","dict":"gamelobby_platform_management","rd3_dict":"_GAMELOBBY_PLATFORM_MANAGEMENT","enable":true,"sort":6,"domain_only":false,"top_sub_domain":false,"open_new":false,"strict":true,"modifiable":true,"old_perm":true,"hierarchy_perm":false,"maintenance":false,"extra":{"icon":"","file":"/system_setting/game/game_platform","host":"RD6_HOST","qstr":"","route":"","conditions":"","api_name":"","api_type":""},"role_perm":[],"role_settable":[7,17],"parent_modify":true,"children":[{"permission_id":2038,"parent_id":1471,"type":"Privilege","name":"GameMaintenanceInfo","note":"遊戲維護資訊","dict":"gamelobby_maintenance_management","rd3_dict":"","enable":true,"sort":2,"domain_only":false,"top_sub_domain":false,"open_new":true,"strict":true,"modifiable":true,"old_perm":true,"hierarchy_perm":false,"maintenance":false,"extra":{"icon":"","file":"","host":"","qstr":"","route":"","conditions":"","api_name":"","api_type":""},"role_perm":[{"role_id":2,"view":true,"modify":true},{"role_id":3,"view":true,"modify":true},{"role_id":4,"view":true,"modify":true},{"role_id":5,"view":true,"modify":true}],"role_settable":[7,17],"parent_modify":true,"children":null}]}]},{"permission_id":405,"parent_id":0,"type":"Category","name":"Menu_405","note":"報表/查詢","dict":"report_and_search","rd3_dict":"_REPORT_AND_SEARCH","enable":true,"sort":2,"domain_only":false,"top_sub_domain":false,"open_new":false,"strict":false,"modifiable":true,"old_perm":true,"hierarchy_perm":false,"maintenance":false,"extra":{"icon":"mdi-file","file":"","host":"","qstr":"","route":"","conditions":"","api_name":"","api_type":""},"role_perm":[{"role_id":2,"view":true,"modify":true},{"role_id":3,"view":true,"modify":true},{"role_id":4,"view":true,"modify":true},{"role_id":5,"view":true,"modify":true},{"role_id":7,"view":true,"modify":true},{"role_id":12,"view":true,"modify":true},{"role_id":13,"view":true,"modify":true},{"role_id":14,"view":true,"modify":true},{"role_id":15,"view":true,"modify":true},{"role_id":17,"view":true,"modify":true}],"role_settable":[7,17],"parent_modify":true,"children":[{"permission_id":1402,"parent_id":405,"type":"Menu","name":"BBTip","note":"BB小費(RD3)","dict":"lobby99","rd3_dict":"_GAME_KIND_99","enable":true,"sort":3,"domain_only":true,"top_sub_domain":false,"open_new":false,"strict":true,"modifiable":true,"old_perm":true,"hierarchy_perm":false,"maintenance":false,"extra":{"icon":"","file":"/game/live_tip","host":"RD3_HOST","qstr":"","route":"","conditions":"BBTip","api_name":"","api_type":""},"role_perm":[],"role_settable":[7,17],"parent_modify":true,"children":null}]}]}}`, w.Body.String())
}

func TestAdminSubAccountOpenablePermissionHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("admin_id", seeder.AdminID)
	query.AddUint32("user_id", seeder.HallId)
	query.AddString("permission_category", `["menu"]`)

	uri := fmt.Sprintf("%s?%s", apiAdminEnabledPermission, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", req.Context(), seeder.HallId).Return(nil, errorx.ConnectionFailed)

	svcCtx.UserCtx = userCtx
	AdminSubAccountOpenablePermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":*********,"message":"Connection failed"}`, w.Body.String())
}

func TestAdminSubAccountOpenablePermissionHandler_AdminID_InvalidParameter(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("user_id", seeder.HallId)
	query.AddString("permission_category", `["menu"]`)

	uri := fmt.Sprintf("%s?%s", apiAdminEnabledPermission, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	AdminSubAccountOpenablePermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestAdminSubAccountOpenablePermissionHandler_UserID_InvalidParameter(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("admin_id", seeder.AdminID)
	query.AddString("permission_category", `["menu"]`)

	uri := fmt.Sprintf("%s?%s", apiAdminEnabledPermission, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	AdminSubAccountOpenablePermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":*********,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestAdminSubAccountOpenablePermissionHandler_CategoroyValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("admin_id", seeder.AdminID)
	query.AddUint32("user_id", seeder.HallId)
	query.AddString("permission_category", `["test"]`)

	uri := fmt.Sprintf("%s?%s", apiAdminEnabledPermission, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	AdminSubAccountOpenablePermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":*********,"message":"Parameter validation error"}`, w.Body.String())
}

func TestAdminSubAccountOpenablePermissionHandler_AdminValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("admin_id", "0")
	query.AddUint32("user_id", seeder.HallId)
	query.AddString("permission_category", `["menu"]`)

	uri := fmt.Sprintf("%s?%s", apiAdminEnabledPermission, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)

	w := httptest.NewRecorder()
	assert.NoError(t, err)

	AdminSubAccountOpenablePermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":*********,"message":"Parameter validation error"}`, w.Body.String())
}
