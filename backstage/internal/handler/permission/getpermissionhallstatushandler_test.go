package permission

import (
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/permission/permissionclient"
	"gbh/utils/strutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestGetPermissionHallStatusHandler(t *testing.T) {
	uri := fmt.Sprintf(apiGetPermissionHallStatus, seeder.UserPermID1402)

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["permissionId"] = strutil.Uint32ToString(seeder.UserPermID1402)
	req = pathvar.WithVars(req, params)

	permissionCtx := mock.NewMockPermissionCtx()

	allPermConfig := seeder.UserPermissionConfigList.GetPermissionConfig()

	indicateName := make([]*permissionclient.UserPermissionConfig, 0, 1)
	indicateName = append(indicateName, allPermConfig[3])

	enable := true
	permissionCtx.On("GetUserPermissionConfigList", req.Context(), permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		Enable:       &enable,
	}).Return(indicateName, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallList", req.Context(), true).Return(&seeder.HallList, nil)

	allPermConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	getPermissionParentsResp := permission.GetPermissionParentsRecursive(allPermConfigMap, seeder.UserPermID1402)

	permissionCtx.On("GetPermissionParents", req.Context(), seeder.UserPermID1402).Return(getPermissionParentsResp, nil)

	permissionCtx.On("UserPermList", req.Context(), permission.UserPermListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		RoleId:       []uint32{0},
		UserId:       []uint32{seeder.HallId},
	}).Return(&seeder.UserPermList, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", req.Context(), seeder.HallId).Return(&seeder.User, nil)

	gameCtx := mock.NewMockGameCtx()
	gameCtx.On("GetGameDetail", req.Context(), game.GetGameDetailRequest{
		HallId: []uint32{seeder.HallId},
		Id:     []uint32{constants.GameDetailID1},
	}).Return(seeder.GameDetail.GetGameDetail(), nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.GameCtx = gameCtx
	svcCtx.UserCtx = userCtx

	w := httptest.NewRecorder()
	GetPermissionHallStatusHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"3820474":{"id":3820474,"enable":true,"modify":true}}}`, w.Body.String())
}

func TestGetPermissionHallStatusHandler_LogicErr(t *testing.T) {
	uri := fmt.Sprintf(apiGetPermissionHallStatus, seeder.UserPermID1402)

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	params := make(map[string]string)
	params["permissionId"] = strutil.Uint32ToString(seeder.UserPermID1402)
	req = pathvar.WithVars(req, params)

	permissionCtx := mock.NewMockPermissionCtx()

	enable := true
	permissionCtx.On("GetUserPermissionConfigList", req.Context(), permission.UserPermissionConfigListRequest{
		PermissionId: []uint32{seeder.UserPermID1402},
		Enable:       &enable,
	}).Return(nil, errorx.ConnectionFailed)

	svcCtx.PermissionCtx = permissionCtx

	w := httptest.NewRecorder()
	GetPermissionHallStatusHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}
