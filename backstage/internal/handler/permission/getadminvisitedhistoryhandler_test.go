package permission

import (
	"bytes"
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetAdminVisitedHistoryHandler(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiGetAdminVisitedHistory, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminVisitedHistory", ctx, seeder.GetAdminSession.GetUser().GetId()).Return(seeder.AdminVisitedHistoryResponse.GetVisitedHistoryDetail(), nil)

	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetWeblateLang", ctx, constants.ZhCn, "layout").Return(&seeder.GetWeblateLayoutZhCn, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.LangCtx = langCtx

	w := httptest.NewRecorder()
	GetAdminVisitedHistoryHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"id":17,"dict":"search_record","translation":"纪录文件查询","is_ai":true,"is_new":false},{"id":22,"dict":"temporary_password","translation":"帳号临时密码","is_ai":true,"is_new":false}]}`, w.Body.String())
}

func TestGetAdminVisitedHistoryHandler_WithParameterLang(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("lang", constants.ZhTw)

	uri := fmt.Sprintf("%s?%s", apiGetAdminVisitedHistory, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("GetAdminVisitedHistory", ctx, seeder.GetAdminSession.GetUser().GetId()).Return(seeder.AdminVisitedHistoryResponse.GetVisitedHistoryDetail(), nil)

	langCtx := mock.NewMockLangCtx()
	langCtx.On("GetWeblateLang", ctx, constants.ZhTw, "layout").Return(&seeder.GetWeblateLayoutZhTw, nil)

	svcCtx.PermissionCtx = permissionCtx
	svcCtx.LangCtx = langCtx

	w := httptest.NewRecorder()
	GetAdminVisitedHistoryHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"id":17,"dict":"search_record","translation":"紀錄文件查詢","is_ai":true,"is_new":false},{"id":22,"dict":"temporary_password","translation":"帳號臨時密碼","is_ai":true,"is_new":false}]}`, w.Body.String())
}

func TestGetAdminVisitedHistoryHandler_LogicError(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiGetAdminVisitedHistory, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetAdminVisitedHistoryHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}

func TestGetAdminVisitedHistoryHandler_InvalidParamters(t *testing.T) {
	invalidJSON := `{"lang": "valid",`
	req := httptest.NewRequest(http.MethodGet, apiGetAdminVisitedHistory, bytes.NewReader([]byte(invalidJSON)))

	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()
	GetAdminVisitedHistoryHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}
