package permission

import (
	"fmt"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminControllerPermissionHandler_WithoutParameterControllerName(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiAdminControllerPermission, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	AdminControllerPermissionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestAdminControllerPermissionHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("controller_name", seeder.AdminControllerName)

	uri := fmt.Sprintf("%s?%s", apiAdminControllerPermission, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	req = req.WithContext(ctx)

	permissionCtx := mock.NewMockPermissionCtx()
	controllerPermissionID := seeder.AdminControllerPermissionIDs.GetPermissionId()
	permissionCtx.On("AdminControllerPermissionIDs", ctx, seeder.AdminControllerName).Return(controllerPermissionID, nil)

	enable := true
	adminPermissionsMockRequest := permission.AdminPermissionsRequest{
		AdminID:      []uint32{seeder.GetAdminSession.GetUser().GetId()},
		PermissionID: controllerPermissionID,
		Enable:       &enable,
	}
	adminPermissions := seeder.AdminPermissions.GetPermissionInfo()
	permissionCtx.On("AdminPermissions", ctx, adminPermissionsMockRequest).Return(adminPermissions, nil)

	permissionID := []uint32{}
	for _, value := range adminPermissions {
		permissionID = append(permissionID, value.GetPermissionId())
	}
	adminPermissionInfoListMockRequest := permission.AdminPermissionInfoListRequest{
		PermissionID: permissionID,
	}
	permissionCtx.On("AdminPermissionInfoList", ctx, adminPermissionInfoListMockRequest).Return(seeder.AdminPermissionInfoList.GetPermissionInfo(), nil)

	svcCtx.PermissionCtx = permissionCtx

	w := httptest.NewRecorder()
	AdminControllerPermissionHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}

func TestAdminControllerPermissionHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("controller_name", seeder.AdminControllerName)

	uri := fmt.Sprintf("%s?%s", apiAdminControllerPermission, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	AdminControllerPermissionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}
