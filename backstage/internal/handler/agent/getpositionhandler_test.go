package agent

import (
	"fmt"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetPositionHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)

	uri := fmt.Sprintf("%s?%s", apiGetPosition, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req = req.WithContext(ctx)
	assert.NoError(t, err)

	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(seeder.HallList.GetData()[0], nil)

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID: seeder.HallId,
	}).Return(&seeder.AgentPositionList, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.HallCtx = mockHallCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"list":[{"id":5164,"name":"test","note":"","created_at":"2024-12-17 23:04:56","count":0,"owner_id":3820474,"parent_name":"bbinbgp"}],"pagination":{"page":1,"page_limit":1,"total_number":1,"total_page":1}}}`, w.Body.String())
}

func TestGetPositionHandler_WithoutParameterHallID(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("session_id", seeder.Token)

	uri := fmt.Sprintf("%s?%s", apiGetPosition, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetPositionHandler_ValidParameterOrder(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)
	query.AddString("order", "test")

	uri := fmt.Sprintf("%s?%s", apiGetPosition, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000014,"message":"Order error"}`, w.Body.String())
}

func TestGetPositionHandler_InvalidParameterPage(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)
	query.AddString("page", "test")

	uri := fmt.Sprintf("%s?%s", apiGetPosition, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetPositionHandler_InvalidParameterPageLimit(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)
	query.AddString("page_limit", "test")

	uri := fmt.Sprintf("%s?%s", apiGetPosition, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetPositionHandler_InvalidParameterFuzzy(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)
	query.AddString("fuzzy", "test")

	uri := fmt.Sprintf("%s?%s", apiGetPosition, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req.RemoteAddr = seeder.ClientIP
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestGetPositionHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)

	uri := fmt.Sprintf("%s?%s", apiGetPosition, query.Encode())
	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetPositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}
