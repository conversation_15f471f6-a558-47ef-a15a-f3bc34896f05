package agent

import (
	"bytes"
	"encoding/json"
	"gbh/admin/adminclient"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestUpdateDepartmentHandler_WithoutParameterDepartmentName(t *testing.T) {
	req, err := http.NewRequest(http.MethodPut, apiUpdateDepartment, http.NoBody)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	params := make(map[string]string)
	params["departmentID"] = "5530"
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	UpdateDepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestUpdateDepartmentHandler(t *testing.T) {
	requestBody := map[string]interface{}{
		"department_name": "gtest0328",
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req, err := http.NewRequest(http.MethodPut, apiUpdateDepartment, bytes.NewBuffer(requestBodyBytes))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	params := make(map[string]string)
	params["departmentID"] = "5530"
	req = req.WithContext(ctx)
	req = pathvar.WithVars(req, params)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetSession", seeder.Token, req.RemoteAddr).Return(&seeder.GetAdminSession, nil)
	adminCtx.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", req.Context(), &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.DepartmentParameterSetID5530},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", req.Context(), agent.DepartmentInfoListRequest{
		ParameterID: seeder.DepartmentParameterSetID5530,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("DepartmentInfoList", req.Context(), agent.DepartmentInfoListRequest{
		OwnerID: seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId(),
		Name:    requestBody["department_name"].(string),
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("UpdateDepartment", req.Context(), agent.UpdateDepartmentRequest{
		ID:   seeder.DepartmentParameterSetID5530,
		Name: requestBody["department_name"].(string),
		Note: "",
	}).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", req.Context(), seeder.HallId).Return(&seeder.GetHallById, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx

	w := httptest.NewRecorder()
	UpdateDepartmentHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}

func TestUpdateDepartmentHandler_LogicError(t *testing.T) {
	requestBody := map[string]interface{}{
		"department_name": "gtest0328",
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req, err := http.NewRequest(http.MethodPut, apiUpdateDepartment, bytes.NewBuffer(requestBodyBytes))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	params := make(map[string]string)
	params["departmentID"] = "5530"
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	UpdateDepartmentHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}
