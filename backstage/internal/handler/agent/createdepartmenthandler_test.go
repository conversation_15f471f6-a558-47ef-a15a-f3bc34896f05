package agent

import (
	"bytes"
	"encoding/json"
	"html"
	"net/http"
	"net/http/httptest"
	"testing"

	"gbh/admin/adminclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"

	"github.com/stretchr/testify/assert"
)

func TestCreateDepartmentHandler_WithoutParameterHallID(t *testing.T) {
	requestBody := map[string]interface{}{
		"department_name": "test",
		"parent_id":       seeder.HallId,
	}
	requestBodyBytes, err := json.<PERSON>(requestBody)
	assert.NoError(t, err)

	req, err := http.NewRequest(http.MethodPost, apiCreateDepartment, bytes.NewBuffer(requestBodyBytes))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreateDepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestCreateDepartmentHandler_WithoutParameterDepartmentName(t *testing.T) {
	requestBody := map[string]interface{}{
		"hall_id":   seeder.HallId,
		"parent_id": seeder.HallId,
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req, err := http.NewRequest(http.MethodPost, apiCreateDepartment, bytes.NewBuffer(requestBodyBytes))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreateDepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestCreateDepartmentHandler_ValidError(t *testing.T) {
	requestBody := map[string]interface{}{
		"hall_id":         seeder.HallId,
		"department_name": "test",
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req, err := http.NewRequest(http.MethodPost, apiCreateDepartment, bytes.NewBuffer(requestBodyBytes))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreateDepartmentHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000031,"message":"Both ParentID and ParentName not found"}`, w.Body.String())
}

func TestCreateDepartmentHandler(t *testing.T) {
	requestBody := map[string]interface{}{
		"hall_id":         seeder.HallId,
		"department_name": "test",
		"parent_id":       seeder.HallId,
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiCreateDepartment, bytes.NewBuffer(requestBodyBytes))

	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", ctx, requestBody["parent_id"]).Return(&seeder.User, nil)

	permissionCtx := mock.NewMockPermissionCtx()
	permissionCtx.On("CheckUserPermission", ctx, types.UserInfo{
		Id:           seeder.User.GetId(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}, constants.UserPermID1605, "enable").Return(true, nil)
	permissionCtx.On("GetPermissionConfigById", ctx, constants.UserPermID1605).Return(&types.UserPermissionConfig{})

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("HasDepartment", ctx, agent.HasDepartmentRequest{
		HallID:  requestBody["hall_id"].(uint32),
		Role:    seeder.User.GetRole(),
		OwnerID: requestBody["parent_id"].(uint32),
		Name:    requestBody["department_name"].(string),
	}).Return(nil)
	agentCtx.On("CreateDepartment", ctx, agent.CreateDepartmentRequest{
		HallID:  requestBody["hall_id"].(uint32),
		Role:    seeder.User.GetRole(),
		OwnerID: requestBody["parent_id"].(uint32),
		Name:    requestBody["department_name"].(string),
	}).Return(nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       requestBody["hall_id"].(uint32),
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Add_Department",
		UserKey:      "R_Add_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "name",
				Value: html.EscapeString(requestBody["department_name"].(string)),
			},
			{
				Key:   "note",
				Value: "",
			},
		},
		URI: req.RequestURI,
		IP:  seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.PermissionCtx = permissionCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	w := httptest.NewRecorder()
	CreateDepartmentHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}

func TestCreateDepartmentHandler_LogicError(t *testing.T) {
	requestBody := map[string]interface{}{
		"hall_id":         seeder.HallId,
		"department_name": "test",
		"parent_id":       seeder.HallId,
	}
	requestBodyBytes, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req, err := http.NewRequest(http.MethodPost, apiCreateDepartment, bytes.NewBuffer(requestBodyBytes))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreateDepartmentHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}
