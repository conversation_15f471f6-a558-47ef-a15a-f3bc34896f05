package agent

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"gbh/admin/adminclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/utils/urlutil"

	"github.com/stretchr/testify/assert"
)

func TestDepartmentListHandler_WithoutParameterHallID(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("role", constants.Role7)

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	DepartmentListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestDepartmentListHandler_WithoutParameterRole(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	DepartmentListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestDepartmentListHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)
	query.AddUint32("role", constants.Role7)
	query.AddString("order", "test")

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	DepartmentListHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000014,"message":"Order error"}`, w.Body.String())
}

func TestDepartmentListHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)
	query.AddUint32("role", constants.Role7)

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	req = req.WithContext(ctx)
	assert.NoError(t, err)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	parentIDs := make([]uint32, 0, len(seeder.DepartmentInfoList.GetList()))
	for _, v := range seeder.DepartmentInfoList.GetList() {
		parentIDs = append(parentIDs, v.GetOwnerId())
	}
	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUsersUsername", ctx, parentIDs).Return(map[uint32]string{
		seeder.HallId: seeder.User.GetUsername(),
	}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("DepartmentInfoList", ctx, agent.DepartmentInfoListRequest{
		HallID: seeder.HallId,
		Role:   constants.Role7,
		Fuzzy:  true,
	}).Return(&seeder.DepartmentInfoList, nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.AgentCtx = agentCtx

	w := httptest.NewRecorder()
	DepartmentListHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":{"departments":[{"id":5514,"department_name":"gtit","note":"gtit","parent_id":3820474,"parent_name":"bbinbgp","total_user":0,"created_at":"2024-12-17T04:14:50-04:00"},{"id":5168,"department_name":"test","note":"test","parent_id":3820474,"parent_name":"bbinbgp","total_user":45,"created_at":"2021-03-04T02:56:52-04:00"}],"pagination":{"page":1,"page_limit":2,"total_number":2,"total_page":1}}}`, w.Body.String())
}

func TestDepartmentListHandler_LoginErr(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddUint32("hall_id", seeder.HallId)
	query.AddUint32("role", constants.Role7)

	uri := fmt.Sprintf("%s?%s", apiDepartmentList, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	DepartmentListHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}
