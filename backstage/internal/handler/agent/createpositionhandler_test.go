package agent

import (
	"bytes"
	"encoding/json"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCreatePositionHandler(t *testing.T) {
	mockHallCtx := mock.NewMockHallCtx()
	mockHallCtx.On("GetHallById", ctx, seeder.HallId).Return(&seeder.GetHallById, nil)

	mockAdminCtx := mock.NewMockAdminCtx()
	mockAdminCtx.On("GetGMHallPrivilege", ctx, seeder.AdminID).Return(&seeder.GMHallPrivilege, nil)

	mockAgentCtx := mock.NewMockAgentCtx()
	mockAgentCtx.On("GetPositionList", ctx, agent.GetPositionListRequest{
		HallID:   seeder.HallId,
		Position: "test",
		Fuzzy:    false,
	}).Return(&agentclient.PositionListResponse{}, nil)

	mockUserCtx := mock.NewMockUserCtx()
	mockUserCtx.On("GetUserByUserId", ctx, seeder.HallId).Return(&seeder.User, nil)

	mockPermissionCtx := mock.NewMockPermissionCtx()
	userPermissionConfigMap := permission.TransferUserPermissionConfig(seeder.UserPermissionConfigList.GetPermissionConfig())
	mockPermissionCtx.On("GetAllPermConfigMap", ctx).Return(userPermissionConfigMap, nil)

	mockLangCtx := mock.NewMockLangCtx()
	mockLangCtx.On("GetWeblateLang", ctx, constants.ZhCn, "agent_menu").Return(&seeder.GetWeblateAgentMenuZhCn, nil)

	operator := types.UserInfo{
		Id:           seeder.User.GetId(),
		Domain:       seeder.User.GetDomain(),
		ParentId:     seeder.User.GetParent(),
		AllParentsId: seeder.User.GetAllParents(),
		Role:         seeder.User.GetRole(),
		IsSub:        seeder.User.GetSub(),
	}
	mockPermissionCtx.On("CheckUserPermission", ctx, operator, seeder.UserPermID2038, constants.Enable).Return(true, nil)

	mockAgentCtx.On("CreatePosition", ctx, agent.CreatePositionRequest{
		HallID: seeder.HallId,
		Name:   "test",
		Note:   "test",
		Permissions: []agent.PositionPermission{
			{
				ID:     seeder.UserPermID2038,
				Modify: true,
			},
		},
	}).Return(nil)

	mockOperationRecordCtx := mock.NewMockOperationRecordCtx()
	mockOperationRecordCtx.On("CreateAGRecord", ctx, operationrecord.CreateAGRecordRequest{
		HallId:       seeder.HallId,
		OperatorId:   seeder.AdminID,
		LevelId:      0,
		ActionId:     constants.ActionId76,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      constants.AGRecordMainKey["boss"][18],
		UserKey:      "",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "domain",
				Value: seeder.User.GetName(),
			},
			{
				Key:   "name",
				Value: "test",
			},
			{
				Key:   "note",
				Value: "test",
			},
			{
				Key:   "menuperm",
				Value: "<b>- 一般权限 -</b><br>游戏管理->游戏平台管理->游戏维护资讯: <span style='color:blue'>可检视/修改</span><br>",
			},
			{
				Key:   "specialperm",
				Value: "",
			},
		},
		XMLCancelTag: false,
		URI:          apiGetPosition,
		IP:           seeder.ClientIP,
	}).Return(nil)

	svcCtx.HallCtx = mockHallCtx
	svcCtx.AdminCtx = mockAdminCtx
	svcCtx.AgentCtx = mockAgentCtx
	svcCtx.UserCtx = mockUserCtx
	svcCtx.PermissionCtx = mockPermissionCtx
	svcCtx.LangCtx = mockLangCtx
	svcCtx.OperationRecordCtx = mockOperationRecordCtx

	param := map[string]interface{}{
		"hall_id": seeder.HallId,
		"name":    "test",
		"note":    "test",
		"permissions": map[string]interface{}{
			"menu": []map[string]interface{}{
				{
					"id":     seeder.UserPermID2038,
					"name":   "游戏维护资讯",
					"enable": true,
					"modify": true,
				},
			},
			"special": []map[string]interface{}{},
		},
	}

	body, err := json.Marshal(param)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	req, err := http.NewRequest(http.MethodPost, apiGetPosition, reader)
	assert.NoError(t, err)

	req = req.WithContext(ctx)
	req.RequestURI = apiGetPosition
	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreatePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":true}`, w.Body.String())
}

func TestCreatePositionHandler_WithoutHallIdParameter(t *testing.T) {
	param := map[string]interface{}{
		"name": "test",
		"note": "test",
		"permissions": map[string]interface{}{
			"menu": []map[string]interface{}{
				{
					"id":     seeder.UserPermID2038,
					"name":   "游戏维护资讯",
					"enable": true,
					"modify": true,
				},
			},
			"special": []map[string]interface{}{},
		},
	}

	body, err := json.Marshal(param)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	req, err := http.NewRequest(http.MethodPost, apiGetPosition, reader)
	assert.NoError(t, err)

	req.RequestURI = apiGetPosition
	req.RemoteAddr = seeder.ClientIP
	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreatePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestCreatePositionHandler_WithoutNameParameter(t *testing.T) {
	param := map[string]interface{}{
		"hall_id": seeder.HallId,
		"note":    "test",
		"permissions": map[string]interface{}{
			"menu": []map[string]interface{}{
				{
					"id":     seeder.UserPermID2038,
					"name":   "游戏维护资讯",
					"enable": true,
					"modify": true,
				},
			},
			"special": []map[string]interface{}{},
		},
	}

	body, err := json.Marshal(param)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	req, err := http.NewRequest(http.MethodPost, apiGetPosition, reader)
	assert.NoError(t, err)

	req.RequestURI = apiGetPosition
	req.RemoteAddr = seeder.ClientIP
	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreatePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestCreatePositionHandler_WithoutPermissionsParameter(t *testing.T) {
	param := map[string]interface{}{
		"hall_id": seeder.HallId,
		"name":    "test",
		"note":    "test",
	}

	body, err := json.Marshal(param)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	req, err := http.NewRequest(http.MethodPost, apiGetPosition, reader)
	assert.NoError(t, err)

	req.RequestURI = apiGetPosition
	req.RemoteAddr = seeder.ClientIP
	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreatePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestCreatePositionHandler_LogicErr(t *testing.T) {
	param := map[string]interface{}{
		"hall_id": seeder.HallId,
		"name":    "test",
		"note":    "test",
		"permissions": map[string]interface{}{
			"menu": []map[string]interface{}{
				{
					"id":     seeder.UserPermID2038,
					"name":   "游戏维护资讯",
					"enable": true,
					"modify": true,
				},
			},
			"special": []map[string]interface{}{},
		},
	}

	body, err := json.Marshal(param)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	req, err := http.NewRequest(http.MethodPost, apiGetPosition, reader)
	assert.NoError(t, err)

	req.RequestURI = apiGetPosition
	req.RemoteAddr = seeder.ClientIP
	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()
	CreatePositionHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}
