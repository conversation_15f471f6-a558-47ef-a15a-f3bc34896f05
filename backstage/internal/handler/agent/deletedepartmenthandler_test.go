package agent

import (
	"html"
	"net/http"
	"net/http/httptest"
	"testing"

	"gbh/admin/adminclient"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/seeder"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/pathvar"
)

func TestDeleteDepartmentHandler(t *testing.T) {
	req, err := http.NewRequest(http.MethodPut, apiDeleteDepartment, http.NoBody)
	req.Header.Set("Content-Type", "application/json")

	params := make(map[string]string)
	params["departmentID"] = "5530"
	req = req.WithContext(ctx)
	req = pathvar.WithVars(req, params)

	adminCtx := mock.NewMockAdminCtx()
	adminCtx.On("GetGMHallPrivilege", req.Context(), seeder.AdminID).Return(&adminclient.GMHallPrivilegeResponse{}, nil)

	agentCtx := mock.NewMockAgentCtx()
	agentCtx.On("GetParameterSet", req.Context(), &agentclient.GetParameterSetRequest{
		Id:   []uint32{seeder.DepartmentParameterSetID5530},
		Type: "department",
	}).Return(&seeder.DepartmentParameterSet, nil)

	agentCtx.On("DepartmentInfoList", req.Context(), agent.DepartmentInfoListRequest{
		ParameterID: seeder.DepartmentParameterSetID5530,
	}).Return(&seeder.DepartmentInfoListID5530, nil)

	agentCtx.On("GetDepartmentList", req.Context(), &agentclient.DepartmentListRequest{
		DepartmentParameterId: seeder.DepartmentParameterSetID5530,
	}).Return(&agentclient.DepartmentListResponse{}, nil)

	agentCtx.On("DeleteDepartment", req.Context(), seeder.DepartmentParameterSetID5530).Return(nil)

	hallCtx := mock.NewMockHallCtx()
	hallCtx.On("GetHallById", req.Context(), seeder.HallId).Return(&seeder.GetHallById, nil)

	userCtx := mock.NewMockUserCtx()
	userCtx.On("GetUserByUserId", req.Context(), seeder.DepartmentInfoListID5530.GetList()[0].GetOwnerId()).Return(&seeder.User, nil)

	operationrecordCtx := mock.NewMockOperationRecordCtx()
	operationrecordCtx.On("CreateAGRecord", req.Context(), operationrecord.CreateAGRecordRequest{
		HallId:       seeder.User.GetDomain(),
		OperatorId:   seeder.GetAdminSession.GetUser().GetId(),
		LevelId:      0,
		ActionId:     constants.ActionId76,
		SubActionId:  0,
		TargetId:     seeder.User.GetId(),
		TargetRoleId: seeder.User.GetRole(),
		BossKey:      "R_Delete_Department",
		UserKey:      "R_Delete_Department",
		XMLDataMsg: []*operationrecord.KeyValue{
			{
				Key:   "role",
				Value: "R_hall",
			},
			{
				Key:   "parent",
				Value: "bbinbgp",
			},
			{
				Key:   "name",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetName()),
			},
			{
				Key:   "note",
				Value: html.EscapeString(seeder.DepartmentParameterSet.GetParameterSet()[0].GetNote()),
			},
		},
		URI: req.RequestURI,
		IP:  seeder.ClientIP,
	}).Return(nil)

	svcCtx.AdminCtx = adminCtx
	svcCtx.AgentCtx = agentCtx
	svcCtx.HallCtx = hallCtx
	svcCtx.UserCtx = userCtx
	svcCtx.OperationRecordCtx = operationrecordCtx

	w := httptest.NewRecorder()
	DeleteDepartmentHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}

func TestDeleteDepartmentHandler_LogicError(t *testing.T) {
	req, err := http.NewRequest(http.MethodPut, apiDeleteDepartment, http.NoBody)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	params := make(map[string]string)
	params["departmentID"] = "5530"
	req = pathvar.WithVars(req, params)

	w := httptest.NewRecorder()
	DeleteDepartmentHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.JSONEq(t, `{"code":562000047,"message":"Operator not found"}`, w.Body.String())
}

func TestDeleteDepartmentHandler_InvalidParameter(t *testing.T) {
	req, err := http.NewRequest(http.MethodPut, apiDeleteDepartment, http.NoBody)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	DeleteDepartmentHandler(svcCtx).ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}
