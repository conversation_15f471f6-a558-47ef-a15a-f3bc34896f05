package lottery

import (
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/mock"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/lotterygame/lotterygameclient"
	"gbh/proto/game"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubWagersURLHandler_WithoutWagersID(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("lang", "zh-tw")

	uri := fmt.Sprintf("%s?%s", apiSubWagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000005,"message":"Invalid parameters"}`, w.Body.String())
}

func TestSubWagersURLHandler_WithoutLang(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("wagers_id", "1")

	uri := fmt.Sprintf("%s?%s", apiSubWagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000005,"message":"Invalid parameters"}`, w.Body.String())
}

func TestSubWagersURLHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("wagers_id", "1")
	query.AddString("lang", "zh-tw")

	uri := fmt.Sprintf("%s?%s", apiSubWagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	wagersByIDRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 1,
	}

	mockLotteryGame := mock.NewMockLotteryGameCtx()

	mockLotteryGame.On("GetWagersByID", ctx, wagersByIDRequest).Return(nil, errorx.InvalidResponse)

	svcCtx.LotteryGameCtx = mockLotteryGame

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000004,"message":"Invalid response"}`, w.Body.String())
}

func TestSubWagersURLHandler_GetSubWagersURL(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("wagers_id", "9215031295")
	query.AddString("lang", "zh-tw")

	uri := fmt.Sprintf("%s?%s", apiSubWagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	wagersByIDRequest := &lotterygameclient.GetWagersByIDRequest{
		Id: 9215031295,
	}

	wagersByIDResponse := &lotterygameclient.GetWagersByIDResponse{
		Id:             9215031295,
		WagersTime:     "2025-01-01 00:00:00",
		Hierarchy:      0,
		Portal:         0,
		WagersType:     0,
		Platform:       0,
		Client:         0,
		GameId:         "BQSQ",
		UserId:         456112001,
		RoundDate:      "2025-01-01",
		RoundTime:      "2025-01-01 00:00:00",
		Commissionable: 3000,
		Currency:       "CNY",
		ExchangeRate:   1.000000,
		Result:         4,
		Payoff:         2880.0000,
		HallId:         3820474,
		RoundSerial:    "20250220-0896",
		ReferenceId:    "9215031295",
		SettledTime:    "2025-01-01 00:00:00",
		Rule:           "L034",
		DbCategory:     122,
		ModifiedDate:   "2025-01-01 00:00:00",
	}

	subWagersURLRequest := &lotterygameclient.SubWagersURLRequest{
		WagersId: 9215031295,
		GameId:   "BQSQ",
		UserId:   456112001,
		HallId:   3820474,
		Lang:     "zh-tw",
	}

	subWagersURLResponse := "https://mock.bblottery/sub_wager_surl"

	gameURLReuqest := &gameclient.GameURLListRequest{
		GameKind: constants.BBLottery,
		HallId: &game.Uint32Value{
			Value: 3820474,
		},
	}

	gameURLResponse := []string{
		"bgp.bblotodemo.net",
		"bgp.bblotodemo1.net",
	}

	mockLotteryGame := mock.NewMockLotteryGameCtx()

	mockLotteryGame.On("GetWagersByID", ctx, wagersByIDRequest).Return(wagersByIDResponse, nil)

	mockLotteryGame.On("GetSubWagersSubURL", ctx, subWagersURLRequest).Return(subWagersURLResponse, nil)

	mockGame := mock.NewMockGameCtx()

	mockGame.On("GetGameURLList", ctx, gameURLReuqest).Return(gameURLResponse, nil)

	svcCtx.LotteryGameCtx = mockLotteryGame

	svcCtx.GameCtx = mockGame

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":"","data":"https://bgp.bblotodemo.net/sub_wager_surl"}`, w.Body.String())
}
