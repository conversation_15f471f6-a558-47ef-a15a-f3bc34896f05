package lottery

import (
	"context"
	"gbh/backstage/internal/config"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

var (
	ctx    context.Context
	svcCtx *svc.ServiceContext
)

const (
	apiSubWagersURL = "/api/lottery/sub_wagers_url"
)

func init() {
	ctx = context.Background()
	conf := config.Config{}
	svcCtx = &svc.ServiceContext{
		Config: conf,
	}

	// 設定統一錯誤處理
	httpx.SetErrorHandler(func(err error) (int, any) {
		errx := errorx.ErrorToErrorx(err)
		return http.StatusOK, &types.BaseResponse{
			Code:    errx.Code,
			Message: errx.Message,
		}
	})
}
