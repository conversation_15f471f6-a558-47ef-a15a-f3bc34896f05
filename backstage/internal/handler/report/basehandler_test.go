package report

import (
	"context"
	"gbh/backstage/internal/config"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/svc"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

var (
	ctx    context.Context
	svcCtx *svc.ServiceContext
)

const (
	apiReportCloseDate   = "/api/report/close_date"
	apiGameAnalysisEvent = "/api/report/game_analysis_event"
)

func init() {
	ctx = context.Background()
	conf := config.Config{}
	svcCtx = &svc.ServiceContext{
		Config:    conf,
		ReportCtx: mock.NewMockReportCtx(),
	}

	// 設定統一錯誤處理
	httpx.SetErrorHandler(func(err error) (int, any) {
		errx := errorx.ErrorToErrorx(err)
		return http.StatusOK, &types.BaseResponse{
			Code:    errx.Code,
			Message: errx.Message,
		}
	})
}
