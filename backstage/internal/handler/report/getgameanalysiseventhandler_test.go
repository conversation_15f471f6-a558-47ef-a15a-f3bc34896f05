package report

import (
	"encoding/json"
	"fmt"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/logic/report"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"gbh/report/reportclient"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetGameAnalysisEventHandler_TheParametersAreNotComplete(t *testing.T) {
	withoutGameKindQuery := urlutil.NewBuilder()
	withoutGameKindQuery.AddString("years", "[2025]")

	withoutYearsQuery := urlutil.NewBuilder()
	withoutYearsQuery.AddString("game_kind", "3")

	tests := []string{
		withoutGameKindQuery.Encode(),
		withoutYearsQuery.Encode(),
	}

	for _, tt := range tests {
		uri := fmt.Sprintf("%s?%s", apiGameAnalysisEvent, tt)

		req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		GetGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

		assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
	}
}

func TestGetGameAnalysisEventHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("game_kind", "3")
	query.AddString("years", "[2025]")

	uri := fmt.Sprintf("%s?%s", apiGameAnalysisEvent, query.Encode())

	mockReportCtx := mock.NewMockReportCtx()

	in := &reportclient.GetGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		Years:    []uint32{seeder.GameAnalysisEventYear},
	}

	mockReportCtx.On("GetGameAnalysisEvent", ctx, in).Return(nil, errorx.ConnectionFailed)

	svcCtx.ReportCtx = mockReportCtx

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}

func TestGetGameAnalysisEventHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("game_kind", "3")
	query.AddString("years", "[2025]")

	uri := fmt.Sprintf("%s?%s", apiGameAnalysisEvent, query.Encode())

	mockReportCtx := mock.NewMockReportCtx()

	in := &reportclient.GetGameAnalysisEventRequest{
		GameKind: constants.BBLive,
		Years:    []uint32{seeder.GameAnalysisEventYear},
	}

	mockReportCtx.On("GetGameAnalysisEvent", ctx, in).Return(&seeder.GameAnalysisEvent, nil)

	svcCtx.ReportCtx = mockReportCtx

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	GetGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

	expectedStruct := &types.BaseResponse{
		Data: []report.GameAnalysisEvent{
			{
				ID:             seeder.GameAnalysisEventId,
				GameKind:       constants.BBLive,
				GameType:       "3001",
				Content:        "",
				Year:           seeder.GameAnalysisEventYear,
				OccurrenceDate: "2025-01-01",
			},
		},
	}

	expected, err := json.Marshal(expectedStruct)

	assert.NoError(t, err)
	assert.Equal(t, string(expected), w.Body.String())
}
