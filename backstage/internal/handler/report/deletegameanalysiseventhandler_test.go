package report

import (
	"fmt"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/report/reportclient"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDeleteGameAnalysisEventHandler_TheParametersAreNotComplete(t *testing.T) {
	req, err := http.NewRequest(http.MethodDelete, apiGameAnalysisEvent, http.NoBody)

	assert.NoError(t, err)

	w := httptest.NewRecorder()

	DeleteGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestDeleteGameAnalysisEventHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("ids", "[1]")

	uri := fmt.Sprintf("%s?%s", apiGameAnalysisEvent, query.Encode())

	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)

	assert.NoError(t, err)

	mockReportCtx := mock.NewMockReportCtx()
	mockReportCtx.On("DeleteGameAnalysisEvent", ctx, &reportclient.DeleteGameAnalysisEventRequest{
		Ids: []uint32{seeder.GameAnalysisEventId},
	}).Return(errorx.ConnectionFailed)

	svcCtx.ReportCtx = mockReportCtx

	w := httptest.NewRecorder()

	DeleteGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}

func TestDeleteGameAnalysisEventHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("ids", "[1]")

	uri := fmt.Sprintf("%s?%s", apiGameAnalysisEvent, query.Encode())

	req, err := http.NewRequest(http.MethodDelete, uri, http.NoBody)

	assert.NoError(t, err)

	mockReportCtx := mock.NewMockReportCtx()
	mockReportCtx.On("DeleteGameAnalysisEvent", ctx, &reportclient.DeleteGameAnalysisEventRequest{
		Ids: []uint32{seeder.GameAnalysisEventId},
	}).Return(nil)

	svcCtx.ReportCtx = mockReportCtx

	w := httptest.NewRecorder()

	DeleteGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}
