package report

import (
	"bytes"
	"encoding/json"
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/game/gameclient"
	"gbh/proto/report"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCreateGameAnalysisEventHandler_TheParametersAreNotComplete(t *testing.T) {
	tests := []struct {
		Description string
		Body        map[string]interface{}
	}{
		{
			Description: "WithoutContentQuery",
			Body: map[string]interface {
			}{
				"date": "2025-01-01 00:00:00",
			},
		},
		{
			Description: "WithoutDateQuery",
			Body: map[string]interface {
			}{
				"content": "",
			},
		},
	}

	expteced := `{"code":562000001,"message":"The parameters are not complete"}`

	for _, tt := range tests {
		t.Run(tt.Description, func(t *testing.T) {
			body, err := json.Marshal(tt.Body)
			assert.NoError(t, err)

			reader := bytes.NewReader(body)

			req, err := http.NewRequest(http.MethodPost, apiGameAnalysisEvent, reader)
			assert.NoError(t, err)

			req.Header.Add("Content-Type", "application/json")

			w := httptest.NewRecorder()

			CreateGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

			assert.Equal(t, expteced, w.Body.String())
		})
	}
}

func TestCreateGameAnalysisEventHandler_InvalidParameters(t *testing.T) {
	tests := []struct {
		Description string
		Body        map[string]interface{}
	}{
		{
			Description: "DateError",
			Body: map[string]interface {
			}{
				"content": "",
				"date":    "abcd",
			},
		},
		{
			Description: "GameIDWithoutGameKind",
			Body: map[string]interface {
			}{
				"game_id": constants.GameID3001,
				"content": "",
				"date":    "2025-01-01 00:00:00",
			},
		},
	}

	expteced := `{"code":562000004,"message":"Parameter validation error"}`

	for _, tt := range tests {
		t.Run(tt.Description, func(t *testing.T) {
			body, err := json.Marshal(tt.Body)
			assert.NoError(t, err)

			reader := bytes.NewReader(body)

			req, err := http.NewRequest(http.MethodPost, apiGameAnalysisEvent, reader)
			assert.NoError(t, err)

			req.Header.Add("Content-Type", "application/json")

			w := httptest.NewRecorder()

			CreateGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

			assert.Equal(t, expteced, w.Body.String())
		})
	}
}

func TestCreateGameAnalysisEventHandler_LogicError(t *testing.T) {
	params := map[string]interface {
	}{
		"content": "",
		"date":    "2025-01-01 00:00:00",
	}

	expteced := `{"code":560000002,"message":"Connection failed"}`

	body, err := json.Marshal(params)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	req, err := http.NewRequest(http.MethodPost, apiGameAnalysisEvent, reader)
	assert.NoError(t, err)

	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()

	mockGameCtx := mock.NewMockGameCtx()

	enableReport := true

	in := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, in).Return(nil, errorx.ConnectionFailed)

	svcCtx.GameCtx = mockGameCtx

	CreateGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, expteced, w.Body.String())
}

func TestCreateGameAnalysisEventHandler(t *testing.T) {
	params := map[string]interface {
	}{
		"game_kind": constants.BBLive,
		"game_id":   constants.GameID3001,
		"content":   "",
		"date":      "2025-01-01 00:00:00",
	}

	body, err := json.Marshal(params)
	assert.NoError(t, err)

	reader := bytes.NewReader(body)

	req, err := http.NewRequest(http.MethodPost, apiGameAnalysisEvent, reader)
	assert.NoError(t, err)

	req.Header.Add("Content-Type", "application/json")

	w := httptest.NewRecorder()

	mockGameCtx := mock.NewMockGameCtx()
	mockReportCtx := mock.NewMockReportCtx()

	enableReport := true

	lobbyRequest := game.LobbyCategoryRequest{
		Report: &enableReport,
	}

	gameListRequest := &gameclient.GetGameListWithSwitchRequest{
		GameKind: []uint32{constants.BBLive},
		PlatformEnable: &gameclient.BoolValue{
			Value: true,
		},
		GameId: []uint32{constants.GameID3001},
	}

	gameListReturn := &gameclient.GetGameListWithSwitchResponse{
		Data: []*gameclient.GameListWithSwitch{
			{
				GameKind:            3,
				GameId:              3001,
				Name:                "百家樂",
				Device:              0,
				PlatformEnable:      true,
				PcEnable:            true,
				MobileEnable:        true,
				DemoEnable:          false,
				IsJackpot:           false,
				CommissionableGroup: "1",
				OpenDate:            "2014-01-01",
				UpdatedAt:           "2014-01-01 00:00:00",
				ExternalId:          "",
				IconKind:            "",
				WhiteList:           []uint32{3820325},
			},
		},
	}

	mockGameCtx.On("FlattenLobbyCategory", ctx, lobbyRequest).Return(seeder.FlattenLobbyCategory, nil)

	mockGameCtx.On("GetGameListWithSwitch", ctx, gameListRequest).Return(gameListReturn, nil)

	mockReportCtx.On("CreateGameAnalysisEvent", ctx, &report.CreateGameAnalysisEventRequest{
		GameKind:       constants.BBLive,
		GameType:       "3001",
		Content:        "",
		OccurrenceDate: "2025-01-01 00:00:00",
	}).Return(nil)

	svcCtx.GameCtx = mockGameCtx
	svcCtx.ReportCtx = mockReportCtx

	CreateGameAnalysisEventHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}
