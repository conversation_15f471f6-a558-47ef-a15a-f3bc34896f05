package report

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCloseDateHandler(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiReportCloseDate, http.NoBody)
	assert.NoError(t, err)

	reportCtx := mock.NewMockReportCtx()

	reportCtx.On("GetReportCloseDate", ctx).Return(seeder.CloseDate.GetCloseDate(), nil)
	svcCtx.ReportCtx = reportCtx

	w := httptest.NewRecorder()
	CloseDateHandler(svcCtx).ServeHTTP(w, req)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":"2020-01-01"}`, w.Body.String())
}

func TestCloseDateHandler_LogicError(t *testing.T) {
	req, err := http.NewRequest(http.MethodGet, apiReportCloseDate, http.NoBody)
	assert.NoError(t, err)

	reportCtx := mock.NewMockReportCtx()

	reportCtx.On("GetReportCloseDate", ctx).Return(nil, errorx.DatabaseError)
	svcCtx.ReportCtx = reportCtx

	w := httptest.NewRecorder()
	CloseDateHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000014,"message":"Database error"}`, w.Body.String())
}
