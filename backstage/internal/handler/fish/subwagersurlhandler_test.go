package fish

import (
	"fmt"
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/utils/urlutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSubWagersURLHandler(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("wagers_id", "[595700193]")

	uri := fmt.Sprintf("%s?%s", apiGetSubwagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	fishGameCtx := mock.NewMockFishGameCtx()

	fishGameCtx.On("GetMultiSubWagersURL", ctx, "zh-cn", []uint64{seeder.WagersID}).Return(&seeder.MultiSubWagersURL, nil)

	svcCtx.FishGameCtx = fishGameCtx

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":"","data":[{"wagers_id":595700193,"url":"https://fisher-test.cc/bet-record/fish/platform/wager/detail?pf=test==\u0026lang=zh-tw"}]}`, w.Body.String())
}

func TestSubWagersURLHandler_LogicError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("wagers_id", "[595700193]")

	uri := fmt.Sprintf("%s?%s", apiGetSubwagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	fishGameCtx := mock.NewMockFishGameCtx()

	fishGameCtx.On("GetMultiSubWagersURL", ctx, "zh-cn", []uint64{seeder.WagersID}).Return(nil, errorx.SystemError)

	svcCtx.FishGameCtx = fishGameCtx

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560009999,"message":"System error"}`, w.Body.String())
}

func TestSubWagersURLHandler_WithoutParameterWagersId(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("wagers_id", "")

	uri := fmt.Sprintf("%s?%s", apiGetSubwagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000001,"message":"The parameters are not complete"}`, w.Body.String())
}

func TestSubWagersURLHandler_ValidError(t *testing.T) {
	query := urlutil.NewBuilder()
	query.AddString("wagers_id", "[0]")

	uri := fmt.Sprintf("%s?%s", apiGetSubwagersURL, query.Encode())

	req, err := http.NewRequest(http.MethodGet, uri, http.NoBody)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	SubWagersURLHandler(svcCtx).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000004,"message":"Parameter validation error"}`, w.Body.String())
}
