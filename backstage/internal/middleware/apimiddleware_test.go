package middleware

import (
	"gbh/backstage/internal/types"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func TestApiMiddleware(t *testing.T) {
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, apiAdminList, http.NoBody)

	m := NewAPIMiddleware()

	m.Handle(func(w http.ResponseWriter, r *http.Request) {
		httpx.OkJsonCtx(r.Context(), w, &types.BaseResponse{})
	}).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
}

func TestApiMiddleware_RPCError(t *testing.T) {
	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, apiAdminList, http.NoBody)

	m := NewAPIMiddleware()

	m.<PERSON>(func(w http.ResponseWriter, r *http.Request) {
		httpx.OkJsonCtx(r.Context(), w, &types.BaseResponse{
			Code:    560000004,
			Message: "Invalid response",
		})
	}).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000004,"message":"Invalid response"}`, w.Body.String())
}
