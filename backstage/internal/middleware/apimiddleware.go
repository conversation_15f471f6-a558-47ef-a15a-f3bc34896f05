package middleware

import (
	"bytes"
	"encoding/json"
	"gbh/backstage/internal/types"
	"gbh/logger/constants"
	"gbh/utils/logger"
	"io"
	"net/http"
	"time"
)

type APIMiddleware struct {
}

func NewAPIMiddleware() *APIMiddleware {
	return &APIMiddleware{}
}

func (m *APIMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		startTime := time.Now()

		tags := []string{constants.LogTagBackstage}

		// 讀取請求
		var requestBody []byte
		if r.Body != nil {
			bodyBytes, err := io.ReadAll(r.Body)
			if err != nil {
				logger.WithAPI(r, tags, string(requestBody), nil, time.Since(startTime), "severe").
					Infof("[Backstage] failed to read request body: %v", err)
				http.Error(w, "failed to read request body", http.StatusInternalServerError)
				return
			}
			requestBody = bodyBytes
			r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		}

		// 包裝 ResponseWriter 以攔截回應
		responseRecorder := &logger.ResponseLogger{
			ResponseWriter: w,
			StatusCode:     http.StatusOK,
		}

		// 執行下一個處理器
		next.ServeHTTP(responseRecorder, r)

		// 計算執行時間
		duration := time.Since(startTime)

		var resp types.BaseResponse
		jsonErr := json.Unmarshal(responseRecorder.Body.Bytes(), &resp)
		if jsonErr != nil {
			logger.WithAPI(r, tags, string(requestBody), responseRecorder, duration, "severe").
				Infof("[Backstage] failed to parse response body: %v, raw response: %s", jsonErr)
			return
		}

		if resp.Code != 0 {
			logger.WithAPI(r, tags, string(requestBody), responseRecorder, duration, "warning").Info()
		} else {
			logger.WithAPI(r, tags, string(requestBody), responseRecorder, duration, "info").Info()
		}
	}
}
