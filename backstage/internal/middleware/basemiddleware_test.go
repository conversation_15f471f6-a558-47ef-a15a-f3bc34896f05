package middleware

import (
	"context"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

const apiAdminList = "/api/admin/list"

var (
	ctx context.Context
)

func init() {
	ctx = context.Background()
	// 設定統一錯誤處理
	httpx.SetErrorHandler(func(err error) (int, any) {
		errx := errorx.ErrorToErrorx(err)
		return http.StatusOK, &types.BaseResponse{
			Code:    errx.Code,
			Message: errx.Message,
		}
	})
}
