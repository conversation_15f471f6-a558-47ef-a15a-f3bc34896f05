package middleware

import (
	"context"
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/operator"
	"gbh/errorx"
	"gbh/utils/iputil"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

type CheckAdminSessionMiddleware struct {
	AdminCtx admin.Context
}

func NewCheckAdminSessionMiddleware(adminCtx admin.Context) *CheckAdminSessionMiddleware {
	return &CheckAdminSessionMiddleware{
		AdminCtx: adminCtx,
	}
}

func (m *CheckAdminSessionMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		session := r.Header.Get("session")

		if session == "" {
			httpx.Error(w, errorx.BackstageAdminSessionNotFound)
			return
		}

		resp, err := m.AdminCtx.GetSession(r.Context(), session[2:34], r.RemoteAddr)

		if err != nil {
			httpx.Error(w, err)
			return
		}

		user := resp.GetUser()

		operaotr := operator.NewOperator(
			operator.WithID(user.GetId()),
			operator.WithUsername(user.GetUsername()),
			operator.WithAlias(user.GetAlias()),
			operator.WithIP(iputil.GetIPWithoutPort((r.RemoteAddr))),
			operator.WithEntry(operator.AdminEntry),
		)

		ctx := context.WithValue(r.Context(), operator.OperatorKey, operaotr)
		r = r.WithContext(ctx)

		next(w, r)
	}
}
