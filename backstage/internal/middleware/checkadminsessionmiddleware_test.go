package middleware

import (
	"gbh/backstage/internal/mock"
	"gbh/backstage/internal/operator"
	"gbh/backstage/internal/seeder"
	"gbh/backstage/internal/types"
	"gbh/errorx"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func TestCheckAdminSessionMiddleware_BackstageAdminSessionNotFound(t *testing.T) {
	adminCtx := mock.NewMockAdminCtx()

	m := NewCheckAdminSessionMiddleware(adminCtx)

	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, apiAdminList, http.NoBody)

	m.Handle(func(w http.ResponseWriter, r *http.Request) {
		httpx.OkJsonCtx(r.Context(), w, &types.BaseResponse{})
	}).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":562000046,"message":"Admin session not found"}`, w.Body.String())
}

func TestCheckAdminSessionMiddleware_GetSessionError(t *testing.T) {
	adminCtx := mock.NewMockAdminCtx()

	adminCtx.On("GetSession", ctx, seeder.AdminSession[2:34], seeder.ClientIP).Return(nil, errorx.ConnectionFailed)

	m := NewCheckAdminSessionMiddleware(adminCtx)

	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, apiAdminList, http.NoBody)

	req.Header.Add("session", seeder.AdminSession)

	req.RemoteAddr = seeder.ClientIP

	m.Handle(func(w http.ResponseWriter, r *http.Request) {
		httpx.OkJsonCtx(r.Context(), w, &types.BaseResponse{})
	}).ServeHTTP(w, req)

	assert.JSONEq(t, `{"code":560000002,"message":"Connection failed"}`, w.Body.String())
}

func TestCheckAdminSessionMiddleware(t *testing.T) {
	adminCtx := mock.NewMockAdminCtx()

	adminCtx.On("GetSession", ctx, seeder.AdminSession[2:34], seeder.ClientIP).Return(&seeder.GetAdminSession, nil)

	m := NewCheckAdminSessionMiddleware(adminCtx)

	w := httptest.NewRecorder()

	req := httptest.NewRequest(http.MethodGet, apiAdminList, http.NoBody)

	req.Header.Add("session", seeder.AdminSession)

	req.RemoteAddr = seeder.ClientIP

	var o *operator.Operator
	var err error

	m.Handle(func(w http.ResponseWriter, r *http.Request) {
		o, err = operator.GetOperator(r.Context())
		httpx.OkJsonCtx(r.Context(), w, &types.BaseResponse{})
	}).ServeHTTP(w, req)

	assert.NoError(t, err)
	assert.JSONEq(t, `{"code":0,"message":""}`, w.Body.String())
	assert.Equal(t, seeder.GetAdminSession.GetUser().GetId(), o.ID())
	assert.Equal(t, seeder.GetAdminSession.GetUser().GetUsername(), o.Username())
	assert.Equal(t, seeder.GetAdminSession.GetUser().GetAlias(), o.Alias())
	assert.Equal(t, seeder.ClientIP, o.IP())
	assert.Equal(t, operator.AdminEntry, o.Entry())
}
