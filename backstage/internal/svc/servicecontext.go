package svc

import (
	"gbh/admin/adminclient"
	"gbh/agent/agentclient"
	"gbh/backstage/internal/admin"
	"gbh/backstage/internal/agent"
	"gbh/backstage/internal/config"
	"gbh/backstage/internal/fishgame"
	"gbh/backstage/internal/game"
	"gbh/backstage/internal/hall"
	"gbh/backstage/internal/jackpot"
	"gbh/backstage/internal/lang"
	"gbh/backstage/internal/livegame"
	"gbh/backstage/internal/lotterygame"
	"gbh/backstage/internal/maintain"
	"gbh/backstage/internal/middleware"
	"gbh/backstage/internal/operationrecord"
	"gbh/backstage/internal/permission"
	"gbh/backstage/internal/report"
	"gbh/backstage/internal/slotgame"
	"gbh/backstage/internal/user"
	"gbh/fishgame/fishgameclient"
	"gbh/game/gameclient"
	"gbh/hall/hallclient"
	"gbh/jackpot/jackpotclient"
	"gbh/lang/langclient"
	"gbh/livegame/livegameclient"
	"gbh/logger"
	"gbh/lotterygame/lotterygameclient"
	"gbh/maintain/maintainclient"
	"gbh/operationrecord/operationrecordclient"
	"gbh/permission/permissionclient"
	"gbh/redis"
	"gbh/report/reportclient"
	"gbh/slotgame/slotgameclient"
	"gbh/user/userclient"
	"log"
	"time"

	"github.com/zeromicro/go-zero/core/collection"

	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
)

const memoryCacheExpiry = 10 * time.Minute

type ServiceContext struct {
	Config                      config.Config
	APIMiddleware               rest.Middleware
	PermissionCtx               permission.Context
	ReportCtx                   report.Context
	AdminCtx                    admin.Context
	LiveGameCtx                 livegame.Context
	LotteryGameCtx              lotterygame.Context
	LangCtx                     lang.Context
	GameCtx                     game.Context
	FishGameCtx                 fishgame.Context
	SlotGameCtx                 slotgame.Context
	UserCtx                     user.Context
	HallCtx                     hall.Context
	AgentCtx                    agent.Context
	OperationRecordCtx          operationrecord.Context
	MaintainCtx                 maintain.Context
	JackpotCtx                  jackpot.Context
	MemoryCache                 *collection.Cache
	CheckAdminSessionMiddleware rest.Middleware
}

func NewServiceContext(c config.Config) *ServiceContext {
	permissionRPC := permissionclient.NewPermission(zrpc.MustNewClient(c.PermissionRPC))
	reportRPC := reportclient.NewReport(zrpc.MustNewClient(c.ReportRPC))
	adminRPC := adminclient.NewAdmin(zrpc.MustNewClient(c.AdminRPC))
	liveGameRPC := livegameclient.NewLiveGame(zrpc.MustNewClient(c.LiveGameRPC))
	lotteryGameRPC := lotterygameclient.NewLotteryGame(zrpc.MustNewClient(c.LotteryGameRPC))
	langRPC := langclient.NewLang(zrpc.MustNewClient(c.LangRPC))
	gameRPC := gameclient.NewGame(zrpc.MustNewClient(c.GameRPC))
	fishGameRPC := fishgameclient.NewFishGame(zrpc.MustNewClient(c.FishGameRPC))
	slotGameRPC := slotgameclient.NewSlotGame(zrpc.MustNewClient(c.SlotGameRPC))
	userRPC := userclient.NewUser(zrpc.MustNewClient(c.UserRPC))
	hallRPC := hallclient.NewHall(zrpc.MustNewClient(c.HallRPC))
	agentRPC := agentclient.NewAgent(zrpc.MustNewClient(c.AgentRPC))
	operationrecordRPC := operationrecordclient.NewOperationRecord(zrpc.MustNewClient(c.OperationRecordRPC))
	maintainRPC := maintainclient.NewMaintain(zrpc.MustNewClient(c.MaintainRPC))
	jackpotRPC := jackpotclient.NewJackpot(zrpc.MustNewClient(c.JackpotRPC))

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	memoryCache, cacheErr := collection.NewCache(memoryCacheExpiry)

	if cacheErr != nil {
		log.Fatal(cacheErr)
	}

	redisCache := redis.New(c.RedisConf, customLogger)

	adminCtx := admin.New(adminRPC)

	return &ServiceContext{
		Config:                      c,
		APIMiddleware:               middleware.NewAPIMiddleware().Handle,
		PermissionCtx:               permission.New(permissionRPC, redisCache, memoryCache),
		ReportCtx:                   report.New(reportRPC),
		AdminCtx:                    adminCtx,
		LiveGameCtx:                 livegame.New(liveGameRPC),
		LotteryGameCtx:              lotterygame.New(lotteryGameRPC),
		LangCtx:                     lang.New(langRPC),
		FishGameCtx:                 fishgame.New(fishGameRPC),
		GameCtx:                     game.New(gameRPC, redisCache),
		SlotGameCtx:                 slotgame.New(slotGameRPC),
		UserCtx:                     user.New(userRPC),
		HallCtx:                     hall.New(hallRPC),
		AgentCtx:                    agent.New(agentRPC),
		OperationRecordCtx:          operationrecord.New(operationrecordRPC),
		MaintainCtx:                 maintain.New(maintainRPC),
		JackpotCtx:                  jackpot.New(jackpotRPC),
		CheckAdminSessionMiddleware: middleware.NewCheckAdminSessionMiddleware(adminCtx).Handle,
	}
}
