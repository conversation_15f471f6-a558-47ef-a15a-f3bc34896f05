package user

import (
	"context"
	"gbh/errorx"
	"gbh/user/userclient"
)

type Context interface {
	GetUserByUserId(ctx context.Context, userId uint32) (*userclient.GetResponse, error)
	GetUserByUsername(ctx context.Context, parentId uint32, username string) (*userclient.GetResponse, error)
	GetUserList(ctx context.Context, in *userclient.GetUserListRequest) (*userclient.GetUserListResponse, error)
	GetUsers(ctx context.Context, userIDs []uint32, extraInfo *bool) (*userclient.GetUsersResponse, error)
	GetUsersUsername(ctx context.Context, userIDs []uint32) (map[uint32]string, error)
	DeleteUser(ctx context.Context, in *userclient.DeleteUserRequest) error
	GetLoginRecord(ctx context.Context, req GetLoginRecordRequest) (*userclient.LoginRecordResponse, error)
	CheckUsernameUnique(ctx context.Context, in *userclient.CheckUsernameUniqueRequest) (*userclient.CheckUsernameUniqueResponse, error)
	EditUser(ctx context.Context, in *userclient.EditUserRequest) error
	SetPassword(ctx context.Context, in *userclient.SetPasswordRequest) error
}

type userContext struct {
	UserRPC userclient.User
}

func New(userRPC userclient.User) Context {
	return &userContext{
		UserRPC: userRPC,
	}
}

type GetLoginRecordRequest struct {
	StartTime string
	EndTime   string
	UserID    uint32
	HallID    uint32
	IP        string
	Page      uint32
	PageLimit uint32
}

func (c *userContext) GetUserByUserId(ctx context.Context, userId uint32) (*userclient.GetResponse, error) {
	userData, err := c.UserRPC.Get(ctx, &userclient.GetRequest{
		UserId: userId,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return userData, nil
}

func (c *userContext) GetUserByUsername(ctx context.Context, parentId uint32, username string) (*userclient.GetResponse, error) {
	userData, err := c.UserRPC.GetUserByUsername(ctx, &userclient.GetUserByUsernameRequest{
		ParentId: parentId,
		Username: username,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return userData, nil
}

func (c *userContext) GetUserList(ctx context.Context, in *userclient.GetUserListRequest) (*userclient.GetUserListResponse, error) {
	users, err := c.UserRPC.GetUserList(ctx, in)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return users, nil
}

func (c *userContext) GetUsers(ctx context.Context, userIDs []uint32, extraInfo *bool) (*userclient.GetUsersResponse, error) {
	params := &userclient.GetUsersRequest{
		Users: userIDs,
	}
	if extraInfo != nil {
		params.ExtraInfo = &userclient.BoolValue{
			Value: *extraInfo,
		}
	}

	users, err := c.UserRPC.GetUsers(ctx, params)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return users, nil
}

func (c *userContext) GetUsersUsername(ctx context.Context, userIDs []uint32) (map[uint32]string, error) {
	users, err := c.GetUsers(ctx, userIDs, nil)

	if err != nil {
		return nil, err
	}

	username := map[uint32]string{}
	for _, v := range users.GetUsers() {
		username[v.GetId()] = v.GetUsername()
	}

	return username, nil
}

func (c *userContext) DeleteUser(ctx context.Context, in *userclient.DeleteUserRequest) error {
	_, err := c.UserRPC.DeleteUser(ctx, in)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *userContext) GetLoginRecord(ctx context.Context, req GetLoginRecordRequest) (*userclient.LoginRecordResponse, error) {
	resp, err := c.UserRPC.LoginRecord(ctx, &userclient.LoginRecordRequest{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		UserId:    req.UserID,
		HallId:    req.HallID,
		Ip:        req.IP,
		Page:      req.Page,
		Limit:     req.PageLimit,
	})

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *userContext) CheckUsernameUnique(ctx context.Context, in *userclient.CheckUsernameUniqueRequest) (*userclient.CheckUsernameUniqueResponse, error) {
	userData, err := c.UserRPC.CheckUsernameUnique(ctx, in)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return userData, nil
}

func (c *userContext) EditUser(ctx context.Context, in *userclient.EditUserRequest) error {
	_, err := c.UserRPC.EditUser(ctx, in)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}

func (c *userContext) SetPassword(ctx context.Context, in *userclient.SetPasswordRequest) error {
	_, err := c.UserRPC.SetPassword(ctx, in)

	if err != nil {
		return errorx.GRPCErrorToErrorx(err)
	}

	return nil
}
