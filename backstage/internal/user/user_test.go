package user

import (
	"gbh/backstage/internal/constants"
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/user/userclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	userRPC := newMockUserRPC()
	userCtx := New(userRPC)

	expectedResponse := &userContext{
		UserRPC: userRPC,
	}

	assert.Equal(t, expectedResponse, userCtx)
}

func Test_UserContext_GetUserByUserId(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetRequest{
		UserId: seeder.User.GetId(),
	}

	userRPC.On("Get", ctx, mockRequest).Return(&seeder.User, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUserByUserId(ctx, seeder.User.GetId())

	assert.NoError(t, err)
	assert.Equal(t, &seeder.User, resp)
}

func Test_UserContext_GetUserByUserId_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetRequest{
		UserId: seeder.User.GetId(),
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("Get", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUserByUserId(ctx, seeder.User.GetId())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_UserContext_GetUserByUsername(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUserByUsernameRequest{
		ParentId: seeder.User.GetParent(),
		Username: seeder.User.GetUsername(),
	}

	userRPC.On("GetUserByUsername", ctx, mockRequest).Return(&seeder.User, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUserByUsername(ctx, seeder.User.GetParent(), seeder.User.GetUsername())

	assert.NoError(t, err)
	assert.Equal(t, &seeder.User, resp)
}

func Test_UserContext_GetUserByUsername_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUserByUsernameRequest{
		ParentId: seeder.User.GetParent(),
		Username: seeder.User.GetUsername(),
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("GetUserByUsername", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUserByUsername(ctx, seeder.User.GetParent(), seeder.User.GetUsername())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_UserContext_GetUserList(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUserListRequest{}

	mockReturn := &userclient.GetUserListResponse{
		Users: []*userclient.GetResponse{
			&seeder.User,
		},
		Pagination: &userclient.Pagination{
			CurrentPage: 1,
			PageLimit:   seeder.UserListPageLimit,
			Total:       1,
			TotalPage:   1,
		},
	}

	userRPC.On("GetUserList", ctx, mockRequest).Return(mockReturn, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUserList(ctx, mockRequest)

	assert.NoError(t, err)
	assert.Equal(t, mockReturn, resp)
}

func Test_UserContext_GetUserList_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUserListRequest{}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("GetUserList", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUserList(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_UserContext_GetUsers(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUsersRequest{
		Users: []uint32{seeder.HallId},
	}

	userRPC.On("GetUsers", ctx, mockRequest).Return(&seeder.Users, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUsers(ctx, []uint32{seeder.HallId}, nil)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.Users, resp)
}

func Test_UserContext_GetUsers_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUsersRequest{
		Users: []uint32{seeder.HallId},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("GetUsers", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUsers(ctx, []uint32{seeder.HallId}, nil)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_UserContext_GetUsers_WithExtraInfo(t *testing.T) {
	userRPC := newMockUserRPC()

	extraInfo := true
	mockRequest := &userclient.GetUsersRequest{
		Users: []uint32{seeder.HallId},
		ExtraInfo: &userclient.BoolValue{
			Value: extraInfo,
		},
	}

	userRPC.On("GetUsers", ctx, mockRequest).Return(&seeder.Users, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUsers(ctx, []uint32{seeder.HallId}, &extraInfo)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.Users, resp)
}

func Test_UserContext_GetUsersUsername(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUsersRequest{
		Users: []uint32{seeder.HallId},
	}

	userRPC.On("GetUsers", ctx, mockRequest).Return(&seeder.Users, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUsersUsername(ctx, []uint32{seeder.HallId})

	expectedResponse := map[uint32]string{
		seeder.SubUsers.GetUsers()[0].GetId(): seeder.SubUsers.GetUsers()[0].GetUsername(),
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_UserContext_GetUsersUsername_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.GetUsersRequest{
		Users: []uint32{seeder.HallId},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("GetUsers", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	resp, err := userCtx.GetUsersUsername(ctx, []uint32{seeder.HallId})

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_UserContext_DeleteUser(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.DeleteUserRequest{
		Id: seeder.UserId,
	}

	userRPC.On("DeleteUser", ctx, mockRequest).Return(nil, nil)
	userCtx := New(userRPC)

	err := userCtx.DeleteUser(ctx, mockRequest)

	assert.NoError(t, err)
}

func Test_UserContext_DeleteUser_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.DeleteUserRequest{
		Id: seeder.UserId,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("DeleteUser", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	err := userCtx.DeleteUser(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_UserContext_GetLoginRecord(t *testing.T) {
	userRPC := newMockUserRPC()

	req := GetLoginRecordRequest{
		StartTime: "2025-01-01T00:00:00-04:00",
		EndTime:   "2025-01-01T23:59:59-04:00",
		UserID:    seeder.GTIHallId,
		HallID:    seeder.HallId,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}

	mockRequest := &userclient.LoginRecordRequest{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		UserId:    req.UserID,
		HallId:    req.HallID,
		Ip:        req.IP,
		Page:      req.Page,
		Limit:     req.PageLimit,
	}

	userRPC.On("LoginRecord", ctx, mockRequest).Return(&seeder.LoginRecord, nil)

	userCtx := New(userRPC)
	resp, err := userCtx.GetLoginRecord(ctx, req)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.LoginRecord, resp)
}

func Test_UserContext_GetLoginRecord_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	req := GetLoginRecordRequest{
		StartTime: "2025-01-01T00:00:00-04:00",
		EndTime:   "2025-01-01T23:59:59-04:00",
		UserID:    seeder.GTIHallId,
		HallID:    seeder.HallId,
		IP:        seeder.ClientIP,
		Page:      0,
		PageLimit: 0,
	}

	mockRequest := &userclient.LoginRecordRequest{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		UserId:    req.UserID,
		HallId:    req.HallID,
		Ip:        req.IP,
		Page:      req.Page,
		Limit:     req.PageLimit,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("LoginRecord", ctx, mockRequest).Return(nil, mockError)

	userCtx := New(userRPC)
	resp, err := userCtx.GetLoginRecord(ctx, req)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_UserContext_CheckUsernameUnique(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.CheckUsernameUniqueRequest{
		Username: "gtptest",
	}

	userRPC.On("CheckUsernameUnique", ctx, mockRequest).Return(&seeder.UsernameUnique, nil)
	userCtx := New(userRPC)

	resp, err := userCtx.CheckUsernameUnique(ctx, mockRequest)

	assert.NoError(t, err)
	assert.Equal(t, &seeder.UsernameUnique, resp)
}

func Test_UserContext_CheckUsernameUnique_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.CheckUsernameUniqueRequest{
		Username: "gtptest",
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("CheckUsernameUnique", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	resp, err := userCtx.CheckUsernameUnique(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_UserContext_EditUser(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.EditUserRequest{
		Id: seeder.UserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
	}

	userRPC.On("EditUser", ctx, mockRequest).Return(nil, nil)
	userCtx := New(userRPC)

	err := userCtx.EditUser(ctx, mockRequest)

	assert.NoError(t, err)
}

func Test_UserContext_EditUser_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.EditUserRequest{
		Id: seeder.UserId,
		Username: &userclient.StringValue{
			Value: "gtptest",
		},
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("EditUser", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	err := userCtx.EditUser(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
}

func Test_UserContext_SetPassword(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.SetPasswordRequest{
		UserId:      seeder.AgentID,
		NewPassword: seeder.ParentPassword,
		ClientIp:    seeder.ClientIP,
		RequestUrl:  seeder.RequestURI,
		Entrance:    constants.EntranceControl,
		OperatorId:  seeder.OperatorID,
		Operator:    seeder.OperatorName,
	}

	userRPC.On("SetPassword", ctx, mockRequest).Return(nil, nil)
	userCtx := New(userRPC)

	err := userCtx.SetPassword(ctx, mockRequest)

	assert.NoError(t, err)
}

func Test_UserContext_SetPassword_GRPCError(t *testing.T) {
	userRPC := newMockUserRPC()

	mockRequest := &userclient.SetPasswordRequest{
		UserId:      seeder.AgentID,
		NewPassword: seeder.ParentPassword,
		ClientIp:    seeder.ClientIP,
		RequestUrl:  seeder.RequestURI,
		Entrance:    constants.EntranceControl,
		OperatorId:  seeder.OperatorID,
		Operator:    seeder.OperatorName,
	}

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	userRPC.On("SetPassword", ctx, mockRequest).Return(nil, mockError)
	userCtx := New(userRPC)

	err := userCtx.SetPassword(ctx, mockRequest)

	assert.Equal(t, errorx.ConnectionFailed, err)
}
