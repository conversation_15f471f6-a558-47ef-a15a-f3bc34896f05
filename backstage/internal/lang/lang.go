package lang

import (
	"context"
	"gbh/errorx"
	"gbh/lang/langclient"
)

type Context interface {
	GetGameFeatures(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error)
	GetGamePlayType(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error)
	GetGameRoundResult(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error)
	GetWeblateLang(ctx context.Context, lang string, category string) (*langclient.WeblateResponse, error)
}

type langContext struct {
	LangRPC langclient.Lang
}

func New(langRPC langclient.Lang) Context {
	return &langContext{
		LangRPC: langRPC,
	}
}

func (c *langContext) GetGameFeatures(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error) {
	request := &langclient.GameDictionaryRequest{
		Lang:     lang,
		GameKind: gameKind,
	}

	resp, err := c.<PERSON>.GetGameFeatures(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *langContext) GetGamePlayType(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error) {
	request := &langclient.GameDictionaryRequest{
		Lang:     lang,
		GameKind: gameKind,
	}

	resp, err := c.LangRPC.GetGamePlayType(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *langContext) GetGameRoundResult(ctx context.Context, lang string, gameKind uint32) (*langclient.GameDictionaryResponse, error) {
	request := &langclient.GameDictionaryRequest{
		Lang:     lang,
		GameKind: gameKind,
	}

	resp, err := c.LangRPC.GetGameRoundResult(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}

func (c *langContext) GetWeblateLang(ctx context.Context, lang string, category string) (*langclient.WeblateResponse, error) {
	request := &langclient.WeblateRequest{
		Lang:     lang,
		Category: category,
	}

	resp, err := c.LangRPC.GetWeblateLang(ctx, request)

	if err != nil {
		return nil, errorx.GRPCErrorToErrorx(err)
	}

	return resp, nil
}
