package lang

import (
	"gbh/backstage/internal/seeder"
	"gbh/errorx"
	"gbh/lang/langclient"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestNew(t *testing.T) {
	langRPC := newMockLangRPC()
	langCtx := New(langRPC)

	expectedResponse := &langContext{
		LangRPC: langRPC,
	}

	assert.Equal(t, expectedResponse, langCtx)
}

func Test_LangContext_GetGameFeatures(t *testing.T) {
	langRPC := newMockLangRPC()

	mockRequest := &langclient.GameDictionaryRequest{
		Lang:     "zh-tw",
		GameKind: 3,
	}
	langRPC.On("GetGameFeatures", ctx, mockRequest).Return(&seeder.GetGameFeaturesZhTw, nil)

	langCtx := New(langRPC)

	resp, err := langCtx.GetGameFeatures(ctx, mockRequest.GetLang(), mockRequest.GetGameKind())

	expectedResponse := &seeder.GetGameFeaturesZhTw

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LangContext_GetGameFeatures_GRPCError(t *testing.T) {
	langRPC := newMockLangRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &langclient.GameDictionaryRequest{
		Lang:     "zh-tw",
		GameKind: 3,
	}
	langRPC.On("GetGameFeatures", ctx, mockRequest).Return(nil, mockError)

	langCtx := New(langRPC)

	resp, err := langCtx.GetGameFeatures(ctx, mockRequest.GetLang(), mockRequest.GetGameKind())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_LangContext_GetGamePlayType(t *testing.T) {
	langRPC := newMockLangRPC()

	mockRequest := &langclient.GameDictionaryRequest{
		Lang:     "zh-tw",
		GameKind: 3,
	}
	langRPC.On("GetGamePlayType", ctx, mockRequest).Return(&seeder.GetGamePlayTypeZhTw, nil)

	langCtx := New(langRPC)

	resp, err := langCtx.GetGamePlayType(ctx, mockRequest.GetLang(), mockRequest.GetGameKind())

	expectedResponse := &seeder.GetGamePlayTypeZhTw

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LangContext_GetGamePlayType_GRPCError(t *testing.T) {
	langRPC := newMockLangRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &langclient.GameDictionaryRequest{
		Lang:     "zh-tw",
		GameKind: 3,
	}
	langRPC.On("GetGamePlayType", ctx, mockRequest).Return(nil, mockError)

	langCtx := New(langRPC)

	resp, err := langCtx.GetGamePlayType(ctx, mockRequest.GetLang(), mockRequest.GetGameKind())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_LangContext_GetGameRoundResult(t *testing.T) {
	langRPC := newMockLangRPC()

	mockRequest := &langclient.GameDictionaryRequest{
		Lang:     "zh-tw",
		GameKind: 3,
	}
	langRPC.On("GetGameRoundResult", ctx, mockRequest).Return(&seeder.GetGameRoundResultZhTw, nil)

	langCtx := New(langRPC)

	resp, err := langCtx.GetGameRoundResult(ctx, mockRequest.GetLang(), mockRequest.GetGameKind())

	expectedResponse := &seeder.GetGameRoundResultZhTw

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LangContext_GetGameRoundResult_GRPCError(t *testing.T) {
	langRPC := newMockLangRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &langclient.GameDictionaryRequest{
		Lang:     "zh-tw",
		GameKind: 3,
	}
	langRPC.On("GetGameRoundResult", ctx, mockRequest).Return(nil, mockError)

	langCtx := New(langRPC)

	resp, err := langCtx.GetGameRoundResult(ctx, mockRequest.GetLang(), mockRequest.GetGameKind())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func Test_LangContext_GetWeblateLang(t *testing.T) {
	langRPC := newMockLangRPC()

	mockRequest := &langclient.WeblateRequest{
		Lang:     "zh-tw",
		Category: "layout",
	}
	langRPC.On("GetWeblateLang", ctx, mockRequest).Return(&seeder.GetWeblateLayoutZhTw, nil)

	langCtx := New(langRPC)

	resp, err := langCtx.GetWeblateLang(ctx, mockRequest.GetLang(), mockRequest.GetCategory())

	expectedResponse := &seeder.GetWeblateLayoutZhTw

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, resp)
}

func Test_LangContext_GetWeblateLang_GRPCError(t *testing.T) {
	langRPC := newMockLangRPC()

	mockError := status.Error(codes.Code(errorx.ConnectionFailed.Code), errorx.ConnectionFailed.Message)
	mockRequest := &langclient.WeblateRequest{
		Lang:     "zh-tw",
		Category: "layout",
	}
	langRPC.On("GetWeblateLang", ctx, mockRequest).Return(nil, mockError)

	langCtx := New(langRPC)

	resp, err := langCtx.GetWeblateLang(ctx, mockRequest.GetLang(), mockRequest.GetCategory())

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}
