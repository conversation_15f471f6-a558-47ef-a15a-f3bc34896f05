syntax = "proto3";

package maintain;
option go_package = "proto/maintain";

message StringValue { string value = 1; }

message GetMaintainByGameKindRequest {
  uint32 game_kind = 1;
  StringValue client_ip = 2;
}

message GetMaintainByGameKindResponse {
  bool is_maintaining = 1;
  bool in_whitelist = 2;
  string begin_at = 3;
  string end_at = 4;
  string modified_at = 5;
  uint32 code = 6;
  string msg = 7;
}

message MaintainRequest { string search_type = 1; }

message MaintainResponse { bool state = 1; }

message GetMaintainByGameKindFromRedisRequest { uint32 game_kind = 1; }

message GetMaintainByGameKindFromRedisResponse {
  string start_time = 1;
  string end_time = 2;
  string message = 3;
  repeated string white_list = 4;
}

message GetMaintainByHallIDRequest {
  uint32 hall_id = 1;
  string client_ip = 2;
}

message GetMaintainByHallIDResponse {
  bool status = 1;
  string start_time = 2;
  string end_time = 3;
  string operator_time = 4;
}

message FeatureEntranceMaintenanceRequest {
  repeated string permission_name = 1;
  uint32 entrance = 2;
}

message FeatureEntranceMaintenanceResponse {
  repeated FeatureEntranceMaintenance maintain_list = 1;
}

message FeatureEntranceMaintenance {
  string feature = 1;
  string start_time = 2;
  string end_time = 3;
  string memo = 4;
  string permission_name = 5;
  uint32 entrance = 6;
}

message EmptyRequest {}

message GetMaintainGameKindResponse { repeated uint32 data = 1; }

message CreateLogoutScheduleRequest {
  uint32 hall_id = 1;
  string start_time = 2;
  string used_in = 3;
}

message EmptyResponse {}

message DeleteLogoutScheduleRequest {
  uint32 hall_id = 1;
  string used_in = 2;
}

message UpdateDomainMaintenanceRequest {
  uint32 hall_id = 1;
  string website = 2;
  string start_time = 3;
  string end_time = 4;
  string operator = 5;
}

message UpdateSystemDBMaintenanceNoteTableRequest {
  uint32 hall_id = 1;
  string note = 2;
}

message DeleteDomainMessageRequest {
  uint32 hall_id = 1;
  string used_in = 2;
  uint32 send_type = 3;
}

message GetSystemDBMaintenanceTableResponse {
  repeated FetchMaintenanceTable list = 1;
}

message FetchMaintenanceTable {
  string website = 1;
  sint32 status = 2;
}

message CreateDomainMessageRequest {
  uint32 operator_id = 1;
  string operator = 2;
  uint32 hall_id = 3;
  uint32 send_type = 4;
  string used_in = 5;
  bool set_status = 6;
  string start_time = 7;
  string end_time = 8;
}

service Maintain {
  rpc GetMaintainByGameKind(GetMaintainByGameKindRequest)
      returns (GetMaintainByGameKindResponse);
  rpc Get(MaintainRequest) returns (MaintainResponse);
  rpc GetMaintainByGameKindFromRedis(GetMaintainByGameKindFromRedisRequest)
      returns (GetMaintainByGameKindFromRedisResponse);
  rpc GetMaintainByHallID(GetMaintainByHallIDRequest)
      returns (GetMaintainByHallIDResponse);
  rpc FeatureEntranceMaintenance(FeatureEntranceMaintenanceRequest)
      returns (FeatureEntranceMaintenanceResponse);
  rpc GetMaintainGameKind(EmptyRequest) returns (GetMaintainGameKindResponse);
  rpc CreateLogoutSchedule(CreateLogoutScheduleRequest) returns (EmptyResponse);
  rpc DeleteLogoutSchedule(DeleteLogoutScheduleRequest) returns (EmptyResponse);
  rpc UpdateDomainMaintenance(UpdateDomainMaintenanceRequest)
      returns (EmptyResponse);
  rpc UpdateSystemDBMaintenanceNoteTable(
      UpdateSystemDBMaintenanceNoteTableRequest) returns (EmptyResponse);
  rpc DeleteDomainMessage(DeleteDomainMessageRequest) returns (EmptyResponse);
  rpc GetSystemDBMaintenanceTable(EmptyRequest)
      returns (GetSystemDBMaintenanceTableResponse);
  rpc CreateDomainMessage(CreateDomainMessageRequest) returns (EmptyResponse);
}
