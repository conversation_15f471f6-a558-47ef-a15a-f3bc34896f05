package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/logger"
	"gbh/maintain/internal/config"
	"gbh/maintain/internal/server"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"
	"gbh/redis"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/maintain.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	apiServiceDB, err := database.New(c.APIServiceDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	sportMemDB, err := database.New(c.SportMemDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	csMemDB, err := database.New(c.CsMemDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	systemDB, err := database.New(c.SystemDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	scheduleDB, err := database.New(c.ScheduleDBConf, customLogger)

	if err != nil {
		log.Fatalln(err)
	}

	pdRedisClient := redis.New(c.PDRedisConf, customLogger)
	extSvc := svc.ExternalContext{
		APIServiceDB: apiServiceDB,
		SportMemDB:   sportMemDB,
		PDRedis:      pdRedisClient,
		CsMemDB:      csMemDB,
		SystemDB:     systemDB,
		ScheduleDB:   scheduleDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		maintain.RegisterMaintainServer(grpcServer, server.NewMaintainServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "maintain",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
