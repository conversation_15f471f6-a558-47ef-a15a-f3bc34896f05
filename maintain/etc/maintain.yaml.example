Name: maintain.rpc
ListenOn: 0.0.0.0:8080
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: maintain.rpc

ACCConf:
  Schema: http
  Host: bgp.durian
  IP: 127.0.0.1
  Port: 80

APIServiceDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: APIService
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: APIService
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

SportMemDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: SPORT_MEM
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: SPORT_MEM
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

PDRedisConf:
  Type: standalone
  Hosts:
    - 127.0.0.1:6379
  Password:
  PoolSize: 10
  Database: 0

LogConf:
  Output: stdout
  FilePath: logs/
  Level: info

CsMemDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: CS_MEM
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: CS_MEM
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

SystemDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: SystemDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: SystemDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

ScheduleDBConf:
  Master:
    Host: 127.0.0.1
    Port: 3306
    Database: ScheduleDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200
  Slave:
    Host: 127.0.0.1
    Port: 3306
    Database: ScheduleDB
    Username: username
    Password: password
    MaxIdleConn: 10
    MaxOpenConn: 10
    SlowThreshold: 200

Middlewares:
  Stat: false
