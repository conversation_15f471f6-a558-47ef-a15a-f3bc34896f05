package schema

type DomainMaintenance struct {
	Domain         uint32 `gorm:"column:domain"`
	Website        string `gorm:"column:website"`
	Status         bool   `gorm:"column:status"`
	NoticeInterval uint32 `gorm:"column:notice_interval"`
	Operator       string `gorm:"column:operator"`
	StartAt        string `gorm:"column:start_at"`
	EndAt          string `gorm:"column:end_at"`
	ModifyAt       string `gorm:"column:modify_at"`
}
