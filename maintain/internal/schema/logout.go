package schema

type Logout struct {
	ID               uint32  `gorm:"column:id"`
	Domain           uint32  `gorm:"column:domain"`
	ExecutionSucceed bool    `gorm:"column:execution_succeed"`
	ExecutionAt      string  `gorm:"column:execution_at"` // 執行時間(美東)
	CreatedAt        string  `gorm:"column:created_at"`   // 新增時間(美東)
	DeletedAt        *string `gorm:"column:deleted_at"`   // 刪除時間(美東)
	UsedIn           string  `gorm:"column:used_in"`
	Locking          bool    `gorm:"column:locking"`
	Remark           *string `gorm:"column:remark"`
}
