package schema

type SystemDBMaintenanceTable struct {
	Website                string `gorm:"column:website"`
	Status                 int32  `gorm:"column:status"`
	ITalkingNoticeInterval int32  `gorm:"column:italking_notice_interval"`
	StartAT                string `gorm:"column:start_at"`
	EndAT                  string `gorm:"column:end_at"`
	ModifyAT               string `gorm:"column:modify_at"`
}

type SystemDBMaintenanceNoteTable struct {
	Domain   string `gorm:"column:domain"`
	Note     string `gorm:"column:note"`
	ModifyAT string `gorm:"column:modify_at"`
}
