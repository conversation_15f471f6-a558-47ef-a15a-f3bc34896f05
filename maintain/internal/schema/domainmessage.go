package schema

type DomainMessage struct {
	ID               uint32  `gorm:"column:id"`
	Domain           uint32  `gorm:"column:domain"`
	SendType         uint32  `gorm:"column:send_type"`
	SubjectTW        string  `gorm:"column:subject_tw"`
	ContentTW        string  `gorm:"column:content_tw"`
	SubjectCN        string  `gorm:"column:subject_cn"`
	ContentCN        string  `gorm:"column:content_cn"`
	SubjectEN        string  `gorm:"column:subject_en"`
	ContentEN        string  `gorm:"column:content_en"`
	Category         uint32  `gorm:"column:category"`
	Operator         string  `gorm:"column:operator"`
	OperatorID       uint32  `gorm:"column:operator_id"`
	ExecutionSucceed bool    `gorm:"column:execution_succeed"`
	ExecutionAt      string  `gorm:"column:execution_at"`
	CreatedAt        string  `gorm:"column:created_at"`
	DeletedAt        *string `gorm:"column:deleted_at"`
	UsedIn           string  `gorm:"column:used_in"`
	Locking          bool    `gorm:"column:locking"`
	Remark           *string `gorm:"column:remark"`
}
