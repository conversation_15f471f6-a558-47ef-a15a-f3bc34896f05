package svc

import (
	"crypto/tls"
	"fmt"
	"gbh/maintain/internal/config"
	"gbh/maintain/internal/constants"
	"gbh/redis"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config       config.Config
	AccClient    *resty.Client
	APIServiceDB *gorm.DB
	SportMemDB   *gorm.DB
	PDRedis      redis.Redis
	CsMemDB      *gorm.DB
	SystemDB     *gorm.DB
	ScheduleDB   *gorm.DB
}

func NewServiceContext(c config.Config, extSvc ExternalContext) *ServiceContext {
	accClient := resty.New()
	baseURL := fmt.Sprintf("%s://%s:%d", c.ACCConf.Schema, c.ACCConf.IP, c.ACCConf.Port)
	accClient.SetBaseURL(baseURL)
	accClient.SetTimeout(constants.APITimeout)
	accClient.SetHeader("Host", c.ACCConf.Host)

	if c.ACCConf.Schema == "https" {
		accClient.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}

	return &ServiceContext{
		Config:       c,
		AccClient:    accClient,
		APIServiceDB: extSvc.APIServiceDB,
		SportMemDB:   extSvc.SportMemDB,
		PDRedis:      extSvc.PDRedis,
		CsMemDB:      extSvc.CsMemDB,
		SystemDB:     extSvc.SystemDB,
		ScheduleDB:   extSvc.ScheduleDB,
	}
}
