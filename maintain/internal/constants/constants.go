package constants

import "time"

const APITimeout = 5 * time.Second

const TimezoneGMT4 = "Etc/GMT+4"

const MemberEntrance = 3

const (
	ACCGameMaintainNotFound = 150100005
	ACCInvalidIP            = 150100026
)

const (
	ACCGetMaintainByGameKindAPI = "/api/maintain/game/%d"
	ACCGetMaintainGameListAPI   = "/api/maintain/game_list"
)

const (
	MaintainUsedInDomain    = "DomainMaintenance"
	MaintainUsedInAgent     = "AgentMaintenance"
	SiteNameDomain          = "廳主端"
	SiteNameAgent           = "管端"
	WebAgentSite            = "%s (Agent Site)"
	NoticeSubjectTw         = "【整合站 %s 維護通知】"
	NoticeSubjectCn         = "【整合站 %s 维护通知】"
	NoticeSubjectEn         = "【IPL agent site server maintenance】"
	NoticeContentTw         = WebAgentSite + " 維護通知，若有任何疑問，請您與專員連繫▍\n造成不便請見諒，在此通知您 謝謝 ▍"
	NoticeContentCn         = WebAgentSite + " 维护通知，若有任何疑问，请您与专员连系▍\n造成不便请见谅，在此通知您 谢谢 ▍"
	NoticeContentEn         = "If you have any question during maintenance, please contact the customer service. \n ▍We apologize for any inconvenience caused. ▍"
	NoticeContentWithTimeTw = WebAgentSite + " ：北京時間 %s ~ %s \n ▍造成不便請見諒，在此通知您 謝謝 ▍"
	NoticeContentWithTimeCn = WebAgentSite + " ：北京时间 %s ~ %s \n ▍造成不便请见谅，在此通知您 谢谢 ▍"
	NoticeContentWithTimeEn = "Agent Site: (UTC+8) %s ~ %s \n ▍We apologize for any inconvenience caused. ▍"
	DoneSubjectTw           = WebAgentSite + " 維護完成通知"
	DoneSubjectCn           = WebAgentSite + " 维护完成通知"
	DoneSubjectEn           = "Agent Site Maintenance Complete"
	DoneContentTw           = DoneSubjectTw + " \n _____通知您"
	DoneContentCn           = DoneSubjectCn + " \n _____通知您"
	DoneContentEn           = DoneSubjectEn + " \n Please be well informed."
	MaintainCategory        = 2
)
