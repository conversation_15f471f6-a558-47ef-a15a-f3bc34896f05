package config

import (
	"gbh/database"
	"gbh/logger"
	"gbh/redis"

	"github.com/zeromicro/go-zero/zrpc"
)

type ACCConf struct {
	Schema string
	Host   string
	IP     string
	Port   int
}

type Config struct {
	zrpc.RpcServerConf
	ACCConf          ACCConf
	APIServiceDBConf database.Conf
	SportMemDBConf   database.Conf
	LogConf          logger.Conf
	PDRedisConf      redis.Conf
	CsMemDBConf      database.Conf
	SystemDBConf     database.Conf
	ScheduleDBConf   database.Conf
}
