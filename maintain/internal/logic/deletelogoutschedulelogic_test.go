package logic

import (
	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/proto/maintain"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestDeleteLogoutScheduleLogic_Success(t *testing.T) {
	request := maintain.DeleteLogoutScheduleRequest{
		HallId: 3820474,
		UsedIn: "DomainMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("DELETE FROM `Logout` WHERE execution_at > ? AND execution_succeed = ? AND used_in = ? AND domain = ? AND locking = ?").
		WithArgs(now, false, request.GetUsedIn(), request.GetHallId(), false).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewDeleteLogoutScheduleLogic(ctx, svcCtx)
	resp, err := l.DeleteLogoutSchedule(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestDeleteLogoutScheduleLogic_DatabaseError(t *testing.T) {
	request := maintain.DeleteLogoutScheduleRequest{
		HallId: 3820474,
		UsedIn: "DomainMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("DELETE FROM `Logout` WHERE execution_at > ? AND execution_succeed = ? AND used_in = ? AND domain = ? AND locking = ?").
		WithArgs(now, false, request.GetUsedIn(), request.GetHallId(), false).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockSchedule.ExpectRollback()

	l := NewDeleteLogoutScheduleLogic(ctx, svcCtx)
	resp, err := l.DeleteLogoutSchedule(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}
