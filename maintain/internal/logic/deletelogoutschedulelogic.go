package logic

import (
	"context"

	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLogoutScheduleLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteLogoutScheduleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLogoutScheduleLogic {
	return &DeleteLogoutScheduleLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteLogoutScheduleLogic) DeleteLogoutSchedule(in *maintain.DeleteLogoutScheduleRequest) (*maintain.EmptyResponse, error) {
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	deleteErr := l.svcCtx.ScheduleDB.Table("Logout").
		Where("execution_at > ?", now).
		Where("execution_succeed = ?", false).
		Where("used_in = ?", in.GetUsedIn()).
		Where("domain = ?", in.GetHallId()).
		Where("locking = ?", false).
		Delete(nil).Error

	if deleteErr != nil {
		return nil, errorx.DatabaseError
	}

	return &maintain.EmptyResponse{}, nil
}
