package logic

import (
	"context"
	"fmt"

	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/maintain/internal/schema"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CreateDomainMessageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDomainMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDomainMessageLogic {
	return &CreateDomainMessageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

type Message struct {
	SubjectTw string
	SubjectCn string
	SubjectEn string
	ContentTw string
	ContentCn string
	ContentEn string
}

func (l *CreateDomainMessageLogic) CreateDomainMessage(in *maintain.CreateDomainMessageRequest) (*maintain.EmptyResponse, error) {
	var siteName = constants.SiteNameAgent

	if in.GetUsedIn() == constants.MaintainUsedInDomain {
		siteName = constants.SiteNameDomain
	}

	message := buildMaintainNotice(in, siteName)

	var now = carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	saveData := schema.DomainMessage{
		Domain:      in.GetHallId(),
		SendType:    in.GetSendType(),
		SubjectTW:   message.SubjectTw,
		SubjectCN:   message.SubjectCn,
		SubjectEN:   message.SubjectEn,
		ContentTW:   message.ContentTw,
		ContentCN:   message.ContentCn,
		ContentEN:   message.ContentEn,
		Category:    constants.MaintainCategory,
		Operator:    in.GetOperator(),
		OperatorID:  in.GetOperatorId(),
		ExecutionAt: now,
		CreatedAt:   now,
		UsedIn:      in.GetUsedIn(),
	}

	err := l.svcCtx.ScheduleDB.Transaction(func(tx *gorm.DB) error {
		domainMessageErr := tx.Table("DomainMessage").Create(&saveData).Error

		if domainMessageErr != nil {
			return domainMessageErr
		}

		domainMessageDomainErr := tx.Table("DomainMessageDomain").
			Create(&schema.DomainMessageDomain{
				DomainMessageID: saveData.ID,
				Domain:          in.GetHallId(),
			}).Error

		if domainMessageDomainErr != nil {
			return domainMessageDomainErr
		}

		return nil
	})

	if err != nil {
		return nil, errorx.DatabaseError
	}

	return &maintain.EmptyResponse{}, nil
}

func buildMaintainNotice(in *maintain.CreateDomainMessageRequest, siteName string) Message {
	var message Message

	if in.GetSetStatus() {
		message.SubjectTw = fmt.Sprintf(constants.NoticeSubjectTw, siteName)
		message.SubjectCn = fmt.Sprintf(constants.NoticeSubjectCn, siteName)
		message.SubjectEn = constants.NoticeSubjectEn

		if in.GetStartTime() != "" && in.GetEndTime() != "" {
			var taipeiStartTime = carbon.Parse(in.GetStartTime()).ToDateTimeString(carbon.Taipei)
			var taipeiEndTime = carbon.Parse(in.GetEndTime()).ToDateTimeString(carbon.Taipei)

			message.ContentTw = fmt.Sprintf(constants.NoticeContentWithTimeTw, siteName, taipeiStartTime, taipeiEndTime)
			message.ContentCn = fmt.Sprintf(constants.NoticeContentWithTimeCn, siteName, taipeiStartTime, taipeiEndTime)
			message.ContentEn = fmt.Sprintf(constants.NoticeContentWithTimeEn, taipeiStartTime, taipeiEndTime)
		} else {
			message.ContentTw = fmt.Sprintf(constants.NoticeContentTw, siteName)
			message.ContentCn = fmt.Sprintf(constants.NoticeContentCn, siteName)
			message.ContentEn = constants.NoticeContentEn
		}
	} else {
		message.SubjectTw = fmt.Sprintf(constants.DoneSubjectTw, siteName)
		message.SubjectCn = fmt.Sprintf(constants.DoneSubjectCn, siteName)
		message.SubjectEn = constants.DoneSubjectEn

		message.ContentTw = fmt.Sprintf(constants.DoneContentTw, siteName)
		message.ContentCn = fmt.Sprintf(constants.DoneContentCn, siteName)
		message.ContentEn = constants.DoneContentEn
	}

	return message
}
