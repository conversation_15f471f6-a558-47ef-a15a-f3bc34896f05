package logic

import (
	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/proto/maintain"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestDeleteDomainMessageLogic_Success(t *testing.T) {
	request := maintain.DeleteDomainMessageRequest{
		HallId:   3820474,
		UsedIn:   "DomainMaintenance",
		SendType: 0,
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("DELETE FROM `DomainMessage` WHERE execution_at > ? AND execution_succeed = ? AND domain = ? AND send_type = ? AND used_in = ? AND locking = ?").
		WithArgs(now, false, request.GetHallId(), request.GetSendType(), request.GetUsedIn(), false).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewDeleteDomainMessageLogic(ctx, svcCtx)
	resp, err := l.DeleteDomainMessage(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestDeleteDomainMessageLogic_DatabaseError(t *testing.T) {
	request := maintain.DeleteDomainMessageRequest{
		HallId:   3820474,
		UsedIn:   "DomainMaintenance",
		SendType: 0,
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("DELETE FROM `DomainMessage` WHERE execution_at > ? AND execution_succeed = ? AND domain = ? AND send_type = ? AND used_in = ? AND locking = ?").
		WithArgs(now, false, request.GetHallId(), request.GetSendType(), request.GetUsedIn(), false).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockSchedule.ExpectRollback()

	l := NewDeleteDomainMessageLogic(ctx, svcCtx)
	resp, err := l.DeleteDomainMessage(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}
