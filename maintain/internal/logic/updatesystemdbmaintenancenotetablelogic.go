package logic

import (
	"context"

	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/maintain/internal/schema"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateSystemDBMaintenanceNoteTableLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateSystemDBMaintenanceNoteTableLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateSystemDBMaintenanceNoteTableLogic {
	return &UpdateSystemDBMaintenanceNoteTableLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateSystemDBMaintenanceNoteTableLogic) UpdateSystemDBMaintenanceNoteTable(in *maintain.UpdateSystemDBMaintenanceNoteTableRequest) (*maintain.EmptyResponse, error) {
	query := l.svcCtx.SystemDB.Table("MaintenanceNote").
		Where("domain = ?", in.GetHallId()).
		Updates(schema.SystemDBMaintenanceNoteTable{
			Note:     in.GetNote(),
			ModifyAT: carbon.Now(constants.TimezoneGMT4).ToDateTimeString(),
		})

	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	return &maintain.EmptyResponse{}, nil
}
