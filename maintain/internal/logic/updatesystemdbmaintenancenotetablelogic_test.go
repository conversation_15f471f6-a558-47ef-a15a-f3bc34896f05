package logic

import (
	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/proto/maintain"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func Test_UpdateSystemDBMaintenanceNoteTableLogic_Get(t *testing.T) {
	request := maintain.UpdateSystemDBMaintenanceNoteTableRequest{
		HallId: 3820635,
		Note:   "Test_UpdateSystemDBMaintenanceNoteTableLogic_Get",
	}
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSystem.ExpectBegin()
	sqlMockSystem.ExpectExec("UPDATE `MaintenanceNote` SET `note`=?,`modify_at`=? WHERE domain = ?").
		WithArgs(request.GetNote(), now, request.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))
	sqlMockSystem.ExpectCommit()

	l := NewUpdateSystemDBMaintenanceNoteTableLogic(ctx, svcCtx)
	resp, err := l.UpdateSystemDBMaintenanceNoteTable(&request)

	assert.NoError(t, err)
	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, sqlMockSystem.ExpectationsWereMet())
}

func Test_UpdateSystemDBMaintenanceNoteTableLogic_DatabaseError(t *testing.T) {
	request := maintain.UpdateSystemDBMaintenanceNoteTableRequest{
		HallId: 3820635,
		Note:   "Test_UpdateSystemDBMaintenanceNoteTableLogic_Get",
	}
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSystem.ExpectBegin()
	sqlMockSystem.ExpectExec("UPDATE `MaintenanceNote` SET `note`=?,`modify_at`=? WHERE domain = ?").
		WithArgs(request.GetNote(), now, request.GetHallId()).
		WillReturnError(gorm.ErrInvalidDB)
	sqlMockSchedule.ExpectRollback()

	l := NewUpdateSystemDBMaintenanceNoteTableLogic(ctx, svcCtx)
	resp, err := l.UpdateSystemDBMaintenanceNoteTable(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSystem.ExpectationsWereMet())
}
