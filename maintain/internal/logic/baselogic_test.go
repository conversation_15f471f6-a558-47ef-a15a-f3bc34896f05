package logic

import (
	"context"
	"fmt"
	"gbh/maintain/internal/config"
	"gbh/maintain/internal/svc"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	ctx               context.Context
	svcCtx            *svc.ServiceContext
	accConf           config.ACCConf
	accURL            string
	sqlMockAPIService sqlmock.Sqlmock
	gormAPIServiceDB  *gorm.DB
	sqlMockSportMem   sqlmock.Sqlmock
	gormSportMemDB    *gorm.DB
	redisCache        *redis.Client
	redisMock         redismock.ClientMock
	sqlMockCsMem      sqlmock.Sqlmock
	gormCsMemDB       *gorm.DB
	sqlMockSystem     sqlmock.Sqlmock
	gormSystemDB      *gorm.DB
	sqlMockSchedule   sqlmock.Sqlmock
	gormScheduleDB    *gorm.DB
)

func init() {
	ctx = context.Background()
	accConf = config.ACCConf{
		Schema: "http",
		Host:   "bgp.durian",
		IP:     "127.0.0.1",
		Port:   80,
	}
	accURL = fmt.Sprintf("%s://%s:%d", accConf.Schema, accConf.IP, accConf.Port)
	conf := config.Config{
		ACCConf: accConf,
	}

	gormAPIServiceDB, sqlMockAPIService = NewMockDB()

	gormSportMemDB, sqlMockSportMem = NewMockDB()

	gormCsMemDB, sqlMockCsMem = NewMockDB()

	gormSystemDB, sqlMockSystem = NewMockDB()

	gormScheduleDB, sqlMockSchedule = NewMockDB()

	redisCache, redisMock = redismock.NewClientMock()

	svcCtx = svc.NewServiceContext(conf, svc.ExternalContext{
		APIServiceDB: gormAPIServiceDB,
		SportMemDB:   gormSportMemDB,
		PDRedis:      redisCache,
		CsMemDB:      gormCsMemDB,
		SystemDB:     gormSystemDB,
		ScheduleDB:   gormScheduleDB,
	})
}

func NewMockDB() (*gorm.DB, sqlmock.Sqlmock) {
	dbObj, sqlMock, _ := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
	dialector := mysql.New(mysql.Config{
		DSN:                       "sqlmock_db_0",
		DriverName:                "mysql",
		Conn:                      dbObj,
		SkipInitializeWithVersion: true,
	})

	gormDB, _ := gorm.Open(dialector, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})

	return gormDB, sqlMock
}
