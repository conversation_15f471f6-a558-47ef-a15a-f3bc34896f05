package logic

import (
	"context"

	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/maintain/internal/schema"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDomainMaintenanceLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDomainMaintenanceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDomainMaintenanceLogic {
	return &UpdateDomainMaintenanceLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateDomainMaintenanceLogic) UpdateDomainMaintenance(in *maintain.UpdateDomainMaintenanceRequest) (*maintain.EmptyResponse, error) {
	var startTime = carbon.Parse(in.GetStartTime(), constants.TimezoneGMT4).ToDateTimeString()
	var endTime = carbon.Parse(in.GetEndTime(), constants.TimezoneGMT4).ToDateTimeString()

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	updateData := schema.DomainMaintenance{
		Operator: in.GetOperator(),
		StartAt:  startTime,
		EndAt:    endTime,
		ModifyAt: now,
	}

	updateErr := l.svcCtx.SystemDB.Table("DomainMaintenance").
		Where("domain = ?", in.GetHallId()).
		Where("website = ?", in.GetWebsite()).
		Updates(&updateData).Error

	if updateErr != nil {
		return nil, errorx.DatabaseError
	}

	return &maintain.EmptyResponse{}, nil
}
