package logic

import (
	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/proto/maintain"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestUpdateDomainMaintenancelogic_Success(t *testing.T) {
	request := maintain.UpdateDomainMaintenanceRequest{
		HallId:    3820635,
		Website:   "domain",
		StartTime: "2025-05-10T00:00:00-04:00",
		EndTime:   "2025-05-10T01:00:00-04:00",
		Operator:  "annatest",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSystem.ExpectBegin()

	sqlMockSystem.ExpectExec("UPDATE `DomainMaintenance` SET `operator`=?,`start_at`=?,`end_at`=?,`modify_at`=? WHERE domain = ? AND website = ?").
		WithArgs(request.GetOperator(), "2025-05-10 00:00:00", "2025-05-10 01:00:00", now, request.GetHallId(), request.GetWebsite()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSystem.ExpectCommit()

	l := NewUpdateDomainMaintenanceLogic(ctx, svcCtx)
	resp, err := l.UpdateDomainMaintenance(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSystem.ExpectationsWereMet())
}

func TestUpdateDomainMaintenancelogic_DatabaseError(t *testing.T) {
	request := maintain.UpdateDomainMaintenanceRequest{
		HallId:    3820635,
		Website:   "domain",
		StartTime: "2025-05-10T00:00:00-04:00",
		EndTime:   "2025-05-10T01:00:00-04:00",
		Operator:  "annatest",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSystem.ExpectBegin()

	sqlMockSystem.ExpectExec("UPDATE `DomainMaintenance` SET `operator`=?,`start_at`=?,`end_at`=?,`modify_at`=? WHERE domain = ? AND website = ?").
		WithArgs(request.GetOperator(), "2025-05-10 00:00:00", "2025-05-10 01:00:00", now, request.GetHallId(), request.GetWebsite()).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockSchedule.ExpectRollback()

	l := NewUpdateDomainMaintenanceLogic(ctx, svcCtx)
	resp, err := l.UpdateDomainMaintenance(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSystem.ExpectationsWereMet())
}
