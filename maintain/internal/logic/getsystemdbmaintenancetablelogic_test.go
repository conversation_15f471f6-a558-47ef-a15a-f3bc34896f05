package logic

import (
	"gbh/errorx"
	"gbh/proto/maintain"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func Test_GetSystemDBMaintenanceTableLogic_Get(t *testing.T) {
	rows := sqlMockSystem.NewRows([]string{
		"website", "status", "italking_notice_interval", "start_at", "end_at", "modify_at"}).
		AddRow("domain", "0", "0", "2024-11-07 04:41:00", "2024-11-07 05:50:00", "2025-02-17 06:22:56")

	sqlMockSystem.ExpectQuery("SELECT * FROM `Maintenance`").
		WillReturnRows(rows)

	l := NewGetSystemDBMaintenanceTableLogic(ctx, svcCtx)
	resp, err := l.GetSystemDBMaintenanceTable(&maintain.EmptyRequest{})

	expected := &maintain.GetSystemDBMaintenanceTableResponse{
		List: []*maintain.FetchMaintenanceTable{
			{
				Website: "domain",
				Status:  0,
			},
		},
	}

	assert.Equal(t, expected, resp)
	assert.NoError(t, err)
}

func Test_GetSystemDBMaintenanceTableLogic_DatabaseError(t *testing.T) {
	rows := sqlMockSystem.NewRows([]string{
		"website", "status", "italking_notice_interval", "start_at", "end_at", "modify_at"}).
		AddRow("domain", "0", "0", "2024-11-07 04:41:00", "2024-11-07 05:50:00", "2025-02-17 06:22:56")

	sqlMockSystem.ExpectQuery("SELECT * FROM `Maintenance`").
		WillReturnRows(rows).
		WillReturnError(gorm.ErrInvalidDB)

	l := NewGetSystemDBMaintenanceTableLogic(ctx, svcCtx)
	resp, err := l.GetSystemDBMaintenanceTable(&maintain.EmptyRequest{})

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSystem.ExpectationsWereMet())
}
