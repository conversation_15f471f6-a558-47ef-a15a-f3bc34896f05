package logic

import (
	"context"

	"gbh/errorx"
	"gbh/maintain/internal/schema"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSystemDBMaintenanceTableLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetSystemDBMaintenanceTableLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSystemDBMaintenanceTableLogic {
	return &GetSystemDBMaintenanceTableLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetSystemDBMaintenanceTableLogic) GetSystemDBMaintenanceTable(_ *maintain.EmptyRequest) (*maintain.GetSystemDBMaintenanceTableResponse, error) {
	var fetchMaintenanceTable []schema.SystemDBMaintenanceTable
	query := l.svcCtx.SystemDB.Table("Maintenance").
		Find(&fetchMaintenanceTable)
	if query.Error != nil {
		return nil, errorx.DatabaseError
	}

	list := make([]*maintain.FetchMaintenanceTable, 0, len(fetchMaintenanceTable))
	for _, v := range fetchMaintenanceTable {
		list = append(list, &maintain.FetchMaintenanceTable{
			Website: v.Website,
			Status:  v.Status,
		})
	}

	return &maintain.GetSystemDBMaintenanceTableResponse{
		List: list,
	}, nil
}
