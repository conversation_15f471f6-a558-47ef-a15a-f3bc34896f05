package logic

import (
	"context"

	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/maintain/internal/schema"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateLogoutScheduleLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateLogoutScheduleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateLogoutScheduleLogic {
	return &CreateLogoutScheduleLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateLogoutScheduleLogic) CreateLogoutSchedule(in *maintain.CreateLogoutScheduleRequest) (*maintain.EmptyResponse, error) {
	startTime := carbon.Parse(in.GetStartTime(), constants.TimezoneGMT4).ToDateTimeString()
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	saveData := schema.Logout{
		Domain:      in.GetHallId(),
		ExecutionAt: startTime,
		CreatedAt:   now,
		UsedIn:      in.GetUsedIn(),
	}

	result := l.svcCtx.ScheduleDB.Table("Logout").Create(&saveData).Error

	if result != nil {
		return nil, errorx.DatabaseError
	}

	return &maintain.EmptyResponse{}, nil
}
