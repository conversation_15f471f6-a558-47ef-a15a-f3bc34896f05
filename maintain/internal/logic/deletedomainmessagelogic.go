package logic

import (
	"context"

	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/maintain/internal/svc"
	"gbh/proto/maintain"

	"github.com/dromara/carbon/v2"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDomainMessageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDomainMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDomainMessageLogic {
	return &DeleteDomainMessageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteDomainMessageLogic) DeleteDomainMessage(in *maintain.DeleteDomainMessageRequest) (*maintain.EmptyResponse, error) {
	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	deleteErr := l.svcCtx.ScheduleDB.Table("DomainMessage").
		Where("execution_at > ?", now).
		Where("execution_succeed = ?", false).
		Where("domain = ?", in.GetHallId()).
		Where("send_type = ?", in.GetSendType()).
		Where("used_in = ?", in.GetUsedIn()).
		Where("locking = ?", false).
		Delete(nil).Error

	if deleteErr != nil {
		return nil, errorx.DatabaseError
	}

	return &maintain.EmptyResponse{}, nil
}
