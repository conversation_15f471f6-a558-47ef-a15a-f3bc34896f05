package logic

import (
	"fmt"
	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/proto/maintain"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreateDomainMessageLogic_SetDomainSite(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  true,
		UsedIn:     "DomainMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.NoticeSubjectTw, constants.SiteNameDomain),
		SubjectCn: fmt.Sprintf(constants.NoticeSubjectCn, constants.SiteNameDomain),
		SubjectEn: constants.NoticeSubjectEn,
		ContentTw: fmt.Sprintf(constants.NoticeContentTw, constants.SiteNameDomain),
		ContentCn: fmt.Sprintf(constants.NoticeContentCn, constants.SiteNameDomain),
		ContentEn: constants.NoticeContentEn,
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessageDomain` (`domain_message_id`,`domain`) VALUES (?,?)").
		WithArgs(1, request.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateDomainMessageLogic_SetAgentSite(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  true,
		UsedIn:     "AgentMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.NoticeSubjectTw, constants.SiteNameAgent),
		SubjectCn: fmt.Sprintf(constants.NoticeSubjectCn, constants.SiteNameAgent),
		SubjectEn: constants.NoticeSubjectEn,
		ContentTw: fmt.Sprintf(constants.NoticeContentTw, constants.SiteNameAgent),
		ContentCn: fmt.Sprintf(constants.NoticeContentCn, constants.SiteNameAgent),
		ContentEn: constants.NoticeContentEn,
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessageDomain` (`domain_message_id`,`domain`) VALUES (?,?)").
		WithArgs(1, request.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateDomainMessageLogic_SetDomainSiteWithTime(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  true,
		UsedIn:     "DomainMaintenance",
		StartTime:  "2025-05-01T03:00:00-04:00",
		EndTime:    "2025-05-01T03:10:00-04:00",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	taipeiStartTime := "2025-05-01 15:00:00"
	taipeiEndTime := "2025-05-01 15:10:00"

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.NoticeSubjectTw, constants.SiteNameDomain),
		SubjectCn: fmt.Sprintf(constants.NoticeSubjectCn, constants.SiteNameDomain),
		SubjectEn: constants.NoticeSubjectEn,
		ContentTw: fmt.Sprintf(constants.NoticeContentWithTimeTw, constants.SiteNameDomain, taipeiStartTime, taipeiEndTime),
		ContentCn: fmt.Sprintf(constants.NoticeContentWithTimeCn, constants.SiteNameDomain, taipeiStartTime, taipeiEndTime),
		ContentEn: fmt.Sprintf(constants.NoticeContentWithTimeEn, taipeiStartTime, taipeiEndTime),
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessageDomain` (`domain_message_id`,`domain`) VALUES (?,?)").
		WithArgs(1, request.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateDomainMessageLogic_SetAgentSiteWithTime(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  true,
		UsedIn:     "AgentMaintenance",
		StartTime:  "2025-05-01T03:00:00-04:00",
		EndTime:    "2025-05-01T03:10:00-04:00",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	taipeiStartTime := "2025-05-01 15:00:00"
	taipeiEndTime := "2025-05-01 15:10:00"

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.NoticeSubjectTw, constants.SiteNameAgent),
		SubjectCn: fmt.Sprintf(constants.NoticeSubjectCn, constants.SiteNameAgent),
		SubjectEn: constants.NoticeSubjectEn,
		ContentTw: fmt.Sprintf(constants.NoticeContentWithTimeTw, constants.SiteNameAgent, taipeiStartTime, taipeiEndTime),
		ContentCn: fmt.Sprintf(constants.NoticeContentWithTimeCn, constants.SiteNameAgent, taipeiStartTime, taipeiEndTime),
		ContentEn: fmt.Sprintf(constants.NoticeContentWithTimeEn, taipeiStartTime, taipeiEndTime),
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessageDomain` (`domain_message_id`,`domain`) VALUES (?,?)").
		WithArgs(1, request.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateDomainMessageLogic_SetDomainSiteDone(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  false,
		UsedIn:     "DomainMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.DoneSubjectTw, constants.SiteNameDomain),
		SubjectCn: fmt.Sprintf(constants.DoneSubjectCn, constants.SiteNameDomain),
		SubjectEn: constants.DoneSubjectEn,
		ContentTw: fmt.Sprintf(constants.DoneContentTw, constants.SiteNameDomain),
		ContentCn: fmt.Sprintf(constants.DoneContentCn, constants.SiteNameDomain),
		ContentEn: constants.DoneContentEn,
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessageDomain` (`domain_message_id`,`domain`) VALUES (?,?)").
		WithArgs(1, request.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateDomainMessageLogic_SetAgentSiteDone(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  false,
		UsedIn:     "AgentMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.DoneSubjectTw, constants.SiteNameAgent),
		SubjectCn: fmt.Sprintf(constants.DoneSubjectCn, constants.SiteNameAgent),
		SubjectEn: constants.DoneSubjectEn,
		ContentTw: fmt.Sprintf(constants.DoneContentTw, constants.SiteNameAgent),
		ContentCn: fmt.Sprintf(constants.DoneContentCn, constants.SiteNameAgent),
		ContentEn: constants.DoneContentEn,
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessageDomain` (`domain_message_id`,`domain`) VALUES (?,?)").
		WithArgs(1, request.GetHallId()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateDomainMessageLogic_DomainMessage_DatabaseError(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  true,
		UsedIn:     "DomainMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.NoticeSubjectTw, constants.SiteNameDomain),
		SubjectCn: fmt.Sprintf(constants.NoticeSubjectCn, constants.SiteNameDomain),
		SubjectEn: constants.NoticeSubjectEn,
		ContentTw: fmt.Sprintf(constants.NoticeContentTw, constants.SiteNameDomain),
		ContentCn: fmt.Sprintf(constants.NoticeContentCn, constants.SiteNameDomain),
		ContentEn: constants.NoticeContentEn,
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockSchedule.ExpectRollback()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateDomainMessageLogic_DomainMessageDomain_DatabaseError(t *testing.T) {
	request := maintain.CreateDomainMessageRequest{
		OperatorId: 11579,
		Operator:   "annatest",
		HallId:     3820474,
		SendType:   0,
		SetStatus:  true,
		UsedIn:     "DomainMaintenance",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	var message = Message{
		SubjectTw: fmt.Sprintf(constants.NoticeSubjectTw, constants.SiteNameDomain),
		SubjectCn: fmt.Sprintf(constants.NoticeSubjectCn, constants.SiteNameDomain),
		SubjectEn: constants.NoticeSubjectEn,
		ContentTw: fmt.Sprintf(constants.NoticeContentTw, constants.SiteNameDomain),
		ContentCn: fmt.Sprintf(constants.NoticeContentCn, constants.SiteNameDomain),
		ContentEn: constants.NoticeContentEn,
	}

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessage` (`domain`,`send_type`,`subject_tw`,`content_tw`,`subject_cn`,`content_cn`,`subject_en`,`content_en`,`category`,`operator`,`operator_id`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), request.GetSendType(), message.SubjectTw, message.ContentTw, message.SubjectCn, message.ContentCn, message.SubjectEn, message.ContentEn, constants.MaintainCategory, request.GetOperator(), request.GetOperatorId(), false, now, now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectExec("INSERT INTO `DomainMessageDomain` (`domain_message_id`,`domain`) VALUES (?,?)").
		WithArgs(1, request.GetHallId()).WillReturnError(gorm.ErrInvalidDB)

	sqlMockSchedule.ExpectRollback()

	l := NewCreateDomainMessageLogic(ctx, svcCtx)
	resp, err := l.CreateDomainMessage(&request)

	assert.Nil(t, resp)
	assert.Equal(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}
