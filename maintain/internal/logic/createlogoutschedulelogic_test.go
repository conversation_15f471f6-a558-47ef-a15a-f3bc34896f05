package logic

import (
	"gbh/errorx"
	"gbh/maintain/internal/constants"
	"gbh/proto/maintain"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/dromara/carbon/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestCreateLogoutScheduleLogic_Success(t *testing.T) {
	request := maintain.CreateLogoutScheduleRequest{
		HallId:    3820474,
		UsedIn:    "DomainMaintenance",
		StartTime: "2025-05-01T00:00:00-04:00",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `Logout` (`domain`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), false, "2025-05-01 00:00:00", now, nil, request.GetUsedIn(), false, nil).
		WillReturnResult(sqlmock.NewResult(1, 1))

	sqlMockSchedule.ExpectCommit()

	l := NewCreateLogoutScheduleLogic(ctx, svcCtx)
	resp, err := l.CreateLogoutSchedule(&request)

	assert.Equal(t, &maintain.EmptyResponse{}, resp)
	assert.NoError(t, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}

func TestCreateLogoutScheduleLogic_DatabaseError(t *testing.T) {
	request := maintain.CreateLogoutScheduleRequest{
		HallId:    3820474,
		UsedIn:    "DomainMaintenance",
		StartTime: "2025-05-01T00:00:00-04:00",
	}

	now := carbon.Now(constants.TimezoneGMT4).ToDateTimeString()

	sqlMockSchedule.ExpectBegin()

	sqlMockSchedule.ExpectExec("INSERT INTO `Logout` (`domain`,`execution_succeed`,`execution_at`,`created_at`,`deleted_at`,`used_in`,`locking`,`remark`) VALUES (?,?,?,?,?,?,?,?)").
		WithArgs(request.GetHallId(), false, "2025-05-01 00:00:00", now, nil, request.GetUsedIn(), false, nil).
		WillReturnError(gorm.ErrInvalidDB)

	sqlMockSchedule.ExpectRollback()

	l := NewCreateLogoutScheduleLogic(ctx, svcCtx)
	resp, err := l.CreateLogoutSchedule(&request)

	assert.Nil(t, resp)
	assert.ErrorIs(t, errorx.DatabaseError, err)
	assert.NoError(t, sqlMockSchedule.ExpectationsWereMet())
}
