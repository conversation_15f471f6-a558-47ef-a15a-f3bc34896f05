syntax = "proto3";

package wallet;
option go_package = "proto/wallet";

message StringValue { string value = 1; }
message Uint32Value { uint32 value = 1; }
message BoolValue { bool value = 1; }
message RepeatedUint32Value { repeated uint32 value = 1; }
message EmptyRequest {}

message Pagination {
  uint32 first_result = 1;
  uint32 max_results = 2;
  uint32 total = 3;
}

message EntryStatusRequest {
  uint32 hall_id = 1;
  string ref_id = 2;
}

message EntryStatusResponse {
  uint32 user_id = 1;
  string username = 2;
  string ref_id = 3;
  double amount = 4;
  int32 status = 5;
}

message UsersBalanceRequest {
  repeated uint32 users = 1;
  string client_ip = 2;
}

message UsersBalanceResponse { repeated BalanceInfo users = 1; }

message BalanceInfo {
  uint32 user_id = 1;
  double balance = 2;
}

message SlotMachineBalanceRequest { uint32 user_id = 1; }

message SlotMachineBalanceResponse { double balance = 1; }

message WithdrawRequest {
  uint32 user_id = 1;
  double amount = 2;
  string ref_id = 3;
}

message WithdrawResponse { double balance = 1; }

message DepositRequest {
  uint32 user_id = 1;
  double amount = 2;
  string ref_id = 3;
}

message DepositResponse { double balance = 1; }

message TransferEntryRequest {
  uint32 user_id = 1;
  StringValue ref_id = 2;
  StringValue transfer_action = 3;
  string start_time = 4;
  string end_time = 5;
  Uint32Value first_result = 6;
  Uint32Value max_results = 7;
}

message TransferEntryResponse {
  repeated TransferEntry entry = 1;
  Pagination pagination = 2;
}

message TransferEntry {
  uint32 id = 1;
  uint32 user_id = 2;
  string username = 3;
  string currency = 4;
  string ref_id = 5;
  double amount = 6;
  double balance = 7;
  string created_at = 8;
  string memo = 9;
}

message TransferEntryListRequest {
  uint32 parent_id = 1;
  Uint32Value level = 2;
  StringValue ref_id = 3;
  RepeatedUint32Value opcode = 4;
  StringValue start_time = 5;
  StringValue end_time = 6;
  Uint32Value first_result = 7;
  Uint32Value max_results = 8;
}

message TransferEntryListResponse {
  repeated TransferEntry entry = 1;
  Pagination pagination = 2;
}

message UsersBalanceByAgentRequest {
  uint32 parent_id = 1;
  StringValue username = 2;
  Uint32Value first_result = 3;
  Uint32Value max_results = 4;
}

message UsersBalanceByAgentResponse {
  repeated BalanceByAgentInfo users = 1;
  Pagination pagination = 2;
}

message BalanceByAgentInfo {
  uint32 user_id = 1;
  string user_name = 2;
  double balance = 3;
  uint32 parent_id = 4;
  bool enable = 5;
  string created_at = 6;
}

message FishMachineBalanceRequest { uint32 user_id = 1; }

message FishMachineBalanceResponse { double balance = 1; }

message GetUserBalanceRequest { uint32 user_id = 1; }

message GetUserBalanceResponse { double balance = 1; }

message GetEntryListRequest {
  uint32 user_id = 1;
  string start_time = 2;
  string end_time = 3;
  repeated string op_code_group = 4;
  string sort = 5;
  uint32 first_result = 6;
  uint32 max_results = 7;
}

message GetEntryListResponse {
  repeated EntryDetail entry_info = 1;
  Pagination pagination = 2;
}

message EntryDetail {
  uint32 op_code = 1;
  string created_time = 2;
  double amount = 3;
  double balance = 4;
  string ref_id = 5;
  string memo = 6;
}

message GetTotalAmountRequest {
  uint32 user_id = 1;
  string start_time = 2;
  string end_time = 3;
  repeated string op_code_group = 4;
}

message GetTotalAmountResponse { double total = 1; }

message GetOpCodeListRequest {
  StringValue result_type = 1;
  Uint32Value game_kind = 2;
  StringValue detail_type = 3;
}

message GetOpCodeListResponse { repeated OpCodeDetail opcode_info = 1; }

message OpCodeDetail {
  uint32 id = 1;
  string category_id = 2;
  string result_type = 3;
  uint32 game_kind = 4;
  string game_id = 5;
  string note = 6;
}

message GetOpCodeDictRequest {
  uint32 id = 1;
  string language = 2;
}

message GetOpCodeDictResponse {
  uint32 id = 1;
  string dictionary = 2;
}

message AccountCloseDateRequest {}

message AccountCloseDateResponse { string date = 1; }

message Int64Value { int64 value = 1; }

message GetTransferEntryDetailRequest {
  string currency = 1;
  string start_time = 2;
  string end_time = 3;
  uint32 parent_id = 4;
  uint32 parent_role = 5;
  uint32 user_id = 6;
  uint64 ref_id = 7;
  repeated uint32 opcode = 8;
  repeated string group_name = 9;
  repeated string disable_group_name = 10;
  Int64Value gte_amount = 11;
  Int64Value lte_amount = 12;
  uint32 page = 13;
  uint32 page_limit = 14;
  repeated string sort = 15;
  repeated string order = 16;
}

message TransferEntryDetail {
  uint64 id = 1;
  uint32 hall_id = 2;
  uint32 parent_id = 3;
  uint32 user_id = 4;
  double amount = 5;
  double balance = 6;
  string created_at = 7;
  string currency = 8;
  string memo = 9;
  uint32 opcode = 10;
  uint64 ref_id = 11;
}

message GetTransferEntryDetailResponse {
  repeated TransferEntryDetail entry_detail = 1;
  uint32 total = 2;
}

message GetUserCashFakeRequest { uint32 user_id = 1; }

message GetUserCashFakeResponse {
  uint32 wallet_id = 1;
  double balance = 2;
  string currency = 3;
  bool enable = 4;
  string last_entry_at = 5;
}

message GetOpcodeListByACCRequest {
  repeated uint32 opcode = 1;
  BoolValue allow_balance_negative = 2;
  BoolValue allow_amount_zero = 3;
  BoolValue disable_not_allow = 4;
  BoolValue disable_for_cash_fake = 5;
  BoolValue cash_deposit_opcode = 6;
  BoolValue is_enabled = 7;
  string tw = 8;
  string cn = 9;
  string en = 10;
  repeated string sort = 11;
  repeated string order = 12;
  uint32 page = 13;
  uint32 page_limit = 14;
}

message GetOpcodeListByACCResponse {
  repeated OpcodeDetailByACC opcode_info = 1;
  PaginationByGTI pagination = 2;
}

message OpcodeDetailByACC {
  uint32 opcode = 1;
  bool allow_balance_negative = 2;
  bool allow_amount_zero = 3;
  bool disable_not_allow = 4;
  bool disable_for_cash_fake = 5;
  bool cash_deposit_opcode = 6;
  bool is_enabled = 7;
  string tw = 8;
  string cn = 9;
  string en = 10;
}

message PaginationByGTI {
  uint32 current_page = 1;
  uint32 page_limit = 2;
  uint32 total = 3;
  uint32 total_page = 4;
}

message GetExchangeRateRequest {
  string currency = 1;
  repeated string sort = 2;
  repeated string order = 3;
  uint32 current_page = 4;
  uint32 page_limit = 5;
}

message ExchangeRate {
  uint32 id = 1;
  string currency = 2;
  double buy = 3;
  double sell = 4;
  double basic = 5;
  string active_at = 6;
}

message GetExchangeRateResponse {
  repeated ExchangeRate exchange_rate = 1;
  PaginationByGTI pagination = 2;
}

message ExchangeRateListResponse { repeated ExchangeRate list = 1; }

message GetOpcodeGroupsListRequest {
  string group_name = 1;
  string parent_group_name = 2;
  uint32 opcode = 3;
  repeated string sort = 11;
  repeated string order = 12;
  uint32 page = 13;
  uint32 page_limit = 14;
}

message GetOpcodeGroupsListResponse {
  repeated OpcodeGroupsList opcode_groups_list = 1;
  PaginationByGTI pagination = 2;
}

message OpcodeGroupsList {
  uint32 id = 1;
  string group_name = 2;
  string parent_group_name = 3;
  uint32 opcode = 4;
}

message GetTransferEntryDetailAllRequest {
  string currency = 1;
  string start_time = 2;
  string end_time = 3;
  uint32 user_id = 4;
  BoolValue filter_opcode = 5;
  repeated string group_name = 6;
  repeated uint32 opcode = 7;
  uint64 ref_id = 8;
  repeated string order = 9;
  repeated string sort = 10;
  uint32 page = 11;
  uint32 page_limit = 12;
}

message GetTransferEntryDetailAllResponse {
  repeated TransferEntryDetail entry_detail = 1;
  PaginationByGTI pagination = 2;
}

message GetCurrencyListRequest { bool is_virtual = 1; }

message GetCurrencyListResponse { repeated string currency = 1; }

service Wallet {
  rpc EntryStatus(EntryStatusRequest) returns (EntryStatusResponse);
  rpc GetUsersBalance(UsersBalanceRequest) returns (UsersBalanceResponse);
  rpc SlotMachineBalance(SlotMachineBalanceRequest)
      returns (SlotMachineBalanceResponse);
  rpc Withdraw(WithdrawRequest) returns (WithdrawResponse);
  rpc Deposit(DepositRequest) returns (DepositResponse);
  rpc TransferEntry(TransferEntryRequest) returns (TransferEntryResponse);
  rpc TransferEntryList(TransferEntryListRequest)
      returns (TransferEntryListResponse);
  rpc FishMachineBalance(FishMachineBalanceRequest)
      returns (FishMachineBalanceResponse);
  rpc GetUsersBalanceByAgent(UsersBalanceByAgentRequest)
      returns (UsersBalanceByAgentResponse);
  rpc GetUserBalance(GetUserBalanceRequest) returns (GetUserBalanceResponse);
  rpc GetEntryList(GetEntryListRequest) returns (GetEntryListResponse);
  rpc GetTotalAmount(GetTotalAmountRequest) returns (GetTotalAmountResponse);
  rpc GetOpCodeList(GetOpCodeListRequest) returns (GetOpCodeListResponse);
  rpc GetOpCodeDict(GetOpCodeDictRequest) returns (GetOpCodeDictResponse);
  rpc GetAccountCloseDate(AccountCloseDateRequest)
      returns (AccountCloseDateResponse);
  rpc GetTransferEntryDetail(GetTransferEntryDetailRequest)
      returns (GetTransferEntryDetailResponse);
  rpc GetUserCashFake(GetUserCashFakeRequest) returns (GetUserCashFakeResponse);
  rpc GetOpcodeListByACC(GetOpcodeListByACCRequest)
      returns (GetOpcodeListByACCResponse);
  rpc GetExchangeRate(GetExchangeRateRequest) returns (GetExchangeRateResponse);
  rpc ExchangeRateList(EmptyRequest) returns (ExchangeRateListResponse);
  rpc GetOpcodeGroupsList(GetOpcodeGroupsListRequest)
      returns (GetOpcodeGroupsListResponse);
  rpc GetTransferEntryDetailAll(GetTransferEntryDetailAllRequest)
      returns (GetTransferEntryDetailAllResponse);
  rpc GetCurrencyList(GetCurrencyListRequest) returns (GetCurrencyListResponse);
}
