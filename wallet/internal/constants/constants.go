package constants

import "time"

const APITimeout = 5 * time.Second

const TimezoneGMT4 = "Etc/GMT+4"

const MemberEntrance = 3

const Precision = 4

const (
	ACCDuplicateRefId      int = *********
	ACCUserNotExist        int = *********
	ACCInsufficientBalance int = *********
	ACCUserSuspend         int = *********
	ACCUserNotFound        int = 150010181
)

const (
	ACCEntryStatusAPI              = "/api/cash_fake/entry/status"
	ACCGetUsersAPI                 = "/api/users"
	SlotMachineBalanceAPI          = "/api/casino/user/%d/machine_balance"
	ACCOperation                   = "/api/user/%d/cash_fake/op"
	ACCTransferEntry               = "/api/user/%d/cash_fake/transfer_entry"
	ACCTransferEntryList           = "/api/cash_fake/transfer_entry/list"
	FishMachineBalanceAPI          = "/GameServer/GetAmount"
	ACCGetUserListAPI              = "/api/user/list"
	ACCGetUserAPI                  = "/api/user/%d"
	ACCGetEntryAPI                 = "/api/user/%d/cash_fake/entry"
	ACCTotalAmountAPI              = "/api/user/%d/cash_fake/total_amount"
	HexCloseAccountAPI             = "/hex/report/close_account"
	BIGetTransferEntryDetailAPI    = "/api/cash_fake/entry/deal-detail"
	BIGetTransferEntryDetailAllAPI = "/api/cash_fake/entry/deal-detail/all"
	ACCGetUserCashFakeAPI          = "/api/user/%d/cash_fake"
	ACCGetOpcodeListAPI            = "/api/opcode/list"
	ACCGetExchangeRateAPI          = "/api/currency/%s/exchange/list"
	ACCGetCurrencyExchangeAPI      = "/api/currency/exchange"
	ACCGetOpcodeGroupsListAPI      = "/api/opcode_groups/list"
	ACCGetCurrencyAPI              = "/api/currency"
)

var OperationErrorCode = [...]int{
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
	*********,
}

const DefaultPageLimit20 = uint32(20)
