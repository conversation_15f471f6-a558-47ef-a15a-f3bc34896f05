// Code generated by goctl. DO NOT EDIT.
// Source: wallet.proto

package server

import (
	"context"

	"gbh/proto/wallet"
	"gbh/wallet/internal/logic"
	"gbh/wallet/internal/svc"
)

type WalletServer struct {
	svcCtx *svc.ServiceContext
	wallet.UnimplementedWalletServer
}

func NewWalletServer(svcCtx *svc.ServiceContext) *WalletServer {
	return &WalletServer{
		svcCtx: svcCtx,
	}
}

func (s *WalletServer) EntryStatus(ctx context.Context, in *wallet.EntryStatusRequest) (*wallet.EntryStatusResponse, error) {
	l := logic.NewEntryStatusLogic(ctx, s.svcCtx)
	return l.EntryStatus(in)
}

func (s *WalletServer) GetUsersBalance(ctx context.Context, in *wallet.UsersBalanceRequest) (*wallet.UsersBalanceResponse, error) {
	l := logic.NewGetUsersBalanceLogic(ctx, s.svcCtx)
	return l.GetUsersBalance(in)
}

func (s *WalletServer) SlotMachineBalance(ctx context.Context, in *wallet.SlotMachineBalanceRequest) (*wallet.SlotMachineBalanceResponse, error) {
	l := logic.NewSlotMachineBalanceLogic(ctx, s.svcCtx)
	return l.SlotMachineBalance(in)
}

func (s *WalletServer) Withdraw(ctx context.Context, in *wallet.WithdrawRequest) (*wallet.WithdrawResponse, error) {
	l := logic.NewWithdrawLogic(ctx, s.svcCtx)
	return l.Withdraw(in)
}

func (s *WalletServer) Deposit(ctx context.Context, in *wallet.DepositRequest) (*wallet.DepositResponse, error) {
	l := logic.NewDepositLogic(ctx, s.svcCtx)
	return l.Deposit(in)
}

func (s *WalletServer) TransferEntry(ctx context.Context, in *wallet.TransferEntryRequest) (*wallet.TransferEntryResponse, error) {
	l := logic.NewTransferEntryLogic(ctx, s.svcCtx)
	return l.TransferEntry(in)
}

func (s *WalletServer) TransferEntryList(ctx context.Context, in *wallet.TransferEntryListRequest) (*wallet.TransferEntryListResponse, error) {
	l := logic.NewTransferEntryListLogic(ctx, s.svcCtx)
	return l.TransferEntryList(in)
}

func (s *WalletServer) FishMachineBalance(ctx context.Context, in *wallet.FishMachineBalanceRequest) (*wallet.FishMachineBalanceResponse, error) {
	l := logic.NewFishMachineBalanceLogic(ctx, s.svcCtx)
	return l.FishMachineBalance(in)
}

func (s *WalletServer) GetUsersBalanceByAgent(ctx context.Context, in *wallet.UsersBalanceByAgentRequest) (*wallet.UsersBalanceByAgentResponse, error) {
	l := logic.NewGetUsersBalanceByAgentLogic(ctx, s.svcCtx)
	return l.GetUsersBalanceByAgent(in)
}

func (s *WalletServer) GetUserBalance(ctx context.Context, in *wallet.GetUserBalanceRequest) (*wallet.GetUserBalanceResponse, error) {
	l := logic.NewGetUserBalanceLogic(ctx, s.svcCtx)
	return l.GetUserBalance(in)
}

func (s *WalletServer) GetEntryList(ctx context.Context, in *wallet.GetEntryListRequest) (*wallet.GetEntryListResponse, error) {
	l := logic.NewGetEntryListLogic(ctx, s.svcCtx)
	return l.GetEntryList(in)
}

func (s *WalletServer) GetTotalAmount(ctx context.Context, in *wallet.GetTotalAmountRequest) (*wallet.GetTotalAmountResponse, error) {
	l := logic.NewGetTotalAmountLogic(ctx, s.svcCtx)
	return l.GetTotalAmount(in)
}

func (s *WalletServer) GetOpCodeList(ctx context.Context, in *wallet.GetOpCodeListRequest) (*wallet.GetOpCodeListResponse, error) {
	l := logic.NewGetOpCodeListLogic(ctx, s.svcCtx)
	return l.GetOpCodeList(in)
}

func (s *WalletServer) GetOpCodeDict(ctx context.Context, in *wallet.GetOpCodeDictRequest) (*wallet.GetOpCodeDictResponse, error) {
	l := logic.NewGetOpCodeDictLogic(ctx, s.svcCtx)
	return l.GetOpCodeDict(in)
}

func (s *WalletServer) GetAccountCloseDate(ctx context.Context, in *wallet.AccountCloseDateRequest) (*wallet.AccountCloseDateResponse, error) {
	l := logic.NewGetAccountCloseDateLogic(ctx, s.svcCtx)
	return l.GetAccountCloseDate(in)
}

func (s *WalletServer) GetTransferEntryDetail(ctx context.Context, in *wallet.GetTransferEntryDetailRequest) (*wallet.GetTransferEntryDetailResponse, error) {
	l := logic.NewGetTransferEntryDetailLogic(ctx, s.svcCtx)
	return l.GetTransferEntryDetail(in)
}

func (s *WalletServer) GetUserCashFake(ctx context.Context, in *wallet.GetUserCashFakeRequest) (*wallet.GetUserCashFakeResponse, error) {
	l := logic.NewGetUserCashFakeLogic(ctx, s.svcCtx)
	return l.GetUserCashFake(in)
}

func (s *WalletServer) GetOpcodeListByACC(ctx context.Context, in *wallet.GetOpcodeListByACCRequest) (*wallet.GetOpcodeListByACCResponse, error) {
	l := logic.NewGetOpcodeListByACCLogic(ctx, s.svcCtx)
	return l.GetOpcodeListByACC(in)
}

func (s *WalletServer) GetExchangeRate(ctx context.Context, in *wallet.GetExchangeRateRequest) (*wallet.GetExchangeRateResponse, error) {
	l := logic.NewGetExchangeRateLogic(ctx, s.svcCtx)
	return l.GetExchangeRate(in)
}

func (s *WalletServer) ExchangeRateList(ctx context.Context, in *wallet.EmptyRequest) (*wallet.ExchangeRateListResponse, error) {
	l := logic.NewExchangeRateListLogic(ctx, s.svcCtx)
	return l.ExchangeRateList(in)
}

func (s *WalletServer) GetOpcodeGroupsList(ctx context.Context, in *wallet.GetOpcodeGroupsListRequest) (*wallet.GetOpcodeGroupsListResponse, error) {
	l := logic.NewGetOpcodeGroupsListLogic(ctx, s.svcCtx)
	return l.GetOpcodeGroupsList(in)
}

func (s *WalletServer) GetTransferEntryDetailAll(ctx context.Context, in *wallet.GetTransferEntryDetailAllRequest) (*wallet.GetTransferEntryDetailAllResponse, error) {
	l := logic.NewGetTransferEntryDetailAllLogic(ctx, s.svcCtx)
	return l.GetTransferEntryDetailAll(in)
}

func (s *WalletServer) GetCurrencyList(ctx context.Context, in *wallet.GetCurrencyListRequest) (*wallet.GetCurrencyListResponse, error) {
	l := logic.NewGetCurrencyListLogic(ctx, s.svcCtx)
	return l.GetCurrencyList(in)
}
