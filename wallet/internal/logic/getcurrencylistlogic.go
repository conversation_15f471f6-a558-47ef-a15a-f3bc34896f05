package logic

import (
	"context"
	"encoding/json"
	"net/http"

	"gbh/errorx"
	"gbh/proto/wallet"
	"gbh/utils/urlutil"
	"gbh/wallet/internal/constants"
	"gbh/wallet/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCurrencyListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCurrencyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCurrencyListLogic {
	return &GetCurrencyListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetCurrencyListLogic) GetCurrencyList(in *wallet.GetCurrencyListRequest) (*wallet.GetCurrencyListResponse, error) {
	params := urlutil.NewBuilder()

	if in.GetIsVirtual() {
		params.AddBool("is_virtual", true)
	}

	resp, err := l.svcCtx.AccClient.R().
		SetQueryParamsFromValues(params.Values()).
		Get(constants.ACCGetCurrencyAPI)

	if err != nil {
		return nil, errorx.ConnectionFailed
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, errorx.InvalidResponse
	}

	var accResp struct {
		Result string `json:"result"`
		Ret    []struct {
			Currency  string `json:"currency"`
			IsVirtual bool   `json:"is_virtual"`
		} `json:"ret"`
	}

	jsonParseErr := json.Unmarshal(resp.Body(), &accResp)

	if jsonParseErr != nil {
		return nil, errorx.JSONParseFailed
	}

	if accResp.Result != "ok" {
		return nil, errorx.InvalidResponse
	}

	currencyList := make([]string, 0, len(accResp.Ret))
	for _, v := range accResp.Ret {
		currencyList = append(currencyList, v.Currency)
	}

	return &wallet.GetCurrencyListResponse{Currency: currencyList}, nil
}
