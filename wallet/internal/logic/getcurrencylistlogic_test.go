package logic

import (
	"net/http"
	"testing"

	"gbh/errorx"
	"gbh/proto/wallet"
	"gbh/wallet/internal/constants"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
)

func TestGetCurrencyListLogic(t *testing.T) {
	request := wallet.GetCurrencyListRequest{}

	responder := httpmock.NewStringResponder(http.StatusOK, `{
		"ret": [
			{
				"currency": "CNY",
				"code": 156,
				"is_virtual": false
			},
			{
				"currency": "EUR",
				"code": 978,
				"is_virtual": false
			},
			{
				"currency": "GBP",
				"code": 826,
				"is_virtual": false
			},
			{
				"currency": "HKD",
				"code": 344,
				"is_virtual": false
			},
			{
				"currency": "IDR",
				"code": 360,
				"is_virtual": false
			},
			{
				"currency": "KIDR",
				"code": 3602,
				"is_virtual": false
			},
			{
				"currency": "JPY",
				"code": 392,
				"is_virtual": false
			},
			{
				"currency": "KRW",
				"code": 410,
				"is_virtual": false
			},
			{
				"currency": "MYR",
				"code": 458,
				"is_virtual": false
			},
			{
				"currency": "SGD",
				"code": 702,
				"is_virtual": false
			},
			{
				"currency": "THB",
				"code": 764,
				"is_virtual": false
			},
			{
				"currency": "TWD",
				"code": 901,
				"is_virtual": false
			},
			{
				"currency": "USD",
				"code": 840,
				"is_virtual": false
			},
			{
				"currency": "VND",
				"code": 704,
				"is_virtual": false
			},
			{
				"currency": "KVD",
				"code": 7042,
				"is_virtual": false
			},
			{
				"currency": "INR",
				"code": 356,
				"is_virtual": false
			},
			{
				"currency": "USDT",
				"code": 8402,
				"is_virtual": false
			},
			{
				"currency": "PHP",
				"code": 608,
				"is_virtual": false
			},
			{
				"currency": "MMK",
				"code": 104,
				"is_virtual": false
			},
			{
				"currency": "BRL",
				"code": 986,
				"is_virtual": false
			},
			{
				"currency": "AUD",
				"code": "036",
				"is_virtual": false
			},
			{
				"currency": "PKR",
				"code": 586,
				"is_virtual": false
			},
			{
				"currency": "KHR",
				"code": 116,
				"is_virtual": false
			}
		],
		"result": "ok",
		"profile": {
			"execution_time": 1,
			"server_name": "durian2-54ddd754d6-sbzc6"
		}
	}`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("GET", constants.ACCGetCurrencyAPI, responder)

	l := NewGetCurrencyListLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyList(&request)

	expectedResp := wallet.GetCurrencyListResponse{
		Currency: []string{
			"CNY", "EUR", "GBP", "HKD", "IDR", "KIDR", "JPY", "KRW", "MYR", "SGD", "THB", "TWD", "USD", "VND", "KVD", "INR", "USDT", "PHP", "MMK", "BRL", "AUD", "PKR", "KHR",
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, &expectedResp, resp)
}

func TestGetCurrencyListLogic_IsVirtual(t *testing.T) {
	request := wallet.GetCurrencyListRequest{
		IsVirtual: true,
	}

	responder := httpmock.NewStringResponder(http.StatusOK, `{
		"ret": [],
		"result": "ok",
		"profile": {
			"execution_time": 1,
			"server_name": "durian2-54ddd754d6-sbzc6"
		}
	}`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("GET", constants.ACCGetCurrencyAPI, responder)

	l := NewGetCurrencyListLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyList(&request)

	expectedResp := wallet.GetCurrencyListResponse{
		Currency: []string{},
	}

	assert.NoError(t, err)
	assert.Equal(t, &expectedResp, resp)
}

func TestGetCurrencyListLogic_ConnectionFailed(t *testing.T) {
	request := wallet.GetCurrencyListRequest{}

	responder := httpmock.NewErrorResponder(http.ErrHandlerTimeout)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("GET", constants.ACCGetCurrencyAPI, responder)

	l := NewGetCurrencyListLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyList(&request)

	assert.Equal(t, errorx.ConnectionFailed, err)
	assert.Nil(t, resp)
}

func TestGetCurrencyListLogic_HttpStatusNotOk(t *testing.T) {
	request := wallet.GetCurrencyListRequest{}

	responder := httpmock.NewStringResponder(503, `ServiceUnavailable`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("GET", constants.ACCGetCurrencyAPI, responder)

	l := NewGetCurrencyListLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyList(&request)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}

func TestGetCurrencyListLogic_JSONParseFailed(t *testing.T) {
	request := wallet.GetCurrencyListRequest{}

	responder := httpmock.NewStringResponder(200, `ok`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("GET", constants.ACCGetCurrencyAPI, responder)

	l := NewGetCurrencyListLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyList(&request)

	assert.Equal(t, errorx.JSONParseFailed, err)
	assert.Nil(t, resp)
}

func TestGetCurrencyListLogic_ResultNotOk(t *testing.T) {
	request := wallet.GetCurrencyListRequest{}

	responder := httpmock.NewStringResponder(http.StatusOK, `{"result":"error"}`)

	httpmock.ActivateNonDefault(svcCtx.AccClient.GetClient())
	defer httpmock.DeactivateAndReset()
	httpmock.RegisterResponder("GET", constants.ACCGetCurrencyAPI, responder)

	l := NewGetCurrencyListLogic(ctx, svcCtx)
	resp, err := l.GetCurrencyList(&request)

	assert.Equal(t, errorx.InvalidResponse, err)
	assert.Nil(t, resp)
}
