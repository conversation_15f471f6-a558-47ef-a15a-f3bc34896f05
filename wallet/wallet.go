package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/database"
	"gbh/logger"
	"gbh/proto/wallet"
	"gbh/rpcserver"
	"gbh/wallet/internal/config"
	"gbh/wallet/internal/server"
	"gbh/wallet/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/wallet.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)

	if logErr != nil {
		log.Fatal(logErr)
	}

	universalDB, err := database.New(c.UniversalDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		UniversalDB: universalDB,
	}

	ctx := svc.NewServiceContext(c, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		wallet.RegisterWalletServer(grpcServer, server.NewWalletServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "wallet",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
