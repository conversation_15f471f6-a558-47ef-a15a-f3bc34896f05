// Code generated by goctl. DO NOT EDIT.
// Source: wallet.proto

package walletclient

import (
	"context"

	"gbh/proto/wallet"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AccountCloseDateRequest           = wallet.AccountCloseDateRequest
	AccountCloseDateResponse          = wallet.AccountCloseDateResponse
	BalanceByAgentInfo                = wallet.BalanceByAgentInfo
	BalanceInfo                       = wallet.BalanceInfo
	BoolValue                         = wallet.BoolValue
	DepositRequest                    = wallet.DepositRequest
	DepositResponse                   = wallet.DepositResponse
	EmptyRequest                      = wallet.EmptyRequest
	EntryDetail                       = wallet.EntryDetail
	EntryStatusRequest                = wallet.EntryStatusRequest
	EntryStatusResponse               = wallet.EntryStatusResponse
	ExchangeRate                      = wallet.ExchangeRate
	ExchangeRateListResponse          = wallet.ExchangeRateListResponse
	FishMachineBalanceRequest         = wallet.FishMachineBalanceRequest
	FishMachineBalanceResponse        = wallet.FishMachineBalanceResponse
	GetCurrencyListRequest            = wallet.GetCurrencyListRequest
	GetCurrencyListResponse           = wallet.GetCurrencyListResponse
	GetEntryListRequest               = wallet.GetEntryListRequest
	GetEntryListResponse              = wallet.GetEntryListResponse
	GetExchangeRateRequest            = wallet.GetExchangeRateRequest
	GetExchangeRateResponse           = wallet.GetExchangeRateResponse
	GetOpCodeDictRequest              = wallet.GetOpCodeDictRequest
	GetOpCodeDictResponse             = wallet.GetOpCodeDictResponse
	GetOpCodeListRequest              = wallet.GetOpCodeListRequest
	GetOpCodeListResponse             = wallet.GetOpCodeListResponse
	GetOpcodeGroupsListRequest        = wallet.GetOpcodeGroupsListRequest
	GetOpcodeGroupsListResponse       = wallet.GetOpcodeGroupsListResponse
	GetOpcodeListByACCRequest         = wallet.GetOpcodeListByACCRequest
	GetOpcodeListByACCResponse        = wallet.GetOpcodeListByACCResponse
	GetTotalAmountRequest             = wallet.GetTotalAmountRequest
	GetTotalAmountResponse            = wallet.GetTotalAmountResponse
	GetTransferEntryDetailAllRequest  = wallet.GetTransferEntryDetailAllRequest
	GetTransferEntryDetailAllResponse = wallet.GetTransferEntryDetailAllResponse
	GetTransferEntryDetailRequest     = wallet.GetTransferEntryDetailRequest
	GetTransferEntryDetailResponse    = wallet.GetTransferEntryDetailResponse
	GetUserBalanceRequest             = wallet.GetUserBalanceRequest
	GetUserBalanceResponse            = wallet.GetUserBalanceResponse
	GetUserCashFakeRequest            = wallet.GetUserCashFakeRequest
	GetUserCashFakeResponse           = wallet.GetUserCashFakeResponse
	Int64Value                        = wallet.Int64Value
	OpCodeDetail                      = wallet.OpCodeDetail
	OpcodeDetailByACC                 = wallet.OpcodeDetailByACC
	OpcodeGroupsList                  = wallet.OpcodeGroupsList
	Pagination                        = wallet.Pagination
	PaginationByGTI                   = wallet.PaginationByGTI
	RepeatedUint32Value               = wallet.RepeatedUint32Value
	SlotMachineBalanceRequest         = wallet.SlotMachineBalanceRequest
	SlotMachineBalanceResponse        = wallet.SlotMachineBalanceResponse
	StringValue                       = wallet.StringValue
	TransferEntry                     = wallet.TransferEntry
	TransferEntryDetail               = wallet.TransferEntryDetail
	TransferEntryListRequest          = wallet.TransferEntryListRequest
	TransferEntryListResponse         = wallet.TransferEntryListResponse
	TransferEntryRequest              = wallet.TransferEntryRequest
	TransferEntryResponse             = wallet.TransferEntryResponse
	Uint32Value                       = wallet.Uint32Value
	UsersBalanceByAgentRequest        = wallet.UsersBalanceByAgentRequest
	UsersBalanceByAgentResponse       = wallet.UsersBalanceByAgentResponse
	UsersBalanceRequest               = wallet.UsersBalanceRequest
	UsersBalanceResponse              = wallet.UsersBalanceResponse
	WithdrawRequest                   = wallet.WithdrawRequest
	WithdrawResponse                  = wallet.WithdrawResponse

	Wallet interface {
		EntryStatus(ctx context.Context, in *EntryStatusRequest, opts ...grpc.CallOption) (*EntryStatusResponse, error)
		GetUsersBalance(ctx context.Context, in *UsersBalanceRequest, opts ...grpc.CallOption) (*UsersBalanceResponse, error)
		SlotMachineBalance(ctx context.Context, in *SlotMachineBalanceRequest, opts ...grpc.CallOption) (*SlotMachineBalanceResponse, error)
		Withdraw(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error)
		Deposit(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error)
		TransferEntry(ctx context.Context, in *TransferEntryRequest, opts ...grpc.CallOption) (*TransferEntryResponse, error)
		TransferEntryList(ctx context.Context, in *TransferEntryListRequest, opts ...grpc.CallOption) (*TransferEntryListResponse, error)
		FishMachineBalance(ctx context.Context, in *FishMachineBalanceRequest, opts ...grpc.CallOption) (*FishMachineBalanceResponse, error)
		GetUsersBalanceByAgent(ctx context.Context, in *UsersBalanceByAgentRequest, opts ...grpc.CallOption) (*UsersBalanceByAgentResponse, error)
		GetUserBalance(ctx context.Context, in *GetUserBalanceRequest, opts ...grpc.CallOption) (*GetUserBalanceResponse, error)
		GetEntryList(ctx context.Context, in *GetEntryListRequest, opts ...grpc.CallOption) (*GetEntryListResponse, error)
		GetTotalAmount(ctx context.Context, in *GetTotalAmountRequest, opts ...grpc.CallOption) (*GetTotalAmountResponse, error)
		GetOpCodeList(ctx context.Context, in *GetOpCodeListRequest, opts ...grpc.CallOption) (*GetOpCodeListResponse, error)
		GetOpCodeDict(ctx context.Context, in *GetOpCodeDictRequest, opts ...grpc.CallOption) (*GetOpCodeDictResponse, error)
		GetAccountCloseDate(ctx context.Context, in *AccountCloseDateRequest, opts ...grpc.CallOption) (*AccountCloseDateResponse, error)
		GetTransferEntryDetail(ctx context.Context, in *GetTransferEntryDetailRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailResponse, error)
		GetUserCashFake(ctx context.Context, in *GetUserCashFakeRequest, opts ...grpc.CallOption) (*GetUserCashFakeResponse, error)
		GetOpcodeListByACC(ctx context.Context, in *GetOpcodeListByACCRequest, opts ...grpc.CallOption) (*GetOpcodeListByACCResponse, error)
		GetExchangeRate(ctx context.Context, in *GetExchangeRateRequest, opts ...grpc.CallOption) (*GetExchangeRateResponse, error)
		ExchangeRateList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ExchangeRateListResponse, error)
		GetOpcodeGroupsList(ctx context.Context, in *GetOpcodeGroupsListRequest, opts ...grpc.CallOption) (*GetOpcodeGroupsListResponse, error)
		GetTransferEntryDetailAll(ctx context.Context, in *GetTransferEntryDetailAllRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailAllResponse, error)
		GetCurrencyList(ctx context.Context, in *GetCurrencyListRequest, opts ...grpc.CallOption) (*GetCurrencyListResponse, error)
	}

	defaultWallet struct {
		cli zrpc.Client
	}
)

func NewWallet(cli zrpc.Client) Wallet {
	return &defaultWallet{
		cli: cli,
	}
}

func (m *defaultWallet) EntryStatus(ctx context.Context, in *EntryStatusRequest, opts ...grpc.CallOption) (*EntryStatusResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.EntryStatus(ctx, in, opts...)
}

func (m *defaultWallet) GetUsersBalance(ctx context.Context, in *UsersBalanceRequest, opts ...grpc.CallOption) (*UsersBalanceResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetUsersBalance(ctx, in, opts...)
}

func (m *defaultWallet) SlotMachineBalance(ctx context.Context, in *SlotMachineBalanceRequest, opts ...grpc.CallOption) (*SlotMachineBalanceResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.SlotMachineBalance(ctx, in, opts...)
}

func (m *defaultWallet) Withdraw(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.Withdraw(ctx, in, opts...)
}

func (m *defaultWallet) Deposit(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.Deposit(ctx, in, opts...)
}

func (m *defaultWallet) TransferEntry(ctx context.Context, in *TransferEntryRequest, opts ...grpc.CallOption) (*TransferEntryResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.TransferEntry(ctx, in, opts...)
}

func (m *defaultWallet) TransferEntryList(ctx context.Context, in *TransferEntryListRequest, opts ...grpc.CallOption) (*TransferEntryListResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.TransferEntryList(ctx, in, opts...)
}

func (m *defaultWallet) FishMachineBalance(ctx context.Context, in *FishMachineBalanceRequest, opts ...grpc.CallOption) (*FishMachineBalanceResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.FishMachineBalance(ctx, in, opts...)
}

func (m *defaultWallet) GetUsersBalanceByAgent(ctx context.Context, in *UsersBalanceByAgentRequest, opts ...grpc.CallOption) (*UsersBalanceByAgentResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetUsersBalanceByAgent(ctx, in, opts...)
}

func (m *defaultWallet) GetUserBalance(ctx context.Context, in *GetUserBalanceRequest, opts ...grpc.CallOption) (*GetUserBalanceResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetUserBalance(ctx, in, opts...)
}

func (m *defaultWallet) GetEntryList(ctx context.Context, in *GetEntryListRequest, opts ...grpc.CallOption) (*GetEntryListResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetEntryList(ctx, in, opts...)
}

func (m *defaultWallet) GetTotalAmount(ctx context.Context, in *GetTotalAmountRequest, opts ...grpc.CallOption) (*GetTotalAmountResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetTotalAmount(ctx, in, opts...)
}

func (m *defaultWallet) GetOpCodeList(ctx context.Context, in *GetOpCodeListRequest, opts ...grpc.CallOption) (*GetOpCodeListResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetOpCodeList(ctx, in, opts...)
}

func (m *defaultWallet) GetOpCodeDict(ctx context.Context, in *GetOpCodeDictRequest, opts ...grpc.CallOption) (*GetOpCodeDictResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetOpCodeDict(ctx, in, opts...)
}

func (m *defaultWallet) GetAccountCloseDate(ctx context.Context, in *AccountCloseDateRequest, opts ...grpc.CallOption) (*AccountCloseDateResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetAccountCloseDate(ctx, in, opts...)
}

func (m *defaultWallet) GetTransferEntryDetail(ctx context.Context, in *GetTransferEntryDetailRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetTransferEntryDetail(ctx, in, opts...)
}

func (m *defaultWallet) GetUserCashFake(ctx context.Context, in *GetUserCashFakeRequest, opts ...grpc.CallOption) (*GetUserCashFakeResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetUserCashFake(ctx, in, opts...)
}

func (m *defaultWallet) GetOpcodeListByACC(ctx context.Context, in *GetOpcodeListByACCRequest, opts ...grpc.CallOption) (*GetOpcodeListByACCResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetOpcodeListByACC(ctx, in, opts...)
}

func (m *defaultWallet) GetExchangeRate(ctx context.Context, in *GetExchangeRateRequest, opts ...grpc.CallOption) (*GetExchangeRateResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetExchangeRate(ctx, in, opts...)
}

func (m *defaultWallet) ExchangeRateList(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*ExchangeRateListResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.ExchangeRateList(ctx, in, opts...)
}

func (m *defaultWallet) GetOpcodeGroupsList(ctx context.Context, in *GetOpcodeGroupsListRequest, opts ...grpc.CallOption) (*GetOpcodeGroupsListResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetOpcodeGroupsList(ctx, in, opts...)
}

func (m *defaultWallet) GetTransferEntryDetailAll(ctx context.Context, in *GetTransferEntryDetailAllRequest, opts ...grpc.CallOption) (*GetTransferEntryDetailAllResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetTransferEntryDetailAll(ctx, in, opts...)
}

func (m *defaultWallet) GetCurrencyList(ctx context.Context, in *GetCurrencyListRequest, opts ...grpc.CallOption) (*GetCurrencyListResponse, error) {
	client := wallet.NewWalletClient(m.cli.Conn())
	return client.GetCurrencyList(ctx, in, opts...)
}
