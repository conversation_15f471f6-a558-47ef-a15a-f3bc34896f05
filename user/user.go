package main

import (
	"flag"
	"fmt"

	"gbh/proto/user"
	"gbh/rpcserver"
	"gbh/user/internal/config"
	"gbh/user/internal/server"
	"gbh/user/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/user.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	ctx := svc.NewServiceContext(c)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		user.RegisterUserServer(grpcServer, server.NewUserServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "user",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
