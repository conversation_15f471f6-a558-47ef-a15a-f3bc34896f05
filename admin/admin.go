package main

import (
	"flag"
	"fmt"
	"log"

	"gbh/admin/internal/config"
	"gbh/admin/internal/server"
	"gbh/admin/internal/svc"
	"gbh/database"
	"gbh/logger"
	"gbh/proto/admin"
	"gbh/rpcserver"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/admin.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	customLogger, logErr := logger.New(c.LogConf)
	if logErr != nil {
		log.Fatal(logErr)
	}

	adminDB, err := database.New(c.AdminDBConf, customLogger)
	if err != nil {
		log.Fatalln(err)
	}

	extSvc := svc.ExternalContext{
		AdminDB: adminDB,
	}

	ctx := svc.NewServiceContext(c, customLogger, extSvc)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		admin.RegisterAdminServer(grpcServer, server.NewAdminServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	statConf := rpcserver.StatConf{
		MetricsName:          "admin",
		IgnoreContentMethods: []string{}, // 可填要略過的方法
	}

	s.AddUnaryInterceptors(rpcserver.LoggingInterceptor(statConf))

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
